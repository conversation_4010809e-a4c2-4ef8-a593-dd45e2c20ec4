# 🔌 Integration SDK

[![npm version](https://badge.fury.io/js/%40saas-kit%2Fintegration-sdk.svg)](https://badge.fury.io/js/%40saas-kit%2Fintegration-sdk)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/%3C%2F%3E-TypeScript-%230074c1.svg)](http://www.typescriptlang.org/)

Universal OAuth and export SDK for SaaS applications. Provides plug-and-play authentication and export functionality for 10+ popular services with consistent interfaces.

## ✨ Features

- 🔐 **Universal OAuth**: Unified authentication for all providers
- 📤 **Export Engine**: Consistent export interface across services
- 🔄 **Token Management**: Automatic refresh and secure storage
- ⚡ **Rate Limiting**: Built-in rate limiting and retry logic
- 🛡️ **Type Safety**: Full TypeScript support
- 🔧 **Extensible**: Easy to add new providers
- 🌐 **Framework Agnostic**: Works with any Node.js framework

## Supported Integrations

### **Productivity**
- Notion (pages, databases)
- Google Drive (files, folders)
- Dropbox (files, sharing)
- OneDrive (files, folders)

### **CRM & Sales**
- HubSpot (contacts, deals, activities)
- Salesforce (leads, opportunities, accounts)
- Pipedrive (deals, activities)
- Airtable (bases, records)

### **Communication**
- Slack (channels, messages, files)
- Microsoft Teams (channels, messages)
- Discord (channels, messages)

### **Project Management**
- Trello (boards, cards, lists)
- Asana (projects, tasks)
- Monday.com (boards, items)
- ClickUp (spaces, tasks)

## Installation

```bash
npm install @your-org/integration-sdk
# or
yarn add @your-org/integration-sdk
# or
pnpm add @your-org/integration-sdk
```

## Quick Start

### **1. Initialize SDK**
```typescript
import { IntegrationSDK } from '@your-org/integration-sdk';

const sdk = new IntegrationSDK({
  // OAuth credentials
  providers: {
    notion: {
      clientId: process.env.NOTION_CLIENT_ID,
      clientSecret: process.env.NOTION_CLIENT_SECRET,
    },
    'google-drive': {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    },
    slack: {
      clientId: process.env.SLACK_CLIENT_ID,
      clientSecret: process.env.SLACK_CLIENT_SECRET,
    }
  },
  
  // Token storage (implement your own)
  tokenStorage: new DatabaseTokenStorage({
    connectionString: process.env.DATABASE_URL
  }),
  
  // Optional: Custom rate limiting
  rateLimiter: new RedisRateLimiter({
    redis: redisClient
  })
});
```

### **2. OAuth Authentication**
```typescript
// Generate auth URL
const { authUrl, state } = await sdk.generateAuthUrl({
  provider: 'notion',
  userId: 'user-123',
  organizationId: 'org-456',
  redirectUri: 'https://yourapp.com/auth/callback',
  scopes: ['read_content', 'insert_content']
});

// Exchange code for token
const authResult = await sdk.exchangeCodeForToken({
  provider: 'notion',
  code: 'auth-code-from-callback',
  state: 'state-from-auth-url',
  redirectUri: 'https://yourapp.com/auth/callback'
});
```

### **3. Export Content**
```typescript
// Export to Notion
const exportResult = await sdk.export({
  provider: 'notion',
  userId: 'user-123',
  organizationId: 'org-456',
  payload: {
    title: 'Meeting Summary',
    content: 'Summary content here...',
    format: 'markdown',
    metadata: {
      tags: ['meeting', 'summary'],
      createdAt: new Date().toISOString()
    }
  },
  destination: {
    type: 'database',
    id: 'notion-database-id'
  }
});

// Export to Google Drive
const driveResult = await sdk.export({
  provider: 'google-drive',
  userId: 'user-123',
  organizationId: 'org-456',
  payload: {
    title: 'Report.pdf',
    content: pdfBuffer,
    format: 'binary',
    mimeType: 'application/pdf'
  },
  destination: {
    type: 'folder',
    id: 'google-drive-folder-id'
  }
});
```

### **4. Batch Operations**
```typescript
// Export to multiple services
const batchResult = await sdk.batchExport({
  userId: 'user-123',
  organizationId: 'org-456',
  exports: [
    {
      provider: 'notion',
      payload: notionPayload,
      destination: notionDestination
    },
    {
      provider: 'slack',
      payload: slackPayload,
      destination: slackDestination
    }
  ]
});
```

## Advanced Usage

### **Custom Provider**
```typescript
import { BaseProvider } from '@your-org/integration-sdk';

class CustomProvider extends BaseProvider {
  name = 'custom-service';
  displayName = 'Custom Service';
  
  async authenticate(config: AuthConfig): Promise<AuthResult> {
    // Custom OAuth implementation
  }
  
  async export(payload: ExportPayload): Promise<ExportResult> {
    // Custom export implementation
  }
}

// Register custom provider
sdk.registerProvider(new CustomProvider());
```

### **Webhook Handling**
```typescript
// Handle webhooks from integrated services
app.post('/webhooks/:provider', async (req, res) => {
  const result = await sdk.handleWebhook({
    provider: req.params.provider,
    payload: req.body,
    signature: req.headers['x-signature'],
    secret: process.env.WEBHOOK_SECRET
  });
  
  res.json(result);
});
```

### **Token Management**
```typescript
// Check token status
const tokenStatus = await sdk.getTokenStatus({
  userId: 'user-123',
  organizationId: 'org-456',
  provider: 'notion'
});

// Refresh expired token
if (tokenStatus.expired) {
  await sdk.refreshToken({
    userId: 'user-123',
    organizationId: 'org-456',
    provider: 'notion'
  });
}

// Revoke token
await sdk.revokeToken({
  userId: 'user-123',
  organizationId: 'org-456',
  provider: 'notion'
});
```

## Configuration

### **Environment Variables**
```bash
# OAuth Credentials
NOTION_CLIENT_ID=your_notion_client_id
NOTION_CLIENT_SECRET=your_notion_client_secret
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
SLACK_CLIENT_ID=your_slack_client_id
SLACK_CLIENT_SECRET=your_slack_client_secret

# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/db

# Redis (optional)
REDIS_URL=redis://localhost:6379

# Encryption
TOKEN_ENCRYPTION_KEY=your-32-character-encryption-key
```

### **Token Storage Interface**
```typescript
interface TokenStorage {
  store(token: OAuthToken): Promise<void>;
  retrieve(userId: string, organizationId: string, provider: string): Promise<OAuthToken | null>;
  update(tokenId: string, updates: Partial<OAuthToken>): Promise<void>;
  delete(tokenId: string): Promise<void>;
  cleanup(olderThan: Date): Promise<number>;
}

// Example implementation
class DatabaseTokenStorage implements TokenStorage {
  constructor(private db: Database) {}
  
  async store(token: OAuthToken): Promise<void> {
    await this.db.query(
      'INSERT INTO oauth_tokens (...) VALUES (...)',
      [token.userId, token.organizationId, ...]
    );
  }
  
  // ... implement other methods
}
```

## Error Handling

```typescript
import { 
  IntegrationError, 
  AuthenticationError, 
  RateLimitError,
  QuotaExceededError 
} from '@your-org/integration-sdk';

try {
  await sdk.export({ ... });
} catch (error) {
  if (error instanceof AuthenticationError) {
    // Handle auth failure - redirect to re-auth
    console.log('Authentication failed:', error.message);
  } else if (error instanceof RateLimitError) {
    // Handle rate limiting - retry after delay
    console.log('Rate limited, retry after:', error.retryAfter);
  } else if (error instanceof QuotaExceededError) {
    // Handle quota exceeded - upgrade plan
    console.log('Quota exceeded:', error.quotaType);
  }
}
```

## Monitoring & Analytics

```typescript
// Built-in analytics
sdk.on('export.success', (event) => {
  console.log('Export successful:', event);
});

sdk.on('export.failure', (event) => {
  console.log('Export failed:', event);
});

sdk.on('auth.success', (event) => {
  console.log('Authentication successful:', event);
});

// Get usage statistics
const stats = await sdk.getUsageStats({
  userId: 'user-123',
  organizationId: 'org-456',
  dateRange: {
    start: '2024-01-01',
    end: '2024-01-31'
  }
});
```

## Testing

```typescript
import { MockIntegrationSDK } from '@your-org/integration-sdk/testing';

// Use mock SDK in tests
const mockSdk = new MockIntegrationSDK();

// Mock successful export
mockSdk.mockExport('notion', {
  success: true,
  destinationUrl: 'https://notion.so/page-id'
});

// Test your code
const result = await yourFunction(mockSdk);
expect(result.success).toBe(true);
```

## Framework Integrations

### **Next.js**
```typescript
// pages/api/integrations/[provider]/auth.ts
import { IntegrationSDK } from '@your-org/integration-sdk';

export default async function handler(req, res) {
  const { provider } = req.query;
  
  const { authUrl } = await sdk.generateAuthUrl({
    provider,
    userId: req.user.id,
    organizationId: req.user.organizationId,
    redirectUri: `${process.env.NEXT_PUBLIC_URL}/auth/callback`
  });
  
  res.redirect(authUrl);
}
```

### **Express.js**
```typescript
import express from 'express';
import { IntegrationSDK } from '@your-org/integration-sdk';

const app = express();

app.get('/auth/:provider', async (req, res) => {
  const { authUrl } = await sdk.generateAuthUrl({
    provider: req.params.provider,
    userId: req.user.id,
    organizationId: req.user.organizationId,
    redirectUri: `${process.env.BASE_URL}/auth/callback`
  });
  
  res.redirect(authUrl);
});
```

## License
MIT

## Contributing
See [CONTRIBUTING.md](./CONTRIBUTING.md) for guidelines.

## Support
- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/your-server)
- 📖 Docs: [Full documentation](https://docs.your-org.com/integration-sdk)
