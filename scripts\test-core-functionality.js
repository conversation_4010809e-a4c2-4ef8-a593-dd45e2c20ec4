#!/usr/bin/env node

/**
 * COMPREHENSIVE CORE FUNCTIONALITY TESTING
 * 
 * Tests all primary SaaS features for production readiness:
 * ✅ Dashboard loading and data display
 * ✅ Slack integration endpoints
 * ✅ File upload functionality
 * ✅ AI summarization features
 * ✅ API endpoint responses
 * ✅ Database connectivity
 * ✅ Error handling
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const http = require('http');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logHeader(message) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`🔍 ${message}`, 'cyan');
  log(`${'='.repeat(60)}`, 'cyan');
}

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

function addTestResult(name, status, message) {
  testResults.tests.push({ name, status, message });
  if (status === 'PASS') {
    testResults.passed++;
    logSuccess(`${name}: ${message}`);
  } else if (status === 'FAIL') {
    testResults.failed++;
    logError(`${name}: ${message}`);
  } else if (status === 'WARN') {
    testResults.warnings++;
    logWarning(`${name}: ${message}`);
  }
}

// Check if file exists
function fileExists(filePath) {
  return fs.existsSync(path.join(process.cwd(), filePath));
}

// Read file content
function readFile(filePath) {
  try {
    return fs.readFileSync(path.join(process.cwd(), filePath), 'utf8');
  } catch (error) {
    return null;
  }
}

// Test API endpoint availability
async function testAPIEndpoint(endpoint, expectedStatus = 200) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: endpoint,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      resolve({
        success: true,
        status: res.statusCode,
        message: `Status: ${res.statusCode}`
      });
    });

    req.on('error', (error) => {
      resolve({
        success: false,
        status: 0,
        message: `Connection failed: ${error.message}`
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        status: 0,
        message: 'Request timeout'
      });
    });

    req.end();
  });
}

// Test core application files
function testCoreFiles() {
  logHeader('CORE APPLICATION FILES VALIDATION');
  
  const coreFiles = [
    'app/dashboard/page.tsx',
    'app/upload/page.tsx',
    'app/slack/connect/page.tsx',
    'lib/supabase-browser.ts',
    'lib/supabase-server.ts',
    'components/AuthGuard.tsx'
  ];
  
  coreFiles.forEach(file => {
    if (fileExists(file)) {
      const content = readFile(file);
      
      // Check for error boundaries
      if (content.includes('ErrorBoundary') || content.includes('try') || content.includes('catch')) {
        addTestResult(`Error Handling: ${file}`, 'PASS', 'Has error handling');
      } else {
        addTestResult(`Error Handling: ${file}`, 'WARN', 'May lack error handling');
      }
      
      addTestResult(`File Exists: ${file}`, 'PASS', 'Core file found');
    } else {
      addTestResult(`File Exists: ${file}`, 'FAIL', 'Core file missing');
    }
  });
}

// Test API routes
async function testAPIRoutes() {
  logHeader('API ENDPOINTS VALIDATION');
  
  const apiEndpoints = [
    { path: '/api/health', name: 'Health Check' },
    { path: '/api/dashboard', name: 'Dashboard API' },
    { path: '/api/slack/auth', name: 'Slack Auth' },
    { path: '/api/upload', name: 'Upload API' },
    { path: '/api/summarize', name: 'Summarization API' }
  ];
  
  for (const endpoint of apiEndpoints) {
    logInfo(`Testing ${endpoint.name}...`);
    const result = await testAPIEndpoint(endpoint.path);
    
    if (result.success && result.status < 500) {
      addTestResult(`API Endpoint: ${endpoint.name}`, 'PASS', result.message);
    } else {
      addTestResult(`API Endpoint: ${endpoint.name}`, 'FAIL', result.message);
    }
  }
}

// Test Slack integration components
function testSlackIntegration() {
  logHeader('SLACK INTEGRATION VALIDATION');
  
  const slackFiles = [
    'app/api/slack/auth/route.ts',
    'app/api/slack/callback/route.ts',
    'app/api/slack/summarize/route.ts',
    'app/slack/connect/page.tsx'
  ];
  
  slackFiles.forEach(file => {
    if (fileExists(file)) {
      const content = readFile(file);
      
      // Check for Slack API integration
      if (content.includes('slack') || content.includes('SLACK')) {
        addTestResult(`Slack Integration: ${file}`, 'PASS', 'Contains Slack integration code');
      } else {
        addTestResult(`Slack Integration: ${file}`, 'WARN', 'May not have Slack integration');
      }
      
      addTestResult(`Slack File: ${file}`, 'PASS', 'Slack component found');
    } else {
      addTestResult(`Slack File: ${file}`, 'WARN', 'Optional Slack component missing');
    }
  });
  
  // Check environment variables
  const envContent = readFile('.env.local');
  if (envContent) {
    if (envContent.includes('SLACK_CLIENT_ID') && envContent.includes('SLACK_CLIENT_SECRET')) {
      addTestResult('Slack Environment', 'PASS', 'Slack credentials configured');
    } else {
      addTestResult('Slack Environment', 'FAIL', 'Slack credentials missing');
    }
  }
}

// Test AI integration
function testAIIntegration() {
  logHeader('AI INTEGRATION VALIDATION');
  
  const aiFiles = [
    'app/api/summarize/route.ts',
    'app/api/ai/compare/route.ts',
    'app/api/ai/models/route.ts'
  ];
  
  aiFiles.forEach(file => {
    if (fileExists(file)) {
      const content = readFile(file);
      
      // Check for AI service integration
      if (content.includes('openrouter') || content.includes('OpenRouter') || content.includes('AI')) {
        addTestResult(`AI Integration: ${file}`, 'PASS', 'Contains AI integration code');
      } else {
        addTestResult(`AI Integration: ${file}`, 'WARN', 'May not have AI integration');
      }
      
      addTestResult(`AI File: ${file}`, 'PASS', 'AI component found');
    } else {
      addTestResult(`AI File: ${file}`, 'WARN', 'Optional AI component missing');
    }
  });
  
  // Check environment variables
  const envContent = readFile('.env.local');
  if (envContent) {
    if (envContent.includes('OPENROUTER_API_KEY')) {
      addTestResult('AI Environment', 'PASS', 'OpenRouter API key configured');
    } else {
      addTestResult('AI Environment', 'FAIL', 'OpenRouter API key missing');
    }
  }
}

// Test file upload functionality
function testFileUpload() {
  logHeader('FILE UPLOAD FUNCTIONALITY');
  
  const uploadFiles = [
    'app/upload/page.tsx',
    'app/api/upload/route.ts',
    'app/api/upload/status/route.ts'
  ];
  
  uploadFiles.forEach(file => {
    if (fileExists(file)) {
      const content = readFile(file);
      
      // Check for file handling
      if (content.includes('FormData') || content.includes('file') || content.includes('upload')) {
        addTestResult(`Upload Feature: ${file}`, 'PASS', 'Contains file upload code');
      } else {
        addTestResult(`Upload Feature: ${file}`, 'WARN', 'May not handle file uploads');
      }
      
      addTestResult(`Upload File: ${file}`, 'PASS', 'Upload component found');
    } else {
      addTestResult(`Upload File: ${file}`, 'WARN', 'Optional upload component missing');
    }
  });
}

// Test database connectivity
function testDatabaseConnectivity() {
  logHeader('DATABASE CONNECTIVITY');
  
  // Check Supabase configuration
  const envContent = readFile('.env.local');
  if (envContent) {
    const hasSupabaseUrl = envContent.includes('NEXT_PUBLIC_SUPABASE_URL');
    const hasSupabaseKey = envContent.includes('NEXT_PUBLIC_SUPABASE_ANON_KEY');
    const hasServiceKey = envContent.includes('SUPABASE_SERVICE_ROLE_KEY');
    
    if (hasSupabaseUrl && hasSupabaseKey && hasServiceKey) {
      addTestResult('Database Configuration', 'PASS', 'Supabase fully configured');
    } else {
      addTestResult('Database Configuration', 'FAIL', 'Incomplete Supabase configuration');
    }
  }
  
  // Check for database client files
  if (fileExists('lib/supabase-browser.ts') && fileExists('lib/supabase-server.ts')) {
    addTestResult('Database Clients', 'PASS', 'Both browser and server clients exist');
  } else {
    addTestResult('Database Clients', 'FAIL', 'Missing database client files');
  }
}

// Generate test report
function generateReport() {
  logHeader('CORE FUNCTIONALITY TESTING REPORT');
  
  log(`\n📊 Test Results Summary:`, 'bright');
  log(`   ✅ Passed: ${testResults.passed}`, 'green');
  log(`   ❌ Failed: ${testResults.failed}`, 'red');
  log(`   ⚠️  Warnings: ${testResults.warnings}`, 'yellow');
  log(`   📝 Total Tests: ${testResults.tests.length}`, 'blue');
  
  const successRate = ((testResults.passed / testResults.tests.length) * 100).toFixed(1);
  log(`   📈 Success Rate: ${successRate}%`, successRate >= 80 ? 'green' : 'red');
  
  if (testResults.failed === 0) {
    log(`\n🎉 ALL CRITICAL TESTS PASSED! Core functionality is production-ready.`, 'green');
  } else {
    log(`\n🚨 ${testResults.failed} CRITICAL ISSUES FOUND. Please fix before deployment.`, 'red');
  }
  
  // Detailed results
  log(`\n📋 Detailed Test Results:`, 'bright');
  testResults.tests.forEach(test => {
    const icon = test.status === 'PASS' ? '✅' : test.status === 'FAIL' ? '❌' : '⚠️';
    log(`   ${icon} ${test.name}: ${test.message}`);
  });
}

// Main execution
async function main() {
  log('🚀 Starting Comprehensive Core Functionality Testing...', 'bright');
  
  testCoreFiles();
  testSlackIntegration();
  testAIIntegration();
  testFileUpload();
  testDatabaseConnectivity();
  
  // Test API endpoints (requires server to be running)
  logInfo('Checking if development server is running...');
  const serverCheck = await testAPIEndpoint('/api/health');
  if (serverCheck.success) {
    await testAPIRoutes();
  } else {
    // Try alternative health check
    const altCheck = await testAPIEndpoint('/api/healthcheck');
    if (altCheck.success) {
      await testAPIRoutes();
    } else {
      addTestResult('Development Server', 'WARN', 'Server not running - API tests skipped');
    }
  }
  
  generateReport();
  
  // Exit with appropriate code
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  testCoreFiles,
  testSlackIntegration,
  testAIIntegration,
  testFileUpload,
  testDatabaseConnectivity,
  generateReport
};
