#!/usr/bin/env node

/**
 * Production Deployment Validation Script
 * Validates the application is ready for production deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Production Deployment Validation');
console.log('=====================================\n');

let validationErrors = [];
let validationWarnings = [];
let validationPassed = [];

// Helper function to check if file exists
function fileExists(filePath) {
  return fs.existsSync(path.join(process.cwd(), filePath));
}

// Helper function to read file content
function readFile(filePath) {
  try {
    return fs.readFileSync(path.join(process.cwd(), filePath), 'utf8');
  } catch (error) {
    return null;
  }
}

// 1. Check build artifacts
console.log('📦 Checking Build Artifacts...');
if (fileExists('.next')) {
  validationPassed.push('✅ Next.js build directory exists');
} else {
  validationErrors.push('❌ Next.js build directory missing - run npm run build');
}

if (fileExists('.next/static')) {
  validationPassed.push('✅ Static assets generated');
} else {
  validationErrors.push('❌ Static assets missing');
}

// 2. Check environment configuration
console.log('\n🔧 Checking Environment Configuration...');
const envContent = readFile('.env.local');
if (envContent) {
  validationPassed.push('✅ Environment file exists');
  
  // Check NODE_ENV
  if (envContent.includes('NODE_ENV=production')) {
    validationPassed.push('✅ NODE_ENV set to production');
  } else {
    validationWarnings.push('⚠️  NODE_ENV not set to production');
  }
  
  // Check required environment variables
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'OPENROUTER_API_KEY',
    'NEXT_PUBLIC_SITE_URL',
    'SLACK_CLIENT_ID',
    'SLACK_CLIENT_SECRET'
  ];
  
  requiredVars.forEach(varName => {
    if (envContent.includes(`${varName}=`) && !envContent.includes(`${varName}=your_`)) {
      validationPassed.push(`✅ ${varName} configured`);
    } else {
      validationErrors.push(`❌ ${varName} missing or not configured`);
    }
  });
  
  // Check monitoring configuration
  if (envContent.includes('NEXT_PUBLIC_SENTRY_DSN=') && !envContent.includes('NEXT_PUBLIC_SENTRY_DSN=your_')) {
    validationPassed.push('✅ Sentry error tracking configured');
  } else {
    validationWarnings.push('⚠️  Sentry error tracking not configured');
  }
  
  if (envContent.includes('NEXT_PUBLIC_POSTHOG_KEY=') && !envContent.includes('NEXT_PUBLIC_POSTHOG_KEY=phc_your_')) {
    validationPassed.push('✅ PostHog analytics configured');
  } else {
    validationWarnings.push('⚠️  PostHog analytics not configured');
  }
  
} else {
  validationErrors.push('❌ Environment file missing');
}

// 3. Check critical files
console.log('\n📁 Checking Critical Files...');
const criticalFiles = [
  'next.config.mjs',
  'middleware.ts',
  'app/layout.tsx',
  'app/dashboard/page.tsx',
  'lib/supabase-browser.ts',
  'lib/supabase-server.ts'
];

criticalFiles.forEach(file => {
  if (fileExists(file)) {
    validationPassed.push(`✅ ${file} exists`);
  } else {
    validationErrors.push(`❌ ${file} missing`);
  }
});

// 4. Check API routes
console.log('\n🔌 Checking API Routes...');
const apiRoutes = [
  'app/api/health/route.ts',
  'app/api/dashboard/route.ts',
  'app/api/summarize/route.ts',
  'app/api/slack/auth/route.ts',
  'app/api/upload/route.ts'
];

apiRoutes.forEach(route => {
  if (fileExists(route)) {
    validationPassed.push(`✅ ${route} exists`);
  } else {
    validationErrors.push(`❌ ${route} missing`);
  }
});

// 5. Check package.json scripts
console.log('\n📜 Checking Package Scripts...');
const packageJson = readFile('package.json');
if (packageJson) {
  const pkg = JSON.parse(packageJson);
  const requiredScripts = ['build', 'start', 'dev'];
  
  requiredScripts.forEach(script => {
    if (pkg.scripts && pkg.scripts[script]) {
      validationPassed.push(`✅ ${script} script configured`);
    } else {
      validationErrors.push(`❌ ${script} script missing`);
    }
  });
} else {
  validationErrors.push('❌ package.json missing');
}

// 6. Check security configuration
console.log('\n🔒 Checking Security Configuration...');
const middlewareContent = readFile('middleware.ts');
if (middlewareContent) {
  if (middlewareContent.includes('X-Frame-Options')) {
    validationPassed.push('✅ Security headers configured');
  } else {
    validationWarnings.push('⚠️  Security headers not found in middleware');
  }
} else {
  validationWarnings.push('⚠️  Middleware file missing');
}

// 7. Check Next.js configuration
console.log('\n⚙️  Checking Next.js Configuration...');
const nextConfigContent = readFile('next.config.mjs');
if (nextConfigContent) {
  validationPassed.push('✅ Next.js configuration exists');
  
  if (nextConfigContent.includes('compress: true')) {
    validationPassed.push('✅ Compression enabled');
  } else {
    validationWarnings.push('⚠️  Compression not enabled');
  }
} else {
  validationErrors.push('❌ Next.js configuration missing');
}

// 8. Check sitemap generation
console.log('\n🗺️  Checking SEO Configuration...');
if (fileExists('public/sitemap.xml') || fileExists('.next/static/sitemap.xml')) {
  validationPassed.push('✅ Sitemap generated');
} else {
  validationWarnings.push('⚠️  Sitemap not found');
}

// 9. Check monitoring setup
console.log('\n📊 Checking Monitoring Setup...');
if (fileExists('lib/sentry.client.ts')) {
  validationPassed.push('✅ Sentry client configured');
} else {
  validationWarnings.push('⚠️  Sentry client not configured');
}

if (fileExists('lib/posthog.client.ts')) {
  validationPassed.push('✅ PostHog client configured');
} else {
  validationWarnings.push('⚠️  PostHog client not configured');
}

// 10. Check deployment readiness
console.log('\n🚀 Checking Deployment Readiness...');
if (fileExists('vercel.json')) {
  validationPassed.push('✅ Vercel configuration exists');
} else {
  validationWarnings.push('⚠️  Vercel configuration not found');
}

// Summary
console.log('\n' + '='.repeat(50));
console.log('📋 VALIDATION SUMMARY');
console.log('='.repeat(50));

console.log(`\n✅ Passed: ${validationPassed.length}`);
validationPassed.forEach(item => console.log(`   ${item}`));

if (validationWarnings.length > 0) {
  console.log(`\n⚠️  Warnings: ${validationWarnings.length}`);
  validationWarnings.forEach(item => console.log(`   ${item}`));
}

if (validationErrors.length > 0) {
  console.log(`\n❌ Errors: ${validationErrors.length}`);
  validationErrors.forEach(item => console.log(`   ${item}`));
}

// Final assessment
console.log('\n' + '='.repeat(50));
if (validationErrors.length === 0) {
  console.log('🎉 PRODUCTION DEPLOYMENT READY!');
  console.log('✅ All critical checks passed');
  if (validationWarnings.length > 0) {
    console.log(`⚠️  ${validationWarnings.length} warnings to address for optimal deployment`);
  }
  console.log('\n🚀 Ready to deploy to Vercel!');
  process.exit(0);
} else {
  console.log('🚨 DEPLOYMENT BLOCKED');
  console.log(`❌ ${validationErrors.length} critical errors must be fixed before deployment`);
  console.log('\n🔧 Fix the errors above and run this script again');
  process.exit(1);
}
