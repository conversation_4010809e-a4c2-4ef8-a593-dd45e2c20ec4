# 📦 Monorepo Migration Guide

## Overview
This guide outlines the migration from a single Next.js application to a scalable monorepo architecture using Turborepo, enabling better code organization, reusability, and team collaboration.

## Target Monorepo Structure

```
slack-summary-scribe-monorepo/
├── apps/
│   ├── slack-summary-scribe/          # Main Next.js application
│   ├── admin-dashboard/               # Standalone admin dashboard
│   ├── marketing-site/                # Marketing website
│   └── docs/                          # Documentation site
├── packages/
│   ├── integration-sdk/               # Universal integration library
│   ├── drip-campaign-engine/          # Email campaign system
│   ├── analytics-core/                # Analytics and metrics
│   ├── ui-kit/                        # Shared UI components
│   ├── auth/                          # Authentication utilities
│   ├── database/                      # Database schemas and migrations
│   ├── cache/                         # Caching utilities
│   ├── monitoring/                    # Observability tools
│   └── config/                        # Shared configurations
├── tools/
│   ├── eslint-config/                 # Shared ESLint configuration
│   ├── typescript-config/             # Shared TypeScript configuration
│   └── build-tools/                   # Custom build utilities
├── scripts/
│   ├── audit.js                       # Enterprise audit script
│   ├── health-check.js                # System health validation
│   ├── migrate-data.js                # Data migration utilities
│   └── deploy.js                      # Deployment automation
├── docs/
│   ├── architecture/                  # Architecture documentation
│   ├── api/                           # API documentation
│   └── deployment/                    # Deployment guides
├── .github/
│   └── workflows/                     # CI/CD workflows
├── turbo.json                         # Turborepo configuration
├── package.json                       # Root package.json
└── README.md                          # Main documentation
```

## Migration Steps

### 1. Initialize Monorepo Structure

```bash
# Create new monorepo directory
mkdir slack-summary-scribe-monorepo
cd slack-summary-scribe-monorepo

# Initialize root package.json
npm init -y

# Install Turborepo
npm install -D turbo

# Create workspace directories
mkdir -p apps packages tools scripts docs
```

### 2. Move Existing Application

```bash
# Move current application to apps directory
mv ../slack-summary-scribe apps/slack-summary-scribe

# Update package.json name
cd apps/slack-summary-scribe
npm pkg set name="@slack-summary-scribe/app"
```

### 3. Extract Packages

#### Integration SDK Package
```bash
mkdir packages/integration-sdk
cd packages/integration-sdk

# Initialize package
npm init -y
npm pkg set name="@slack-summary-scribe/integration-sdk"
npm pkg set main="dist/index.js"
npm pkg set types="dist/index.d.ts"

# Move integration code
mv ../../apps/slack-summary-scribe/features/integrations/* src/
```

#### UI Kit Package
```bash
mkdir packages/ui-kit
cd packages/ui-kit

# Initialize package
npm init -y
npm pkg set name="@slack-summary-scribe/ui-kit"
npm pkg set main="dist/index.js"
npm pkg set types="dist/index.d.ts"

# Move shared components
mv ../../apps/slack-summary-scribe/components/ui/* src/
```

#### Analytics Core Package
```bash
mkdir packages/analytics-core
cd packages/analytics-core

# Initialize package
npm init -y
npm pkg set name="@slack-summary-scribe/analytics-core"

# Move analytics utilities
mv ../../apps/slack-summary-scribe/features/analytics/* src/
```

### 4. Configure Turborepo

Create `turbo.json` in root:
```json
{
  "$schema": "https://turbo.build/schema.json",
  "ui": "tui",
  "tasks": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "dist/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "lint": {
      "dependsOn": ["^build"]
    },
    "test": {
      "dependsOn": ["^build"],
      "outputs": ["coverage/**"]
    }
  }
}
```

### 5. Update Package Dependencies

#### Root package.json
```json
{
  "name": "slack-summary-scribe-monorepo",
  "private": true,
  "workspaces": ["apps/*", "packages/*"],
  "scripts": {
    "build": "turbo build",
    "dev": "turbo dev",
    "lint": "turbo lint",
    "test": "turbo test"
  },
  "devDependencies": {
    "turbo": "^1.11.0"
  }
}
```

#### App package.json
```json
{
  "name": "@slack-summary-scribe/app",
  "dependencies": {
    "@slack-summary-scribe/integration-sdk": "workspace:*",
    "@slack-summary-scribe/ui-kit": "workspace:*",
    "@slack-summary-scribe/analytics-core": "workspace:*"
  }
}
```

### 6. Configure Shared Tools

#### ESLint Config Package
```bash
mkdir tools/eslint-config
cd tools/eslint-config

# Create shared ESLint config
cat > index.js << 'EOF'
module.exports = {
  extends: [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  rules: {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn"
  }
};
EOF
```

#### TypeScript Config Package
```bash
mkdir tools/typescript-config
cd tools/typescript-config

# Create base tsconfig
cat > base.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true
  },
  "include": ["src"],
  "exclude": ["node_modules"]
}
EOF
```

## Package Configurations

### Integration SDK Package

#### package.json
```json
{
  "name": "@slack-summary-scribe/integration-sdk",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "lint": "eslint src --ext .ts,.tsx",
    "test": "vitest"
  },
  "dependencies": {
    "ioredis": "^5.3.2"
  },
  "devDependencies": {
    "@slack-summary-scribe/typescript-config": "workspace:*",
    "@slack-summary-scribe/eslint-config": "workspace:*",
    "typescript": "^5.3.0",
    "vitest": "^1.0.0"
  }
}
```

#### tsconfig.json
```json
{
  "extends": "@slack-summary-scribe/typescript-config/base.json",
  "compilerOptions": {
    "outDir": "dist",
    "declaration": true,
    "declarationMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["dist", "node_modules"]
}
```

### UI Kit Package

#### package.json
```json
{
  "name": "@slack-summary-scribe/ui-kit",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsup",
    "dev": "tsup --watch",
    "lint": "eslint src --ext .ts,.tsx",
    "test": "vitest"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@radix-ui/react-dialog": "^1.0.5",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0"
  },
  "devDependencies": {
    "@slack-summary-scribe/typescript-config": "workspace:*",
    "@slack-summary-scribe/eslint-config": "workspace:*",
    "tsup": "^8.0.0",
    "typescript": "^5.3.0"
  },
  "peerDependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  }
}
```

## CI/CD Configuration

### GitHub Workflow for Monorepo

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build packages
        run: npm run build
      
      - name: Run tests
        run: npm run test
      
      - name: Run linting
        run: npm run lint

  deploy:
    needs: build
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build for production
        run: npm run build
      
      - name: Deploy to Vercel
        run: npm run deploy:production
```

## Development Workflow

### Local Development
```bash
# Install all dependencies
npm install

# Start development servers for all apps
npm run dev

# Build all packages and apps
npm run build

# Run tests across all packages
npm run test

# Lint all code
npm run lint
```

### Package Development
```bash
# Work on specific package
cd packages/integration-sdk
npm run dev

# Test package in isolation
npm run test

# Build package
npm run build
```

### Adding Dependencies
```bash
# Add dependency to specific package
npm install --workspace=@slack-summary-scribe/integration-sdk ioredis

# Add dev dependency to root
npm install -D --workspace=root prettier
```

## Benefits of Monorepo Architecture

### Code Reusability
- Shared packages across multiple applications
- Consistent UI components and utilities
- Centralized business logic

### Developer Experience
- Single repository for all related code
- Shared tooling and configurations
- Simplified dependency management

### Build Optimization
- Turborepo caching for faster builds
- Incremental builds based on changes
- Parallel execution of tasks

### Team Collaboration
- Clear package boundaries
- Independent versioning
- Easier code reviews

## Migration Checklist

- [ ] Create monorepo structure
- [ ] Move existing application to apps/
- [ ] Extract reusable packages
- [ ] Configure Turborepo
- [ ] Update CI/CD workflows
- [ ] Configure shared tooling
- [ ] Update documentation
- [ ] Test all functionality
- [ ] Deploy to staging
- [ ] Deploy to production

## Maintenance

### Adding New Packages
1. Create package directory in `packages/`
2. Initialize with `npm init`
3. Add to workspace in root `package.json`
4. Configure build scripts in `turbo.json`

### Updating Dependencies
```bash
# Update all workspaces
npm update

# Update specific workspace
npm update --workspace=@slack-summary-scribe/app
```

### Publishing Packages
```bash
# Version packages
npm run changeset

# Publish to npm
npm run release
```

This monorepo architecture provides a solid foundation for scaling your SaaS application while maintaining code quality and developer productivity.
