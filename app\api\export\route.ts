import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@notionhq/client';
import { auth } from '@clerk/nextjs/server';
import { withDemoLimitCheck } from '@/lib/demo-middleware';

// Apply demo limit check middleware
export const POST = withDemoLimitCheck('exports')(async (request: NextRequest, context) => {
  try {
    // Get authenticated user
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
  devLog.log(`📤 Export: Processing request for user ${userId} (Demo: ${context.isInDemo})`);

    const body = await request.json();
    const {
      summary,
      format,
      fileName = 'summary',
      notionPageId,
      notionToken
    } = body;

    if (!summary) {
      return NextResponse.json(
        { error: 'Summary data is required' },
        { status: 400 }
      );
    }

    switch (format) {
      case 'pdf':
        return await exportToPDF(summary, fileName);
      case 'excel':
        return await exportToExcel(summary, fileName);
      case 'notion':
        return await exportToNotion(summary, notionPageId, notionToken);
      default:
        return NextResponse.json(
          { error: 'Unsupported export format. Use: pdf, excel, or notion' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Export error:', error);
    return NextResponse.json(
      { error: 'Export failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
});

async function exportToPDF(summary: any, fileName: string) {
  try {
    // For now, return a simple text-based PDF alternative
    // This avoids font file issues in serverless environments
    const content = generatePDFContent(summary);
    const buffer = Buffer.from(content, 'utf-8');

    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Content-Disposition': `attachment; filename="${fileName}.txt"`,
        'Content-Length': buffer.length.toString()
      }
    });
  } catch (error) {
    console.error('PDF export error:', error);
    throw new Error('Failed to generate PDF');
  }
}

function generatePDFContent(summary: any): string {
  let content = '';

  // Header
  content += '='.repeat(60) + '\n';
  content += '                    SUMMARY REPORT\n';
  content += '='.repeat(60) + '\n\n';

  // Metadata
  content += `Generated: ${new Date().toLocaleString()}\n`;
  content += `Model: ${summary.metadata?.model || summary.model || 'AI Model'}\n\n`;

  // Summary
  content += 'SUMMARY\n';
  content += '-'.repeat(20) + '\n';
  content += (summary.summary || summary.summary_text || 'No summary available') + '\n\n';

  // Key Points
  if (summary.keyPoints && summary.keyPoints.length > 0) {
    content += 'KEY POINTS\n';
    content += '-'.repeat(20) + '\n';
    summary.keyPoints.forEach((point: string, index: number) => {
      content += `${index + 1}. ${point}\n`;
    });
    content += '\n';
  }

  // Action Items
  if (summary.actionItems && summary.actionItems.length > 0) {
    content += 'ACTION ITEMS\n';
    content += '-'.repeat(20) + '\n';
    summary.actionItems.forEach((item: string, index: number) => {
      content += `${index + 1}. ${item}\n`;
    });
    content += '\n';
  }

  // Red Flags
  if (summary.redFlags && summary.redFlags.length > 0) {
    content += 'RED FLAGS\n';
    content += '-'.repeat(20) + '\n';
    summary.redFlags.forEach((flag: string, index: number) => {
      content += `${index + 1}. ${flag}\n`;
    });
    content += '\n';
  }

  // Skills
  if (summary.skills && summary.skills.length > 0) {
    content += 'SKILLS MENTIONED\n';
    content += '-'.repeat(20) + '\n';
    summary.skills.forEach((skill: string, index: number) => {
      content += `${index + 1}. ${skill}\n`;
    });
    content += '\n';
  }

  // Participants
  if (summary.participants && summary.participants.length > 0) {
    content += 'PARTICIPANTS\n';
    content += '-'.repeat(20) + '\n';
    summary.participants.forEach((participant: string, index: number) => {
      content += `${index + 1}. ${participant}\n`;
    });
    content += '\n';
  }

  // Footer
  content += '='.repeat(60) + '\n';
  content += 'Generated by Slack Summary Scribe\n';
  content += '='.repeat(60) + '\n';

  return content;
}

async function exportToExcel(summary: any, fileName: string) {
  try {
    // Dynamic import for ExcelJS
    const ExcelJS = await import('exceljs');
    const workbook = new ExcelJS.default.Workbook();
    const worksheet = workbook.addWorksheet('Summary');

    // Add title
    worksheet.addRow(['Summary Report']);
    worksheet.addRow(['Generated:', new Date().toLocaleString()]);
    worksheet.addRow(['Model:', summary.model || 'AI Model']);
    worksheet.addRow([]);

    // Add summary
    worksheet.addRow(['Summary']);
    worksheet.addRow([summary.summary || summary.summary_text || 'No summary available']);
    worksheet.addRow([]);

    // Add key points
    if (summary.keyPoints && summary.keyPoints.length > 0) {
      worksheet.addRow(['Key Points']);
      summary.keyPoints.forEach((point: string, index: number) => {
        worksheet.addRow([`${index + 1}.`, point]);
      });
      worksheet.addRow([]);
    }

    // Add action items
    if (summary.actionItems && summary.actionItems.length > 0) {
      worksheet.addRow(['Action Items']);
      summary.actionItems.forEach((item: string, index: number) => {
        worksheet.addRow([`${index + 1}.`, item]);
      });
      worksheet.addRow([]);
    }

    // Add red flags
    if (summary.redFlags && summary.redFlags.length > 0) {
      worksheet.addRow(['Red Flags']);
      summary.redFlags.forEach((flag: string, index: number) => {
        worksheet.addRow([`${index + 1}.`, flag]);
      });
      worksheet.addRow([]);
    }

    // Add skills
    if (summary.skills && summary.skills.length > 0) {
      worksheet.addRow(['Skills Mentioned']);
      summary.skills.forEach((skill: string, index: number) => {
        worksheet.addRow([`${index + 1}.`, skill]);
      });
      worksheet.addRow([]);
    }

    // Add participants
    if (summary.participants && summary.participants.length > 0) {
      worksheet.addRow(['Participants']);
      summary.participants.forEach((participant: string, index: number) => {
        worksheet.addRow([`${index + 1}.`, participant]);
      });
    }

    // Style the worksheet
    worksheet.getRow(1).font = { bold: true, size: 16 };
    worksheet.getRow(1).alignment = { horizontal: 'center' };

    const buffer = await workbook.xlsx.writeBuffer();
    
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${fileName}.xlsx"`,
        'Content-Length': buffer.length.toString()
      }
    });
  } catch (error) {
    console.error('Excel export error:', error);
    throw new Error('Failed to generate Excel file');
  }
}

async function exportToNotion(summary: any, pageId?: string, token?: string) {
  try {
    // In demo mode, return a markdown file instead of actual Notion integration
    if (!pageId || !token) {
  devLog.log('📝 Notion Export: Demo mode - generating markdown file');
      const markdownContent = generateNotionMarkdown(summary);
      const buffer = Buffer.from(markdownContent, 'utf-8');

      return new NextResponse(buffer, {
        status: 200,
        headers: {
          'Content-Type': 'text/markdown',
          'Content-Disposition': `attachment; filename="${summary.title || 'summary'}.md"`,
          'Content-Length': buffer.length.toString()
        }
      });
    }

    const notion = new Client({ auth: token });

    // Create the summary content
    const summaryContent = [
      {
        type: 'heading_1',
        heading_1: {
          rich_text: [{ type: 'text', text: { content: 'Summary Report' } }]
        }
      },
      {
        type: 'paragraph',
        paragraph: {
          rich_text: [
            { 
              type: 'text', 
              text: { content: `Generated: ${new Date().toLocaleString()}` } 
            }
          ]
        }
      },
      {
        type: 'paragraph',
        paragraph: {
          rich_text: [
            { 
              type: 'text', 
              text: { content: `Model: ${summary.model || 'AI Model'}` } 
            }
          ]
        }
      },
      {
        type: 'heading_2',
        heading_2: {
          rich_text: [{ type: 'text', text: { content: 'Summary' } }]
        }
      },
      {
        type: 'paragraph',
        paragraph: {
          rich_text: [
            { 
              type: 'text', 
              text: { content: summary.summary || summary.summary_text || 'No summary available' } 
            }
          ]
        }
      }
    ];

    // Add key points
    if (summary.keyPoints && summary.keyPoints.length > 0) {
      summaryContent.push({
        type: 'heading_2',
        heading_2: {
          rich_text: [{ type: 'text', text: { content: 'Key Points' } }]
        }
      });

      summary.keyPoints.forEach((point: string) => {
        summaryContent.push({
          type: 'bulleted_list_item',
          bulleted_list_item: {
            rich_text: [{ type: 'text', text: { content: point } }]
          }
        });
      });
    }

    // Add action items
    if (summary.actionItems && summary.actionItems.length > 0) {
      summaryContent.push({
        type: 'heading_2',
        heading_2: {
          rich_text: [{ type: 'text', text: { content: 'Action Items' } }]
        }
      });

      summary.actionItems.forEach((item: string) => {
        summaryContent.push({
          type: 'to_do',
          to_do: {
            rich_text: [{ type: 'text', text: { content: item } }],
            checked: false
          }
        });
      });
    }

    // Add red flags
    if (summary.redFlags && summary.redFlags.length > 0) {
      summaryContent.push({
        type: 'heading_2',
        heading_2: {
          rich_text: [{ type: 'text', text: { content: 'Red Flags' } }]
        }
      });

      summary.redFlags.forEach((flag: string) => {
        summaryContent.push({
          type: 'callout',
          callout: {
            rich_text: [{ type: 'text', text: { content: flag } }],
            icon: { type: 'emoji', emoji: '⚠️' },
            color: 'red_background'
          }
        });
      });
    }

    // Add skills
    if (summary.skills && summary.skills.length > 0) {
      summaryContent.push({
        type: 'heading_2',
        heading_2: {
          rich_text: [{ type: 'text', text: { content: 'Skills Mentioned' } }]
        }
      });

      summary.skills.forEach((skill: string) => {
        summaryContent.push({
          type: 'bulleted_list_item',
          bulleted_list_item: {
            rich_text: [{ type: 'text', text: { content: skill } }]
          }
        });
      });
    }

    // Add participants
    if (summary.participants && summary.participants.length > 0) {
      summaryContent.push({
        type: 'heading_2',
        heading_2: {
          rich_text: [{ type: 'text', text: { content: 'Participants' } }]
        }
      });

      summary.participants.forEach((participant: string) => {
        summaryContent.push({
          type: 'bulleted_list_item',
          bulleted_list_item: {
            rich_text: [{ type: 'text', text: { content: participant } }]
          }
        });
      });
    }

    // Append to the Notion page
    await notion.blocks.children.append({
      block_id: pageId,
      children: summaryContent
    });

    return NextResponse.json({
      success: true,
      message: 'Summary exported to Notion successfully',
      pageId
    });
  } catch (error) {
    console.error('Notion export error:', error);
    throw new Error('Failed to export to Notion');
  }
}

function generateNotionMarkdown(summary: any): string {
  let markdown = '';

  // Header
  markdown += `# Summary Report\n\n`;
  markdown += `**Generated:** ${new Date().toLocaleString()}\n`;
  markdown += `**Model:** ${summary.metadata?.model || summary.model || 'AI Model'}\n\n`;

  // Summary
  markdown += `## Summary\n\n`;
  markdown += `${summary.summary || summary.summary_text || 'No summary available'}\n\n`;

  // Key Points
  if (summary.keyPoints && summary.keyPoints.length > 0) {
    markdown += `## Key Points\n\n`;
    summary.keyPoints.forEach((point: string, index: number) => {
      markdown += `${index + 1}. ${point}\n`;
    });
    markdown += '\n';
  }

  // Action Items
  if (summary.actionItems && summary.actionItems.length > 0) {
    markdown += `## Action Items\n\n`;
    summary.actionItems.forEach((item: string, index: number) => {
      markdown += `- [ ] ${item}\n`;
    });
    markdown += '\n';
  }

  // Red Flags
  if (summary.redFlags && summary.redFlags.length > 0) {
    markdown += `## Red Flags\n\n`;
    summary.redFlags.forEach((flag: string, index: number) => {
      markdown += `⚠️ ${flag}\n`;
    });
    markdown += '\n';
  }

  // Skills
  if (summary.skills && summary.skills.length > 0) {
    markdown += `## Skills Mentioned\n\n`;
    summary.skills.forEach((skill: string) => {
      markdown += `- ${skill}\n`;
    });
    markdown += '\n';
  }

  // Footer
  markdown += `---\n\n`;
  markdown += `*Generated by Slack Summary Scribe*\n`;
  markdown += `*Import this file into Notion by copying and pasting the content.*\n`;

  return markdown;
}

export async function GET(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
} 