import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createClient } from '@supabase/supabase-js';
import { createSupabaseServerClient } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
  devLog.log('🔔 Notifications API called for user:', user.id);

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const unreadOnly = searchParams.get('unread') === 'true';

    // Initialize Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
  devLog.log('🔔 Fetching real notifications from Supabase');

    let notifications: any[] = [];

    try {
      // Try to get real notifications from Supabase
      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (unreadOnly) {
        query = query.eq('read', false);
      }

      const { data, error } = await query;

      if (error) {
        console.warn('Supabase notifications query failed:', error);
        // Fallback to welcome notification for new users
        notifications = [{
          id: 'welcome-1',
          user_id: user.id,
          title: 'Welcome to Slack Summary Scribe!',
          message: 'Start creating AI-powered summaries of your conversations.',
          type: 'welcome',
          read: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            action_url: '/upload',
            icon: '🚀'
          }
        }];
      } else {
        notifications = data || [];
      }
  devLog.log(`🔔 Found ${notifications.length} notifications for user ${user.id}`);

    } catch (error) {
      console.warn('Error fetching notifications:', error);
      // Fallback to empty array
      notifications = [];
    }

    return NextResponse.json({
      success: true,
      data: notifications || [],
      total: notifications?.length || 0
    });

  } catch (error) {
    console.error('Notifications API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      type, 
      message, 
      title, 
      userId, 
      summaryId, 
      slackChannel,
      slackWebhookUrl 
    } = body;

    if (!type || !message) {
      return NextResponse.json(
        { error: 'Type and message are required' },
        { status: 400 }
      );
    }

    switch (type) {
      case 'summary_complete':
        await handleSummaryComplete(message, title, userId, summaryId, slackChannel, slackWebhookUrl);
        break;
      case 'summary_error':
        await handleSummaryError(message, title, userId, summaryId, slackChannel, slackWebhookUrl);
        break;
      case 'export_complete':
        await handleExportComplete(message, title, userId, summaryId, slackChannel, slackWebhookUrl);
        break;
      case 'export_error':
        await handleExportError(message, title, userId, summaryId, slackChannel, slackWebhookUrl);
        break;
      default:
        return NextResponse.json(
          { error: 'Unsupported notification type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: 'Notification sent successfully'
    });

  } catch (error) {
    console.error('Notification error:', error);
    return NextResponse.json(
      { error: 'Failed to send notification', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

async function handleSummaryComplete(
  message: string, 
  title: string, 
  userId: string, 
  summaryId: string, 
  slackChannel?: string, 
  slackWebhookUrl?: string
) {
  // Send Slack notification if configured
  if (slackWebhookUrl) {
    await sendSlackNotification({
      channel: slackChannel || '#general',
      text: `✅ Summary Complete!\n\n*${title}*\n${message}\n\nSummary ID: ${summaryId}`,
      webhookUrl: slackWebhookUrl
    });
  }

  // Log for analytics
  devLog.log(`📊 Summary completed: ${summaryId} for user ${userId}`);
}

async function handleSummaryError(
  message: string, 
  title: string, 
  userId: string, 
  summaryId: string, 
  slackChannel?: string, 
  slackWebhookUrl?: string
) {
  // Send Slack notification if configured
  if (slackWebhookUrl) {
    await sendSlackNotification({
      channel: slackChannel || '#alerts',
      text: `❌ Summary Error!\n\n*${title}*\n${message}\n\nSummary ID: ${summaryId}`,
      webhookUrl: slackWebhookUrl
    });
  }

  // Log for analytics
  console.error(`📊 Summary error: ${summaryId} for user ${userId} - ${message}`);
}

async function handleExportComplete(
  message: string, 
  title: string, 
  userId: string, 
  summaryId: string, 
  slackChannel?: string, 
  slackWebhookUrl?: string
) {
  // Send Slack notification if configured
  if (slackWebhookUrl) {
    await sendSlackNotification({
      channel: slackChannel || '#general',
      text: `📄 Export Complete!\n\n*${title}*\n${message}\n\nSummary ID: ${summaryId}`,
      webhookUrl: slackWebhookUrl
    });
  }

  // Log for analytics
  devLog.log(`📊 Export completed: ${summaryId} for user ${userId}`);
}

async function handleExportError(
  message: string, 
  title: string, 
  userId: string, 
  summaryId: string, 
  slackChannel?: string, 
  slackWebhookUrl?: string
) {
  // Send Slack notification if configured
  if (slackWebhookUrl) {
    await sendSlackNotification({
      channel: slackChannel || '#alerts',
      text: `❌ Export Error!\n\n*${title}*\n${message}\n\nSummary ID: ${summaryId}`,
      webhookUrl: slackWebhookUrl
    });
  }

  // Log for analytics
  console.error(`📊 Export error: ${summaryId} for user ${userId} - ${message}`);
}

async function sendSlackNotification({
  channel,
  text,
  webhookUrl
}: {
  channel: string;
  text: string;
  webhookUrl: string;
}) {
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channel,
        text,
        username: 'Slack Summary Scribe',
        icon_emoji: ':robot_face:'
      })
    });

    if (!response.ok) {
      throw new Error(`Slack webhook failed: ${response.status}`);
    }
  devLog.log('✅ Slack notification sent successfully');
  } catch (error) {
    console.error('❌ Failed to send Slack notification:', error);
    // Don't throw - we don't want notification failures to break the main flow
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
  devLog.log('🔔 Update Notification API called for user:', user.id);

    const body = await request.json();
    const { notificationId, markAllAsRead } = body;
    const supabase = await createSupabaseServerClient();

    if (markAllAsRead) {
      // Mark all notifications as read
      const { error } = await supabase
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('user_id', user.id)
        .eq('read', false);

      if (error) {
        console.error('Error marking all notifications as read:', error);
        return NextResponse.json(
          { error: 'Failed to mark notifications as read' },
          { status: 500 }
        );
      }
  devLog.log('🔔 All notifications marked as read for user:', user.id);

      return NextResponse.json({
        success: true,
        message: 'All notifications marked as read'
      });
    } else if (notificationId) {
      // Mark specific notification as read
      const { data: notification, error } = await supabase
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('id', notificationId)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Error marking notification as read:', error);
        return NextResponse.json(
          { error: 'Failed to mark notification as read' },
          { status: 500 }
        );
      }
  devLog.log('🔔 Notification marked as read:', notificationId);

      return NextResponse.json({
        success: true,
        data: notification,
        message: 'Notification marked as read (Demo Mode)'
      });
    } else {
      return NextResponse.json(
        { error: 'Missing notification ID or markAllAsRead flag' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Update notification API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
