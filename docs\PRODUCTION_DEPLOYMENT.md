# Production Deployment Guide

## Overview
This guide covers the complete production deployment process for Slack Summary Scribe SaaS application.

## Prerequisites

### 1. Environment Validation
Run the production environment validation script:
```bash
npm run validate-production
```

This will check all required environment variables and identify any issues that need to be resolved before deployment.

## Critical Issues to Resolve

### 1. Clerk Authentication Keys (CRITICAL)
**Current Status**: Using invalid demo keys
**Required Action**: Replace with real Clerk project keys

#### Steps to Fix:
1. Go to [Clerk Dashboard](https://dashboard.clerk.com/)
2. Create a new project or use existing project
3. Get the production keys from your project settings
4. Update `.env.local` with real keys:
   ```bash
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_your_real_publishable_key
   CLERK_SECRET_KEY=sk_live_your_real_secret_key
   ```

### 2. Production URLs
Update the site URLs for production deployment:
```bash
# For Vercel deployment
NEXT_PUBLIC_SITE_URL=https://your-app-name.vercel.app
NEXT_PUBLIC_APP_URL=https://your-app-name.vercel.app
NEXT_PUBLIC_ENVIRONMENT=production
```

## Vercel Deployment Steps

### 1. Environment Variables Setup
In Vercel dashboard, add these environment variables:

#### Core Application
- `NODE_ENV=production`
- `NEXT_PUBLIC_SITE_URL=https://your-app-name.vercel.app`
- `NEXT_PUBLIC_APP_URL=https://your-app-name.vercel.app`
- `NEXT_PUBLIC_ENVIRONMENT=production`

#### Authentication (Clerk) - CRITICAL
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_your_real_key`
- `CLERK_SECRET_KEY=sk_live_your_real_key`

#### Database (Supabase)
- `NEXT_PUBLIC_SUPABASE_URL=https://holuppwejzcqwrbdbgkf.supabase.co`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- `SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- `DATABASE_URL=postgresql://postgres.holuppwejzcqwrbdbgkf:...`

#### AI Services
- `OPENROUTER_API_KEY=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`

#### Optional Services
- `SLACK_CLIENT_ID=8996307659333.8996321533445`
- `SLACK_CLIENT_SECRET=9ebbe3313ae29fb10d31dbb742fed179`
- `SLACK_SIGNING_SECRET=8bd4591adb4c6e25e497eb51ee1acd88`
- `NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/4509565394419712`
- `RESEND_API_KEY=re_CFojG8Ne_4JKVu1Memmai8Ti4bVDWNQFn`
- `EMAIL_FROM=<EMAIL>`

### 2. Build Configuration
Ensure your `next.config.js` is production-ready:
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['@supabase/supabase-js']
  },
  images: {
    domains: ['holuppwejzcqwrbdbgkf.supabase.co']
  }
}

module.exports = nextConfig
```

### 3. Deployment Process
1. **Validate Environment**:
   ```bash
   npm run validate-production
   ```

2. **Build Locally** (optional):
   ```bash
   npm run build
   ```

3. **Deploy to Vercel**:
   - Connect your GitHub repository to Vercel
   - Configure environment variables in Vercel dashboard
   - Deploy from main branch

### 4. Post-Deployment Validation
After deployment, test these critical features:
- [ ] User authentication (sign up/sign in)
- [ ] Dashboard loading with real data
- [ ] AI summarization functionality
- [ ] File upload and processing
- [ ] Slack OAuth integration
- [ ] Settings page functionality

## Security Considerations

### 1. Environment Variables
- Never commit real API keys to version control
- Use Vercel's environment variable encryption
- Rotate keys regularly

### 2. Supabase Security
- Ensure RLS (Row Level Security) policies are enabled
- Validate all database queries use proper user context
- Monitor database access logs

### 3. Clerk Security
- Configure proper redirect URLs in Clerk dashboard
- Enable session security features
- Set up proper CORS policies

## Monitoring & Error Tracking

### 1. Sentry Integration
- Verify Sentry DSN is configured
- Test error reporting in production
- Set up alerts for critical errors

### 2. Performance Monitoring
- Monitor Core Web Vitals
- Track API response times
- Monitor database query performance

## Troubleshooting

### Common Issues
1. **Authentication Errors**: Check Clerk keys and redirect URLs
2. **Database Connection**: Verify Supabase credentials and RLS policies
3. **Build Failures**: Run `npm run validate-production` to identify issues
4. **API Errors**: Check environment variables and service configurations

### Debug Commands
```bash
# Validate all environment variables
npm run validate-production

# Check build locally
npm run build

# Test authentication setup
npm run validate:auth
```

## Launch Checklist
- [ ] All environment variables validated
- [ ] Clerk authentication configured with real keys
- [ ] Production URLs updated
- [ ] Vercel deployment successful
- [ ] All features tested in production
- [ ] Error monitoring active
- [ ] Performance metrics baseline established
