/**
 * Multi-Layer Cache Manager for Hyperscale Performance
 * 
 * Implements intelligent caching strategies across multiple layers:
 * - Memory cache (Redis)
 * - Database query cache
 * - AI generation cache
 * - Export artifact cache
 * - CDN cache
 */

// import Redis from 'ioredis'; // Optional dependency
import { createHash } from 'crypto';

// Mock Redis interface for when Redis is not available
interface MockRedis {
  get(key: string): Promise<string | null>;
  setex(key: string, ttl: number, value: string): Promise<void>;
  del(...keys: string[]): Promise<number>;
  keys(pattern: string): Promise<string[]>;
  smembers(key: string): Promise<string[]>;
  info(section: string): Promise<string>;
  dbsize(): Promise<number>;
  flushdb(): Promise<void>;
  ping(): Promise<string>;
  pipeline(): any;
  config(command: string, key: string, value?: string): Promise<void>;
  psubscribe(pattern: string): void;
  on(event: string, callback: Function): void;
}

// In-memory fallback implementation
class MemoryCache implements MockRedis {
  private cache = new Map<string, { value: string; expires: number }>();
  private tags = new Map<string, Set<string>>();

  async get(key: string): Promise<string | null> {
    const entry = this.cache.get(key);
    if (!entry) return null;
    if (Date.now() > entry.expires) {
      this.cache.delete(key);
      return null;
    }
    return entry.value;
  }

  async setex(key: string, ttl: number, value: string): Promise<void> {
    this.cache.set(key, {
      value,
      expires: Date.now() + (ttl * 1000)
    });
  }

  async del(...keys: string[]): Promise<number> {
    let deleted = 0;
    for (const key of keys) {
      if (this.cache.delete(key)) deleted++;
    }
    return deleted;
  }

  async keys(pattern: string): Promise<string[]> {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return Array.from(this.cache.keys()).filter(key => regex.test(key));
  }

  async smembers(key: string): Promise<string[]> {
    const set = this.tags.get(key);
    return set ? Array.from(set) : [];
  }

  async info(section: string): Promise<string> {
    return `used_memory:${this.cache.size * 100}`;
  }

  async dbsize(): Promise<number> {
    return this.cache.size;
  }

  async flushdb(): Promise<void> {
    this.cache.clear();
    this.tags.clear();
  }

  async ping(): Promise<string> {
    return 'PONG';
  }

  pipeline() {
    return {
      sadd: () => this,
      expire: () => this,
      exec: async () => []
    };
  }

  async config(command: string, key: string, value?: string): Promise<void> {
    // No-op for memory cache
  }

  psubscribe(pattern: string): void {
    // No-op for memory cache
  }

  on(event: string, callback: Function): void {
    // No-op for memory cache
  }
}

export interface CacheConfig {
  redis: {
    host: string;
    port: number;
    password?: string;
    db?: number;
  };
  defaultTTL: number;
  maxMemoryUsage: string;
  compressionThreshold: number;
}

export interface CacheEntry<T = any> {
  data: T | string; // Can be original data or compressed string
  timestamp: number;
  ttl: number;
  compressed: boolean;
  tags: string[];
}

export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  memoryUsage: number;
  keyCount: number;
  evictions: number;
}

export class CacheManager {
  private redis: MockRedis;
  private config: CacheConfig;
  private stats: CacheStats;
  private compressionEnabled: boolean;

  constructor(config: CacheConfig) {
    this.config = config;
    // Use memory cache as fallback when Redis is not available
    this.redis = new MemoryCache();

    this.stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      memoryUsage: 0,
      keyCount: 0,
      evictions: 0
    };

    this.compressionEnabled = true;
    this.setupRedisConfig();
  }

  /**
   * Get cached value with automatic decompression
   */
  async get<T>(key: string, tags?: string[]): Promise<T | null> {
    try {
      const cacheKey = this.buildCacheKey(key, tags);
      const cached = await this.redis.get(cacheKey);
      
      if (!cached) {
        this.stats.misses++;
        this.updateHitRate();
        return null;
      }

      const entry: CacheEntry<T> = JSON.parse(cached);
      
      // Check if entry is expired
      if (Date.now() > entry.timestamp + (entry.ttl * 1000)) {
        await this.redis.del(cacheKey);
        this.stats.misses++;
        this.updateHitRate();
        return null;
      }

      this.stats.hits++;
      this.updateHitRate();

      // Decompress if needed
      if (entry.compressed && this.compressionEnabled) {
        return this.decompress(entry.data as string);
      }

      return entry.data as T;
    } catch (error) {
      console.error('Cache get error:', error);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
  }

  /**
   * Set cached value with automatic compression
   */
  async set<T>(
    key: string, 
    data: T, 
    ttl?: number, 
    tags?: string[]
  ): Promise<boolean> {
    try {
      const cacheKey = this.buildCacheKey(key, tags);
      const cacheTTL = ttl || this.config.defaultTTL;
      
      let processedData: T | string = data;
      let compressed = false;

      // Compress large data
      const dataSize = JSON.stringify(data).length;
      if (dataSize > this.config.compressionThreshold && this.compressionEnabled) {
        processedData = this.compress(data);
        compressed = true;
      }

      const entry: CacheEntry<T> = {
        data: processedData,
        timestamp: Date.now(),
        ttl: cacheTTL,
        compressed,
        tags: tags || []
      };

      await this.redis.setex(cacheKey, cacheTTL, JSON.stringify(entry));
      
      // Add to tag index for invalidation
      if (tags && tags.length > 0) {
        await this.addToTagIndex(tags, cacheKey);
      }

      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Get or set pattern - fetch from cache or execute function and cache result
   */
  async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl?: number,
    tags?: string[]
  ): Promise<T> {
    const cached = await this.get<T>(key, tags);
    
    if (cached !== null) {
      return cached;
    }

    const data = await fetchFunction();
    await this.set(key, data, ttl, tags);
    return data;
  }

  /**
   * Invalidate cache by key pattern
   */
  async invalidate(pattern: string): Promise<number> {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length === 0) return 0;

      await this.redis.del(...keys);
      return keys.length;
    } catch (error) {
      console.error('Cache invalidation error:', error);
      return 0;
    }
  }

  /**
   * Invalidate cache by tags
   */
  async invalidateByTags(tags: string[]): Promise<number> {
    try {
      let totalInvalidated = 0;

      for (const tag of tags) {
        const tagKey = `tag:${tag}`;
        const keys = await this.redis.smembers(tagKey);
        
        if (keys.length > 0) {
          await this.redis.del(...keys);
          await this.redis.del(tagKey);
          totalInvalidated += keys.length;
        }
      }

      return totalInvalidated;
    } catch (error) {
      console.error('Cache tag invalidation error:', error);
      return 0;
    }
  }

  /**
   * Warm cache with precomputed data
   */
  async warmCache(entries: Array<{
    key: string;
    data: any;
    ttl?: number;
    tags?: string[];
  }>): Promise<number> {
    let warmed = 0;

    for (const entry of entries) {
      const success = await this.set(
        entry.key,
        entry.data,
        entry.ttl,
        entry.tags
      );
      if (success) warmed++;
    }

    return warmed;
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<CacheStats> {
    try {
      const info = await this.redis.info('memory');
      const memoryMatch = info.match(/used_memory:(\d+)/);
      const keyCount = await this.redis.dbsize();

      this.stats.memoryUsage = memoryMatch ? parseInt(memoryMatch[1]) : 0;
      this.stats.keyCount = keyCount;

      return { ...this.stats };
    } catch (error) {
      console.error('Cache stats error:', error);
      return this.stats;
    }
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<boolean> {
    try {
      await this.redis.flushdb();
      this.resetStats();
      return true;
    } catch (error) {
      console.error('Cache clear error:', error);
      return false;
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    healthy: boolean;
    latency: number;
    error?: string;
  }> {
    const start = Date.now();
    
    try {
      await this.redis.ping();
      const latency = Date.now() - start;
      
      return {
        healthy: true,
        latency
      };
    } catch (error) {
      return {
        healthy: false,
        latency: Date.now() - start,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Private methods

  private buildCacheKey(key: string, tags?: string[]): string {
    if (!tags || tags.length === 0) {
      return `cache:${key}`;
    }
    
    const tagHash = createHash('md5')
      .update(tags.sort().join(':'))
      .digest('hex')
      .substring(0, 8);
    
    return `cache:${key}:${tagHash}`;
  }

  private async addToTagIndex(tags: string[], cacheKey: string): Promise<void> {
    const pipeline = this.redis.pipeline();
    
    for (const tag of tags) {
      pipeline.sadd(`tag:${tag}`, cacheKey);
      pipeline.expire(`tag:${tag}`, this.config.defaultTTL);
    }
    
    await pipeline.exec();
  }

  private compress<T>(data: T): string {
    // Simple compression using JSON + base64
    // In production, use proper compression like gzip
    const json = JSON.stringify(data);
    return Buffer.from(json).toString('base64');
  }

  private decompress<T>(compressed: string): T {
    const json = Buffer.from(compressed, 'base64').toString();
    return JSON.parse(json);
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }

  private resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      memoryUsage: 0,
      keyCount: 0,
      evictions: 0
    };
  }

  private async setupRedisConfig(): Promise<void> {
    try {
      // Configure cache for optimal performance (no-op for memory cache)
      await this.redis.config('SET', 'maxmemory', this.config.maxMemoryUsage.toString());
      await this.redis.config('SET', 'maxmemory-policy', 'allkeys-lru');

      // Enable keyspace notifications for eviction tracking (no-op for memory cache)
      await this.redis.config('SET', 'notify-keyspace-events', 'Ex');

      // Subscribe to eviction events (no-op for memory cache)
      this.redis.psubscribe('__keyevent@*__:evicted');
      this.redis.on('pmessage', () => {
        this.stats.evictions++;
      });
    } catch (error) {
      console.error('Cache config setup error:', error);
    }
  }
}

// Specialized cache managers for different use cases

export class QueryCache extends CacheManager {
  constructor(config: CacheConfig) {
    super(config);
  }

  async cacheQuery<T>(
    query: string,
    params: any[],
    result: T,
    ttl: number = 300 // 5 minutes default
  ): Promise<void> {
    const key = this.generateQueryKey(query, params);
    await this.set(key, result, ttl, ['query', 'database']);
  }

  async getCachedQuery<T>(query: string, params: any[]): Promise<T | null> {
    const key = this.generateQueryKey(query, params);
    return await this.get<T>(key, ['query', 'database']);
  }

  private generateQueryKey(query: string, params: any[]): string {
    const hash = createHash('md5')
      .update(query + JSON.stringify(params))
      .digest('hex');
    return `query:${hash}`;
  }
}

export class AICache extends CacheManager {
  constructor(config: CacheConfig) {
    super(config);
  }

  async cacheSummary(
    content: string,
    model: string,
    summary: string,
    ttl: number = 3600 // 1 hour default
  ): Promise<void> {
    const key = this.generateContentKey(content, model);
    await this.set(key, summary, ttl, ['ai', 'summary', model]);
  }

  async getCachedSummary(content: string, model: string): Promise<string | null> {
    const key = this.generateContentKey(content, model);
    return await this.get<string>(key, ['ai', 'summary', model]);
  }

  private generateContentKey(content: string, model: string): string {
    const hash = createHash('sha256')
      .update(content + model)
      .digest('hex');
    return `ai:summary:${model}:${hash}`;
  }
}

export class ExportCache extends CacheManager {
  constructor(config: CacheConfig) {
    super(config);
  }

  async cacheExport(
    summaryId: string,
    format: string,
    exportData: Buffer | string,
    ttl: number = 1800 // 30 minutes default
  ): Promise<void> {
    const key = `export:${summaryId}:${format}`;
    await this.set(key, exportData, ttl, ['export', format, summaryId]);
  }

  async getCachedExport(
    summaryId: string,
    format: string
  ): Promise<Buffer | string | null> {
    const key = `export:${summaryId}:${format}`;
    return await this.get(key, ['export', format, summaryId]);
  }
}

// Factory function for creating cache instances
export function createCacheManager(type: 'default' | 'query' | 'ai' | 'export', config: CacheConfig) {
  switch (type) {
    case 'query':
      return new QueryCache(config);
    case 'ai':
      return new AICache(config);
    case 'export':
      return new ExportCache(config);
    default:
      return new CacheManager(config);
  }
}
