# 🏗️ Enterprise Architecture Guide

## **Optimized Folder Structure**

```
/
├── features/                    # Domain-driven feature modules
│   ├── accounts/               # User accounts, profiles, preferences
│   ├── organizations/          # Multi-tenant org management
│   ├── summaries/             # Core summary generation & management
│   ├── integrations/          # Third-party service integrations
│   ├── billing/               # Stripe billing, subscriptions, invoices
│   ├── notifications/         # Email, Slack, in-app notifications
│   ├── analytics/             # Usage analytics & business metrics
│   ├── admin/                 # Admin dashboard & management tools
│   └── observability/         # Monitoring, health checks, alerts
├── shared/                     # Shared utilities across features
│   ├── types/                 # Global TypeScript definitions
│   ├── schemas/               # Zod schemas for validation
│   ├── utils/                 # Pure utility functions
│   ├── constants/             # Application constants
│   └── hooks/                 # Reusable React hooks
├── lib/                       # External service integrations
│   ├── supabase/             # Supabase client configurations
│   ├── stripe/               # Stripe utilities
│   ├── ai/                   # AI service integrations
│   ├── email/                # Email service (Resend)
│   └── storage/              # File storage utilities
├── components/                # Reusable UI components
│   ├── ui/                   # Base UI components (shadcn/ui)
│   ├── forms/                # Form components
│   ├── charts/               # Chart components
│   └── layouts/              # Layout components
├── app/                      # Next.js App Router
├── tests/                    # Test files
│   ├── e2e/                  # End-to-end tests
│   ├── integration/          # Integration tests
│   ├── unit/                 # Unit tests
│   └── fixtures/             # Test data and fixtures
└── docs/                     # Documentation
    ├── api/                  # API documentation
    ├── features/             # Feature documentation
    └── deployment/           # Deployment guides
```

## **Module Boundaries & Dependencies**

### **Core Principles**
1. **Domain-Driven Design**: Each feature module owns its domain
2. **Dependency Direction**: Features can depend on `shared/` and `lib/`, not each other
3. **Interface Segregation**: Clear contracts between modules
4. **Single Responsibility**: Each module has one clear purpose

### **Dependency Graph**
```
features/accounts ──┐
features/organizations ──┼── shared/ ── lib/
features/summaries ──┤
features/integrations ──┘
```

### **Shared Types Structure**
```typescript
// shared/types/index.ts
export * from './auth';
export * from './billing';
export * from './organizations';
export * from './summaries';
export * from './integrations';
export * from './api';

// shared/types/auth.ts
export interface User {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Session {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresAt: string;
}

// shared/types/organizations.ts
export interface Organization {
  id: string;
  name: string;
  plan: 'FREE' | 'PRO' | 'ENTERPRISE';
  settings: OrganizationSettings;
  createdAt: string;
  updatedAt: string;
}

export interface OrganizationMember {
  id: string;
  userId: string;
  organizationId: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  permissions: Permission[];
  joinedAt: string;
}
```

## **Feature Module Template**

Each feature module should follow this structure:

```
/features/[module]/
├── README.md                  # Module documentation
├── types.ts                   # Module-specific types
├── schemas.ts                 # Zod validation schemas
├── services/                  # Business logic services
│   ├── [module].service.ts
│   └── [module].repository.ts
├── api/                       # API route handlers
│   └── handlers/
├── components/                # Module-specific components
├── hooks/                     # Module-specific hooks
├── utils/                     # Module-specific utilities
└── tests/                     # Module tests
    ├── unit/
    ├── integration/
    └── fixtures/
```

## **Recommended Refactors**

### **1. Split Current Modules**

**Current `/monitoring` + `/analytics` → `/observability`**
```
/features/observability/
├── README.md
├── health/                    # Health checks & system status
├── metrics/                   # Application metrics
├── alerts/                    # Alert management
├── logging/                   # Structured logging
└── dashboards/               # Monitoring dashboards
```

**Current `/teams` → `/organizations` + `/accounts`**
```
/features/organizations/       # Multi-tenant org management
├── services/
├── api/
└── components/

/features/accounts/           # Individual user management
├── profiles/
├── preferences/
└── authentication/
```

### **2. Extract New Modules**

**`/features/summaries/`** - Core business logic
```
/features/summaries/
├── generation/               # AI summary generation
├── management/              # CRUD operations
├── scheduling/              # Automated summaries
├── templates/               # Summary templates
└── exports/                 # Export functionality
```

**`/features/notifications/`** - All notification types
```
/features/notifications/
├── email/                   # Email notifications
├── slack/                   # Slack notifications
├── in-app/                  # In-app notifications
├── preferences/             # User notification preferences
└── templates/               # Notification templates
```

**`/features/billing/`** - Stripe & subscription management
```
/features/billing/
├── subscriptions/           # Subscription management
├── invoices/               # Invoice handling
├── payments/               # Payment processing
├── webhooks/               # Stripe webhooks
└── portal/                 # Customer portal
```

## **Shared Utilities Organization**

### **`/shared/schemas/`** - Zod Validation
```typescript
// shared/schemas/auth.ts
import { z } from 'zod';

export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  fullName: z.string().optional(),
  avatarUrl: z.string().url().optional(),
});

export const SessionSchema = z.object({
  user: UserSchema,
  accessToken: z.string(),
  refreshToken: z.string(),
  expiresAt: z.string().datetime(),
});

// shared/schemas/api.ts
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});
```

### **`/shared/utils/`** - Pure Functions
```typescript
// shared/utils/date.ts
export function formatDate(date: Date | string): string {
  return new Date(date).toLocaleDateString();
}

export function isDateInRange(date: Date, start: Date, end: Date): boolean {
  return date >= start && date <= end;
}

// shared/utils/validation.ts
export function validateEmail(email: string): boolean {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

export function validatePassword(password: string): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain an uppercase letter');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}
```

### **`/shared/hooks/`** - Reusable React Hooks
```typescript
// shared/hooks/useLocalStorage.ts
import { useState, useEffect } from 'react';

export function useLocalStorage<T>(
  key: string,
  defaultValue: T
): [T, (value: T) => void] {
  const [value, setValue] = useState<T>(() => {
    if (typeof window === 'undefined') return defaultValue;
    
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.localStorage.setItem(key, JSON.stringify(value));
    }
  }, [key, value]);

  return [value, setValue];
}

// shared/hooks/useDebounce.ts
import { useState, useEffect } from 'react';

export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}
```

## **Migration Strategy**

### **Phase 1: Shared Infrastructure** (Week 1)
1. Create `/shared/` directory structure
2. Move common types and utilities
3. Update imports across codebase

### **Phase 2: Feature Extraction** (Week 2)
1. Extract `/features/summaries/`
2. Extract `/features/billing/`
3. Extract `/features/notifications/`

### **Phase 3: Module Refinement** (Week 3)
1. Split `/teams/` into `/organizations/` and `/accounts/`
2. Merge `/monitoring/` and `/analytics/` into `/observability/`
3. Create `/features/admin/`

### **Phase 4: Testing & Documentation** (Week 4)
1. Add comprehensive tests for each module
2. Update documentation
3. Validate module boundaries

## **Benefits of This Architecture**

1. **Scalability**: Clear module boundaries enable team scaling
2. **Maintainability**: Domain-driven design reduces complexity
3. **Testability**: Isolated modules are easier to test
4. **Reusability**: Shared utilities reduce code duplication
5. **Type Safety**: Comprehensive TypeScript coverage
6. **Documentation**: Clear structure and documentation standards
