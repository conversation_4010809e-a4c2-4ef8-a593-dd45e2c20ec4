/**
 * Analytics Export API Route
 * Export analytics data in various formats
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import {
  exportAnalytics,
  type AnalyticsFilters,
} from '@/lib/analytics-service';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
// Removed: import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/analytics/export
 * Export analytics data
 */
export async function GET(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const { searchParams } = new URL(req.url);
      const format = (searchParams.get('format') || 'json') as 'csv' | 'json' | 'pdf';
      const period = (searchParams.get('period') || 'weekly') as 'daily' | 'weekly' | 'monthly';
      const startDate = searchParams.get('start_date');
      const endDate = searchParams.get('end_date');
      const organizationId = searchParams.get('organization_id') || undefined;

      // Validate format
      if (!['csv', 'json', 'pdf'].includes(format)) {
        return NextResponse.json(
          { error: 'Invalid format. Must be csv, json, or pdf' },
          { status: 400 }
        );
      }

      // Export requires Pro or Enterprise subscription
      if (context.subscription.tier === 'FREE') {
        return NextResponse.json(
          {
            error: 'Analytics export requires Pro or Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const filters: AnalyticsFilters = {
        period,
        start_date: startDate || undefined,
        end_date: endDate || undefined,
        organization_id: organizationId,
      };

      const result = await exportAnalytics(context.userId, organizationId || user.id, filters, format);

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      // Set appropriate headers for file download
      const headers = new Headers();
      
      if (format === 'csv') {
        headers.set('Content-Type', 'text/csv');
        headers.set('Content-Disposition', `attachment; filename="${result.filename}"`);
      } else if (format === 'json') {
        headers.set('Content-Type', 'application/json');
        headers.set('Content-Disposition', `attachment; filename="${result.filename}"`);
      } else if (format === 'pdf') {
        headers.set('Content-Type', 'application/pdf');
        headers.set('Content-Disposition', `attachment; filename="${result.filename}"`);
      }

      return new NextResponse(result.data, {
        status: 200,
        headers,
      });

    } catch (error) {
      console.error('Analytics export error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to export analytics' },
        { status: 500 }
      );
    }
  });
}
