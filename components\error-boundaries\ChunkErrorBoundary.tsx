'use client';

import React, { Component, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertTriangle } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

export class ChunkErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error for debugging
    console.error('ChunkErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Check if it's a chunk loading error
    if (this.isChunkLoadError(error)) {
      // Attempt to reload the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  }

  isChunkLoadError = (error: Error): boolean => {
    return (
      error.name === 'ChunkLoadError' ||
      error.message.includes('Loading chunk') ||
      error.message.includes('Loading CSS chunk') ||
      error.message.includes('Failed to import')
    );
  };

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Check if it's a chunk loading error
      if (this.state.error && this.isChunkLoadError(this.state.error)) {
        return (
          <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
            <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
              <div className="flex justify-center mb-4">
                <RefreshCw className="h-12 w-12 text-blue-500 animate-spin" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Loading Application...
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                The application is updating. Please wait while we reload the latest version.
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                This page will refresh automatically.
              </div>
            </div>
          </div>
        );
      }

      // Custom fallback UI or default error UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
            <div className="flex justify-center mb-4">
              <AlertTriangle className="h-12 w-12 text-red-500" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Something went wrong
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              We encountered an unexpected error. Please try refreshing the page.
            </p>
            <div className="space-y-3">
              <Button onClick={this.handleRetry} variant="outline" className="w-full">
                Try Again
              </Button>
              <Button onClick={this.handleReload} className="w-full">
                Refresh Page
              </Button>
            </div>
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm text-gray-500 dark:text-gray-400">
                  Error Details (Development)
                </summary>
                <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Enhanced hook for handling all types of runtime errors
export const useChunkErrorRecovery = () => {
  React.useEffect(() => {
    let retryCount = 0;
    const maxRetries = 3;

    const handleChunkError = (event: ErrorEvent) => {
      const error = event.error;
      const message = event.message || '';

      if (
        error?.name === 'ChunkLoadError' ||
        message.includes('Loading chunk') ||
        message.includes('Loading CSS chunk') ||
        message.includes('Failed to import') ||
        message.includes("Cannot read properties of undefined (reading 'call')") ||
        message.includes('runtime.js') ||
        message.includes('main.js') ||
        message.includes('framework.js')
      ) {
        event.preventDefault();

        retryCount++;
        console.warn(`Chunk loading error detected (attempt ${retryCount}/${maxRetries}), attempting recovery...`);

        if (retryCount >= maxRetries) {
          console.error('Max retries exceeded, clearing caches and reloading...');

          // Clear all caches before reload
          if ('caches' in window) {
            caches.keys().then(names => {
              names.forEach(name => caches.delete(name));
              setTimeout(() => window.location.reload(), 1000);
            });
          } else {
            setTimeout(() => window.location.reload(), 1000);
          }
        } else {
          // Retry with exponential backoff
          setTimeout(() => {
            window.location.reload();
          }, 1000 * retryCount);
        }
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason;
      if (
        error?.message?.includes('Loading chunk') ||
        error?.message?.includes('Failed to import') ||
        error?.message?.includes('ChunkLoadError')
      ) {
        event.preventDefault();
        console.warn('Chunk loading promise rejection detected, attempting recovery...');

        // Clear dynamic import cache if available
        if (typeof window !== 'undefined' && (window as any).__webpack_require__?.cache) {
          Object.keys((window as any).__webpack_require__.cache).forEach(key => {
            delete (window as any).__webpack_require__.cache[key];
          });
        }

        setTimeout(() => window.location.reload(), 1000);
      }
    };

    // Enhanced error monitoring
    const handleGlobalError = (event: ErrorEvent) => {
      // Track errors for monitoring
      if (typeof window !== 'undefined' && (window as any).posthog) {
        (window as any).posthog.capture('runtime_error', {
          error: event.error?.message || event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack
        });
      }
    };

    window.addEventListener('error', handleChunkError);
    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleChunkError);
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);
};

export default ChunkErrorBoundary;
