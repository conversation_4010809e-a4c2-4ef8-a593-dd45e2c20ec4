{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "dist", "rootDir": ".", "allowJs": true, "baseUrl": ".", "paths": {"@/*": ["./*"]}, "lib": ["dom", "dom.iterable", "esnext"], "noEmit": true, "incremental": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", "api/**/*", "next-env.d.ts", "server.ts", "src/**/*", ".next/types/**/*.ts"], "exclude": ["node_modules", "supabase/functions/**/*", "microservices/**/*", "packages/**/*", "dist/**/*", "__tests__/**/*", "tests/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts"]}