#!/usr/bin/env tsx

/**
 * Debug version of dev-start script
 */

console.log('🔍 DEBUG: Starting dev-start script');
console.log('🔍 DEBUG: Script file:', __filename);
console.log('🔍 DEBUG: Working directory:', process.cwd());

import fs from 'fs';
import path from 'path';

console.log('🔍 DEBUG: Imports loaded successfully');

// Load environment variables from .env.local
function loadEnvironmentVariables() {
  console.log('🔍 DEBUG: Loading environment variables...');
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    console.log('🔍 DEBUG: .env.local file found');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
        const [key, ...valueParts] = trimmed.split('=');
        const value = valueParts.join('=');
        if (key && value && !process.env[key]) {
          process.env[key] = value;
        }
      }
    }
    console.log('🔍 DEBUG: Environment variables loaded');
  } else {
    console.log('🔍 DEBUG: .env.local file not found');
  }
}

console.log('🔍 DEBUG: About to load environment variables');
loadEnvironmentVariables();

console.log('🔍 DEBUG: Environment variables loaded, starting main function');

async function main() {
  console.log('🔍 DEBUG: Main function started');
  console.log('🚀 SLACK SUMMARY SCRIBE - DEVELOPMENT SERVER');
  console.log('✅ Debug version running successfully');
  console.log('🔍 DEBUG: Main function completed');
}

console.log('🔍 DEBUG: About to call main function');
main().then(() => {
  console.log('🔍 DEBUG: Main function completed successfully');
}).catch((error) => {
  console.error('🔍 DEBUG: Error in main function:', error);
  process.exit(1);
});
