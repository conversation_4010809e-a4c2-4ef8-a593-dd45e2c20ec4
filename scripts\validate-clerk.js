#!/usr/bin/env node

/**
 * Clerk Keys Validation Script
 * Run this to check if your Clerk keys are properly configured
 */

const fs = require('fs');
const path = require('path');

function validateClerkKeys() {
  console.log('🔍 Validating Clerk Configuration...\n');

  // Read .env.local file
  const envPath = path.join(process.cwd(), '.env.local');
  
  if (!fs.existsSync(envPath)) {
    console.log('❌ .env.local file not found');
    return false;
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  
  let publishableKey = '';
  let secretKey = '';
  
  // Extract Clerk keys
  envLines.forEach(line => {
    if (line.startsWith('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=')) {
      publishableKey = line.split('=')[1]?.trim() || '';
    }
    if (line.startsWith('CLERK_SECRET_KEY=')) {
      secretKey = line.split('=')[1]?.trim() || '';
    }
  });

  console.log('📋 Current Configuration:');
  console.log('========================');
  
  // Validate publishable key
  console.log('\n🔑 Publishable Key:');
  if (!publishableKey) {
    console.log('❌ NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY is missing');
    return false;
  } else if (!publishableKey.startsWith('pk_test_') && !publishableKey.startsWith('pk_live_')) {
    console.log('❌ Invalid format - must start with "pk_test_" or "pk_live_"');
    console.log(`   Current: ${publishableKey.substring(0, 20)}...`);
    return false;
  } else if (publishableKey.includes('demo') || publishableKey.includes('1234567890') || publishableKey.includes('Y2xlcmstZGVtby0x')) {
    console.log('❌ Appears to be a placeholder/demo key');
    console.log(`   Current: ${publishableKey.substring(0, 20)}...`);
    return false;
  } else if (publishableKey.length < 50) {
    console.log('❌ Key appears to be too short');
    console.log(`   Current length: ${publishableKey.length} (should be 50+)`);
    return false;
  } else {
    console.log('✅ Valid format');
    console.log(`   Key: ${publishableKey.substring(0, 20)}...`);
  }

  // Validate secret key
  console.log('\n🔐 Secret Key:');
  if (!secretKey) {
    console.log('❌ CLERK_SECRET_KEY is missing');
    return false;
  } else if (!secretKey.startsWith('sk_test_') && !secretKey.startsWith('sk_live_')) {
    console.log('❌ Invalid format - must start with "sk_test_" or "sk_live_"');
    console.log(`   Current: ${secretKey.substring(0, 20)}...`);
    return false;
  } else if (secretKey.includes('demo') || secretKey.includes('1234567890') || secretKey.includes('Y2xlcmstZGVtby0x')) {
    console.log('❌ Appears to be a placeholder/demo key');
    console.log(`   Current: ${secretKey.substring(0, 20)}...`);
    return false;
  } else if (secretKey.length < 50) {
    console.log('❌ Key appears to be too short');
    console.log(`   Current length: ${secretKey.length} (should be 50+)`);
    return false;
  } else {
    console.log('✅ Valid format');
    console.log(`   Key: ${secretKey.substring(0, 20)}...`);
  }

  return true;
}

function showInstructions() {
  console.log('\n💡 How to get real Clerk keys:');
  console.log('==============================');
  console.log('1. Go to https://dashboard.clerk.com/');
  console.log('2. Sign up for a free account or sign in');
  console.log('3. Create a new application or select existing one');
  console.log('4. Go to "API Keys" in the left sidebar');
  console.log('5. Copy the Publishable Key and Secret Key');
  console.log('6. Update your .env.local file:');
  console.log('');
  console.log('   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YOUR_KEY_HERE');
  console.log('   CLERK_SECRET_KEY=sk_test_YOUR_KEY_HERE');
  console.log('');
  console.log('7. Restart your dev server: npm run dev');
}

// Run validation
const isValid = validateClerkKeys();

if (isValid) {
  console.log('\n🎉 SUCCESS!');
  console.log('============');
  console.log('✅ Your Clerk keys appear to be valid!');
  console.log('✅ You should be able to run your app without the "Publishable key not valid" error');
  console.log('');
  console.log('Next steps:');
  console.log('1. Restart your dev server: npm run dev');
  console.log('2. Visit http://localhost:3000');
  console.log('3. Test the sign-in/sign-up functionality');
} else {
  console.log('\n❌ VALIDATION FAILED');
  console.log('===================');
  console.log('Your Clerk keys need to be updated with real keys from Clerk.');
  showInstructions();
}

console.log('\n' + '='.repeat(50));
