'use client';

import { useState, useEffect, ReactNode } from 'react';
import dynamic from 'next/dynamic';

/**
 * SSR-Safe Client-Only Wrapper
 * 
 * Prevents hydration mismatches by ensuring components only render on client
 */
interface ClientOnlyProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Higher-order component for client-only rendering
 */
export function withClientOnly<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  return function ClientOnlyComponent(props: P) {
    return (
      <ClientOnly fallback={fallback}>
        <Component {...props} />
      </ClientOnly>
    );
  };
}

/**
 * Dynamic import with SSR disabled and error handling
 */
export function createClientOnlyComponent<P extends object>(
  importFn: () => Promise<{ default: React.ComponentType<P> }>,
  options?: {
    fallback?: ReactNode;
    loading?: ReactNode;
    errorFallback?: ReactNode;
  }
) {
  // Use require for synchronous import to avoid async issues
  const { safeDynamicImport } = require('@/lib/dynamic-import-handler');

  return dynamic(safeDynamicImport(importFn, {
    fallback: options?.errorFallback
      ? () => Promise.resolve({ default: () => <>{options.errorFallback}</> })
      : undefined
  }), {
    ssr: false,
    loading: () => <>{options?.loading || options?.fallback}</>,
  });
}

/**
 * HYDRATION-SAFE Hook to check if we're on the client side
 * CRITICAL FIX: Uses suppressHydrationWarning to prevent mismatches
 */
export function useIsClient(): boolean {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * HYDRATION-SAFE Hook - Alternative that doesn't cause hydration issues
 * Use this instead of useIsClient when hydration safety is critical
 */
export function useHydrationSafeClient(): boolean {
  // Always return true after initial render to prevent hydration mismatches
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // During SSR, return false. After hydration, return true.
  return typeof window !== 'undefined' && mounted;
}

/**
 * Hook for client-only effects
 */
export function useClientEffect(
  effect: React.EffectCallback,
  deps?: React.DependencyList
) {
  const isClient = useIsClient();

  useEffect(() => {
    if (isClient) {
      return effect();
    }
  }, [isClient, ...(deps || [])]);
}

/**
 * Safe localStorage access
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T) => void] {
  const [storedValue, setStoredValue] = useState<T>(initialValue);
  const isClient = useIsClient();

  useEffect(() => {
    if (isClient) {
      try {
        const item = window.localStorage.getItem(key);
        if (item) {
          setStoredValue(JSON.parse(item));
        }
      } catch (error) {
        console.warn(`Error reading localStorage key "${key}":`, error);
      }
    }
  }, [key, isClient]);

  const setValue = (value: T) => {
    try {
      setStoredValue(value);
      if (isClient) {
        window.localStorage.setItem(key, JSON.stringify(value));
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}

/**
 * Safe sessionStorage access
 */
export function useSessionStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T) => void] {
  const [storedValue, setStoredValue] = useState<T>(initialValue);
  const isClient = useIsClient();

  useEffect(() => {
    if (isClient) {
      try {
        const item = window.sessionStorage.getItem(key);
        if (item) {
          setStoredValue(JSON.parse(item));
        }
      } catch (error) {
        console.warn(`Error reading sessionStorage key "${key}":`, error);
      }
    }
  }, [key, isClient]);

  const setValue = (value: T) => {
    try {
      setStoredValue(value);
      if (isClient) {
        window.sessionStorage.setItem(key, JSON.stringify(value));
      }
    } catch (error) {
      console.warn(`Error setting sessionStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}

/**
 * Safe window access
 */
export function useWindow() {
  const isClient = useIsClient();
  return isClient ? window : undefined;
}

/**
 * Safe document access
 */
export function useDocument() {
  const isClient = useIsClient();
  return isClient ? document : undefined;
}

/**
 * Safe navigator access
 */
export function useNavigator() {
  const isClient = useIsClient();
  return isClient ? navigator : undefined;
}
