'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, ArrowRight, Home, Download, Share2, Copy } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

export default function SuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isClient, setIsClient] = useState(false);
  
  // Get success type and related data from URL params
  const type = searchParams.get('type') || 'general';
  const summaryId = searchParams.get('summaryId');
  const fileName = searchParams.get('fileName');
  const exportType = searchParams.get('exportType');
  const paymentId = searchParams.get('paymentId');
  const subscriptionId = searchParams.get('subscriptionId');

  useEffect(() => {
    setIsClient(true);
    
    // Log success event to analytics
    if (typeof window !== 'undefined' && (window as any).posthog) {
      (window as any).posthog.capture('success_page_viewed', {
        success_type: type,
        summary_id: summaryId,
        file_name: fileName,
        export_type: exportType,
        payment_id: paymentId,
        subscription_id: subscriptionId
      });
    }
  }, [type, summaryId, fileName, exportType, paymentId, subscriptionId]);

  const getSuccessContent = () => {
    switch (type) {
      case 'upload':
        return {
          title: 'File Uploaded Successfully!',
          description: fileName 
            ? `Your file "${fileName}" has been uploaded and is being processed.`
            : 'Your file has been uploaded and is being processed.',
          icon: <CheckCircle className="w-16 h-16 text-green-500" />,
          actions: [
            {
              label: 'View Summary',
              href: summaryId ? `/summaries/${summaryId}` : '/dashboard',
              variant: 'default' as const,
              icon: <ArrowRight className="w-4 h-4" />
            },
            {
              label: 'Upload Another',
              href: '/upload',
              variant: 'outline' as const
            }
          ]
        };
        
      case 'export':
        return {
          title: 'Export Completed!',
          description: exportType 
            ? `Your ${exportType.toUpperCase()} export has been generated successfully.`
            : 'Your export has been generated successfully.',
          icon: <Download className="w-16 h-16 text-green-500" />,
          actions: [
            {
              label: 'Back to Summary',
              href: summaryId ? `/summaries/${summaryId}` : '/dashboard',
              variant: 'default' as const,
              icon: <ArrowRight className="w-4 h-4" />
            },
            {
              label: 'View Dashboard',
              href: '/dashboard',
              variant: 'outline' as const
            }
          ]
        };
        
      case 'payment':
        return {
          title: 'Payment Successful!',
          description: subscriptionId 
            ? 'Your subscription has been activated successfully.'
            : 'Your payment has been processed successfully.',
          icon: <CheckCircle className="w-16 h-16 text-green-500" />,
          actions: [
            {
              label: 'View Dashboard',
              href: '/dashboard',
              variant: 'default' as const,
              icon: <ArrowRight className="w-4 h-4" />
            },
            {
              label: 'Billing Settings',
              href: '/billing',
              variant: 'outline' as const
            }
          ]
        };
        
      case 'subscription':
        return {
          title: 'Welcome to Pro!',
          description: 'Your subscription has been activated. You now have access to all premium features.',
          icon: <CheckCircle className="w-16 h-16 text-green-500" />,
          actions: [
            {
              label: 'Explore Features',
              href: '/dashboard',
              variant: 'default' as const,
              icon: <ArrowRight className="w-4 h-4" />
            },
            {
              label: 'View Billing',
              href: '/billing',
              variant: 'outline' as const
            }
          ]
        };
        
      case 'slack':
        return {
          title: 'Slack Connected!',
          description: 'Your Slack workspace has been connected successfully. You can now summarize Slack conversations.',
          icon: <CheckCircle className="w-16 h-16 text-green-500" />,
          actions: [
            {
              label: 'Try Slack Summary',
              href: '/dashboard',
              variant: 'default' as const,
              icon: <ArrowRight className="w-4 h-4" />
            },
            {
              label: 'Integration Settings',
              href: '/integrations',
              variant: 'outline' as const
            }
          ]
        };
        
      default:
        return {
          title: 'Success!',
          description: 'Your action has been completed successfully.',
          icon: <CheckCircle className="w-16 h-16 text-green-500" />,
          actions: [
            {
              label: 'Continue',
              href: '/dashboard',
              variant: 'default' as const,
              icon: <ArrowRight className="w-4 h-4" />
            }
          ]
        };
    }
  };

  const handleCopyLink = () => {
    if (summaryId) {
      const summaryUrl = `${window.location.origin}/summaries/${summaryId}`;
      navigator.clipboard.writeText(summaryUrl);
      toast.success('Summary link copied to clipboard!');
    }
  };

  const handleShare = () => {
    if (summaryId && navigator.share) {
      navigator.share({
        title: 'AI Summary',
        text: 'Check out this AI-generated summary',
        url: `${window.location.origin}/summaries/${summaryId}`
      });
    } else {
      handleCopyLink();
    }
  };

  if (!isClient) {
    return null; // Prevent hydration issues
  }

  const content = getSuccessContent();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="text-center max-w-md w-full">
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">
              {content.icon}
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {content.title}
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400 mt-2">
              {content.description}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Primary Actions */}
            <div className="space-y-3">
              {content.actions.map((action, index) => (
                <Button
                  key={index}
                  asChild
                  variant={action.variant}
                  className="w-full"
                >
                  <Link href={action.href}>
                    {action.icon && <span className="mr-2">{action.icon}</span>}
                    {action.label}
                  </Link>
                </Button>
              ))}
            </div>

            {/* Additional Actions for Summary Success */}
            {type === 'upload' && summaryId && (
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-2 gap-2">
                  <Button onClick={handleShare} variant="outline" size="sm">
                    <Share2 className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                  <Button onClick={handleCopyLink} variant="outline" size="sm">
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Link
                  </Button>
                </div>
              </div>
            )}

            {/* Home Link */}
            <div className="pt-4">
              <Button asChild variant="ghost" size="sm" className="w-full">
                <Link href="/">
                  <Home className="w-4 h-4 mr-2" />
                  Back to Home
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
