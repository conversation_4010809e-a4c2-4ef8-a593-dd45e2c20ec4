import { devLog } from '@/lib/console-cleaner';
/**
 * Supabase Database Client (No Auth)
 *
 * This file provides a Supabase client for database operations only.
 * Authentication has been completely removed.
 */

import { createBrowserClient } from '@supabase/ssr'
import type { SupabaseClient } from '@supabase/supabase-js'

// Global singleton instance
let supabaseInstance: SupabaseClient | null = null

/**
 * Get or create the singleton Supabase client instance for database operations only
 * No authentication functionality included
 */
export function getSupabaseClient(): SupabaseClient {
  // Only create client on browser side
  if (typeof window === 'undefined') {
    throw new Error('Supabase client can only be used on the browser side')
  }

  // Return existing instance if available
  if (supabaseInstance) {
    return supabaseInstance
  }

  // Validate environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl) {
    throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
  }

  if (!supabaseAnonKey) {
    throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
  }

  // Create new instance with minimal configuration (database only)
  supabaseInstance = createBrowserClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      // Disable all auth functionality
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false,
    },
  })

  if (process.env.NODE_ENV === 'development') {
  devLog.log('✅ Supabase database client created successfully (no auth)')
  }

  return supabaseInstance
}

/**
 * Reset the singleton instance (useful for testing or when switching environments)
 */
export function resetSupabaseClient(): void {
  supabaseInstance = null
  if (process.env.NODE_ENV === 'development') {
  devLog.log('🔄 Supabase database client instance reset')
  }
}

/**
 * Check if Supabase client is initialized
 */
export function isSupabaseClientInitialized(): boolean {
  return supabaseInstance !== null
}

/**
 * Get Supabase client with error handling (database operations only)
 */
export function getSupabaseClientSafe(): SupabaseClient | null {
  try {
    return getSupabaseClient()
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ Failed to get Supabase database client:', error)
    }
    return null
  }
}

// Export the main function as default for convenience
export default getSupabaseClient
