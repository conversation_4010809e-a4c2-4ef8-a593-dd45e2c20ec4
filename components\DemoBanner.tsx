'use client';

import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Clock, 
  Crown, 
  X, 
  TrendingUp,
  AlertTriangle,
  Zap,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface DemoBannerProps {
  isInDemo: boolean;
  trialDaysRemaining: number;
  trialExpired: boolean;
  usage: {
    summaries: { used: number; limit: number; remaining: number };
    exports: { used: number; limit: number; remaining: number };
    aiRequests: { used: number; limit: number; remaining: number };
    fileUploads: { used: number; limit: number; remaining: number };
    slackConnections: { used: number; limit: number; remaining: number };
  };
  className?: string;
  onDismiss?: () => void;
  showDetails?: boolean;
}

export default function DemoBanner({
  isInDemo,
  trialDaysRemaining,
  trialExpired,
  usage,
  className = '',
  onDismiss,
  showDetails = false
}: DemoBannerProps) {
  const router = useRouter();
  const [dismissed, setDismissed] = useState(false);
  const [expanded, setExpanded] = useState(false);

  // Don't show banner if not in demo mode or dismissed
  if (!isInDemo || dismissed) {
    return null;
  }

  const handleUpgrade = () => {
    router.push('/billing');
  };

  const handleDismiss = () => {
    setDismissed(true);
    onDismiss?.();
  };

  const getBannerStyle = () => {
    if (trialExpired) {
      return {
        bgColor: 'bg-red-50 dark:bg-red-950',
        borderColor: 'border-red-200 dark:border-red-800',
        textColor: 'text-red-800 dark:text-red-200',
        iconColor: 'text-red-600 dark:text-red-400'
      };
    }
    
    if (trialDaysRemaining <= 2) {
      return {
        bgColor: 'bg-orange-50 dark:bg-orange-950',
        borderColor: 'border-orange-200 dark:border-orange-800',
        textColor: 'text-orange-800 dark:text-orange-200',
        iconColor: 'text-orange-600 dark:text-orange-400'
      };
    }
    
    return {
      bgColor: 'bg-blue-50 dark:bg-blue-950',
      borderColor: 'border-blue-200 dark:border-blue-800',
      textColor: 'text-blue-800 dark:text-blue-200',
      iconColor: 'text-blue-600 dark:text-blue-400'
    };
  };

  const style = getBannerStyle();

  const getStatusBadge = () => {
    if (trialExpired) {
      return <Badge variant="destructive">Trial Expired</Badge>;
    }
    
    if (trialDaysRemaining <= 2) {
      return <Badge variant="secondary" className="bg-orange-100 text-orange-800">
        {trialDaysRemaining} Days Left
      </Badge>;
    }
    
    return <Badge variant="default" className="bg-blue-100 text-blue-800">
      Trial Active
    </Badge>;
  };

  const getMainMessage = () => {
    if (trialExpired) {
      return 'Your trial has expired. Upgrade now to continue using all features.';
    }
    
    if (trialDaysRemaining <= 2) {
      return `Only ${trialDaysRemaining} days left in your trial. Upgrade to keep your data and unlock unlimited access.`;
    }
    
    return `You're on a ${trialDaysRemaining}-day trial. Upgrade anytime to unlock unlimited summaries, exports, and premium features.`;
  };

  const getUsageSummary = () => {
    const totalUsed = usage.summaries.used + usage.exports.used + usage.fileUploads.used;
    const anyLimitReached = Object.values(usage).some(u => u.remaining === 0);
    
    if (anyLimitReached) {
      const limitReachedItems = Object.entries(usage)
        .filter(([_, u]) => u.remaining === 0)
        .map(([key, _]) => key.replace(/([A-Z])/g, ' $1').toLowerCase());
      
      return `Limit reached for: ${limitReachedItems.join(', ')}`;
    }
    
    return `${totalUsed} actions used this trial`;
  };

  return (
    <div className={`mb-4 ${className}`}>
      <Alert className={`${style.bgColor} ${style.borderColor} border-l-4`}>
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 mt-0.5">
            {trialExpired ? (
              <AlertTriangle className={`h-5 w-5 ${style.iconColor}`} />
            ) : (
              <Clock className={`h-5 w-5 ${style.iconColor}`} />
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <AlertDescription>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className={`font-medium ${style.textColor}`}>
                    Trial Status
                  </span>
                  {getStatusBadge()}
                </div>
                
                <div className="flex items-center space-x-2">
                  {showDetails && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setExpanded(!expanded)}
                      className={`${style.textColor} hover:bg-white/20`}
                    >
                      {expanded ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  )}
                  
                  <Button
                    onClick={handleUpgrade}
                    size="sm"
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                  >
                    <Crown className="h-4 w-4 mr-1" />
                    Upgrade
                  </Button>
                  
                  {onDismiss && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleDismiss}
                      className={`${style.textColor} hover:bg-white/20`}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
              
              <div className={`${style.textColor} mb-2`}>
                {getMainMessage()}
              </div>
              
              <div className={`text-sm ${style.textColor} opacity-80`}>
                {getUsageSummary()}
              </div>
              
              {/* Expanded Details */}
              {expanded && showDetails && (
                <div className="mt-4 pt-4 border-t border-white/20">
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
                    {Object.entries(usage).map(([key, data]) => {
                      const percentage = (data.used / data.limit) * 100;
                      const isLimitReached = data.remaining === 0;
                      
                      return (
                        <div key={key} className="space-y-1">
                          <div className="flex items-center justify-between text-xs">
                            <span className="capitalize">
                              {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                            </span>
                            {isLimitReached && (
                              <AlertTriangle className="h-3 w-3 text-red-500" />
                            )}
                          </div>
                          <Progress 
                            value={percentage} 
                            className="h-1.5"
                          />
                          <div className="text-xs opacity-70">
                            {data.used}/{data.limit} used
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  
                  <div className="mt-3 flex items-center space-x-4 text-xs opacity-80">
                    <div className="flex items-center space-x-1">
                      <Zap className="h-3 w-3" />
                      <span>Unlimited with Pro</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="h-3 w-3" />
                      <span>Advanced features included</span>
                    </div>
                  </div>
                </div>
              )}
            </AlertDescription>
          </div>
        </div>
      </Alert>
    </div>
  );
}
