/**
 * Test AI Summarization API
 * Quick test to verify the AI summarization pipeline is working
 */

const testSummarization = async () => {
  const testData = {
    transcriptText: `
<PERSON>: Hey team, I wanted to discuss our Q4 goals and the new product launch.
<PERSON>: Great! I've been working on the marketing strategy. We should focus on our target demographics.
Mike: From a technical perspective, we need to ensure our infrastructure can handle the increased load.
<PERSON>: Excellent points. <PERSON>, can you prepare a detailed marketing plan by next Friday?
<PERSON>: Absolutely, I'll have it ready.
<PERSON>: I'll also run some load tests this week to identify any potential bottlenecks.
<PERSON>: Perfect. Let's reconvene next Monday to review everything.
    `,
    type: 'meeting',
    context: 'Team meeting about Q4 goals and product launch',
    preferredModel: 'deepseek-r1'
  };

  try {
    console.log('🧪 Testing AI Summarization API...');
    
    const response = await fetch('http://localhost:3000/api/summarize', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ AI Summarization Test PASSED');
      console.log('📊 Summary Result:', {
        summary: result.summary?.substring(0, 100) + '...',
        keyPoints: result.keyPoints?.length || 0,
        actionItems: result.actionItems?.length || 0,
        redFlags: result.redFlags?.length || 0,
        skills: result.skills?.length || 0,
        model: result.model,
        processingTime: result.processingTime + 'ms'
      });
    } else {
      console.log('❌ AI Summarization Test FAILED');
      console.log('Error:', result);
    }
  } catch (error) {
    console.log('❌ AI Summarization Test ERROR');
    console.error('Error:', error);
  }
};

// Run the test
testSummarization();
