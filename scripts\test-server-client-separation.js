#!/usr/bin/env node

/**
 * TEST SUPABASE SERVER/CLIENT SEPARATION
 * 
 * This script validates that the Supabase client separation is working correctly
 * and that there are no more build errors related to next/headers imports.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Testing Supabase Server/Client Separation...\n');

let issuesFound = 0;
let warningsFound = 0;

// Helper functions
const checkFileExists = (filePath) => fs.existsSync(filePath);
const readFile = (filePath) => fs.readFileSync(filePath, 'utf8');
const logSuccess = (message) => console.log(`✅ ${message}`);
const logError = (message) => {
  console.log(`❌ ${message}`);
  issuesFound++;
};
const logWarning = (message) => {
  console.log(`⚠️  ${message}`);
  warningsFound++;
};

// 1. Check that problematic files are removed
console.log('1️⃣ Checking problematic files are removed...');

const problematicFiles = [
  'lib/supabase-client.ts'
];

problematicFiles.forEach(file => {
  if (!checkFileExists(file)) {
    logSuccess(`${file} properly removed`);
  } else {
    logError(`${file} still exists - should be removed`);
  }
});

// 2. Check that proper separation files exist
console.log('\n2️⃣ Checking proper separation files exist...');

const requiredFiles = [
  'lib/supabase-server.ts',
  'lib/supabase-browser.ts',
  'middleware.ts'
];

requiredFiles.forEach(file => {
  if (checkFileExists(file)) {
    logSuccess(`${file} exists`);
  } else {
    logError(`${file} missing`);
  }
});

// 3. Check server-only files don't import next/headers in client context
console.log('\n3️⃣ Checking server-only imports...');

if (checkFileExists('lib/supabase-server.ts')) {
  const serverContent = readFile('lib/supabase-server.ts');
  
  if (serverContent.includes("import { cookies } from 'next/headers'")) {
    logSuccess('Server client properly imports next/headers');
  } else {
    logError('Server client missing next/headers import');
  }
  
  if (serverContent.includes('createServerClient')) {
    logSuccess('Server client uses createServerClient');
  } else {
    logError('Server client missing createServerClient');
  }
} else {
  logError('lib/supabase-server.ts missing');
}

// 4. Check browser-only files don't import next/headers
console.log('\n4️⃣ Checking browser-only imports...');

if (checkFileExists('lib/supabase-browser.ts')) {
  const browserContent = readFile('lib/supabase-browser.ts');
  
  if (browserContent.includes("'use client'")) {
    logSuccess('Browser client has use client directive');
  } else {
    logError('Browser client missing use client directive');
  }
  
  if (!browserContent.includes("import { cookies } from 'next/headers'") &&
      !browserContent.includes('import { cookies } from "next/headers"')) {
    logSuccess('Browser client does not import next/headers');
  } else {
    logError('Browser client incorrectly imports next/headers');
  }
  
  if (browserContent.includes('createBrowserClient')) {
    logSuccess('Browser client uses createBrowserClient');
  } else {
    logError('Browser client missing createBrowserClient');
  }
} else {
  logError('lib/supabase-browser.ts missing');
}

// 5. Check client components use browser client
console.log('\n5️⃣ Checking client component imports...');

const clientFiles = [
  'app/login/page.tsx',
  'components/providers.tsx',
  'components/providers/AuthProvider.tsx',
  'lib/user-context.tsx'
];

clientFiles.forEach(file => {
  if (checkFileExists(file)) {
    const content = readFile(file);
    
    if (content.includes("'use client'")) {
      if (content.includes('supabase-browser')) {
        logSuccess(`${file} correctly imports from supabase-browser`);
      } else if (content.includes('supabase-client') || content.includes('lib/supabase')) {
        logError(`${file} incorrectly imports from old supabase client`);
      } else {
        logWarning(`${file} may not be importing Supabase client`);
      }
    } else {
      logWarning(`${file} missing 'use client' directive`);
    }
  } else {
    logWarning(`${file} not found`);
  }
});

// 6. Check API routes use server client
console.log('\n6️⃣ Checking API route imports...');

const apiFiles = [
  'app/api/upload/route.ts',
  'app/api/summarize/route.ts',
  'app/api/integrations/notion/route.ts'
];

apiFiles.forEach(file => {
  if (checkFileExists(file)) {
    const content = readFile(file);
    
    if (content.includes('supabase-server')) {
      logSuccess(`${file} correctly imports from supabase-server`);
    } else if (content.includes('supabase-client') || content.includes('lib/supabase')) {
      logError(`${file} incorrectly imports from old supabase client`);
    } else {
      logWarning(`${file} may not be importing Supabase client`);
    }
  } else {
    logWarning(`${file} not found`);
  }
});

// 7. Check middleware uses proper server client
console.log('\n7️⃣ Checking middleware configuration...');

if (checkFileExists('middleware.ts')) {
  const middlewareContent = readFile('middleware.ts');
  
  if (middlewareContent.includes('createServerClient')) {
    logSuccess('Middleware uses createServerClient');
  } else {
    logError('Middleware missing createServerClient');
  }
  
  if (!middlewareContent.includes("import { cookies } from 'next/headers'") &&
      !middlewareContent.includes('import { cookies } from "next/headers"')) {
    logSuccess('Middleware does not import next/headers');
  } else {
    logError('Middleware incorrectly imports next/headers');
  }
  
  if (middlewareContent.includes('PROTECTED_ROUTES') && middlewareContent.includes('PUBLIC_ROUTES')) {
    logSuccess('Middleware has proper route configuration');
  } else {
    logError('Middleware missing route configuration');
  }
} else {
  logError('middleware.ts missing');
}

// 8. Test build to ensure no import conflicts
console.log('\n8️⃣ Testing build for import conflicts...');

try {
  console.log('Running Next.js build test...');
  execSync('npm run build', { stdio: 'pipe', timeout: 120000 });
  logSuccess('Build completed successfully - no import conflicts');
} catch (error) {
  const errorOutput = error.stdout?.toString() || error.stderr?.toString() || error.message;
  
  if (errorOutput.includes("You're importing a component that needs 'next/headers'")) {
    logError('Build failed: next/headers import conflict detected');
    console.log('   This means a client component is importing from a server-only file');
  } else if (errorOutput.includes('Multiple GoTrueClient instances')) {
    logError('Build failed: Multiple Supabase client instances detected');
  } else {
    logWarning('Build failed for other reasons (may not be related to client separation)');
    console.log(`   Error: ${errorOutput.substring(0, 200)}...`);
  }
}

// 9. Check for any remaining problematic imports
console.log('\n9️⃣ Scanning for problematic imports...');

function scanDirectory(dir, extensions = ['.ts', '.tsx']) {
  const files = [];
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath);
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  scan(dir);
  return files;
}

const allFiles = scanDirectory('.');
let problematicImports = 0;

allFiles.forEach(file => {
  try {
    const content = readFile(file);
    const isClientComponent = content.includes("'use client'");
    
    // Check for problematic patterns
    if (isClientComponent && content.includes('supabase-client')) {
      logError(`${file}: Client component imports from removed supabase-client`);
      problematicImports++;
    }
    
    if (isClientComponent && content.includes("from 'next/headers'")) {
      logError(`${file}: Client component imports next/headers`);
      problematicImports++;
    }
    
    if (!isClientComponent && file.includes('app/api/') && content.includes('supabase-browser')) {
      logError(`${file}: API route imports from browser client`);
      problematicImports++;
    }
  } catch (error) {
    // Skip files that can't be read
  }
});

if (problematicImports === 0) {
  logSuccess('No problematic imports found');
} else {
  logError(`Found ${problematicImports} problematic imports`);
}

// Summary
console.log('\n📊 SEPARATION TEST SUMMARY');
console.log('==========================');

if (issuesFound === 0 && warningsFound === 0) {
  console.log('🎉 Perfect! Supabase server/client separation is complete!');
  console.log('✅ No build errors related to next/headers');
  console.log('✅ Proper client/server separation implemented');
  console.log('✅ All imports are correctly configured');
  console.log('✅ Build passes without conflicts');
} else {
  if (issuesFound > 0) {
    console.log(`❌ Found ${issuesFound} critical issue(s) that need to be fixed`);
  }
  if (warningsFound > 0) {
    console.log(`⚠️  Found ${warningsFound} warning(s) - consider addressing`);
  }
}

console.log('\n📝 Next Steps:');
if (issuesFound === 0) {
  console.log('1. Test your application locally: npm run dev');
  console.log('2. Verify authentication flows work correctly');
  console.log('3. Test both HTTP and HTTPS development modes');
  console.log('4. Deploy to production when ready');
} else {
  console.log('1. Fix the critical issues listed above');
  console.log('2. Re-run this test script');
  console.log('3. Ensure build passes before proceeding');
}

console.log('\n📚 File Structure:');
console.log('- lib/supabase-server.ts: Server components, API routes, middleware');
console.log('- lib/supabase-browser.ts: Client components, React hooks');
console.log('- middleware.ts: Route protection and session validation');

process.exit(issuesFound > 0 ? 1 : 0);
