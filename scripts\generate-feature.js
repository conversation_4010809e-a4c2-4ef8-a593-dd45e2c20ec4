#!/usr/bin/env node

/**
 * Feature Generator CLI
 * 
 * Automatically scaffolds new features with:
 * - Folder structure
 * - Component templates
 * - API routes
 * - Tests
 * - Documentation
 * - Plugin registration
 */

const { program } = require('commander');
const inquirer = require('inquirer');
const chalk = require('chalk');
const fs = require('fs');
const path = require('path');

program
  .name('generate-feature')
  .description('Generate new feature scaffolding')
  .version('1.0.0');

program
  .command('create')
  .description('Create a new feature')
  .option('-n, --name <name>', 'Feature name')
  .option('-t, --type <type>', 'Feature type (integration, ui, analytics, admin)')
  .option('--skip-tests', 'Skip test file generation')
  .option('--skip-docs', 'Skip documentation generation')
  .action(async (options) => {
    try {
      console.log(chalk.blue.bold('🧬 Feature Generator\n'));

      // Gather feature information
      const featureConfig = await gatherFeatureConfig(options);

      // Validate feature name
      validateFeatureName(featureConfig.name);

      // Create feature structure
      await createFeatureStructure(featureConfig);

      // Generate files
      await generateFeatureFiles(featureConfig);

      // Update exports
      await updateExports(featureConfig);

      // Register plugin (if applicable)
      if (featureConfig.isPlugin) {
        await registerPlugin(featureConfig);
      }

      console.log(chalk.green.bold('\n✅ Feature generated successfully!'));
      console.log(chalk.yellow('\n📝 Next steps:'));
      console.log(`1. Implement feature logic in features/${featureConfig.name}/`);
      console.log(`2. Add tests: npm run test features/${featureConfig.name}`);
      console.log(`3. Update documentation: docs/features/${featureConfig.name}.md`);
      console.log(`4. Register routes in app/api/${featureConfig.name}/`);

    } catch (error) {
      console.error(chalk.red('❌ Feature generation failed:'), error.message);
      process.exit(1);
    }
  });

program
  .command('list')
  .description('List existing features')
  .action(() => {
    const featuresDir = path.join(process.cwd(), 'features');
    
    if (!fs.existsSync(featuresDir)) {
      console.log(chalk.yellow('No features directory found.'));
      return;
    }

    const features = fs.readdirSync(featuresDir, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    console.log(chalk.blue.bold('📋 Existing Features:\n'));
    
    features.forEach(feature => {
      const featurePath = path.join(featuresDir, feature);
      const configPath = path.join(featurePath, 'feature.config.json');
      
      if (fs.existsSync(configPath)) {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        console.log(chalk.green(`  ✅ ${feature}`));
        console.log(chalk.gray(`     Type: ${config.type}, Version: ${config.version}`));
      } else {
        console.log(chalk.yellow(`  ⚠️  ${feature} (no config)`));
      }
    });
  });

async function gatherFeatureConfig(options) {
  const questions = [];

  if (!options.name) {
    questions.push({
      type: 'input',
      name: 'name',
      message: 'Feature name (kebab-case):',
      validate: (input) => {
        if (!input) return 'Feature name is required';
        if (!/^[a-z][a-z0-9-]*$/.test(input)) {
          return 'Feature name must be kebab-case (lowercase, hyphens only)';
        }
        return true;
      }
    });
  }

  if (!options.type) {
    questions.push({
      type: 'list',
      name: 'type',
      message: 'Feature type:',
      choices: [
        { name: '🔌 Integration (OAuth, API, Export)', value: 'integration' },
        { name: '🎨 UI Component (React, Tailwind)', value: 'ui' },
        { name: '📊 Analytics (Metrics, Reports)', value: 'analytics' },
        { name: '🧑‍💼 Admin (Management, Tools)', value: 'admin' },
        { name: '⚙️ Core (Business Logic)', value: 'core' }
      ]
    });
  }

  questions.push(
    {
      type: 'input',
      name: 'description',
      message: 'Feature description:',
      default: `${options.name || 'New'} feature for Slack Summary Scribe`
    },
    {
      type: 'confirm',
      name: 'hasAPI',
      message: 'Include API routes?',
      default: true
    },
    {
      type: 'confirm',
      name: 'hasUI',
      message: 'Include UI components?',
      default: true
    },
    {
      type: 'confirm',
      name: 'hasDatabase',
      message: 'Include database models?',
      default: false
    },
    {
      type: 'confirm',
      name: 'isPlugin',
      message: 'Register as plugin?',
      default: false
    },
    {
      type: 'input',
      name: 'author',
      message: 'Author name:',
      default: 'SaaS Kit Team'
    }
  );

  const answers = await inquirer.prompt(questions);
  
  return {
    name: options.name || answers.name,
    type: options.type || answers.type,
    description: answers.description,
    hasAPI: answers.hasAPI,
    hasUI: answers.hasUI,
    hasDatabase: answers.hasDatabase,
    isPlugin: answers.isPlugin,
    author: answers.author,
    skipTests: options.skipTests || false,
    skipDocs: options.skipDocs || false,
    version: '1.0.0'
  };
}

function validateFeatureName(name) {
  const featuresDir = path.join(process.cwd(), 'features');
  const featurePath = path.join(featuresDir, name);

  if (fs.existsSync(featurePath)) {
    throw new Error(`Feature ${name} already exists`);
  }

  const reservedNames = ['auth', 'api', 'lib', 'components', 'pages', 'app'];
  if (reservedNames.includes(name)) {
    throw new Error(`Feature name ${name} is reserved`);
  }
}

async function createFeatureStructure(config) {
  console.log(chalk.blue('📁 Creating feature structure...'));

  const featurePath = path.join(process.cwd(), 'features', config.name);
  
  // Create main directories
  const directories = [
    featurePath,
    path.join(featurePath, 'components'),
    path.join(featurePath, 'hooks'),
    path.join(featurePath, 'services'),
    path.join(featurePath, 'types'),
    path.join(featurePath, 'utils')
  ];

  if (config.hasAPI) {
    directories.push(path.join(featurePath, 'api'));
  }

  if (config.hasDatabase) {
    directories.push(path.join(featurePath, 'models'));
    directories.push(path.join(featurePath, 'migrations'));
  }

  if (!config.skipTests) {
    directories.push(path.join(featurePath, '__tests__'));
  }

  directories.forEach(dir => {
    fs.mkdirSync(dir, { recursive: true });
  });

  console.log(chalk.green('  ✅ Directory structure created'));
}

async function generateFeatureFiles(config) {
  console.log(chalk.blue('📝 Generating feature files...'));

  const featurePath = path.join(process.cwd(), 'features', config.name);

  // Generate feature config
  const featureConfig = {
    name: config.name,
    type: config.type,
    description: config.description,
    version: config.version,
    author: config.author,
    hasAPI: config.hasAPI,
    hasUI: config.hasUI,
    hasDatabase: config.hasDatabase,
    isPlugin: config.isPlugin,
    createdAt: new Date().toISOString()
  };

  fs.writeFileSync(
    path.join(featurePath, 'feature.config.json'),
    JSON.stringify(featureConfig, null, 2)
  );

  // Generate index file
  generateIndexFile(featurePath, config);

  // Generate types
  generateTypesFile(featurePath, config);

  // Generate main component (if UI)
  if (config.hasUI) {
    generateMainComponent(featurePath, config);
  }

  // Generate API routes (if API)
  if (config.hasAPI) {
    generateAPIRoutes(featurePath, config);
  }

  // Generate service
  generateServiceFile(featurePath, config);

  // Generate hook (if UI)
  if (config.hasUI) {
    generateHookFile(featurePath, config);
  }

  // Generate tests
  if (!config.skipTests) {
    generateTestFiles(featurePath, config);
  }

  // Generate documentation
  if (!config.skipDocs) {
    generateDocumentation(featurePath, config);
  }

  // Generate plugin file (if plugin)
  if (config.isPlugin) {
    generatePluginFile(featurePath, config);
  }

  console.log(chalk.green('  ✅ Feature files generated'));
}

function generateIndexFile(featurePath, config) {
  const content = `/**
 * ${config.name} Feature
 * 
 * ${config.description}
 */

export * from './types';
export * from './services';
${config.hasUI ? "export * from './components';" : ''}
${config.hasUI ? "export * from './hooks';" : ''}
export * from './utils';
${config.isPlugin ? "export { default as plugin } from './plugin';" : ''}
`;

  fs.writeFileSync(path.join(featurePath, 'index.ts'), content);
}

function generateTypesFile(featurePath, config) {
  const pascalName = toPascalCase(config.name);
  
  const content = `/**
 * ${config.name} Types
 */

export interface ${pascalName}Config {
  enabled: boolean;
  settings?: Record<string, any>;
}

export interface ${pascalName}Data {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ${pascalName}CreateInput {
  name: string;
  description?: string;
}

export interface ${pascalName}UpdateInput {
  name?: string;
  description?: string;
}

export interface ${pascalName}Response {
  success: boolean;
  data?: ${pascalName}Data;
  error?: string;
}

export interface ${pascalName}ListResponse {
  success: boolean;
  data?: ${pascalName}Data[];
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
  };
  error?: string;
}
`;

  fs.writeFileSync(path.join(featurePath, 'types', 'index.ts'), content);
}

function generateMainComponent(featurePath, config) {
  const pascalName = toPascalCase(config.name);
  
  const content = `'use client';

/**
 * ${pascalName} Component
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { use${pascalName} } from '../hooks/use-${config.name}';
import { ${pascalName}Data } from '../types';

interface ${pascalName}Props {
  className?: string;
}

export function ${pascalName}({ className }: ${pascalName}Props) {
  const { data, loading, error, refresh } = use${pascalName}();

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-red-600">
            Error loading ${config.name}: {error}
          </div>
          <Button onClick={refresh} className="mt-2">
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>${pascalName}</CardTitle>
        <CardDescription>
          ${config.description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {data ? (
          <div>
            <h3 className="font-semibold">{data.name}</h3>
            {data.description && (
              <p className="text-gray-600 mt-1">{data.description}</p>
            )}
            <p className="text-sm text-gray-500 mt-2">
              Created: {new Date(data.createdAt).toLocaleDateString()}
            </p>
          </div>
        ) : (
          <div className="text-gray-500">
            No ${config.name} data available
          </div>
        )}
        
        <div className="mt-4">
          <Button onClick={refresh}>
            Refresh
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

export default ${pascalName};
`;

  fs.writeFileSync(path.join(featurePath, 'components', `${pascalName}.tsx`), content);

  // Generate component index
  const indexContent = `export { ${pascalName}, default } from './${pascalName}';
`;
  fs.writeFileSync(path.join(featurePath, 'components', 'index.ts'), indexContent);
}

function generateAPIRoutes(featurePath, config) {
  const apiPath = path.join(process.cwd(), 'app', 'api', config.name);
  fs.mkdirSync(apiPath, { recursive: true });

  const content = `import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { withAuth } from '@/lib/auth-middleware';
import { ${config.name}Service } from '@/features/${config.name}/services';

export const GET = withAuth(async (request: NextRequest, { user }) => {
  try {
    const supabase = await createSupabaseServerClient();
    const service = new ${toPascalCase(config.name)}Service(supabase);

    const data = await service.getAll(user.id);

    return NextResponse.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('${config.name} GET error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const POST = withAuth(async (request: NextRequest, { user }) => {
  try {
    const body = await request.json();
    const supabase = await createSupabaseServerClient();
    const service = new ${toPascalCase(config.name)}Service(supabase);

    const data = await service.create(user.id, body);

    return NextResponse.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('${config.name} POST error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});
`;

  fs.writeFileSync(path.join(apiPath, 'route.ts'), content);
}

function generateServiceFile(featurePath, config) {
  const pascalName = toPascalCase(config.name);
  
  const content = `/**
 * ${pascalName} Service
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { 
  ${pascalName}Data, 
  ${pascalName}CreateInput, 
  ${pascalName}UpdateInput 
} from '../types';

export class ${pascalName}Service {
  constructor(private supabase: SupabaseClient) {}

  async getAll(userId: string): Promise<${pascalName}Data[]> {
    const { data, error } = await this.supabase
      .from('${config.name}')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(\`Failed to fetch ${config.name}: \${error.message}\`);
    }

    return data || [];
  }

  async getById(userId: string, id: string): Promise<${pascalName}Data | null> {
    const { data, error } = await this.supabase
      .from('${config.name}')
      .select('*')
      .eq('user_id', userId)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(\`Failed to fetch ${config.name}: \${error.message}\`);
    }

    return data;
  }

  async create(userId: string, input: ${pascalName}CreateInput): Promise<${pascalName}Data> {
    const { data, error } = await this.supabase
      .from('${config.name}')
      .insert({
        ...input,
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw new Error(\`Failed to create ${config.name}: \${error.message}\`);
    }

    return data;
  }

  async update(userId: string, id: string, input: ${pascalName}UpdateInput): Promise<${pascalName}Data> {
    const { data, error } = await this.supabase
      .from('${config.name}')
      .update({
        ...input,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(\`Failed to update ${config.name}: \${error.message}\`);
    }

    return data;
  }

  async delete(userId: string, id: string): Promise<void> {
    const { error } = await this.supabase
      .from('${config.name}')
      .delete()
      .eq('user_id', userId)
      .eq('id', id);

    if (error) {
      throw new Error(\`Failed to delete ${config.name}: \${error.message}\`);
    }
  }
}
`;

  fs.writeFileSync(path.join(featurePath, 'services', 'index.ts'), content);
}

function generateHookFile(featurePath, config) {
  const pascalName = toPascalCase(config.name);
  
  const content = `/**
 * use${pascalName} Hook
 */

import { useState, useEffect, useCallback } from 'react';
import { ${pascalName}Data } from '../types';

interface Use${pascalName}Return {
  data: ${pascalName}Data | null;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

export function use${pascalName}(): Use${pascalName}Return {
  const [data, setData] = useState<${pascalName}Data | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/${config.name}');
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch ${config.name}');
      }

      setData(result.data?.[0] || null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refresh: fetchData
  };
}
`;

  fs.writeFileSync(path.join(featurePath, 'hooks', `use-${config.name}.ts`), content);

  // Generate hook index
  const indexContent = `export { use${pascalName} } from './use-${config.name}';
`;
  fs.writeFileSync(path.join(featurePath, 'hooks', 'index.ts'), indexContent);
}

function generateTestFiles(featurePath, config) {
  const pascalName = toPascalCase(config.name);
  
  const serviceTestContent = `/**
 * ${pascalName} Service Tests
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ${pascalName}Service } from '../services';

// Mock Supabase client
const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        order: vi.fn(() => ({
          data: [],
          error: null
        }))
      }))
    })),
    insert: vi.fn(() => ({
      select: vi.fn(() => ({
        single: vi.fn(() => ({
          data: { id: '1', name: 'Test' },
          error: null
        }))
      }))
    }))
  }))
};

describe('${pascalName}Service', () => {
  let service: ${pascalName}Service;

  beforeEach(() => {
    service = new ${pascalName}Service(mockSupabase as any);
    vi.clearAllMocks();
  });

  describe('getAll', () => {
    it('should fetch all ${config.name} items', async () => {
      const result = await service.getAll('user-123');
      
      expect(mockSupabase.from).toHaveBeenCalledWith('${config.name}');
      expect(result).toEqual([]);
    });
  });

  describe('create', () => {
    it('should create a new ${config.name} item', async () => {
      const input = { name: 'Test ${pascalName}' };
      const result = await service.create('user-123', input);
      
      expect(mockSupabase.from).toHaveBeenCalledWith('${config.name}');
      expect(result).toEqual({ id: '1', name: 'Test' });
    });
  });
});
`;

  fs.writeFileSync(path.join(featurePath, '__tests__', 'service.test.ts'), serviceTestContent);

  if (config.hasUI) {
    const componentTestContent = `/**
 * ${pascalName} Component Tests
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { ${pascalName} } from '../components';

// Mock the hook
vi.mock('../hooks/use-${config.name}', () => ({
  use${pascalName}: () => ({
    data: { id: '1', name: 'Test ${pascalName}', createdAt: '2024-01-01' },
    loading: false,
    error: null,
    refresh: vi.fn()
  })
}));

describe('${pascalName}', () => {
  it('renders correctly', () => {
    render(<${pascalName} />);
    
    expect(screen.getByText('${pascalName}')).toBeInTheDocument();
    expect(screen.getByText('Test ${pascalName}')).toBeInTheDocument();
  });
});
`;

    fs.writeFileSync(path.join(featurePath, '__tests__', 'component.test.tsx'), componentTestContent);
  }
}

function generateDocumentation(featurePath, config) {
  const docsDir = path.join(process.cwd(), 'docs', 'features');
  fs.mkdirSync(docsDir, { recursive: true });

  const content = `# ${toPascalCase(config.name)} Feature

${config.description}

## Overview

This feature provides ${config.type} functionality for the Slack Summary Scribe application.

## Features

- ✅ Feature 1
- ✅ Feature 2
- ✅ Feature 3

## Usage

### Basic Usage

\`\`\`typescript
import { ${toPascalCase(config.name)} } from '@/features/${config.name}';

function MyComponent() {
  return <${toPascalCase(config.name)} />;
}
\`\`\`

${config.hasAPI ? `### API Usage

\`\`\`typescript
// GET /api/${config.name}
const response = await fetch('/api/${config.name}');
const data = await response.json();

// POST /api/${config.name}
const response = await fetch('/api/${config.name}', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ name: 'Example' })
});
\`\`\`
` : ''}

## Configuration

\`\`\`json
{
  "enabled": true,
  "settings": {
    "option1": "value1",
    "option2": "value2"
  }
}
\`\`\`

## Development

### Running Tests

\`\`\`bash
npm run test features/${config.name}
\`\`\`

### Building

\`\`\`bash
npm run build
\`\`\`

## Contributing

Please read the [Contributing Guide](../../CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This feature is part of the Slack Summary Scribe project and is licensed under the MIT License.
`;

  fs.writeFileSync(path.join(docsDir, `${config.name}.md`), content);
}

function generatePluginFile(featurePath, config) {
  const pascalName = toPascalCase(config.name);
  
  const content = `/**
 * ${pascalName} Plugin
 */

import { BasePlugin, PluginMetadata, PluginContext } from '@/lib/plugin-framework';
import { ${pascalName}Service } from './services';

export class ${pascalName}Plugin extends BasePlugin {
  metadata: PluginMetadata = {
    name: '${config.name}',
    version: '${config.version}',
    description: '${config.description}',
    author: '${config.author}',
    category: '${config.type}',
    dependencies: [],
    permissions: ['read', 'write'],
    tags: ['${config.type}', '${config.name}']
  };

  private service?: ${pascalName}Service;

  async onEnable(context: PluginContext): Promise<void> {
    await super.onEnable(context);
    
    this.service = new ${pascalName}Service(context.database);
    
    this.log('Plugin enabled successfully');
  }

  async onDisable(context: PluginContext): Promise<void> {
    this.service = undefined;
    this.log('Plugin disabled');
  }

  ${config.hasAPI ? `registerRoute() {
    return [
      {
        path: '/api/${config.name}',
        method: 'GET',
        handler: async (req, res) => {
          if (!this.service) {
            return res.status(503).json({ error: 'Service not available' });
          }
          
          const data = await this.service.getAll(req.user.id);
          res.json({ success: true, data });
        },
        permissions: ['read']
      },
      {
        path: '/api/${config.name}',
        method: 'POST',
        handler: async (req, res) => {
          if (!this.service) {
            return res.status(503).json({ error: 'Service not available' });
          }
          
          const data = await this.service.create(req.user.id, req.body);
          res.json({ success: true, data });
        },
        permissions: ['write']
      }
    ];
  }` : ''}

  ${config.hasUI ? `registerComponent() {
    return [
      {
        name: '${pascalName}',
        component: () => import('./components/${pascalName}'),
        slot: '${config.type}-features'
      }
    ];
  }` : ''}
}

export default new ${pascalName}Plugin();
`;

  fs.writeFileSync(path.join(featurePath, 'plugin.ts'), content);
}

async function updateExports(config) {
  console.log(chalk.blue('🔗 Updating exports...'));

  const featuresIndexPath = path.join(process.cwd(), 'features', 'index.ts');
  
  let content = '';
  if (fs.existsSync(featuresIndexPath)) {
    content = fs.readFileSync(featuresIndexPath, 'utf8');
  }

  const exportLine = `export * from './${config.name}';`;
  
  if (!content.includes(exportLine)) {
    content += `${exportLine}\n`;
    fs.writeFileSync(featuresIndexPath, content);
  }

  console.log(chalk.green('  ✅ Exports updated'));
}

async function registerPlugin(config) {
  console.log(chalk.blue('🔌 Registering plugin...'));

  const pluginRegistryPath = path.join(process.cwd(), 'lib', 'plugin-registry.ts');
  
  let content = '';
  if (fs.existsSync(pluginRegistryPath)) {
    content = fs.readFileSync(pluginRegistryPath, 'utf8');
  } else {
    content = `/**
 * Plugin Registry
 * 
 * Auto-generated plugin registrations
 */

import { PluginManager } from './plugin-framework';

export const plugins = [
];

export async function registerAllPlugins(manager: PluginManager) {
  for (const plugin of plugins) {
    await manager.registerPlugin(plugin.instance, plugin.config);
  }
}
`;
  }

  const importLine = `import ${config.name}Plugin from '@/features/${config.name}/plugin';`;
  const registrationLine = `  { instance: ${config.name}Plugin, config: { enabled: true } },`;

  if (!content.includes(importLine)) {
    // Add import at the top
    const lines = content.split('\n');
    const importIndex = lines.findIndex(line => line.includes('import { PluginManager }'));
    lines.splice(importIndex + 1, 0, importLine);
    
    // Add to plugins array
    const arrayIndex = lines.findIndex(line => line.includes('export const plugins = ['));
    lines.splice(arrayIndex + 1, 0, registrationLine);
    
    content = lines.join('\n');
    fs.writeFileSync(pluginRegistryPath, content);
  }

  console.log(chalk.green('  ✅ Plugin registered'));
}

function toPascalCase(str) {
  return str
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}

program.parse();
