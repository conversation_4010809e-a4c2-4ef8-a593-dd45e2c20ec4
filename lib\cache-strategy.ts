import { devLog } from '@/lib/console-cleaner';
/**
 * Advanced Caching Strategy for Slack Summary Scribe
 * 
 * Implements multi-layer caching with:
 * - Browser cache (localStorage/sessionStorage)
 * - Memory cache (in-memory)
 * - Service Worker cache (if available)
 * - CDN cache headers
 */

interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  maxSize: number; // Maximum cache size
  strategy: 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB';
  compression?: boolean;
  encryption?: boolean;
}

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
  size: number;
  key: string;
  compressed?: boolean;
  encrypted?: boolean;
}

class CacheManager {
  private memoryCache = new Map<string, CacheItem<any>>();
  private maxMemorySize = 50 * 1024 * 1024; // 50MB
  private currentMemorySize = 0;

  constructor() {
    // Clean up expired items periodically
    setInterval(() => this.cleanup(), 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Set item in cache with specified strategy
   */
  async set<T>(
    key: string, 
    data: T, 
    config: Partial<CacheConfig> = {}
  ): Promise<void> {
    const defaultConfig: CacheConfig = {
      ttl: 30 * 60 * 1000, // 30 minutes
      maxSize: 1024 * 1024, // 1MB
      strategy: 'memory',
      compression: false,
      encryption: false
    };

    const finalConfig = { ...defaultConfig, ...config };
    const timestamp = Date.now();
    const serializedData = JSON.stringify(data);
    const size = new Blob([serializedData]).size;

    // Check size limits
    if (size > finalConfig.maxSize) {
      console.warn(`Cache item ${key} exceeds max size (${size} > ${finalConfig.maxSize})`);
      return;
    }

    const cacheItem: CacheItem<T> = {
      data,
      timestamp,
      ttl: finalConfig.ttl,
      size,
      key,
      compressed: finalConfig.compression,
      encrypted: finalConfig.encryption
    };

    switch (finalConfig.strategy) {
      case 'memory':
        await this.setMemoryCache(key, cacheItem);
        break;
      case 'localStorage':
        await this.setLocalStorageCache(key, cacheItem);
        break;
      case 'sessionStorage':
        await this.setSessionStorageCache(key, cacheItem);
        break;
      case 'indexedDB':
        await this.setIndexedDBCache(key, cacheItem);
        break;
    }
  }

  /**
   * Get item from cache
   */
  async get<T>(key: string, strategy: CacheConfig['strategy'] = 'memory'): Promise<T | null> {
    let cacheItem: CacheItem<T> | null = null;

    switch (strategy) {
      case 'memory':
        cacheItem = this.getMemoryCache(key);
        break;
      case 'localStorage':
        cacheItem = this.getLocalStorageCache(key);
        break;
      case 'sessionStorage':
        cacheItem = this.getSessionStorageCache(key);
        break;
      case 'indexedDB':
        cacheItem = await this.getIndexedDBCache(key);
        break;
    }

    if (!cacheItem) return null;

    // Check if expired
    if (Date.now() - cacheItem.timestamp > cacheItem.ttl) {
      await this.delete(key, strategy);
      return null;
    }

    return cacheItem.data;
  }

  /**
   * Delete item from cache
   */
  async delete(key: string, strategy: CacheConfig['strategy'] = 'memory'): Promise<void> {
    switch (strategy) {
      case 'memory':
        const item = this.memoryCache.get(key);
        if (item) {
          this.currentMemorySize -= item.size;
          this.memoryCache.delete(key);
        }
        break;
      case 'localStorage':
        if (typeof window !== 'undefined') {
          localStorage.removeItem(`cache_${key}`);
        }
        break;
      case 'sessionStorage':
        if (typeof window !== 'undefined') {
          sessionStorage.removeItem(`cache_${key}`);
        }
        break;
      case 'indexedDB':
        // IndexedDB deletion would go here
        break;
    }
  }

  /**
   * Clear all cache
   */
  async clear(strategy?: CacheConfig['strategy']): Promise<void> {
    if (!strategy) {
      // Clear all strategies
      this.memoryCache.clear();
      this.currentMemorySize = 0;
      
      if (typeof window !== 'undefined') {
        // Clear localStorage cache items
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('cache_')) {
            localStorage.removeItem(key);
          }
        });
        
        // Clear sessionStorage cache items
        Object.keys(sessionStorage).forEach(key => {
          if (key.startsWith('cache_')) {
            sessionStorage.removeItem(key);
          }
        });
      }
      return;
    }

    switch (strategy) {
      case 'memory':
        this.memoryCache.clear();
        this.currentMemorySize = 0;
        break;
      case 'localStorage':
        if (typeof window !== 'undefined') {
          Object.keys(localStorage).forEach(key => {
            if (key.startsWith('cache_')) {
              localStorage.removeItem(key);
            }
          });
        }
        break;
      case 'sessionStorage':
        if (typeof window !== 'undefined') {
          Object.keys(sessionStorage).forEach(key => {
            if (key.startsWith('cache_')) {
              sessionStorage.removeItem(key);
            }
          });
        }
        break;
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const memoryItems = this.memoryCache.size;
    const memorySize = this.currentMemorySize;
    
    let localStorageItems = 0;
    let sessionStorageItems = 0;
    
    if (typeof window !== 'undefined') {
      localStorageItems = Object.keys(localStorage).filter(k => k.startsWith('cache_')).length;
      sessionStorageItems = Object.keys(sessionStorage).filter(k => k.startsWith('cache_')).length;
    }

    return {
      memory: {
        items: memoryItems,
        size: memorySize,
        maxSize: this.maxMemorySize,
        utilization: (memorySize / this.maxMemorySize) * 100
      },
      localStorage: {
        items: localStorageItems
      },
      sessionStorage: {
        items: sessionStorageItems
      }
    };
  }

  // Private methods for different cache strategies
  private async setMemoryCache<T>(key: string, item: CacheItem<T>): Promise<void> {
    // Check if we need to evict items
    while (this.currentMemorySize + item.size > this.maxMemorySize && this.memoryCache.size > 0) {
      this.evictLRU();
    }

    this.memoryCache.set(key, item);
    this.currentMemorySize += item.size;
  }

  private getMemoryCache<T>(key: string): CacheItem<T> | null {
    return this.memoryCache.get(key) || null;
  }

  private setLocalStorageCache<T>(key: string, item: CacheItem<T>): void {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(`cache_${key}`, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to set localStorage cache:', error);
    }
  }

  private getLocalStorageCache<T>(key: string): CacheItem<T> | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const item = localStorage.getItem(`cache_${key}`);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.warn('Failed to get localStorage cache:', error);
      return null;
    }
  }

  private setSessionStorageCache<T>(key: string, item: CacheItem<T>): void {
    if (typeof window === 'undefined') return;
    
    try {
      sessionStorage.setItem(`cache_${key}`, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to set sessionStorage cache:', error);
    }
  }

  private getSessionStorageCache<T>(key: string): CacheItem<T> | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const item = sessionStorage.getItem(`cache_${key}`);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.warn('Failed to get sessionStorage cache:', error);
      return null;
    }
  }

  private async setIndexedDBCache<T>(key: string, item: CacheItem<T>): Promise<void> {
    // IndexedDB implementation would go here
    console.warn('IndexedDB cache not implemented yet');
  }

  private async getIndexedDBCache<T>(key: string): Promise<CacheItem<T> | null> {
    // IndexedDB implementation would go here
    console.warn('IndexedDB cache not implemented yet');
    return null;
  }

  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, item] of this.memoryCache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      const item = this.memoryCache.get(oldestKey);
      if (item) {
        this.currentMemorySize -= item.size;
        this.memoryCache.delete(oldestKey);
      }
    }
  }

  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    // Clean memory cache
    for (const [key, item] of this.memoryCache.entries()) {
      if (now - item.timestamp > item.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      const item = this.memoryCache.get(key);
      if (item) {
        this.currentMemorySize -= item.size;
        this.memoryCache.delete(key);
      }
    });
  devLog.log(`Cache cleanup: removed ${expiredKeys.length} expired items`);
  }
}

// Singleton instance
export const cacheManager = new CacheManager();

// Convenience functions for common cache operations
export const cache = {
  // API response caching
  async setApiResponse<T>(endpoint: string, data: T, ttl = 5 * 60 * 1000): Promise<void> {
    await cacheManager.set(`api_${endpoint}`, data, { ttl, strategy: 'memory' });
  },

  async getApiResponse<T>(endpoint: string): Promise<T | null> {
    return await cacheManager.get<T>(`api_${endpoint}`, 'memory');
  },

  // User data caching
  async setUserData<T>(userId: string, data: T, ttl = 30 * 60 * 1000): Promise<void> {
    await cacheManager.set(`user_${userId}`, data, { ttl, strategy: 'localStorage' });
  },

  async getUserData<T>(userId: string): Promise<T | null> {
    return await cacheManager.get<T>(`user_${userId}`, 'localStorage');
  },

  // Summary caching
  async setSummary<T>(summaryId: string, data: T, ttl = 60 * 60 * 1000): Promise<void> {
    await cacheManager.set(`summary_${summaryId}`, data, { ttl, strategy: 'localStorage' });
  },

  async getSummary<T>(summaryId: string): Promise<T | null> {
    return await cacheManager.get<T>(`summary_${summaryId}`, 'localStorage');
  },

  // Session data caching
  async setSessionData<T>(key: string, data: T): Promise<void> {
    await cacheManager.set(`session_${key}`, data, { 
      ttl: 24 * 60 * 60 * 1000, // 24 hours
      strategy: 'sessionStorage' 
    });
  },

  async getSessionData<T>(key: string): Promise<T | null> {
    return await cacheManager.get<T>(`session_${key}`, 'sessionStorage');
  },

  // Clear all caches
  async clearAll(): Promise<void> {
    await cacheManager.clear();
  },

  // Get cache statistics
  getStats() {
    return cacheManager.getStats();
  }
};

export default cacheManager;
