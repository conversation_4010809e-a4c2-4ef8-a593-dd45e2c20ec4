import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { analytics } from '@/lib/posthog.client';

export async function POST(request: NextRequest) {
  try {
  devLog.log('📄 PDF Parse API called');

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'Invalid file type. Expected PDF.' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
  devLog.log(`📄 Parsing PDF: ${file.name} (${buffer.length} bytes)`);

    // Parse PDF with dynamic import
    const pdf = await import('pdf-parse');
    const data = await pdf.default(buffer);
    
    if (!data.text || data.text.trim().length === 0) {
      return NextResponse.json(
        { error: 'No text content found in PDF' },
        { status: 400 }
      );
    }
  devLog.log(`📄 PDF parsed successfully: ${data.text.length} characters`);

    // Track analytics
    if (typeof window !== 'undefined') {
      analytics.track('pdf_parsed', {
        file_name: file.name,
        file_size: file.size,
        text_length: data.text.length,
        pages: data.numpages
      });
    }

    return NextResponse.json({
      success: true,
      content: data.text,
      metadata: {
        pages: data.numpages,
        info: data.info,
        textLength: data.text.length
      }
    });

  } catch (error) {
    console.error('PDF parsing error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to parse PDF',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
