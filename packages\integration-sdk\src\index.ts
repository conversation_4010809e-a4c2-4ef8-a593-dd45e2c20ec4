/**
 * Integration SDK - Universal SaaS Integration Library
 * 
 * Provides plug-and-play OAuth authentication and export functionality
 * for popular SaaS services with consistent interfaces.
 */

export * from './types';
export * from './errors';
export * from './providers';
export * from './storage';
export * from './rate-limiter';

import { EventEmitter } from 'events';
import { 
  IntegrationConfig,
  ExportRequest,
  ExportResult,
  AuthRequest,
  AuthResult,
  BatchExportRequest,
  BatchExportResult,
  TokenStatus,
  UsageStats,
  WebhookRequest,
  WebhookResult,
  IntegrationProvider
} from './types';
import { TokenStorage } from './storage';
import { RateLimiter } from './rate-limiter';
import { ProviderRegistry } from './providers/registry';
import { OAuthManager } from './oauth/manager';
import { ExportEngine } from './export/engine';
import { WebhookHandler } from './webhooks/handler';
import { IntegrationError, AuthenticationError } from './errors';

export class IntegrationSDK extends EventEmitter {
  private config: IntegrationConfig;
  private tokenStorage: TokenStorage;
  private rateLimiter?: RateLimiter;
  private providerRegistry: ProviderRegistry;
  private oauthManager: OAuthManager;
  private exportEngine: ExportEngine;
  private webhookHandler: WebhookHandler;

  constructor(config: IntegrationConfig) {
    super();
    
    this.config = config;
    this.tokenStorage = config.tokenStorage;
    this.rateLimiter = config.rateLimiter;
    
    // Initialize core components
    this.providerRegistry = new ProviderRegistry(config.providers);
    this.oauthManager = new OAuthManager(this.providerRegistry, this.tokenStorage);
    this.exportEngine = new ExportEngine(this.providerRegistry, this.tokenStorage, this.rateLimiter);
    this.webhookHandler = new WebhookHandler(this.providerRegistry);
    
    // Set up event forwarding
    this.setupEventForwarding();
  }

  /**
   * Generate OAuth authorization URL
   */
  async generateAuthUrl(request: AuthRequest): Promise<{ authUrl: string; state: string }> {
    try {
      const result = await this.oauthManager.generateAuthUrl(request);
      
      this.emit('auth.url_generated', {
        provider: request.provider,
        userId: request.userId,
        organizationId: request.organizationId,
        timestamp: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      this.emit('auth.error', {
        provider: request.provider,
        userId: request.userId,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(request: {
    provider: IntegrationProvider;
    code: string;
    state: string;
    redirectUri: string;
  }): Promise<AuthResult> {
    try {
      const result = await this.oauthManager.exchangeCodeForToken(request);
      
      this.emit('auth.success', {
        provider: request.provider,
        userId: result.metadata?.userId,
        organizationId: result.metadata?.organizationId,
        timestamp: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      this.emit('auth.failure', {
        provider: request.provider,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Export content to integrated service
   */
  async export(request: ExportRequest): Promise<ExportResult> {
    const startTime = Date.now();
    
    try {
      // Check rate limits
      if (this.rateLimiter) {
        await this.rateLimiter.checkLimit(
          `export:${request.userId}:${request.provider}`,
          this.getProviderRateLimit(request.provider)
        );
      }
      
      const result = await this.exportEngine.export(request);
      
      const duration = Date.now() - startTime;
      
      this.emit('export.success', {
        provider: request.provider,
        userId: request.userId,
        organizationId: request.organizationId,
        format: request.payload.format,
        destination: request.destination,
        duration,
        timestamp: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.emit('export.failure', {
        provider: request.provider,
        userId: request.userId,
        organizationId: request.organizationId,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
        timestamp: new Date().toISOString()
      });
      
      throw error;
    }
  }

  /**
   * Batch export to multiple services
   */
  async batchExport(request: BatchExportRequest): Promise<BatchExportResult> {
    const startTime = Date.now();
    const results: ExportResult[] = [];
    let successCount = 0;
    let failureCount = 0;

    for (const exportRequest of request.exports) {
      try {
        const result = await this.export({
          ...exportRequest,
          userId: request.userId,
          organizationId: request.organizationId
        });
        
        results.push(result);
        if (result.success) successCount++;
        else failureCount++;
      } catch (error) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          exportedAt: new Date().toISOString(),
          format: exportRequest.payload.format
        });
        failureCount++;
      }
    }

    const batchResult: BatchExportResult = {
      batchId: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      totalItems: request.exports.length,
      successfulItems: successCount,
      failedItems: failureCount,
      results,
      startedAt: new Date(startTime).toISOString(),
      completedAt: new Date().toISOString(),
      status: failureCount === 0 ? 'completed' : 'partial_failure'
    };

    this.emit('batch_export.completed', {
      userId: request.userId,
      organizationId: request.organizationId,
      batchId: batchResult.batchId,
      totalItems: batchResult.totalItems,
      successfulItems: batchResult.successfulItems,
      failedItems: batchResult.failedItems,
      duration: Date.now() - startTime,
      timestamp: new Date().toISOString()
    });

    return batchResult;
  }

  /**
   * Get token status for user and provider
   */
  async getTokenStatus(request: {
    userId: string;
    organizationId: string;
    provider: IntegrationProvider;
  }): Promise<TokenStatus> {
    return await this.oauthManager.getTokenStatus(request);
  }

  /**
   * Refresh expired token
   */
  async refreshToken(request: {
    userId: string;
    organizationId: string;
    provider: IntegrationProvider;
  }): Promise<AuthResult> {
    try {
      const result = await this.oauthManager.refreshToken(request);
      
      this.emit('token.refreshed', {
        provider: request.provider,
        userId: request.userId,
        organizationId: request.organizationId,
        timestamp: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      this.emit('token.refresh_failed', {
        provider: request.provider,
        userId: request.userId,
        organizationId: request.organizationId,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Revoke token
   */
  async revokeToken(request: {
    userId: string;
    organizationId: string;
    provider: IntegrationProvider;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      const result = await this.oauthManager.revokeToken(request);
      
      this.emit('token.revoked', {
        provider: request.provider,
        userId: request.userId,
        organizationId: request.organizationId,
        timestamp: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      this.emit('token.revoke_failed', {
        provider: request.provider,
        userId: request.userId,
        organizationId: request.organizationId,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Handle webhook from integrated service
   */
  async handleWebhook(request: WebhookRequest): Promise<WebhookResult> {
    try {
      const result = await this.webhookHandler.handle(request);
      
      this.emit('webhook.received', {
        provider: request.provider,
        eventType: result.eventType,
        processed: result.processed,
        timestamp: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      this.emit('webhook.error', {
        provider: request.provider,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Get usage statistics
   */
  async getUsageStats(request: {
    userId?: string;
    organizationId?: string;
    provider?: IntegrationProvider;
    dateRange: { start: string; end: string };
  }): Promise<UsageStats> {
    // This would typically query your analytics database
    // For now, return mock data structure
    return {
      totalExports: 0,
      successfulExports: 0,
      failedExports: 0,
      byProvider: {},
      byFormat: {},
      averageResponseTime: 0,
      dateRange: request.dateRange
    };
  }

  /**
   * Register custom provider
   */
  registerProvider(provider: any): void {
    this.providerRegistry.register(provider);
  }

  /**
   * Get list of available providers
   */
  getAvailableProviders(): IntegrationProvider[] {
    return this.providerRegistry.getAvailableProviders();
  }

  /**
   * Get provider information
   */
  getProviderInfo(provider: IntegrationProvider) {
    return this.providerRegistry.getProviderInfo(provider);
  }

  /**
   * Test connection to provider
   */
  async testConnection(request: {
    userId: string;
    organizationId: string;
    provider: IntegrationProvider;
  }): Promise<{ success: boolean; responseTime: number; error?: string }> {
    const startTime = Date.now();
    
    try {
      const provider = this.providerRegistry.getProvider(request.provider);
      if (!provider.testConnection) {
        throw new IntegrationError(
          'Provider does not support connection testing',
          'NOT_SUPPORTED',
          request.provider
        );
      }
      
      const result = await provider.testConnection();
      const responseTime = Date.now() - startTime;
      
      this.emit('connection.tested', {
        provider: request.provider,
        userId: request.userId,
        organizationId: request.organizationId,
        success: result.success,
        responseTime,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: result.success,
        responseTime,
        error: result.error
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      this.emit('connection.test_failed', {
        provider: request.provider,
        userId: request.userId,
        organizationId: request.organizationId,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: false,
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Cleanup expired tokens
   */
  async cleanupExpiredTokens(olderThan?: Date): Promise<number> {
    const cutoffDate = olderThan || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days
    return await this.tokenStorage.cleanup(cutoffDate);
  }

  // Private methods

  private setupEventForwarding(): void {
    // Forward events from internal components
    this.oauthManager.on('*', (eventName: string, data: any) => {
      this.emit(eventName, data);
    });
    
    this.exportEngine.on('*', (eventName: string, data: any) => {
      this.emit(eventName, data);
    });
    
    this.webhookHandler.on('*', (eventName: string, data: any) => {
      this.emit(eventName, data);
    });
  }

  private getProviderRateLimit(provider: IntegrationProvider): number {
    const rateLimits: Record<IntegrationProvider, number> = {
      'notion': 3, // 3 requests per second
      'google-drive': 10,
      'dropbox': 5,
      'slack': 1,
      'hubspot': 10,
      'salesforce': 5,
      'airtable': 5,
      'trello': 10,
      'asana': 15,
      'monday': 10,
      'clickup': 10
    };
    
    return rateLimits[provider] || 5; // Default 5 requests per second
  }
}

// Export default instance factory
export function createIntegrationSDK(config: IntegrationConfig): IntegrationSDK {
  return new IntegrationSDK(config);
}
