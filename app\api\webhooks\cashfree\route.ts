import { devLog } from '@/lib/console-cleaner';
/**
 * Cashfree Webhook Handler
 * Process Cashfree payment webhooks
 */

import { NextRequest, NextResponse } from 'next/server';
import { handleWebhookEvent } from '@/lib/dual-payment-gateway';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * POST /api/webhooks/cashfree
 * Handle Cashfree webhook events
 */
export async function POST(request: NextRequest) {
  try {
    const rawBody = await request.text();
    const signature = request.headers.get('x-webhook-signature') || '';
  devLog.log('Received Cashfree webhook');

    const result = await handleWebhookEvent('cashfree', rawBody, signature);

    if (!result.success) {
      console.error('Cashfree webhook processing failed:', result.error);
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }
  devLog.log('Cashfree webhook processed successfully:', result.event?.event_type);

    return NextResponse.json({ 
      success: true,
      event_type: result.event?.event_type,
    });

  } catch (error) {
    console.error('Cashfree webhook error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/webhooks/cashfree
 * Health check endpoint
 */
export async function GET() {
  return NextResponse.json({ 
    status: 'ok',
    webhook: 'cashfree',
    timestamp: new Date().toISOString(),
  });
}
