/**
 * Pricing Configuration
 * 
 * Centralized pricing plans and Stripe configuration for SaaS billing
 */

export interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  stripePriceId: string;
  features: string[];
  limits: {
    workspaces: number;
    summariesPerMonth: number;
    aiModel: string;
    exportFormats: string[];
    support: string;
  };
  popular?: boolean;
  cta: string;
}

export const PRICING_PLANS: PricingPlan[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Perfect for individuals getting started',
    price: 0,
    currency: 'USD',
    interval: 'month',
    stripePriceId: '', // No Stripe price for free plan
    features: [
      '1 Slack workspace',
      'Basic AI summaries',
      'Email support',
      'Standard export formats',
      'Community access'
    ],
    limits: {
      workspaces: 1,
      summariesPerMonth: 50,
      aiModel: 'DeepSeek R1',
      exportFormats: ['PDF', 'TXT'],
      support: 'Email'
    },
    cta: 'Get Started Free'
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'For growing teams and power users',
    price: 29,
    currency: 'USD',
    interval: 'month',
    stripePriceId: process.env.STRIPE_PRO_PRICE_ID || 'price_pro_monthly',
    features: [
      '3 Slack workspaces',
      'Advanced AI summaries with GPT-4o-mini',
      'Smart tagging & categorization',
      'Priority support',
      'All export formats',
      'Slack auto-posting',
      'Basic analytics'
    ],
    limits: {
      workspaces: 3,
      summariesPerMonth: 500,
      aiModel: 'GPT-4o-mini',
      exportFormats: ['PDF', 'TXT', 'DOCX', 'MD'],
      support: 'Priority Email'
    },
    popular: true,
    cta: 'Start Pro Trial'
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For large teams with advanced needs',
    price: 99,
    currency: 'USD',
    interval: 'month',
    stripePriceId: process.env.STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise_monthly',
    features: [
      'Unlimited Slack workspaces',
      'Premium AI with GPT-4o & Claude',
      'Advanced CRM integrations',
      'Custom AI models',
      'Team management',
      '24/7 priority support',
      'Advanced analytics & reporting',
      'SSO & enterprise security',
      'Custom integrations'
    ],
    limits: {
      workspaces: -1, // Unlimited
      summariesPerMonth: -1, // Unlimited
      aiModel: 'GPT-4o',
      exportFormats: ['PDF', 'TXT', 'DOCX', 'MD', 'JSON', 'CSV'],
      support: '24/7 Phone & Chat'
    },
    cta: 'Contact Sales'
  }
];

// Annual pricing (20% discount)
export const ANNUAL_PRICING_PLANS: PricingPlan[] = PRICING_PLANS.map(plan => ({
  ...plan,
  interval: 'year' as const,
  price: plan.price === 0 ? 0 : Math.round(plan.price * 12 * 0.8), // 20% discount
  stripePriceId: plan.stripePriceId.replace('monthly', 'yearly'),
  description: plan.description + ' (Save 20% annually)'
}));

// Helper functions
export function getPlanById(planId: string, annual = false): PricingPlan | undefined {
  const plans = annual ? ANNUAL_PRICING_PLANS : PRICING_PLANS;
  return plans.find(plan => plan.id === planId);
}

export function getPlanByStripeId(stripePriceId: string): PricingPlan | undefined {
  const allPlans = [...PRICING_PLANS, ...ANNUAL_PRICING_PLANS];
  return allPlans.find(plan => plan.stripePriceId === stripePriceId);
}

export function formatPrice(price: number, currency = 'USD'): string {
  if (price === 0) return 'Free';
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
}

export function getFeatureLimit(planId: string, feature: keyof PricingPlan['limits']): number | string {
  const plan = getPlanById(planId);
  if (!plan) return 0;

  const limit = plan.limits[feature];
  if (typeof limit === 'number') {
    return limit === -1 ? 'Unlimited' : limit;
  }
  if (Array.isArray(limit)) {
    return limit.join(', ');
  }
  return limit;
}

export function canAccessFeature(userPlan: string, requiredPlan: string): boolean {
  const planHierarchy = ['free', 'pro', 'enterprise'];
  const userIndex = planHierarchy.indexOf(userPlan);
  const requiredIndex = planHierarchy.indexOf(requiredPlan);
  
  return userIndex >= requiredIndex;
}

// Stripe configuration
export const STRIPE_CONFIG = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
  secretKey: process.env.STRIPE_SECRET_KEY!,
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
  successUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/billing/success`,
  cancelUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/pricing`,
  portalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/billing`,
};

// Subscription status types
export type SubscriptionStatus = 
  | 'active'
  | 'canceled'
  | 'incomplete'
  | 'incomplete_expired'
  | 'past_due'
  | 'trialing'
  | 'unpaid';

export interface UserSubscription {
  id: string;
  userId: string;
  stripeCustomerId: string;
  stripeSubscriptionId: string;
  stripePriceId: string;
  status: SubscriptionStatus;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  planId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Trial configuration
export const TRIAL_CONFIG = {
  durationDays: 14,
  allowedPlans: ['pro', 'enterprise'],
  features: {
    pro: PRICING_PLANS.find(p => p.id === 'pro')?.features || [],
    enterprise: PRICING_PLANS.find(p => p.id === 'enterprise')?.features || []
  }
};

// Usage tracking
export interface UsageMetrics {
  userId: string;
  planId: string;
  period: string; // YYYY-MM format
  summariesGenerated: number;
  workspacesConnected: number;
  exportsCreated: number;
  apiCalls: number;
  lastUpdated: Date;
}

export function isUsageLimitReached(
  usage: UsageMetrics, 
  planId: string, 
  metric: keyof Pick<UsageMetrics, 'summariesGenerated' | 'workspacesConnected'>
): boolean {
  const plan = getPlanById(planId);
  if (!plan) return true;
  
  const limit = plan.limits[metric === 'summariesGenerated' ? 'summariesPerMonth' : 'workspaces'];
  if (limit === -1) return false; // Unlimited
  
  return usage[metric] >= limit;
}

// Billing intervals
export const BILLING_INTERVALS = {
  month: {
    label: 'Monthly',
    description: 'Billed monthly',
    multiplier: 1
  },
  year: {
    label: 'Annual',
    description: 'Billed annually (Save 20%)',
    multiplier: 12 * 0.8
  }
} as const;
