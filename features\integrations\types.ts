/**
 * Unified Integration System Types
 * 
 * Consistent interfaces for all third-party service integrations
 */

// Base integration interface that all services must implement
export interface ExportService {
  name: string;
  displayName: string;
  description: string;
  icon: string;
  category: 'productivity' | 'storage' | 'crm' | 'communication' | 'analytics';
  authRequired: boolean;
  configOptions: ExportOptions;
  
  // Core methods
  authenticate(config: AuthConfig): Promise<AuthResult>;
  export(payload: SummaryPayload): Promise<ExportResult>;
  validateConfig(config: ServiceConfig): Promise<ValidationResult>;
  getStatus(): Promise<ServiceStatus>;
  
  // Optional methods
  listDestinations?(): Promise<Destination[]>;
  createDestination?(config: DestinationConfig): Promise<Destination>;
  testConnection?(): Promise<ConnectionTest>;
}

// Authentication interfaces
export interface AuthConfig {
  type: 'oauth2' | 'api_key' | 'basic' | 'custom';
  credentials: Record<string, string>;
  scopes?: string[];
  redirectUri?: string;
  state?: string;
}

export interface AuthResult {
  success: boolean;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: string;
  tokenType?: string;
  scopes?: string[];
  error?: string;
  metadata?: Record<string, any>;
}

// Export payload and result interfaces
export interface SummaryPayload {
  id: string;
  title: string;
  content: string;
  format: 'markdown' | 'html' | 'plain' | 'rich';
  metadata: {
    createdAt: string;
    updatedAt: string;
    tags: string[];
    sourceType: string;
    wordCount: number;
    author: {
      id: string;
      name: string;
      email: string;
    };
    organization: {
      id: string;
      name: string;
    };
  };
  attachments?: Attachment[];
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  mimeType: string;
}

export interface ExportResult {
  success: boolean;
  destinationId?: string;
  destinationUrl?: string;
  exportedAt: string;
  format: string;
  size?: number;
  error?: string;
  metadata?: Record<string, any>;
}

// Configuration interfaces
export interface ExportOptions {
  formats: ExportFormat[];
  destinations: DestinationType[];
  customFields: CustomField[];
  batchSupport: boolean;
  maxFileSize?: number;
  rateLimits?: RateLimit;
}

export interface ExportFormat {
  id: string;
  name: string;
  description: string;
  mimeType: string;
  extension: string;
  supportsAttachments: boolean;
}

export interface DestinationType {
  id: string;
  name: string;
  description: string;
  icon: string;
  requiresSelection: boolean;
  selectionType?: 'folder' | 'database' | 'channel' | 'list';
}

export interface CustomField {
  id: string;
  name: string;
  type: 'text' | 'select' | 'boolean' | 'number' | 'date';
  required: boolean;
  defaultValue?: any;
  options?: string[];
  description?: string;
}

export interface RateLimit {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  burstLimit?: number;
}

// Service configuration and status
export interface ServiceConfig {
  enabled: boolean;
  authConfig: AuthConfig;
  defaultDestination?: string;
  customSettings: Record<string, any>;
  webhookUrl?: string;
  retryConfig: RetryConfig;
}

export interface RetryConfig {
  maxRetries: number;
  backoffMs: number;
  maxBackoffMs: number;
  retryableErrors: string[];
}

export interface ServiceStatus {
  connected: boolean;
  lastSync?: string;
  lastError?: string;
  rateLimitRemaining?: number;
  rateLimitReset?: string;
  health: 'healthy' | 'degraded' | 'unhealthy';
  metadata?: Record<string, any>;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}

// Destination management
export interface Destination {
  id: string;
  name: string;
  type: string;
  path: string;
  metadata: Record<string, any>;
  createdAt: string;
  lastUsed?: string;
}

export interface DestinationConfig {
  name: string;
  type: string;
  parentId?: string;
  metadata: Record<string, any>;
}

export interface ConnectionTest {
  success: boolean;
  responseTime: number;
  error?: string;
  details?: Record<string, any>;
}

// OAuth token management
export interface OAuthToken {
  id: string;
  userId: string;
  organizationId: string;
  provider: string;
  accessToken: string;
  refreshToken?: string;
  tokenType: string;
  expiresAt?: string;
  scopes: string[];
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface TokenRefreshResult {
  success: boolean;
  newAccessToken?: string;
  newRefreshToken?: string;
  expiresAt?: string;
  error?: string;
}

// Integration analytics
export interface IntegrationUsage {
  integrationId: string;
  userId: string;
  organizationId: string;
  action: 'export' | 'sync' | 'auth' | 'test';
  success: boolean;
  duration: number;
  bytesTransferred?: number;
  error?: string;
  metadata: Record<string, any>;
  timestamp: string;
}

export interface IntegrationMetrics {
  totalExports: number;
  successRate: number;
  averageResponseTime: number;
  popularFormats: Record<string, number>;
  popularDestinations: Record<string, number>;
  errorsByType: Record<string, number>;
  usageByDay: Array<{ date: string; count: number }>;
}

// Webhook interfaces
export interface WebhookEvent {
  id: string;
  type: string;
  provider: string;
  data: Record<string, any>;
  timestamp: string;
  signature?: string;
}

export interface WebhookHandler {
  provider: string;
  eventTypes: string[];
  handle(event: WebhookEvent): Promise<WebhookHandleResult>;
  validateSignature(payload: string, signature: string, secret: string): boolean;
}

export interface WebhookHandleResult {
  success: boolean;
  processed: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

// Batch operations
export interface BatchExportRequest {
  summaryIds: string[];
  service: string;
  destination: string;
  format: string;
  options: Record<string, any>;
}

export interface BatchExportResult {
  batchId: string;
  totalItems: number;
  successfulItems: number;
  failedItems: number;
  results: ExportResult[];
  startedAt: string;
  completedAt?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

// Service-specific types
export interface NotionConfig extends ServiceConfig {
  defaultDatabase?: string;
  pageTemplate?: string;
  propertyMappings: Record<string, string>;
}

export interface GoogleDriveConfig extends ServiceConfig {
  defaultFolder?: string;
  shareSettings: {
    makePublic: boolean;
    allowComments: boolean;
    allowEditing: boolean;
  };
}

export interface SlackConfig extends ServiceConfig {
  defaultChannel?: string;
  messageTemplate?: string;
  threadReplies: boolean;
  mentionUsers: string[];
}

export interface HubSpotConfig extends ServiceConfig {
  defaultPipeline?: string;
  contactMapping: Record<string, string>;
  dealProperties: Record<string, any>;
}

export interface SalesforceConfig extends ServiceConfig {
  defaultObject?: string;
  fieldMappings: Record<string, string>;
  recordType?: string;
}

// Error types
export class IntegrationError extends Error {
  constructor(
    message: string,
    public code: string,
    public provider: string,
    public retryable: boolean = false,
    public metadata?: Record<string, any>
  ) {
    super(message);
    this.name = 'IntegrationError';
  }
}

export class AuthenticationError extends IntegrationError {
  constructor(provider: string, message: string = 'Authentication failed') {
    super(message, 'AUTH_FAILED', provider, false);
    this.name = 'AuthenticationError';
  }
}

export class RateLimitError extends IntegrationError {
  constructor(
    provider: string, 
    resetTime?: string,
    message: string = 'Rate limit exceeded'
  ) {
    super(message, 'RATE_LIMIT', provider, true, { resetTime });
    this.name = 'RateLimitError';
  }
}

export class QuotaExceededError extends IntegrationError {
  constructor(
    provider: string,
    quotaType: string,
    message: string = 'Quota exceeded'
  ) {
    super(message, 'QUOTA_EXCEEDED', provider, false, { quotaType });
    this.name = 'QuotaExceededError';
  }
}

export class NetworkError extends IntegrationError {
  constructor(
    provider: string,
    message: string = 'Network error'
  ) {
    super(message, 'NETWORK_ERROR', provider, true);
    this.name = 'NetworkError';
  }
}

// Utility types
export type IntegrationProvider = 
  | 'notion'
  | 'google-drive'
  | 'dropbox'
  | 'slack'
  | 'hubspot'
  | 'salesforce'
  | 'airtable'
  | 'trello'
  | 'asana'
  | 'monday'
  | 'clickup';

export type ExportStatus = 
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled';

export type AuthStatus = 
  | 'connected'
  | 'expired'
  | 'revoked'
  | 'error';

// Integration registry
export interface IntegrationRegistry {
  [key: string]: ExportService;
}

// Factory pattern for creating integrations
export interface IntegrationFactory {
  create(provider: IntegrationProvider, config: ServiceConfig): ExportService;
  getAvailableProviders(): IntegrationProvider[];
  getProviderInfo(provider: IntegrationProvider): ProviderInfo;
}

export interface ProviderInfo {
  name: string;
  displayName: string;
  description: string;
  icon: string;
  category: string;
  website: string;
  documentation: string;
  supportedFeatures: string[];
  pricingModel?: string;
}
