#!/usr/bin/env node

/**
 * Comprehensive Validation Script for All Fixes
 * 
 * This script validates that all CSP, Slack OAuth, and development environment
 * fixes have been properly applied and are working correctly.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function logHeader(text) {
  console.log(`\n${colors.cyan}${colors.bright}${text}${colors.reset}`);
  console.log('='.repeat(text.length));
}

function logSuccess(text) {
  console.log(`${colors.green}✅ ${text}${colors.reset}`);
}

function logError(text) {
  console.log(`${colors.red}❌ ${text}${colors.reset}`);
}

function logWarning(text) {
  console.log(`${colors.yellow}⚠️  ${text}${colors.reset}`);
}

function logInfo(text) {
  console.log(`${colors.blue}ℹ️  ${text}${colors.reset}`);
}

// Load environment variables
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  
  if (!fs.existsSync(envPath)) {
    logError('.env.local file not found');
    return {};
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key] = valueParts.join('=');
      }
    }
  });
  
  return env;
}

// Validate CSP Configuration
function validateCSPConfiguration() {
  logHeader('🛡️  CSP CONFIGURATION VALIDATION');
  
  const configPath = path.join(process.cwd(), 'next.config.mjs');
  
  if (!fs.existsSync(configPath)) {
    logError('next.config.mjs not found');
    return false;
  }

  const configContent = fs.readFileSync(configPath, 'utf8');
  
  // Check for development mode CSP bypass
  if (configContent.includes('process.env.NODE_ENV === \'production\'')) {
    logSuccess('CSP only applies in production mode');
  } else {
    logError('CSP configuration missing production mode check');
    return false;
  }

  // Check for required CSP domains
  const requiredDomains = [
    '*.supabase.co',
    '*.clerk.com',
    'posthog.com',
    'stripe.com',
    'google.com',
    'recaptcha.net'
  ];

  let allDomainsFound = true;
  requiredDomains.forEach(domain => {
    if (configContent.includes(domain)) {
      logSuccess(`CSP includes ${domain}`);
    } else {
      logError(`CSP missing ${domain}`);
      allDomainsFound = false;
    }
  });

  // Check for MIME type headers
  if (configContent.includes('Content-Type')) {
    logSuccess('MIME type headers configured');
  } else {
    logWarning('MIME type headers may be missing');
  }

  return allDomainsFound;
}

// Validate Slack OAuth Configuration
function validateSlackOAuth(env) {
  logHeader('🔗 SLACK OAUTH VALIDATION');
  
  let valid = true;

  // Check required Slack environment variables
  const requiredSlackVars = [
    'NEXT_PUBLIC_SLACK_CLIENT_ID',
    'SLACK_CLIENT_ID',
    'SLACK_CLIENT_SECRET',
    'SLACK_SIGNING_SECRET'
  ];

  requiredSlackVars.forEach(varName => {
    if (env[varName]) {
      logSuccess(`${varName}: Configured`);
    } else {
      logError(`${varName}: Missing`);
      valid = false;
    }
  });

  // Check if client IDs match
  if (env.NEXT_PUBLIC_SLACK_CLIENT_ID === env.SLACK_CLIENT_ID) {
    logSuccess('Client IDs match between public and server configs');
  } else {
    logError('Client ID mismatch between NEXT_PUBLIC_SLACK_CLIENT_ID and SLACK_CLIENT_ID');
    valid = false;
  }

  // Check for test mode detection
  if (env.NEXT_PUBLIC_STRIPE_TEST_MODE) {
    logSuccess('Stripe test mode detection enabled');
  } else {
    logWarning('Stripe test mode detection not configured');
  }

  return valid;
}

// Validate Error Boundaries
function validateErrorBoundaries() {
  logHeader('🚨 ERROR BOUNDARY VALIDATION');
  
  const errorBoundaryPath = path.join(process.cwd(), 'components/error-boundaries/ChunkErrorBoundary.tsx');
  
  if (fs.existsSync(errorBoundaryPath)) {
    logSuccess('ChunkErrorBoundary component exists');
    
    const content = fs.readFileSync(errorBoundaryPath, 'utf8');
    
    if (content.includes('ChunkLoadError')) {
      logSuccess('Chunk load error detection implemented');
    } else {
      logError('Chunk load error detection missing');
      return false;
    }

    if (content.includes('window.location.reload')) {
      logSuccess('Automatic reload on chunk errors implemented');
    } else {
      logError('Automatic reload functionality missing');
      return false;
    }

    return true;
  } else {
    logError('ChunkErrorBoundary component not found');
    return false;
  }
}

// Validate Development Environment
function validateDevelopmentEnvironment() {
  logHeader('🔧 DEVELOPMENT ENVIRONMENT VALIDATION');
  
  // Check if we're in development mode
  const isDev = process.env.NODE_ENV !== 'production';
  
  if (isDev) {
    logSuccess('Running in development mode');
    logInfo('CSP headers should be disabled');
    logInfo('All external services should work without restrictions');
  } else {
    logWarning('Running in production mode');
    logInfo('CSP headers should be active');
  }

  // Check for required files
  const requiredFiles = [
    'next.config.mjs',
    '.env.local',
    'components/error-boundaries/ChunkErrorBoundary.tsx',
    'docs/SLACK_OAUTH_FIX.md'
  ];

  let allFilesExist = true;
  requiredFiles.forEach(file => {
    if (fs.existsSync(path.join(process.cwd(), file))) {
      logSuccess(`${file}: Found`);
    } else {
      logError(`${file}: Missing`);
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

// Main validation function
async function main() {
  logHeader('🚀 COMPREHENSIVE VALIDATION - ALL FIXES');
  
  const env = loadEnvFile();
  
  const results = {
    csp: validateCSPConfiguration(),
    slack: validateSlackOAuth(env),
    errorBoundaries: validateErrorBoundaries(),
    development: validateDevelopmentEnvironment()
  };
  
  logHeader('📊 VALIDATION SUMMARY');
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([category, success]) => {
    if (success) {
      logSuccess(`${category.toUpperCase()}: All checks passed`);
    } else {
      logError(`${category.toUpperCase()}: Issues found`);
    }
  });
  
  console.log(`\n${colors.bright}Result: ${passed}/${total} categories passed${colors.reset}`);
  
  if (passed === total) {
    logSuccess('🎉 All fixes have been successfully applied!');
    
    logHeader('✅ READY FOR TESTING');
    console.log('✅ CSP disabled in development, enabled in production');
    console.log('✅ Slack OAuth configured for public distribution');
    console.log('✅ Error boundaries implemented for chunk loading');
    console.log('✅ MIME types and module loading fixed');
    console.log('✅ Stripe test mode detection enabled');
    
    logHeader('🧪 NEXT STEPS');
    console.log('1. Enable Slack app public distribution at https://api.slack.com/apps');
    console.log('2. Add redirect URLs to Slack app settings');
    console.log('3. Test all integrations in development mode');
    console.log('4. Deploy to production and verify CSP is active');
    
    return true;
  } else {
    logError('❌ Some fixes need attention before proceeding');
    return false;
  }
}

if (require.main === module) {
  main()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      logError(`Validation failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { main };
