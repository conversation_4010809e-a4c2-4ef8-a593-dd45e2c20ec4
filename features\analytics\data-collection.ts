/**
 * Analytics Data Collection System
 * 
 * Handles event tracking, data collection, and metric aggregation
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';
import { 
  AnalyticsEvent, 
  UsageMetric, 
  BusinessMetric,
  EventType,
  AggregationType 
} from './types';

// Event queue for batch processing
let eventQueue: AnalyticsEvent[] = [];
const BATCH_SIZE = 100;
const FLUSH_INTERVAL = 30000; // 30 seconds

/**
 * Track an analytics event
 */
export async function trackEvent(event: Omit<AnalyticsEvent, 'id' | 'timestamp'>): Promise<void> {
  try {
    const analyticsEvent: AnalyticsEvent = {
      ...event,
      timestamp: new Date().toISOString()
    };

    // Add to queue for batch processing
    eventQueue.push(analyticsEvent);

    // Flush if batch size reached
    if (eventQueue.length >= BATCH_SIZE) {
      await flushEventQueue();
    }

    // Also send to PostHog if configured
    if (typeof window !== 'undefined' && (window as any).posthog) {
      (window as any).posthog.capture(event.eventName, {
        ...event.properties,
        organizationId: event.organizationId,
        userId: event.userId,
        eventType: event.eventType
      });
    }

  } catch (error) {
    console.error('Failed to track event:', error);
  }
}

/**
 * Track page view
 */
export async function trackPageView(
  path: string,
  userId?: string,
  organizationId?: string,
  additionalProperties: Record<string, any> = {}
): Promise<void> {
  await trackEvent({
    eventType: 'page_view',
    eventName: 'page_viewed',
    userId,
    organizationId,
    properties: {
      path,
      referrer: typeof window !== 'undefined' ? document.referrer : '',
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : '',
      ...additionalProperties
    }
  });
}

/**
 * Track feature usage
 */
export async function trackFeatureUsage(
  feature: string,
  action: string,
  userId?: string,
  organizationId?: string,
  properties: Record<string, any> = {}
): Promise<void> {
  await trackEvent({
    eventType: 'feature_usage',
    eventName: `${feature}_${action}`,
    userId,
    organizationId,
    properties: {
      feature,
      action,
      ...properties
    }
  });
}

/**
 * Track business event
 */
export async function trackBusinessEvent(
  eventName: string,
  userId?: string,
  organizationId?: string,
  properties: Record<string, any> = {}
): Promise<void> {
  await trackEvent({
    eventType: 'business_event',
    eventName,
    userId,
    organizationId,
    properties
  });
}

/**
 * Track system performance
 */
export async function trackPerformance(
  operation: string,
  duration: number,
  success: boolean,
  metadata: Record<string, any> = {}
): Promise<void> {
  await trackEvent({
    eventType: 'system_event',
    eventName: 'performance_metric',
    properties: {
      operation,
      duration,
      success,
      ...metadata
    }
  });
}

/**
 * Track error event
 */
export async function trackError(
  error: Error | string,
  context: string,
  userId?: string,
  organizationId?: string,
  additionalData: Record<string, any> = {}
): Promise<void> {
  const errorMessage = error instanceof Error ? error.message : error;
  const errorStack = error instanceof Error ? error.stack : undefined;

  await trackEvent({
    eventType: 'error_event',
    eventName: 'error_occurred',
    userId,
    organizationId,
    properties: {
      error: errorMessage,
      stack: errorStack,
      context,
      ...additionalData
    }
  });
}

/**
 * Flush event queue to database
 */
export async function flushEventQueue(): Promise<void> {
  if (eventQueue.length === 0) return;

  try {
    const supabase = await createSupabaseServerClient();
    const events = [...eventQueue];
    eventQueue = [];

    const { error } = await supabase
      .from('analytics_events')
      .insert(events.map(event => ({
        organization_id: event.organizationId,
        user_id: event.userId,
        event_type: event.eventType,
        event_name: event.eventName,
        properties: event.properties,
        session_id: event.sessionId,
        created_at: event.timestamp
      })));

    if (error) {
      console.error('Failed to flush events:', error);
      // Re-add events to queue for retry
      eventQueue.unshift(...events);
    }

  } catch (error) {
    console.error('Failed to flush event queue:', error);
  }
}

/**
 * Record usage metric
 */
export async function recordUsageMetric(metric: Omit<UsageMetric, 'id' | 'createdAt'>): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();

    await supabase
      .from('usage_metrics')
      .insert({
        organization_id: metric.organizationId,
        metric_type: metric.metricType,
        metric_name: metric.metricName,
        value: metric.value,
        dimensions: metric.dimensions,
        period_start: metric.periodStart,
        period_end: metric.periodEnd,
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('Failed to record usage metric:', error);
  }
}

/**
 * Record business metric
 */
export async function recordBusinessMetric(metric: Omit<BusinessMetric, 'id' | 'createdAt'>): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();

    await supabase
      .from('business_metrics')
      .insert({
        metric_name: metric.metricName,
        value: metric.value,
        metadata: metric.metadata,
        period_start: metric.periodStart,
        period_end: metric.periodEnd,
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('Failed to record business metric:', error);
  }
}

/**
 * Aggregate events into metrics
 */
export async function aggregateEvents(
  eventType: string,
  metricName: string,
  aggregationType: AggregationType,
  field: string,
  periodStart: string,
  periodEnd: string,
  organizationId?: string
): Promise<number> {
  try {
    const supabase = await createSupabaseServerClient();

    let query = supabase
      .from('analytics_events')
      .select(field)
      .eq('event_type', eventType)
      .gte('created_at', periodStart)
      .lte('created_at', periodEnd);

    if (organizationId) {
      query = query.eq('organization_id', organizationId);
    }

    const { data: events, error } = await query;

    if (error || !events) {
      return 0;
    }

    // Perform aggregation
    switch (aggregationType) {
      case 'count':
        return events.length;
      
      case 'sum':
        return events.reduce((sum: number, event: any) => {
          const value = getNestedValue(event, field);
          return sum + (typeof value === 'number' ? value : 0);
        }, 0);
      
      case 'avg':
        const values = events.map((event: any) => getNestedValue(event, field))
          .filter((value: any) => typeof value === 'number');
        return values.length > 0 ? values.reduce((sum: number, val: number) => sum + val, 0) / values.length : 0;
      
      case 'min':
        const minValues = events.map(event => getNestedValue(event, field))
          .filter(value => typeof value === 'number');
        return minValues.length > 0 ? Math.min(...minValues) : 0;
      
      case 'max':
        const maxValues = events.map(event => getNestedValue(event, field))
          .filter(value => typeof value === 'number');
        return maxValues.length > 0 ? Math.max(...maxValues) : 0;
      
      case 'unique':
        const uniqueValues = new Set(events.map(event => getNestedValue(event, field)));
        return uniqueValues.size;
      
      default:
        return 0;
    }

  } catch (error) {
    console.error('Failed to aggregate events:', error);
    return 0;
  }
}

/**
 * Get nested value from object using dot notation
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Calculate daily active users
 */
export async function calculateDailyActiveUsers(
  date: string,
  organizationId?: string
): Promise<number> {
  try {
    const supabase = await createSupabaseServerClient();
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    let query = supabase
      .from('analytics_events')
      .select('user_id')
      .gte('created_at', startOfDay.toISOString())
      .lte('created_at', endOfDay.toISOString())
      .not('user_id', 'is', null);

    if (organizationId) {
      query = query.eq('organization_id', organizationId);
    }

    const { data: events, error } = await query;

    if (error || !events) {
      return 0;
    }

    const uniqueUsers = new Set(events.map(event => event.user_id));
    return uniqueUsers.size;

  } catch (error) {
    console.error('Failed to calculate daily active users:', error);
    return 0;
  }
}

/**
 * Initialize analytics system
 */
export function initializeAnalytics(): void {
  // Set up periodic event queue flushing
  if (typeof window === 'undefined') {
    setInterval(flushEventQueue, FLUSH_INTERVAL);
  }

  // Flush events on page unload (browser only)
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
      flushEventQueue();
    });
  }
}

/**
 * Get analytics configuration
 */
export function getAnalyticsConfig() {
  return {
    enabled: process.env.ANALYTICS_ENABLED === 'true',
    retentionDays: parseInt(process.env.ANALYTICS_RETENTION_DAYS || '90'),
    realTimeEnabled: process.env.REAL_TIME_ANALYTICS === 'true',
    exportMaxRecords: parseInt(process.env.EXPORT_MAX_RECORDS || '10000'),
    cacheTtl: parseInt(process.env.CHART_CACHE_TTL || '300'),
    batchSize: BATCH_SIZE,
    flushInterval: FLUSH_INTERVAL
  };
}
