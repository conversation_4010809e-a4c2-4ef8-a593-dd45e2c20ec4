'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Crown, 
  Zap, 
  Sparkles, 
  Calendar, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { getCurrentUserClient } from '@/lib/user-management';
import { toast } from 'sonner';

interface SubscriptionData {
  subscription: {
    id: string;
    tier: 'FREE' | 'PRO' | 'ENTERPRISE';
    status: string;
    current_period_start: string;
    current_period_end: string;
    cancel_at_period_end: boolean;
    monthly_summary_limit: number;
    monthly_summaries_used: number;
    created_at: string;
    updated_at: string;
  };
  plan: {
    name: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    limits: {
      monthlySummaries: number;
      aiModels: string[];
    };
  };
  usage: {
    summaries_used: number;
    summaries_limit: number;
    usage_percentage: number;
    can_create_summary: boolean;
  };
}

export function SubscriptionStatus() {
  const [user, setUser] = useState<any>(null);
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { getCurrentUserClient } = await import('@/lib/user-management-client');
        const currentUser = await getCurrentUserClient();
        setUser(currentUser);
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    };

    fetchUser();
  }, []);

  useEffect(() => {
    if (user) {
      fetchSubscriptionData();
    }
  }, [user]);

  const fetchSubscriptionData = async () => {
    try {
      const response = await fetch('/api/subscription');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSubscriptionData(data);
        }
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscriptionAction = async (action: 'cancel' | 'reactivate') => {
    setActionLoading(action);
    
    try {
      const response = await fetch('/api/subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        await fetchSubscriptionData(); // Refresh data
      } else {
        toast.error(data.error || 'Action failed');
      }
    } catch (error) {
      console.error('Subscription action error:', error);
      toast.error('Failed to perform action');
    } finally {
      setActionLoading(null);
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'FREE':
        return <Sparkles className="h-5 w-5" />;
      case 'PRO':
        return <Zap className="h-5 w-5" />;
      case 'ENTERPRISE':
        return <Crown className="h-5 w-5" />;
      default:
        return <Sparkles className="h-5 w-5" />;
    }
  };

  const getStatusBadge = (status: string, cancelAtPeriodEnd: boolean) => {
    if (cancelAtPeriodEnd) {
      return <Badge variant="destructive">Canceling</Badge>;
    }
    
    switch (status.toLowerCase()) {
      case 'active':
        return <Badge variant="default">Active</Badge>;
      case 'trialing':
        return <Badge variant="secondary">Trial</Badge>;
      case 'past_due':
        return <Badge variant="destructive">Past Due</Badge>;
      case 'canceled':
        return <Badge variant="outline">Canceled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscriptionData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription Status</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Unable to load subscription data.</p>
        </CardContent>
      </Card>
    );
  }

  const { subscription, plan, usage } = subscriptionData;

  return (
    <div className="space-y-6">
      {/* Main Subscription Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getTierIcon(subscription.tier)}
              <CardTitle>{plan.name} Plan</CardTitle>
            </div>
            {getStatusBadge(subscription.status, subscription.cancel_at_period_end)}
          </div>
          <CardDescription>
            ${plan.price}/{plan.interval} • {plan.features.length} features included
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Usage Progress */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Monthly Usage</span>
              <span className="text-sm text-muted-foreground">
                {usage.summaries_used} / {usage.summaries_limit === -1 ? '∞' : usage.summaries_limit}
              </span>
            </div>
            <Progress 
              value={usage.usage_percentage} 
              className="h-2"
            />
            <div className="flex items-center gap-2 mt-2">
              {usage.can_create_summary ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span className="text-xs text-muted-foreground">
                {usage.can_create_summary ? 'Can create summaries' : 'Usage limit reached'}
              </span>
            </div>
          </div>

          {/* Billing Period */}
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              Current period: {new Date(subscription.current_period_start).toLocaleDateString()} - {new Date(subscription.current_period_end).toLocaleDateString()}
            </span>
          </div>

          {/* AI Models */}
          <div>
            <h4 className="text-sm font-medium mb-2">Available AI Models</h4>
            <div className="flex flex-wrap gap-2">
              {plan.limits.aiModels.map((model) => (
                <Badge key={model} variant="secondary" className="text-xs">
                  {model}
                </Badge>
              ))}
            </div>
          </div>

          {/* Subscription Actions */}
          {subscription.tier !== 'FREE' && (
            <div className="flex gap-2 pt-4 border-t">
              {subscription.cancel_at_period_end ? (
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => handleSubscriptionAction('reactivate')}
                  disabled={actionLoading === 'reactivate'}
                >
                  {actionLoading === 'reactivate' ? 'Processing...' : 'Reactivate Subscription'}
                </Button>
              ) : (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleSubscriptionAction('cancel')}
                  disabled={actionLoading === 'cancel'}
                >
                  {actionLoading === 'cancel' ? 'Processing...' : 'Cancel Subscription'}
                </Button>
              )}
            </div>
          )}

          {/* Warning for canceled subscriptions */}
          {subscription.cancel_at_period_end && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <span className="text-sm text-yellow-800">
                Your subscription will end on {new Date(subscription.current_period_end).toLocaleDateString()}
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default SubscriptionStatus;
