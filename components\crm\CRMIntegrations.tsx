'use client';

/**
 * CRM Integrations Component
 * Manages Notion and HubSpot integrations with OAuth flows
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, ExternalLink, Settings, Trash2, Plus, CheckCircle, AlertCircle } from 'lucide-react';
import { getCurrentUserClient } from '@/lib/user-management-client';
import { toast } from 'sonner';

interface CRMIntegration {
  id: string;
  integration_type: 'notion' | 'hubspot';
  workspace_name?: string;
  workspace_id?: string;
  is_active: boolean;
  settings: {
    auto_sync: boolean;
    sync_frequency: 'realtime' | 'hourly' | 'daily';
    tag_mapping: Record<string, string>;
  };
  created_at: string;
  updated_at: string;
}

export default function CRMIntegrations() {
  const [user, setUser] = useState<any>(null);
  const [integrations, setIntegrations] = useState<CRMIntegration[]>([]);
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { getCurrentUserClient } = await import('@/lib/user-management-client');
        const currentUser = await getCurrentUserClient();
        setUser(currentUser);
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    };

    fetchUser();
  }, []);

  useEffect(() => {
    fetchIntegrations();
  }, [user]);

  const fetchIntegrations = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const response = await fetch('/api/crm/integrations');
      const data = await response.json();

      if (data.success) {
        setIntegrations(data.integrations || []);
      } else {
        toast.error('Failed to load CRM integrations');
      }
    } catch (error) {
      console.error('Error fetching integrations:', error);
      toast.error('Failed to load CRM integrations');
    } finally {
      setLoading(false);
    }
  };

  const connectCRM = async (type: 'notion' | 'hubspot') => {
    try {
      setConnecting(type);
      
      // Get OAuth URL
      const response = await fetch(`/api/crm/oauth?type=${type}`);
      const data = await response.json();

      if (data.success) {
        // Redirect to OAuth provider
        window.location.href = data.oauth_url;
      } else {
        toast.error(data.error || `Failed to connect to ${type}`);
      }
    } catch (error) {
      console.error(`Error connecting to ${type}:`, error);
      toast.error(`Failed to connect to ${type}`);
    } finally {
      setConnecting(null);
    }
  };

  const toggleIntegration = async (integrationId: string, isActive: boolean) => {
    try {
      const response = await fetch('/api/crm/integrations', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          integration_id: integrationId,
          is_active: isActive,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setIntegrations(prev =>
          prev.map(integration =>
            integration.id === integrationId
              ? { ...integration, is_active: isActive }
              : integration
          )
        );
        toast.success(`Integration ${isActive ? 'enabled' : 'disabled'}`);
      } else {
        toast.error(data.error || 'Failed to update integration');
      }
    } catch (error) {
      console.error('Error updating integration:', error);
      toast.error('Failed to update integration');
    }
  };

  const removeIntegration = async (integrationId: string, type: string) => {
    if (!confirm(`Are you sure you want to remove the ${type} integration?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/crm/integrations?integration_id=${integrationId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        setIntegrations(prev => prev.filter(integration => integration.id !== integrationId));
        toast.success('Integration removed successfully');
      } else {
        toast.error(data.error || 'Failed to remove integration');
      }
    } catch (error) {
      console.error('Error removing integration:', error);
      toast.error('Failed to remove integration');
    }
  };

  const getIntegrationIcon = (type: string) => {
    switch (type) {
      case 'notion':
        return '📝';
      case 'hubspot':
        return '🔶';
      default:
        return '🔗';
    }
  };

  const getIntegrationDescription = (type: string) => {
    switch (type) {
      case 'notion':
        return 'Sync summaries to Notion databases with tags and action items';
      case 'hubspot':
        return 'Create contacts and deals in HubSpot with lead scoring';
      default:
        return 'CRM integration';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading CRM integrations...</span>
        </CardContent>
      </Card>
    );
  }

  const notionIntegration = integrations.find(i => i.integration_type === 'notion');
  const hubspotIntegration = integrations.find(i => i.integration_type === 'hubspot');

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">CRM Integrations</h3>
        <p className="text-sm text-gray-600">
          Connect your CRM tools to automatically sync summaries and create leads
        </p>
      </div>

      {/* Notion Integration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="text-2xl">📝</span>
            Notion
            {notionIntegration && (
              <Badge variant={notionIntegration.is_active ? 'default' : 'secondary'}>
                {notionIntegration.is_active ? 'Active' : 'Inactive'}
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            {getIntegrationDescription('notion')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {notionIntegration ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{notionIntegration.workspace_name}</p>
                  <p className="text-sm text-gray-600">
                    Connected on {new Date(notionIntegration.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={notionIntegration.is_active}
                    onCheckedChange={(checked) => toggleIntegration(notionIntegration.id, checked)}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeIntegration(notionIntegration.id, 'Notion')}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              {notionIntegration.is_active && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Summaries will be automatically synced to your Notion workspace
                  </AlertDescription>
                </Alert>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Connect Notion to automatically create pages for your summaries with tags and action items.
              </p>
              <Button
                onClick={() => connectCRM('notion')}
                disabled={connecting === 'notion'}
                className="w-full"
              >
                {connecting === 'notion' ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Connect Notion
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* HubSpot Integration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="text-2xl">🔶</span>
            HubSpot
            {hubspotIntegration && (
              <Badge variant={hubspotIntegration.is_active ? 'default' : 'secondary'}>
                {hubspotIntegration.is_active ? 'Active' : 'Inactive'}
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            {getIntegrationDescription('hubspot')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {hubspotIntegration ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{hubspotIntegration.workspace_name}</p>
                  <p className="text-sm text-gray-600">
                    Connected on {new Date(hubspotIntegration.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={hubspotIntegration.is_active}
                    onCheckedChange={(checked) => toggleIntegration(hubspotIntegration.id, checked)}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeIntegration(hubspotIntegration.id, 'HubSpot')}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              {hubspotIntegration.is_active && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Summaries will be automatically synced to your HubSpot CRM
                  </AlertDescription>
                </Alert>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Connect HubSpot to automatically create contacts and deals from your summaries with lead scoring.
              </p>
              <Button
                onClick={() => connectCRM('hubspot')}
                disabled={connecting === 'hubspot'}
                className="w-full"
              >
                {connecting === 'hubspot' ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Connect HubSpot
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Integration Status */}
      {integrations.length > 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Pro Tip:</strong> CRM integrations are available for Pro and Enterprise plans. 
            Summaries will be automatically synced when you create them.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
