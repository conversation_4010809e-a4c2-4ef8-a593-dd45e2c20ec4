'use client';

import { devLog } from '@/lib/console-cleaner';

/**
 * CLERK AUTHENTICATION SYSTEM
 * 
 * This provides a production-ready authentication system using Clerk
 * for the Slack Summarizer SaaS with proper user management and security.
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser, useAuth, useClerk } from '@clerk/nextjs';
import { User } from '@clerk/nextjs/server';

// Enhanced User Types for Live SaaS
export interface LiveUser {
  id: string;
  email: string;
  name: string;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
  orgId?: string;
  orgName?: string;
  plan: 'free' | 'pro' | 'enterprise';
  createdAt: string;
  isAnonymous: false; // Always false for authenticated users
  clerkUser?: User;
}

export interface LiveAuthContext {
  user: LiveUser | null;
  isLoading: boolean;
  isSignedIn: boolean;
  signOut: () => Promise<void>;
  openSignIn: () => void;
  openSignUp: () => void;
  openUserProfile: () => void;
}

// Create context
const LiveAuthContext = createContext<LiveAuthContext | null>(null);

// Live Auth Provider using Clerk
export function LiveAuthProvider({ children }: { children: React.ReactNode }) {
  const { user: clerkUser, isLoaded: userLoaded } = useUser();
  const { isSignedIn, isLoaded: authLoaded } = useAuth();
  const { signOut, openSignIn, openSignUp, openUserProfile } = useClerk();
  
  const [user, setUser] = useState<LiveUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Convert Clerk user to our LiveUser format
  useEffect(() => {
    if (userLoaded && authLoaded) {
      if (isSignedIn && clerkUser) {
        const liveUser: LiveUser = {
          id: clerkUser.id,
          email: clerkUser.primaryEmailAddress?.emailAddress || '',
          name: clerkUser.fullName || `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || 'User',
          firstName: clerkUser.firstName || undefined,
          lastName: clerkUser.lastName || undefined,
          imageUrl: clerkUser.imageUrl,
          orgId: clerkUser.organizationMemberships?.[0]?.organization?.id,
          orgName: clerkUser.organizationMemberships?.[0]?.organization?.name,
          plan: 'free', // Default plan, can be enhanced with metadata
          createdAt: clerkUser.createdAt?.toISOString() || new Date().toISOString(),
          isAnonymous: false,
          clerkUser: clerkUser as User,
        };
        setUser(liveUser);
      } else {
        setUser(null);
      }
      setIsLoading(false);
    }
  }, [clerkUser, isSignedIn, userLoaded, authLoaded]);

  const handleSignOut = async () => {
    await signOut();
    setUser(null);
  };

  return (
    <LiveAuthContext.Provider value={{
      user,
      isLoading,
      isSignedIn: !!isSignedIn,
      signOut: handleSignOut,
      openSignIn,
      openSignUp,
      openUserProfile,
    }}>
      {children}
    </LiveAuthContext.Provider>
  );
}

// Live Auth Hook
export function useLiveAuth(): LiveAuthContext {
  const context = useContext(LiveAuthContext);
  if (!context) {
    throw new Error('useLiveAuth must be used within LiveAuthProvider');
  }
  return context;
}

// Utility functions for live authentication
export const getCurrentUser = async (): Promise<LiveUser | null> => {
  // This should be called from server-side with Clerk's auth()
  // For client-side, use the useLiveAuth hook instead
  return null;
};

export const getCurrentUserClient = async (): Promise<LiveUser | null> => {
  // For client-side, use the useLiveAuth hook instead
  return null;
};

// Session check for live authentication
export const getSession = async () => {
  // This should be implemented with Clerk's auth() on server-side
  return {
    data: {
      session: null
    },
    error: null
  };
};

// Live SaaS mode - authentication always required
export const isPublicMode = () => {
  return false; // Always false for live SaaS
};

// Backward compatibility exports (deprecated - use Clerk directly)
export const useDevAuth = useLiveAuth;
export const PublicAuthProvider = LiveAuthProvider;
export const usePublicAuth = useLiveAuth;
  devLog.log('🔐 Live Authentication initialized - Clerk-powered SaaS');
