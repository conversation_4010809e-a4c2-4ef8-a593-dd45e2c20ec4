/**
 * Post Templates API Routes
 * Manages Slack post templates
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import {
  getUserPostTemplates,
  createPostTemplate,
  updatePostTemplate,
  deletePostTemplate,
  initializeDefaultTemplates,
  validateTemplate,
  TEMPLATE_VARIABLES,
} from '@/lib/post-templates';
// Removed: import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/slack/templates
 * Get user's post templates
 */
export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;
    

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id') || `org-${userId}`;
    const includeVariables = searchParams.get('include_variables') === 'true';

    const templates = await getUserPostTemplates(userId, organizationId);

    // Initialize default templates if user has none
    if (templates.length === 0) {
      await initializeDefaultTemplates(userId, organizationId);
      const newTemplates = await getUserPostTemplates(userId, organizationId);
      
      return NextResponse.json({
        success: true,
        templates: newTemplates,
        variables: includeVariables ? TEMPLATE_VARIABLES : undefined,
      });
    }

    return NextResponse.json({
      success: true,
      templates,
      variables: includeVariables ? TEMPLATE_VARIABLES : undefined,
    });

  } catch (error) {
    console.error('Error fetching post templates:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to fetch post templates' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/slack/templates
 * Create a new post template
 */
export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;
    

    const body = await request.json();
    const {
      organization_id,
      name,
      description,
      template_content,
      is_default = false,
    } = body;

    // Validate required fields
    if (!name || !template_content) {
      return NextResponse.json(
        { error: 'Name and template content are required' },
        { status: 400 }
      );
    }

    // Validate template content
    const validation = validateTemplate(template_content);
    if (!validation.valid) {
      return NextResponse.json(
        { 
          error: 'Invalid template content',
          validation_errors: validation.errors
        },
        { status: 400 }
      );
    }

    const orgId = organization_id || `org-${userId}`;

    const result = await createPostTemplate(userId, orgId, {
      name,
      description: description || '',
      template_content,
      variables: [], // Will be extracted in the service
      is_default,
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      template: result.template,
    });

  } catch (error) {
    console.error('Error creating post template:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to create post template' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/slack/templates
 * Update a post template
 */
export async function PUT(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;
    

    const body = await request.json();
    const { template_id, ...updates } = body;

    if (!template_id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Validate template content if it's being updated
    if (updates.template_content) {
      const validation = validateTemplate(updates.template_content);
      if (!validation.valid) {
        return NextResponse.json(
          { 
            error: 'Invalid template content',
            validation_errors: validation.errors
          },
          { status: 400 }
        );
      }
    }

    const result = await updatePostTemplate(template_id, userId, updates);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Template updated successfully',
    });

  } catch (error) {
    console.error('Error updating post template:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to update post template' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/slack/templates
 * Delete a post template
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;
    

    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('template_id');

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const result = await deletePostTemplate(templateId, userId);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully',
    });

  } catch (error) {
    console.error('Error deleting post template:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to delete post template' },
      { status: 500 }
    );
  }
}
