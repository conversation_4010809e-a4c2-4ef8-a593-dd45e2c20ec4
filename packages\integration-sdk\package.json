{"name": "@saas-kit/integration-sdk", "version": "1.0.0", "description": "Universal OAuth and export SDK for SaaS integrations with 10+ popular services", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"integration-sdk": "dist/cli.js"}, "files": ["dist", "README.md", "LICENSE"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "build:cli": "tsup src/cli.ts --format cjs --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "prepublishOnly": "npm run clean && npm run build && npm run build:cli"}, "keywords": ["o<PERSON>h", "integrations", "saas", "api", "sdk", "notion", "slack", "google-drive", "hubspot", "salesforce", "typescript"], "author": "SaaS Kit Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/saas-kit/integration-sdk.git"}, "bugs": {"url": "https://github.com/saas-kit/integration-sdk/issues"}, "homepage": "https://github.com/saas-kit/integration-sdk#readme", "dependencies": {"ioredis": "^5.3.2", "node-fetch": "^3.3.2", "crypto": "^1.0.1"}, "devDependencies": {"@types/node": "^20.10.0", "eslint": "^8.54.0", "tsup": "^8.0.0", "typescript": "^5.3.0", "vitest": "^1.0.0", "@vitest/coverage-v8": "^1.0.0"}, "peerDependencies": {"typescript": ">=4.5.0"}, "engines": {"node": ">=16.0.0"}, "publishConfig": {"access": "public"}}