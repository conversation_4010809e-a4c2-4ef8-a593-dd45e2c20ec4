/**
 * Production Readiness Validation Script
 * 
 * Validates all production optimizations and deployment readiness
 */

const { default: fetch } = require('node-fetch');
const fs = require('fs');
const path = require('path');

const testResults = [];

function addTestResult(test, status, details) {
  testResults.push({ test, status, details });
  const emoji = status === 'PASS' ? '✅' : status === 'WARN' ? '⚠️' : '❌';
  console.log(`${emoji} ${test}: ${details}`);
}

function logHeader(title) {
  console.log(`\n🔍 ${title}`);
  console.log('='.repeat(50));
}

// Test error boundaries
function testErrorBoundaries() {
  logHeader('ERROR BOUNDARIES VALIDATION');
  
  const errorBoundaryFiles = [
    'components/ErrorBoundary.tsx',
    'components/ChunkErrorBoundary.tsx',
    'app/error.tsx',
    'app/global-error.tsx'
  ];
  
  errorBoundaryFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('componentDidCatch') || content.includes('ErrorBoundary')) {
        addTestResult(`Error Boundary: ${file}`, 'PASS', 'Error boundary implemented');
      } else {
        addTestResult(`Error Boundary: ${file}`, 'WARN', 'File exists but may not be error boundary');
      }
    } else {
      addTestResult(`Error Boundary: ${file}`, 'WARN', 'File not found');
    }
  });
}

// Test loading states
function testLoadingStates() {
  logHeader('LOADING STATES VALIDATION');
  
  const loadingFiles = [
    'components/ui/loading-states.tsx',
    'components/ui/skeleton.tsx',
    'lib/lazy-loading.tsx'
  ];
  
  loadingFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('loading') || content.includes('Skeleton') || content.includes('Loader')) {
        addTestResult(`Loading States: ${file}`, 'PASS', 'Loading components implemented');
      } else {
        addTestResult(`Loading States: ${file}`, 'WARN', 'File exists but may lack loading patterns');
      }
    } else {
      addTestResult(`Loading States: ${file}`, 'WARN', 'File not found');
    }
  });
}

// Test performance optimizations
function testPerformanceOptimizations() {
  logHeader('PERFORMANCE OPTIMIZATIONS VALIDATION');
  
  // Check Next.js config
  if (fs.existsSync('next.config.mjs')) {
    const config = fs.readFileSync('next.config.mjs', 'utf8');
    
    if (config.includes('splitChunks')) {
      addTestResult('Chunk Splitting', 'PASS', 'Webpack chunk optimization configured');
    } else {
      addTestResult('Chunk Splitting', 'WARN', 'Chunk optimization may be missing');
    }
    
    if (config.includes('optimizePackageImports')) {
      addTestResult('Package Optimization', 'PASS', 'Package import optimization enabled');
    } else {
      addTestResult('Package Optimization', 'WARN', 'Package optimization may be missing');
    }
    
    if (config.includes('webpackBuildWorker')) {
      addTestResult('Build Worker', 'PASS', 'Webpack build worker enabled');
    } else {
      addTestResult('Build Worker', 'WARN', 'Build worker may be disabled');
    }
  } else {
    addTestResult('Next.js Config', 'FAIL', 'next.config.mjs not found');
  }
  
  // Check Vercel config
  if (fs.existsSync('vercel.json')) {
    const vercelConfig = fs.readFileSync('vercel.json', 'utf8');
    
    if (vercelConfig.includes('Cache-Control')) {
      addTestResult('Static Caching', 'PASS', 'Static asset caching configured');
    } else {
      addTestResult('Static Caching', 'WARN', 'Static caching may be missing');
    }
    
    if (vercelConfig.includes('maxDuration')) {
      addTestResult('Function Timeouts', 'PASS', 'Function timeout limits configured');
    } else {
      addTestResult('Function Timeouts', 'WARN', 'Function timeouts may be missing');
    }
  } else {
    addTestResult('Vercel Config', 'WARN', 'vercel.json not found');
  }
}

// Test security headers
function testSecurityHeaders() {
  logHeader('SECURITY HEADERS VALIDATION');
  
  const configFiles = ['next.config.mjs', 'vercel.json'];
  
  configFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      
      if (content.includes('Content-Security-Policy')) {
        addTestResult(`CSP Headers: ${file}`, 'PASS', 'Content Security Policy configured');
      } else {
        addTestResult(`CSP Headers: ${file}`, 'WARN', 'CSP may be missing');
      }
      
      if (content.includes('X-Frame-Options')) {
        addTestResult(`Frame Options: ${file}`, 'PASS', 'X-Frame-Options configured');
      } else {
        addTestResult(`Frame Options: ${file}`, 'WARN', 'Frame options may be missing');
      }
    }
  });
}

// Test API endpoints
async function testAPIEndpoints() {
  logHeader('API ENDPOINTS VALIDATION');
  
  const endpoints = [
    { path: '/api/health', name: 'Health Check' },
    { path: '/api/dashboard', name: 'Dashboard API' },
    { path: '/api/upload/status?fileId=test', name: 'Upload Status' },
    { path: '/api/summaries/demo-summary-123/tags', name: 'Smart Tagging' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`http://localhost:3000${endpoint.path}`);
      if (response.status === 200) {
        addTestResult(`API: ${endpoint.name}`, 'PASS', `${response.status} ${response.statusText}`);
      } else if (response.status === 404) {
        addTestResult(`API: ${endpoint.name}`, 'WARN', `${response.status} - Expected for some endpoints`);
      } else {
        addTestResult(`API: ${endpoint.name}`, 'WARN', `${response.status} ${response.statusText}`);
      }
    } catch (error) {
      addTestResult(`API: ${endpoint.name}`, 'FAIL', `Connection error: ${error.message}`);
    }
  }
}

// Test monitoring setup
function testMonitoringSetup() {
  logHeader('MONITORING & ANALYTICS VALIDATION');
  
  const monitoringFiles = [
    'lib/sentry.client.ts',
    'lib/posthog.client.ts',
    'lib/analytics.ts',
    'lib/performance-monitor.ts'
  ];
  
  monitoringFiles.forEach(file => {
    if (fs.existsSync(file)) {
      addTestResult(`Monitoring: ${file}`, 'PASS', 'Monitoring service configured');
    } else {
      addTestResult(`Monitoring: ${file}`, 'WARN', 'Monitoring file not found');
    }
  });
  
  // Check environment variables
  if (process.env.SENTRY_DSN || process.env.NEXT_PUBLIC_SENTRY_DSN) {
    addTestResult('Sentry Configuration', 'PASS', 'Sentry DSN configured');
  } else {
    addTestResult('Sentry Configuration', 'WARN', 'Sentry DSN not found in environment');
  }
  
  if (process.env.NEXT_PUBLIC_POSTHOG_KEY) {
    addTestResult('PostHog Configuration', 'PASS', 'PostHog key configured');
  } else {
    addTestResult('PostHog Configuration', 'WARN', 'PostHog key not found in environment');
  }
}

// Test build output
function testBuildOutput() {
  logHeader('BUILD OUTPUT VALIDATION');
  
  if (fs.existsSync('.next')) {
    addTestResult('Build Directory', 'PASS', '.next directory exists');
    
    if (fs.existsSync('.next/static')) {
      addTestResult('Static Assets', 'PASS', 'Static assets generated');
    } else {
      addTestResult('Static Assets', 'WARN', 'Static assets directory missing');
    }
    
    if (fs.existsSync('.next/server')) {
      addTestResult('Server Build', 'PASS', 'Server build generated');
    } else {
      addTestResult('Server Build', 'WARN', 'Server build directory missing');
    }
  } else {
    addTestResult('Build Directory', 'FAIL', '.next directory not found - run npm run build');
  }
}

// Main test runner
async function runProductionTests() {
  console.log('🚀 PRODUCTION READINESS VALIDATION');
  console.log('=====================================\n');
  
  testErrorBoundaries();
  testLoadingStates();
  testPerformanceOptimizations();
  testSecurityHeaders();
  await testAPIEndpoints();
  testMonitoringSetup();
  testBuildOutput();
  
  // Summary
  console.log('\n📊 PRODUCTION READINESS SUMMARY');
  console.log('================================');
  
  const passed = testResults.filter(r => r.status === 'PASS').length;
  const warned = testResults.filter(r => r.status === 'WARN').length;
  const failed = testResults.filter(r => r.status === 'FAIL').length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`⚠️  Warnings: ${warned}`);
  console.log(`❌ Failed: ${failed}`);
  
  if (failed === 0 && warned <= 3) {
    console.log('\n🎉 PRODUCTION READY! ✅');
    console.log('Your application is optimized and ready for deployment.');
  } else if (failed === 0) {
    console.log('\n✅ MOSTLY READY');
    console.log('Minor warnings detected but application should work in production.');
  } else {
    console.log('\n⚠️  NEEDS ATTENTION');
    console.log('Some critical issues need to be addressed before production deployment.');
  }
}

// Run the tests
runProductionTests();
