/**
 * Post Templates Service
 * Manages Slack post templates for scheduled posting
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';
import { SentryTracker } from '@/lib/sentry.client';

export interface PostTemplate {
  id: string;
  user_id: string;
  organization_id: string;
  name: string;
  description: string;
  template_content: string;
  variables: string[]; // Available template variables
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface TemplateVariable {
  name: string;
  description: string;
  example: string;
}

// Available template variables
export const TEMPLATE_VARIABLES: TemplateVariable[] = [
  {
    name: '{{date}}',
    description: 'Current date',
    example: 'January 15, 2024'
  },
  {
    name: '{{time}}',
    description: 'Current time',
    example: '2:30 PM'
  },
  {
    name: '{{summary_count}}',
    description: 'Number of summaries in the period',
    example: '5'
  },
  {
    name: '{{top_summary}}',
    description: 'Most recent or important summary',
    example: 'Team standup discussion about Q1 goals'
  },
  {
    name: '{{summary_list}}',
    description: 'List of all summaries in the period',
    example: '• Team standup\n• Client meeting\n• Project review'
  },
  {
    name: '{{user_name}}',
    description: 'Name of the user who created the schedule',
    example: 'John Doe'
  },
  {
    name: '{{organization_name}}',
    description: 'Organization name',
    example: 'Acme Corp'
  },
  {
    name: '{{period}}',
    description: 'Time period (daily, weekly, monthly)',
    example: 'weekly'
  }
];

// Default templates
export const DEFAULT_TEMPLATES = [
  {
    name: 'Daily Summary Digest',
    description: 'Simple daily summary with key highlights',
    template_content: `📊 **Daily Summary Digest** - {{date}}

Here's what happened today:

{{summary_list}}

Total summaries: {{summary_count}}

Have a great day! 🚀`,
    variables: ['{{date}}', '{{summary_list}}', '{{summary_count}}'],
    is_default: true
  },
  {
    name: 'Weekly Team Update',
    description: 'Comprehensive weekly team update',
    template_content: `📈 **Weekly Team Update** - Week of {{date}}

Hi team! Here's our {{period}} summary:

**Key Highlights:**
{{top_summary}}

**All Activities:**
{{summary_list}}

**Stats:**
- Total summaries: {{summary_count}}
- Generated by: {{user_name}}

Keep up the great work! 💪`,
    variables: ['{{date}}', '{{period}}', '{{top_summary}}', '{{summary_list}}', '{{summary_count}}', '{{user_name}}'],
    is_default: true
  },
  {
    name: 'Executive Brief',
    description: 'Concise executive-level summary',
    template_content: `🎯 **Executive Brief** - {{date}}

**{{organization_name}} Summary Report**

{{top_summary}}

**Period:** {{period}}
**Total Activities:** {{summary_count}}
**Compiled by:** {{user_name}} at {{time}}

---
*Automated by Slack Summary Scribe*`,
    variables: ['{{date}}', '{{organization_name}}', '{{top_summary}}', '{{period}}', '{{summary_count}}', '{{user_name}}', '{{time}}'],
    is_default: true
  }
];

/**
 * Get user's post templates
 */
export async function getUserPostTemplates(
  userId: string,
  organizationId: string
): Promise<PostTemplate[]> {
  try {
    const supabase = await createSupabaseServerClient();

    const { data, error } = await supabase
      .from('post_templates')
      .select('*')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .order('is_default', { ascending: false })
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return data || [];

  } catch (error) {
    console.error('Error fetching post templates:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return [];
  }
}

/**
 * Create a new post template
 */
export async function createPostTemplate(
  userId: string,
  organizationId: string,
  templateData: Omit<PostTemplate, 'id' | 'user_id' | 'organization_id' | 'created_at' | 'updated_at'>
): Promise<{ success: boolean; template?: PostTemplate; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Extract variables from template content
    const variables = extractTemplateVariables(templateData.template_content);

    const { data, error } = await supabase
      .from('post_templates')
      .insert({
        user_id: userId,
        organization_id: organizationId,
        name: templateData.name,
        description: templateData.description,
        template_content: templateData.template_content,
        variables,
        is_default: templateData.is_default,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { success: true, template: data };

  } catch (error) {
    console.error('Error creating post template:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to create template' 
    };
  }
}

/**
 * Update post template
 */
export async function updatePostTemplate(
  templateId: string,
  userId: string,
  updates: Partial<PostTemplate>
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // If template content is updated, re-extract variables
    if (updates.template_content) {
      updates.variables = extractTemplateVariables(updates.template_content);
    }

    const { error } = await supabase
      .from('post_templates')
      .update(updates)
      .eq('id', templateId)
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    return { success: true };

  } catch (error) {
    console.error('Error updating post template:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update template' 
    };
  }
}

/**
 * Delete post template
 */
export async function deletePostTemplate(
  templateId: string,
  userId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    const { error } = await supabase
      .from('post_templates')
      .delete()
      .eq('id', templateId)
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    return { success: true };

  } catch (error) {
    console.error('Error deleting post template:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to delete template' 
    };
  }
}

/**
 * Initialize default templates for a user
 */
export async function initializeDefaultTemplates(
  userId: string,
  organizationId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Check if user already has templates
    const { data: existingTemplates } = await supabase
      .from('post_templates')
      .select('id')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .limit(1);

    if (existingTemplates && existingTemplates.length > 0) {
      return { success: true }; // User already has templates
    }

    // Create default templates
    const templatesData = DEFAULT_TEMPLATES.map(template => ({
      user_id: userId,
      organization_id: organizationId,
      name: template.name,
      description: template.description,
      template_content: template.template_content,
      variables: template.variables,
      is_default: template.is_default,
    }));

    const { error } = await supabase
      .from('post_templates')
      .insert(templatesData);

    if (error) {
      throw error;
    }

    return { success: true };

  } catch (error) {
    console.error('Error initializing default templates:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to initialize templates' 
    };
  }
}

/**
 * Render template with actual values
 */
export async function renderTemplate(
  templateContent: string,
  variables: Record<string, string>
): Promise<string> {
  let rendered = templateContent;

  // Replace all template variables
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
    rendered = rendered.replace(regex, value);
  }

  return rendered;
}

/**
 * Extract template variables from content
 */
function extractTemplateVariables(content: string): string[] {
  const regex = /\{\{([^}]+)\}\}/g;
  const variables: string[] = [];
  let match;

  while ((match = regex.exec(content)) !== null) {
    const variable = `{{${match[1]}}}`;
    if (!variables.includes(variable)) {
      variables.push(variable);
    }
  }

  return variables;
}

/**
 * Validate template content
 */
export function validateTemplate(content: string): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  const variables = extractTemplateVariables(content);

  // Check for unknown variables
  const knownVariables = TEMPLATE_VARIABLES.map(v => v.name);
  for (const variable of variables) {
    if (!knownVariables.includes(variable)) {
      errors.push(`Unknown variable: ${variable}`);
    }
  }

  // Check for unclosed variables
  const unclosedRegex = /\{\{[^}]*$/;
  if (unclosedRegex.test(content)) {
    errors.push('Template contains unclosed variables');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}
