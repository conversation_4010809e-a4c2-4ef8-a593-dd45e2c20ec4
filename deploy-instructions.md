# 🚀 PRODUCTION DEPLOYMENT INSTRUCTIONS

## 📋 Pre-Deployment Checklist

### ✅ Environment Variables
1. Copy `.env.example` to `.env.local`
2. Fill in all required values:
   - Supabase credentials
   - Clerk authentication keys
   - Slack OAuth credentials
   - OpenRouter API key
   - Stripe/Cashfree payment keys
   - PostHog analytics key
   - Sentry DSN

### ✅ Build Test
```bash
npm run build
```
Should complete without errors.

## 🌐 NETLIFY DEPLOYMENT (Recommended)

### Option 1: Netlify CLI
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy
netlify deploy --prod
```

### Option 2: GitHub Integration
1. Push code to GitHub repository
2. Connect repository to Netlify
3. Set build command: `npm run build`
4. Set publish directory: `.next`
5. Add environment variables in Netlify dashboard

### Environment Variables for Netlify
Set these in Netlify dashboard under Site Settings > Environment Variables:
- All variables from `.env.example`
- `NODE_OPTIONS=--max_old_space_size=8192`
- `NEXT_TELEMETRY_DISABLED=1`

## 🚂 RAILWAY DEPLOYMENT (Alternative)

### Railway CLI
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Deploy
railway deploy
```

### Environment Variables for Railway
Set these in Railway dashboard:
- All variables from `.env.example`
- `NODE_OPTIONS=--max_old_space_size=8192`
- `NEXT_TELEMETRY_DISABLED=1`

## 🔧 Build Configuration

### Memory Optimization
- Build command includes `NODE_OPTIONS='--max_old_space_size=8192'`
- Configured in `netlify.toml` and `railway.toml`

### Dynamic Imports
All Node.js modules use dynamic imports:
- `pdf-parse` for PDF parsing
- `mammoth` for DOCX parsing
- `pdfkit` for PDF generation
- `exceljs` for Excel exports

## 🌍 Domain Configuration

### Update Environment Variables
After deployment, update these URLs:
- `NEXT_PUBLIC_SITE_URL`
- `NEXT_PUBLIC_APP_URL`
- `NEXTAUTH_URL`

### Clerk Configuration
Update allowed origins in Clerk dashboard:
- Add your production domain
- Update redirect URLs

### Slack App Configuration
Update OAuth redirect URLs in Slack app settings:
- `https://your-domain.com/api/auth/slack/callback`

## 🔍 Post-Deployment Verification

### Test Core Features
1. ✅ Homepage loads
2. ✅ Authentication works
3. ✅ File upload works
4. ✅ AI summarization works
5. ✅ Export functions work
6. ✅ Slack integration works

### Monitor Performance
- Check PostHog analytics
- Monitor Sentry for errors
- Verify database connections

## 🚨 Troubleshooting

### Build Failures
- Check Node.js version (18+)
- Verify all environment variables
- Check memory allocation

### Runtime Errors
- Check Sentry dashboard
- Verify API endpoints
- Check database connectivity

### Performance Issues
- Monitor memory usage
- Check API response times
- Verify CDN configuration

## 📞 Support
If deployment fails, check:
1. Build logs in platform dashboard
2. Environment variable configuration
3. Domain/DNS settings
4. SSL certificate status
