/**
 * Database Validation and Health Check Service
 * Validates Supabase RLS policies and database connectivity
 */

import { createClient } from '@supabase/supabase-js';
import { logAuditEvent } from './audit-logger';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Create service role client for admin operations
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

interface DatabaseHealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  checks: {
    connectivity: boolean;
    tables: boolean;
    rls_policies: boolean;
    functions: boolean;
    indexes: boolean;
  };
  errors: string[];
  warnings: string[];
  timestamp: string;
}

interface RLSPolicyCheck {
  table_name: string;
  policy_name: string;
  policy_type: string;
  exists: boolean;
  valid: boolean;
  error?: string;
}

/**
 * Validate database connectivity
 */
export async function validateDatabaseConnectivity(): Promise<{
  success: boolean;
  error?: string;
  latency?: number;
}> {
  try {
    const startTime = Date.now();
    
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .select('count')
      .limit(1);

    const latency = Date.now() - startTime;

    if (error) {
      return {
        success: false,
        error: error.message,
        latency
      };
    }

    return {
      success: true,
      latency
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown connectivity error'
    };
  }
}

/**
 * Validate required tables exist
 */
export async function validateRequiredTables(): Promise<{
  success: boolean;
  missing_tables: string[];
  existing_tables: string[];
  error?: string;
}> {
  const requiredTables = [
    'profiles',
    'summaries',
    'files',
    'smart_tags',
    'analytics_logs',
    'audit_logs',
    'slack_tokens',
    'user_subscriptions',
    'payment_references'
  ];

  try {
    const { data: tables, error } = await supabaseAdmin
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', requiredTables);

    if (error) {
      return {
        success: false,
        missing_tables: requiredTables,
        existing_tables: [],
        error: error.message
      };
    }

    const existingTables = tables?.map(t => t.table_name) || [];
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));

    return {
      success: missingTables.length === 0,
      missing_tables: missingTables,
      existing_tables: existingTables
    };
  } catch (error) {
    return {
      success: false,
      missing_tables: requiredTables,
      existing_tables: [],
      error: error instanceof Error ? error.message : 'Unknown table validation error'
    };
  }
}

/**
 * Validate RLS policies
 */
export async function validateRLSPolicies(): Promise<{
  success: boolean;
  policies: RLSPolicyCheck[];
  error?: string;
}> {
  const expectedPolicies = [
    { table: 'profiles', policy: 'Users can view own profile', type: 'SELECT' },
    { table: 'profiles', policy: 'Users can update own profile', type: 'UPDATE' },
    { table: 'summaries', policy: 'Users can view own summaries', type: 'SELECT' },
    { table: 'summaries', policy: 'Users can insert own summaries', type: 'INSERT' },
    { table: 'files', policy: 'Users can view own files', type: 'SELECT' },
    { table: 'files', policy: 'Users can insert own files', type: 'INSERT' },
    { table: 'smart_tags', policy: 'Users can view own smart tags', type: 'SELECT' },
    { table: 'analytics_logs', policy: 'Users can insert own analytics', type: 'INSERT' },
    { table: 'audit_logs', policy: 'Service role can access all audit logs', type: 'ALL' },
    { table: 'slack_tokens', policy: 'Users can manage own tokens', type: 'ALL' },
    { table: 'user_subscriptions', policy: 'Users can view own subscription', type: 'SELECT' }
  ];

  try {
    const { data: policies, error } = await supabaseAdmin
      .from('pg_policies')
      .select('schemaname, tablename, policyname, cmd')
      .eq('schemaname', 'public');

    if (error) {
      return {
        success: false,
        policies: [],
        error: error.message
      };
    }

    const policyChecks: RLSPolicyCheck[] = expectedPolicies.map(expected => {
      const found = policies?.find(p => 
        p.tablename === expected.table && 
        p.policyname === expected.policy
      );

      return {
        table_name: expected.table,
        policy_name: expected.policy,
        policy_type: expected.type,
        exists: !!found,
        valid: !!found,
        error: found ? undefined : 'Policy not found'
      };
    });

    const allValid = policyChecks.every(check => check.valid);

    return {
      success: allValid,
      policies: policyChecks
    };
  } catch (error) {
    return {
      success: false,
      policies: [],
      error: error instanceof Error ? error.message : 'Unknown RLS validation error'
    };
  }
}

/**
 * Validate database functions
 */
export async function validateDatabaseFunctions(): Promise<{
  success: boolean;
  functions: { name: string; exists: boolean; error?: string }[];
  error?: string;
}> {
  const requiredFunctions = [
    'handle_new_user',
    'get_user_analytics',
    'cleanup_old_files',
    'update_user_subscription'
  ];

  try {
    const { data: functions, error } = await supabaseAdmin
      .from('information_schema.routines')
      .select('routine_name')
      .eq('routine_schema', 'public')
      .in('routine_name', requiredFunctions);

    if (error) {
      return {
        success: false,
        functions: [],
        error: error.message
      };
    }

    const existingFunctions = functions?.map(f => f.routine_name) || [];
    
    const functionChecks = requiredFunctions.map(name => ({
      name,
      exists: existingFunctions.includes(name),
      error: existingFunctions.includes(name) ? undefined : 'Function not found'
    }));

    const allExist = functionChecks.every(check => check.exists);

    return {
      success: allExist,
      functions: functionChecks
    };
  } catch (error) {
    return {
      success: false,
      functions: [],
      error: error instanceof Error ? error.message : 'Unknown function validation error'
    };
  }
}

/**
 * Comprehensive database health check
 */
export async function performDatabaseHealthCheck(): Promise<DatabaseHealthCheck> {
  const startTime = new Date().toISOString();
  const errors: string[] = [];
  const warnings: string[] = [];

  // Test connectivity
  const connectivityResult = await validateDatabaseConnectivity();
  const connectivityOk = connectivityResult.success;
  
  if (!connectivityOk) {
    errors.push(`Database connectivity failed: ${connectivityResult.error}`);
  } else if (connectivityResult.latency && connectivityResult.latency > 5000) {
    warnings.push(`High database latency: ${connectivityResult.latency}ms`);
  }

  // Test tables
  const tablesResult = await validateRequiredTables();
  const tablesOk = tablesResult.success;
  
  if (!tablesOk) {
    errors.push(`Missing tables: ${tablesResult.missing_tables.join(', ')}`);
    if (tablesResult.error) {
      errors.push(`Table validation error: ${tablesResult.error}`);
    }
  }

  // Test RLS policies
  const rlsResult = await validateRLSPolicies();
  const rlsOk = rlsResult.success;
  
  if (!rlsOk) {
    const missingPolicies = rlsResult.policies
      .filter(p => !p.valid)
      .map(p => `${p.table_name}.${p.policy_name}`);
    
    if (missingPolicies.length > 0) {
      warnings.push(`Missing RLS policies: ${missingPolicies.join(', ')}`);
    }
    
    if (rlsResult.error) {
      errors.push(`RLS validation error: ${rlsResult.error}`);
    }
  }

  // Test functions
  const functionsResult = await validateDatabaseFunctions();
  const functionsOk = functionsResult.success;
  
  if (!functionsOk) {
    const missingFunctions = functionsResult.functions
      .filter(f => !f.exists)
      .map(f => f.name);
    
    if (missingFunctions.length > 0) {
      warnings.push(`Missing functions: ${missingFunctions.join(', ')}`);
    }
    
    if (functionsResult.error) {
      errors.push(`Function validation error: ${functionsResult.error}`);
    }
  }

  // Determine overall status
  let status: 'healthy' | 'degraded' | 'unhealthy';
  
  if (errors.length > 0) {
    status = 'unhealthy';
  } else if (warnings.length > 0) {
    status = 'degraded';
  } else {
    status = 'healthy';
  }

  const healthCheck: DatabaseHealthCheck = {
    status,
    checks: {
      connectivity: connectivityOk,
      tables: tablesOk,
      rls_policies: rlsOk,
      functions: functionsOk,
      indexes: true // Assume indexes are OK for now
    },
    errors,
    warnings,
    timestamp: startTime
  };

  // Log health check result
  try {
    await logAuditEvent({
      event_type: 'DATABASE_HEALTH_CHECK',
      user_id: 'system',
      action: 'Database health check performed',
      resource_type: 'database',
      resource_id: 'supabase',
      metadata: {
        status,
        errors: errors.length,
        warnings: warnings.length,
        connectivity_latency: connectivityResult.latency
      }
    });
  } catch (logError) {
    console.error('Failed to log health check:', logError);
  }

  return healthCheck;
}
