import { devLog } from '@/lib/console-cleaner';
import { createClient } from '@supabase/supabase-js'
import { toast } from 'sonner'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabase = createClient(supabaseUrl, supabaseKey)

export interface NotificationData {
  userId: string;
  organizationId?: string;
  type: 'upload_complete' | 'summary_ready' | 'export_complete' | 'system' | 'file_uploaded' | 'summary_completed' | 'export_triggered';
  title: string;
  message: string;
  data?: Record<string, unknown>;
}

export interface PushNotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: Record<string, unknown>;
}

/**
 * Create an in-app notification (Public Mode)
 */
export async function createNotification(notification: NotificationData): Promise<boolean> {
  try {
    // In public mode, we'll use local storage for notifications instead of database
  devLog.log('📢 Creating notification (Public Mode):', notification.title);

    // Store notification in localStorage for session persistence
    const existingNotifications = JSON.parse(localStorage.getItem('app_notifications') || '[]');
    const newNotification = {
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      user_id: notification.userId,
      organization_id: notification.organizationId,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      data: notification.data,
      read: false,
      created_at: new Date().toISOString()
    };

    existingNotifications.unshift(newNotification);

    // Keep only last 50 notifications
    if (existingNotifications.length > 50) {
      existingNotifications.splice(50);
    }

    localStorage.setItem('app_notifications', JSON.stringify(existingNotifications));

    // Show toast notification
    toast.success(notification.title, {
      description: notification.message
    });

    return true;
  } catch (error) {
    console.error('Error creating notification:', error);
    return false;
  }
}

/**
 * Send push notification (demo mode)
 */
export async function sendPushNotification(
  userId: string, 
  payload: PushNotificationPayload
): Promise<boolean> {
  devLog.log('📱 Sending push notification (demo mode):', { userId, payload });
  return true;
}

/**
 * Send Slack notification (demo mode)
 */
export async function sendSlackNotification(
  userId: string,
  message: string,
  title?: string
): Promise<boolean> {
  devLog.log('💬 Sending Slack notification (demo mode):', { userId, message, title });
  return true;
}

/**
 * Send email notification (demo mode)
 */
export async function sendEmailNotification(
  userId: string,
  subject: string,
  message: string,
  templateData?: Record<string, unknown>
): Promise<boolean> {
  devLog.log('📧 Sending email notification (demo mode):', { userId, subject, message, templateData });
  return true;
}

/**
 * Send notification via all enabled channels (demo mode)
 */
export async function sendNotification(
  notification: NotificationData,
  channels: {
    inApp?: boolean;
    email?: boolean;
    push?: boolean;
    slack?: boolean;
  } = { inApp: true }
): Promise<boolean> {
  devLog.log('🔔 Sending multi-channel notification (demo mode):', { notification, channels });
  
  let success = true;
  
  if (channels.inApp) {
    success = success && await createNotification(notification);
  }
  
  if (channels.email) {
    success = success && await sendEmailNotification(
      notification.userId,
      notification.title,
      notification.message,
      notification.data
    );
  }
  
  if (channels.push) {
    success = success && await sendPushNotification(notification.userId, {
      title: notification.title,
      body: notification.message,
      data: notification.data
    });
  }
  
  if (channels.slack) {
    success = success && await sendSlackNotification(
      notification.userId,
      notification.message,
      notification.title
    );
  }
  
  return success;
}

/**
 * Get user's notification preferences (demo mode)
 */
export async function getUserNotificationPreferences(userId: string) {
  devLog.log('⚙️ Getting notification preferences (demo mode):', userId);
  
  return {
    email: true,
    push: true,
    slack: true,
    digest: true
  };
}

/**
 * Update user's notification preferences (demo mode)
 */
export async function updateUserNotificationPreferences(
  userId: string,
  preferences: {
    email?: boolean;
    push?: boolean;
    slack?: boolean;
    digest?: boolean;
  }
): Promise<boolean> {
  devLog.log('⚙️ Updating notification preferences (demo mode):', { userId, preferences });
  return true;
}

/**
 * Mark notification as read
 */
export async function markNotificationAsRead(notificationId: string, userId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({
        read: true,
        read_at: new Date().toISOString()
      })
      .eq('id', notificationId)
      .eq('user_id', userId);

    if (error) {
      console.error('Failed to mark notification as read:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return false;
  }
}

/**
 * Get unread notifications for user
 */
export async function getUnreadNotifications(userId: string, limit: number = 10) {
  try {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .eq('read', false)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Failed to get notifications:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error getting notifications:', error);
    return [];
  }
}

/**
 * Send digest email (demo mode)
 */
export async function sendDigestEmail(userId: string, period: 'daily' | 'weekly'): Promise<boolean> {
  devLog.log('📊 Sending digest email (demo mode):', { userId, period });
  return true;
}

/**
 * Schedule digest notifications (demo mode)
 */
export async function scheduleDigestNotifications(): Promise<void> {
  devLog.log('⏰ Scheduling digest notifications (demo mode)');
}

// Notification helpers for common scenarios

/**
 * Notify when file upload is complete (demo mode)
 */
export async function notifyUploadComplete(
  userId: string,
  fileName: string,
  organizationId?: string
): Promise<boolean> {
  return sendNotification({
    userId,
    organizationId,
    type: 'upload_complete',
    title: 'Upload Complete',
    message: `Your file "${fileName}" has been uploaded successfully`,
    data: { fileName }
  }, { inApp: true, email: true });
}

/**
 * Notify when summary is ready (demo mode)
 */
export async function notifySummaryReady(
  userId: string,
  summaryId: string,
  fileName: string,
  organizationId?: string
): Promise<boolean> {
  return sendNotification({
    userId,
    organizationId,
    type: 'summary_ready',
    title: 'Summary Ready',
    message: `Your summary for "${fileName}" is ready to view`,
    data: { summaryId, fileName }
  }, { inApp: true, push: true });
}

/**
 * Notify when export is complete (demo mode)
 */
export async function notifyExportComplete(
  userId: string,
  exportType: string,
  downloadUrl: string,
  organizationId?: string
): Promise<boolean> {
  return sendNotification({
    userId,
    organizationId,
    type: 'export_complete',
    title: 'Export Complete',
    message: `Your ${exportType} export is ready for download`,
    data: { exportType, downloadUrl }
  }, { inApp: true, email: true });
}

/**
 * Send Slack webhook notification (Public Mode)
 */
export async function sendSlackWebhook(
  webhookUrl: string,
  message: string,
  title?: string,
  color?: 'good' | 'warning' | 'danger'
): Promise<boolean> {
  try {
    if (!webhookUrl || !webhookUrl.startsWith('https://hooks.slack.com/')) {
      console.warn('Invalid Slack webhook URL provided');
      return false;
    }

    const payload = {
      text: title || 'Slack Summary Scribe Notification',
      attachments: [
        {
          color: color || 'good',
          text: message,
          footer: 'Slack Summary Scribe',
          ts: Math.floor(Date.now() / 1000)
        }
      ]
    };

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      console.error('Slack webhook failed:', response.status, response.statusText);
      return false;
    }
  devLog.log('📢 Slack webhook sent successfully');
    return true;
  } catch (error) {
    console.error('Error sending Slack webhook:', error);
    return false;
  }
}

/**
 * Notify via Slack when summary is ready
 */
export async function notifySlackSummaryReady(
  webhookUrl: string,
  fileName: string,
  summaryId: string,
  processingTime?: number
): Promise<boolean> {
  const message = `🎉 Summary completed for "${fileName}"!\n\n` +
    `📊 Summary ID: ${summaryId}\n` +
    (processingTime ? `⏱️ Processing time: ${processingTime.toFixed(1)}s\n` : '') +
    `🔗 View your summary in the dashboard`;

  return sendSlackWebhook(
    webhookUrl,
    message,
    'Summary Ready - Slack Summary Scribe',
    'good'
  );
}
