/**
 * Test Simple AI API
 */

const testSimpleAI = async () => {
  const testData = {
    text: `<PERSON>: Hey team, I wanted to discuss our Q4 goals and the new product launch.
<PERSON>: Great! I've been working on the marketing strategy. We should focus on our target demographics.
<PERSON>: From a technical perspective, we need to ensure our infrastructure can handle the increased load.
<PERSON>: Excellent points. <PERSON>, can you prepare a detailed marketing plan by next Friday?
<PERSON>: Absolutely, I'll have it ready.
<PERSON>: I'll also run some load tests this week to identify any potential bottlenecks.
<PERSON>: Perfect. Let's reconvene next Monday to review everything.`
  };

  try {
    console.log('🧪 Testing Simple AI API...');
    
    const response = await fetch('http://localhost:3000/api/test-ai', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Simple AI Test PASSED');
      console.log('📊 Result:', {
        summary: result.summary?.substring(0, 200) + '...',
        model: result.model,
        usage: result.usage
      });
    } else {
      console.log('❌ Simple AI Test FAILED');
      console.log('Error:', result);
    }
  } catch (error) {
    console.log('❌ Simple AI Test ERROR');
    console.error('Error:', error);
  }
};

// Run the test
testSimpleAI();
