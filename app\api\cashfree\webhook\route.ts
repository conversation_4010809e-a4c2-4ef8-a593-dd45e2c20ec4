import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { verifyPaymentSignature } from '@/lib/cashfree';

// import { SubscriptionStatus } from '@prisma/client'; // Type not available

// Define the subscription status type locally
type SubscriptionStatus = 'ACTIVE' | 'INACTIVE' | 'CANCELLED' | 'EXPIRED';

interface CashfreeWebhookPayload {
  type: string;
  data: {
    order: {
      order_id: string;
      order_amount: number;
      order_currency: string;
      order_status: string;
    };
    payment: {
      cf_payment_id: string;
      payment_status: string;
      payment_amount: number;
      payment_currency: string;
      payment_message: string;
      payment_time: string;
      bank_reference: string;
      auth_id: string;
      payment_method: {
        card?: {
          channel: string;
          card_number: string;
          card_network: string;
          card_type: string;
          card_country: string;
          card_bank_name: string;
        };
        upi?: {
          channel: string;
          upi_id: string;
        };
        netbanking?: {
          channel: string;
          netbanking_bank_code: string;
          netbanking_bank_name: string;
        };
      };
    };
    customer_details: {
      customer_name: string;
      customer_id: string;
      customer_email: string;
      customer_phone: string;
    };
  };
}

export async function POST(request: NextRequest) {
  try {
    // Get raw body for signature verification
    const rawBody = await request.text();
    const signature = request.headers.get('x-webhook-signature') || '';

    // Verify webhook signature
    if (!verifyPaymentSignature(rawBody, signature)) {
      console.error('Invalid webhook signature');
      return NextResponse.json(
        { success: false, error: 'Invalid signature' },
        { status: 401 }
      );
    }

    // Parse webhook payload
    const payload: CashfreeWebhookPayload = JSON.parse(rawBody);
  devLog.log('Cashfree webhook received:', {
      type: payload.type,
      orderId: payload.data.order.order_id,
      orderStatus: payload.data.order.order_status,
      paymentStatus: payload.data.payment.payment_status
    });

    // Mock webhook processing for production deployment
  devLog.log('Webhook processed successfully (mock implementation)');

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error processing Cashfree webhook:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle GET requests (for webhook verification)
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Cashfree webhook endpoint is active'
  });
}
