#!/usr/bin/env node

/**
 * COMPREHENSIVE ENVIRONMENT & CONFIGURATION TESTING
 * 
 * Tests all environment and configuration aspects for production readiness:
 * ✅ Environment variable validation
 * ✅ Production build stability
 * ✅ Configuration file integrity
 * ✅ Security settings
 * ✅ Performance optimizations
 * ✅ Deployment readiness
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logHeader(message) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`🔍 ${message}`, 'cyan');
  log(`${'='.repeat(60)}`, 'cyan');
}

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

function addTestResult(name, status, message) {
  testResults.tests.push({ name, status, message });
  if (status === 'PASS') {
    testResults.passed++;
    logSuccess(`${name}: ${message}`);
  } else if (status === 'FAIL') {
    testResults.failed++;
    logError(`${name}: ${message}`);
  } else if (status === 'WARN') {
    testResults.warnings++;
    logWarning(`${name}: ${message}`);
  }
}

// Check if file exists
function fileExists(filePath) {
  return fs.existsSync(path.join(process.cwd(), filePath));
}

// Read file content
function readFile(filePath) {
  try {
    return fs.readFileSync(path.join(process.cwd(), filePath), 'utf8');
  } catch (error) {
    return null;
  }
}

// Test environment variables
function testEnvironmentVariables() {
  logHeader('ENVIRONMENT VARIABLES COMPREHENSIVE VALIDATION');
  
  const envContent = readFile('.env.local');
  if (!envContent) {
    addTestResult('Environment File', 'FAIL', '.env.local not found');
    return;
  }
  
  // Critical environment variables
  const criticalVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXT_PUBLIC_SITE_URL'
  ];
  
  // Optional but recommended variables
  const optionalVars = [
    'OPENROUTER_API_KEY',
    'SLACK_CLIENT_ID',
    'SLACK_CLIENT_SECRET',
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'NEXT_PUBLIC_SENTRY_DSN',
    'RESEND_API_KEY'
  ];
  
  criticalVars.forEach(envVar => {
    if (envContent.includes(`${envVar}=`) && !envContent.includes(`${envVar}=\n`)) {
      addTestResult(`Critical Env: ${envVar}`, 'PASS', 'Present and configured');
    } else {
      addTestResult(`Critical Env: ${envVar}`, 'FAIL', 'Missing or empty');
    }
  });
  
  optionalVars.forEach(envVar => {
    if (envContent.includes(`${envVar}=`) && !envContent.includes(`${envVar}=\n`)) {
      addTestResult(`Optional Env: ${envVar}`, 'PASS', 'Present and configured');
    } else {
      addTestResult(`Optional Env: ${envVar}`, 'WARN', 'Missing - feature may be disabled');
    }
  });
  
  // Check for localhost configuration
  if (envContent.includes('localhost:3000')) {
    addTestResult('Environment Mode', 'PASS', 'Correctly configured for development');
  } else {
    addTestResult('Environment Mode', 'WARN', 'May be configured for production');
  }
}

// Test configuration files
function testConfigurationFiles() {
  logHeader('CONFIGURATION FILES VALIDATION');
  
  const configFiles = [
    { file: 'next.config.mjs', name: 'Next.js Config', fallback: 'next.config.js' },
    { file: 'tailwind.config.ts', name: 'Tailwind Config', fallback: 'tailwind.config.js' },
    { file: 'tsconfig.json', name: 'TypeScript Config' },
    { file: 'package.json', name: 'Package Config' },
    { file: 'middleware.ts', name: 'Middleware Config' }
  ];
  
  configFiles.forEach(({ file, name, fallback }) => {
    let configFile = file;
    let exists = fileExists(file);

    // Check fallback if primary file doesn't exist
    if (!exists && fallback && fileExists(fallback)) {
      configFile = fallback;
      exists = true;
    }

    if (exists) {
      const content = readFile(configFile);

      // Check for basic configuration integrity
      if (content && content.length > 10) {
        addTestResult(`Config File: ${name}`, 'PASS', `Present and non-empty (${configFile})`);
      } else {
        addTestResult(`Config File: ${name}`, 'FAIL', 'Empty or corrupted');
      }
    } else {
      addTestResult(`Config File: ${name}`, 'FAIL', 'Missing configuration file');
    }
  });
  
  // Check package.json scripts
  const packageJson = readFile('package.json');
  if (packageJson) {
    const pkg = JSON.parse(packageJson);
    const requiredScripts = ['dev', 'build', 'start'];
    
    requiredScripts.forEach(script => {
      if (pkg.scripts && pkg.scripts[script]) {
        addTestResult(`Package Script: ${script}`, 'PASS', 'Script configured');
      } else {
        addTestResult(`Package Script: ${script}`, 'FAIL', 'Missing required script');
      }
    });
  }
}

// Test production build
function testProductionBuild() {
  logHeader('PRODUCTION BUILD VALIDATION');
  
  try {
    logInfo('Running production build test...');
    const buildOutput = execSync('npm run build', { 
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 120000 // 2 minutes timeout
    });
    
    // Check for successful build indicators
    if (buildOutput.includes('✓ Compiled successfully') || buildOutput.includes('Build completed')) {
      addTestResult('Production Build', 'PASS', 'Build completed successfully');
    } else {
      addTestResult('Production Build', 'WARN', 'Build completed with warnings');
    }
    
    // Check for build artifacts
    if (fileExists('.next/BUILD_ID')) {
      addTestResult('Build Artifacts', 'PASS', 'Build artifacts generated');
    } else {
      addTestResult('Build Artifacts', 'FAIL', 'Build artifacts missing');
    }
    
    // Check for static optimization
    if (buildOutput.includes('○') && buildOutput.includes('Static')) {
      addTestResult('Static Optimization', 'PASS', 'Pages statically optimized');
    } else {
      addTestResult('Static Optimization', 'WARN', 'Limited static optimization');
    }
    
  } catch (error) {
    const errorOutput = error.stdout ? error.stdout.toString() : error.message;
    
    if (errorOutput.includes('Type error') || errorOutput.includes('TypeScript')) {
      addTestResult('Production Build', 'FAIL', 'TypeScript errors in build');
    } else if (errorOutput.includes('Module not found') || errorOutput.includes('Cannot resolve')) {
      addTestResult('Production Build', 'FAIL', 'Module resolution errors');
    } else {
      addTestResult('Production Build', 'FAIL', 'Build failed with errors');
    }
  }
}

// Test security configuration
function testSecurityConfiguration() {
  logHeader('SECURITY CONFIGURATION VALIDATION');
  
  // Check middleware security
  const middlewareContent = readFile('middleware.ts');
  if (middlewareContent) {
    if (middlewareContent.includes('X-Frame-Options') || middlewareContent.includes('security')) {
      addTestResult('Security Headers', 'PASS', 'Security headers configured');
    } else {
      addTestResult('Security Headers', 'WARN', 'Security headers may be missing');
    }
    
    if (middlewareContent.includes('PROTECTED_ROUTES')) {
      addTestResult('Route Protection', 'PASS', 'Route protection configured');
    } else {
      addTestResult('Route Protection', 'FAIL', 'Route protection missing');
    }
  }
  
  // Check for sensitive data exposure
  const envContent = readFile('.env.local');
  if (envContent) {
    if (envContent.includes('SECRET') && !envContent.includes('your_secret_here')) {
      addTestResult('Secret Management', 'PASS', 'Secrets appear to be configured');
    } else {
      addTestResult('Secret Management', 'WARN', 'Secrets may use default values');
    }
  }
  
  // Check .gitignore
  const gitignoreContent = readFile('.gitignore');
  if (gitignoreContent) {
    if (gitignoreContent.includes('.env') && gitignoreContent.includes('node_modules')) {
      addTestResult('Git Security', 'PASS', '.gitignore properly configured');
    } else {
      addTestResult('Git Security', 'FAIL', '.gitignore missing critical entries');
    }
  }
}

// Test performance configuration
function testPerformanceConfiguration() {
  logHeader('PERFORMANCE CONFIGURATION VALIDATION');
  
  // Check Next.js config for performance optimizations
  const nextConfigContent = readFile('next.config.js');
  if (nextConfigContent) {
    if (nextConfigContent.includes('compress') || nextConfigContent.includes('optimization')) {
      addTestResult('Performance Config', 'PASS', 'Performance optimizations configured');
    } else {
      addTestResult('Performance Config', 'WARN', 'Performance optimizations may be missing');
    }
  }
  
  // Check for bundle analysis
  const packageJson = readFile('package.json');
  if (packageJson) {
    const pkg = JSON.parse(packageJson);
    if (pkg.scripts && (pkg.scripts['analyze'] || pkg.scripts['bundle-analyzer'])) {
      addTestResult('Bundle Analysis', 'PASS', 'Bundle analysis tools available');
    } else {
      addTestResult('Bundle Analysis', 'WARN', 'Bundle analysis tools not configured');
    }
  }
  
  // Check for image optimization
  if (nextConfigContent && nextConfigContent.includes('images')) {
    addTestResult('Image Optimization', 'PASS', 'Image optimization configured');
  } else {
    addTestResult('Image Optimization', 'WARN', 'Image optimization may not be configured');
  }
}

// Generate test report
function generateReport() {
  logHeader('ENVIRONMENT & CONFIGURATION TESTING REPORT');
  
  log(`\n📊 Test Results Summary:`, 'bright');
  log(`   ✅ Passed: ${testResults.passed}`, 'green');
  log(`   ❌ Failed: ${testResults.failed}`, 'red');
  log(`   ⚠️  Warnings: ${testResults.warnings}`, 'yellow');
  log(`   📝 Total Tests: ${testResults.tests.length}`, 'blue');
  
  const successRate = ((testResults.passed / testResults.tests.length) * 100).toFixed(1);
  log(`   📈 Success Rate: ${successRate}%`, successRate >= 80 ? 'green' : 'red');
  
  if (testResults.failed === 0) {
    log(`\n🎉 ALL CRITICAL TESTS PASSED! Environment and configuration are production-ready.`, 'green');
  } else {
    log(`\n🚨 ${testResults.failed} CRITICAL ISSUES FOUND. Please fix before deployment.`, 'red');
  }
  
  // Deployment readiness assessment
  const criticalFailures = testResults.tests.filter(t => t.status === 'FAIL').length;
  if (criticalFailures === 0) {
    log(`\n🚀 DEPLOYMENT READY: Application is ready for production deployment.`, 'green');
  } else {
    log(`\n⚠️  DEPLOYMENT BLOCKED: Fix ${criticalFailures} critical issues before deploying.`, 'red');
  }
}

// Main execution
async function main() {
  log('🚀 Starting Comprehensive Environment & Configuration Testing...', 'bright');
  
  testEnvironmentVariables();
  testConfigurationFiles();
  testSecurityConfiguration();
  testPerformanceConfiguration();
  testProductionBuild();
  
  generateReport();
  
  // Exit with appropriate code
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  testEnvironmentVariables,
  testConfigurationFiles,
  testSecurityConfiguration,
  testPerformanceConfiguration,
  generateReport
};
