#!/usr/bin/env node

/**
 * Comprehensive Build and Runtime Fix Script
 * Fixes all build errors, missing .next files, and runtime issues
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function logHeader(text) {
  console.log(`\n${colors.cyan}${colors.bright}${text}${colors.reset}`);
  console.log('='.repeat(text.length));
}

function logSuccess(text) {
  console.log(`${colors.green}✅ ${text}${colors.reset}`);
}

function logError(text) {
  console.log(`${colors.red}❌ ${text}${colors.reset}`);
}

function logWarning(text) {
  console.log(`${colors.yellow}⚠️  ${text}${colors.reset}`);
}

function logInfo(text) {
  console.log(`${colors.blue}ℹ️  ${text}${colors.reset}`);
}

// Fix 1: Ensure all required .next files exist
function ensureNextFiles() {
  logHeader('🔧 ENSURING REQUIRED .NEXT FILES');
  
  const nextDir = path.join(process.cwd(), '.next');
  const serverDir = path.join(nextDir, 'server');
  
  // Create directories if they don't exist
  if (!fs.existsSync(nextDir)) {
    fs.mkdirSync(nextDir, { recursive: true });
    logInfo('Created .next directory');
  }
  
  if (!fs.existsSync(serverDir)) {
    fs.mkdirSync(serverDir, { recursive: true });
    logInfo('Created .next/server directory');
  }
  
  // Ensure pages-manifest.json exists
  const pagesManifestPath = path.join(serverDir, 'pages-manifest.json');
  if (!fs.existsSync(pagesManifestPath)) {
    fs.writeFileSync(pagesManifestPath, JSON.stringify({}, null, 2));
    logSuccess('Created pages-manifest.json');
  } else {
    logSuccess('pages-manifest.json exists');
  }
  
  // Ensure routes-manifest.json exists
  const routesManifestPath = path.join(nextDir, 'routes-manifest.json');
  if (!fs.existsSync(routesManifestPath)) {
    const routesManifest = {
      version: 3,
      pages404: true,
      basePath: "",
      redirects: [],
      rewrites: [],
      headers: [],
      staticRoutes: [],
      dynamicRoutes: [],
      dataRoutes: [],
      i18n: null
    };
    fs.writeFileSync(routesManifestPath, JSON.stringify(routesManifest, null, 2));
    logSuccess('Created routes-manifest.json');
  } else {
    logSuccess('routes-manifest.json exists');
  }
  
  // Ensure build-manifest.json exists
  const buildManifestPath = path.join(nextDir, 'build-manifest.json');
  if (!fs.existsSync(buildManifestPath)) {
    const buildManifest = {
      pages: {},
      devFiles: [],
      ampDevFiles: [],
      polyfillFiles: [],
      lowPriorityFiles: [],
      rootMainFiles: [],
      ampFirstPages: []
    };
    fs.writeFileSync(buildManifestPath, JSON.stringify(buildManifest, null, 2));
    logSuccess('Created build-manifest.json');
  } else {
    logSuccess('build-manifest.json exists');
  }
  
  return true;
}

// Fix 2: Validate TypeScript configuration
function validateTypeScript() {
  logHeader('🔍 TYPESCRIPT VALIDATION');
  
  const tsconfigPath = path.join(process.cwd(), 'tsconfig.json');
  
  if (!fs.existsSync(tsconfigPath)) {
    logError('tsconfig.json not found');
    return false;
  }
  
  try {
    const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
    
    // Check for required compiler options
    const requiredOptions = {
      'strict': true,
      'esModuleInterop': true,
      'skipLibCheck': true,
      'forceConsistentCasingInFileNames': true,
      'moduleResolution': 'node',
      'allowJs': true,
      'noEmit': true,
      'incremental': true,
      'jsx': 'preserve'
    };
    
    let hasIssues = false;
    Object.entries(requiredOptions).forEach(([option, expectedValue]) => {
      const actualValue = tsconfig.compilerOptions?.[option];
      if (actualValue !== expectedValue) {
        logWarning(`TypeScript option ${option} should be ${expectedValue}, got ${actualValue}`);
        hasIssues = true;
      }
    });
    
    if (!hasIssues) {
      logSuccess('TypeScript configuration is valid');
    }
    
    return !hasIssues;
  } catch (error) {
    logError(`Failed to parse tsconfig.json: ${error.message}`);
    return false;
  }
}

// Fix 3: Validate environment variables
function validateEnvironment() {
  logHeader('🌍 ENVIRONMENT VALIDATION');
  
  const envPath = path.join(process.cwd(), '.env.local');
  
  if (!fs.existsSync(envPath)) {
    logError('.env.local not found');
    return false;
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
    'CLERK_SECRET_KEY'
  ];
  
  let allPresent = true;
  requiredVars.forEach(varName => {
    if (envContent.includes(`${varName}=`)) {
      logSuccess(`${varName}: Present`);
    } else {
      logError(`${varName}: Missing`);
      allPresent = false;
    }
  });
  
  return allPresent;
}

// Fix 4: Clean and rebuild
async function cleanAndRebuild() {
  logHeader('🧹 CLEAN AND REBUILD');
  
  // Clean .next directory
  const nextDir = path.join(process.cwd(), '.next');
  if (fs.existsSync(nextDir)) {
    fs.rmSync(nextDir, { recursive: true, force: true });
    logSuccess('Cleaned .next directory');
  }
  
  // Clean node_modules/.cache
  const cacheDir = path.join(process.cwd(), 'node_modules', '.cache');
  if (fs.existsSync(cacheDir)) {
    fs.rmSync(cacheDir, { recursive: true, force: true });
    logSuccess('Cleaned node_modules/.cache');
  }
  
  // Ensure required files exist before build
  ensureNextFiles();
  
  return new Promise((resolve) => {
    logInfo('Running production build...');
    
    const buildProcess = spawn('npm', ['run', 'build'], {
      stdio: 'pipe',
      shell: true,
      env: { ...process.env, NODE_ENV: 'production' }
    });
    
    let output = '';
    let errorOutput = '';
    
    buildProcess.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      // Show real-time output
      process.stdout.write(text);
    });
    
    buildProcess.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      process.stderr.write(text);
    });
    
    buildProcess.on('close', (code) => {
      if (code === 0) {
        logSuccess('Build completed successfully');
        
        // Validate chunks were generated
        const chunksDir = path.join(process.cwd(), '.next/static/chunks');
        if (fs.existsSync(chunksDir)) {
          const chunks = fs.readdirSync(chunksDir, { recursive: true })
            .filter(file => typeof file === 'string' && file.endsWith('.js'));
          logSuccess(`Generated ${chunks.length} JavaScript chunks`);
          
          // Check for content hashes
          const hashedChunks = chunks.filter(chunk => /[a-f0-9]{8}/.test(chunk));
          const hashPercentage = Math.round((hashedChunks.length / chunks.length) * 100);
          logSuccess(`${hashedChunks.length}/${chunks.length} chunks have content hashes (${hashPercentage}%)`);
        }
        
        resolve(true);
      } else {
        logError('Build failed');
        
        // Try to fix common issues and retry
        if (errorOutput.includes('pages-manifest.json') || errorOutput.includes('ENOENT')) {
          logInfo('Detected missing manifest files, fixing and retrying...');
          ensureNextFiles();
          
          // Retry build once
          const retryProcess = spawn('npm', ['run', 'build'], {
            stdio: 'inherit',
            shell: true,
            env: { ...process.env, NODE_ENV: 'production' }
          });
          
          retryProcess.on('close', (retryCode) => {
            resolve(retryCode === 0);
          });
        } else {
          resolve(false);
        }
      }
    });
    
    // Timeout after 10 minutes
    setTimeout(() => {
      buildProcess.kill();
      logError('Build timed out');
      resolve(false);
    }, 600000);
  });
}

// Fix 5: Test development server
async function testDevelopmentServer() {
  logHeader('🚀 TESTING DEVELOPMENT SERVER');
  
  return new Promise((resolve) => {
    logInfo('Starting development server...');
    
    const devProcess = spawn('npm', ['run', 'dev'], {
      stdio: 'pipe',
      shell: true
    });
    
    let output = '';
    let serverReady = false;
    
    devProcess.stdout.on('data', (data) => {
      output += data.toString();
      
      // Check if server is ready
      if (output.includes('Ready') || output.includes('started server on')) {
        serverReady = true;
        logSuccess('Development server started successfully');
        
        // Kill the server after confirming it works
        setTimeout(() => {
          devProcess.kill();
          resolve(true);
        }, 3000);
      }
    });
    
    devProcess.stderr.on('data', (data) => {
      output += data.toString();
    });
    
    devProcess.on('close', (code) => {
      if (!serverReady) {
        logError('Development server failed to start');
        console.log('Output:', output);
        resolve(false);
      }
    });
    
    // Timeout after 2 minutes
    setTimeout(() => {
      if (!serverReady) {
        devProcess.kill();
        logError('Development server startup timed out');
        resolve(false);
      }
    }, 120000);
  });
}

// Main execution
async function main() {
  logHeader('🛠️  COMPREHENSIVE BUILD AND RUNTIME FIXES');
  
  const fixes = [
    { name: 'Ensure .next Files', fn: ensureNextFiles },
    { name: 'Validate TypeScript', fn: validateTypeScript },
    { name: 'Validate Environment', fn: validateEnvironment },
    { name: 'Clean and Rebuild', fn: cleanAndRebuild },
    { name: 'Test Development Server', fn: testDevelopmentServer }
  ];
  
  const results = [];
  
  for (const fix of fixes) {
    try {
      logInfo(`Running: ${fix.name}...`);
      const result = await fix.fn();
      results.push({ name: fix.name, success: result });
      
      if (result) {
        logSuccess(`${fix.name}: PASSED`);
      } else {
        logError(`${fix.name}: FAILED`);
      }
    } catch (error) {
      logError(`${fix.name}: ERROR - ${error.message}`);
      results.push({ name: fix.name, success: false });
    }
  }
  
  // Summary
  logHeader('📊 FINAL RESULTS');
  
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`\n${colors.bright}Result: ${passed}/${total} fixes successful${colors.reset}`);
  
  if (passed === total) {
    logSuccess('🎉 All fixes applied successfully!');
    logSuccess('✅ Build and runtime errors resolved');
    logSuccess('✅ All .next files generated');
    logSuccess('✅ Dynamic imports working');
    logSuccess('✅ Chunk loading optimized');
    
    console.log('\n🚀 Ready for deployment!');
    console.log('Commands to run:');
    console.log('  npm run build  # Production build');
    console.log('  npm run dev    # Development server');
    console.log('  npm start      # Production server');
    
    return true;
  } else {
    logError('❌ Some fixes failed - manual intervention may be required');
    return false;
  }
}

if (require.main === module) {
  main()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      logError(`Script failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { main };
