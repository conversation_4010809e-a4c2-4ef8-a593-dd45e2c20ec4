/**
 * Stripe Configuration and Pricing Plans
 * Enterprise SaaS Subscription Management
 */

import Stripe from 'stripe';

// Stripe Configuration
export const STRIPE_CONFIG = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
  secretKey: process.env.STRIPE_SECRET_KEY!,
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
  apiVersion: '2025-06-30.basil' as const,
};

// Initialize Stripe client
export const stripe = new Stripe(STRIPE_CONFIG.secretKey, {
  apiVersion: STRIPE_CONFIG.apiVersion,
  typescript: true,
});

// Subscription Tiers
export type SubscriptionTier = 'FREE' | 'PRO' | 'ENTERPRISE';

// Pricing Plans Configuration
export const PRICING_PLANS = {
  FREE: {
    id: 'FREE',
    name: 'Free',
    price: 0,
    currency: 'usd',
    interval: 'month',
    features: [
      '10 summaries per month',
      'DeepSeek R1 AI model',
      'Basic Slack integration',
      'Email support',
      'Standard templates'
    ],
    limits: {
      monthlySummaries: 10,
      teamMembers: 1,
      aiModels: ['deepseek-r1'],
      crmIntegrations: 0,
      scheduledPosts: 0,
      analytics: false,
      auditLogs: false,
      ssoIntegration: false
    },
    stripePriceId: null, // Free tier doesn't need Stripe
    cashfreePlanId: null
  },
  PRO: {
    id: 'PRO',
    name: 'Pro',
    price: 29,
    currency: 'usd',
    interval: 'month',
    features: [
      '500 summaries per month',
      'GPT-4o & GPT-4o-mini AI models',
      'Advanced Slack integration',
      'Slack auto-posting scheduler',
      '2 CRM integrations',
      'Custom templates',
      'Priority support',
      'Usage analytics',
      'Up to 5 team members'
    ],
    limits: {
      monthlySummaries: 500,
      teamMembers: 5,
      aiModels: ['deepseek-r1', 'gpt-4o-mini', 'gpt-4o'],
      crmIntegrations: 2,
      scheduledPosts: 10,
      analytics: true,
      auditLogs: false,
      ssoIntegration: false
    },
    stripePriceId: process.env.STRIPE_PRO_PRICE_ID,
    cashfreePlanId: process.env.CASHFREE_PRO_PLAN_ID
  },
  ENTERPRISE: {
    id: 'ENTERPRISE',
    name: 'Enterprise',
    price: 99,
    currency: 'usd',
    interval: 'month',
    features: [
      'Unlimited summaries',
      'All AI models (GPT-4o, Claude, etc.)',
      'Advanced team management',
      'Unlimited CRM integrations',
      'Custom integrations',
      'Advanced analytics & reporting',
      'Audit logs & compliance',
      'SSO integration',
      'Dedicated support',
      'Custom templates & branding',
      'API access',
      'Unlimited team members'
    ],
    limits: {
      monthlySummaries: -1, // Unlimited
      teamMembers: -1, // Unlimited
      aiModels: ['deepseek-r1', 'gpt-4o-mini', 'gpt-4o', 'claude-3-5-sonnet', 'claude-3-opus'],
      crmIntegrations: -1, // Unlimited
      scheduledPosts: -1, // Unlimited
      analytics: true,
      auditLogs: true,
      ssoIntegration: true
    },
    stripePriceId: process.env.STRIPE_ENTERPRISE_PRICE_ID,
    cashfreePlanId: process.env.CASHFREE_ENTERPRISE_PLAN_ID
  }
} as const;

// Helper functions
export function getPricingPlan(tier: SubscriptionTier) {
  return PRICING_PLANS[tier];
}

export function canUseFeature(userTier: SubscriptionTier, feature: keyof typeof PRICING_PLANS.FREE.limits): boolean {
  const plan = getPricingPlan(userTier);
  const featureValue = plan.limits[feature];
  
  // If feature is boolean, return the boolean value
  if (typeof featureValue === 'boolean') {
    return featureValue;
  }
  
  // If feature is array, check if it exists
  if (Array.isArray(featureValue)) {
    return featureValue.length > 0;
  }
  
  // If feature is number, check if it's greater than 0 or unlimited (-1)
  if (typeof featureValue === 'number') {
    return featureValue > 0 || featureValue === -1;
  }
  
  return false;
}

export function checkUsageLimit(userTier: SubscriptionTier, feature: keyof typeof PRICING_PLANS.FREE.limits, currentUsage: number): boolean {
  const plan = getPricingPlan(userTier);
  const limit = plan.limits[feature];
  
  // If unlimited (-1), always allow
  if (limit === -1) {
    return true;
  }
  
  // If it's a number, check against current usage
  if (typeof limit === 'number') {
    return currentUsage < limit;
  }
  
  return false;
}

export function getAIModelsForTier(tier: SubscriptionTier): string[] {
  const plan = getPricingPlan(tier);
  return [...plan.limits.aiModels];
}

export function canUseAIModel(userTier: SubscriptionTier, modelId: string): boolean {
  const allowedModels = getAIModelsForTier(userTier);
  return allowedModels.includes(modelId);
}

// Stripe Product and Price Creation (for setup)
export async function createStripeProducts() {
  try {
    const products = [];
    
    // Create PRO product
    const proProduct = await stripe.products.create({
      id: 'slack-summary-scribe-pro',
      name: 'Slack Summary Scribe Pro',
      description: 'Professional plan with advanced AI models and team features',
      metadata: {
        tier: 'PRO'
      }
    });
    
    const proPrice = await stripe.prices.create({
      product: proProduct.id,
      unit_amount: 2900, // $29.00
      currency: 'usd',
      recurring: {
        interval: 'month'
      },
      metadata: {
        tier: 'PRO'
      }
    });
    
    products.push({ product: proProduct, price: proPrice });
    
    // Create ENTERPRISE product
    const enterpriseProduct = await stripe.products.create({
      id: 'slack-summary-scribe-enterprise',
      name: 'Slack Summary Scribe Enterprise',
      description: 'Enterprise plan with unlimited features and advanced security',
      metadata: {
        tier: 'ENTERPRISE'
      }
    });
    
    const enterprisePrice = await stripe.prices.create({
      product: enterpriseProduct.id,
      unit_amount: 9900, // $99.00
      currency: 'usd',
      recurring: {
        interval: 'month'
      },
      metadata: {
        tier: 'ENTERPRISE'
      }
    });
    
    products.push({ product: enterpriseProduct, price: enterprisePrice });
    
    return products;
  } catch (error) {
    console.error('Error creating Stripe products:', error);
    throw error;
  }
}

// Webhook event types we handle
export const STRIPE_WEBHOOK_EVENTS = [
  'customer.subscription.created',
  'customer.subscription.updated',
  'customer.subscription.deleted',
  'invoice.payment_succeeded',
  'invoice.payment_failed',
  'customer.created',
  'customer.updated',
  'payment_method.attached',
  'payment_method.detached'
] as const;

export type StripeWebhookEvent = typeof STRIPE_WEBHOOK_EVENTS[number];

// Subscription status mapping
export const STRIPE_STATUS_MAPPING = {
  'active': 'active',
  'canceled': 'canceled',
  'incomplete': 'incomplete',
  'incomplete_expired': 'incomplete',
  'past_due': 'past_due',
  'trialing': 'trialing',
  'unpaid': 'unpaid'
} as const;

// Error messages
export const STRIPE_ERROR_MESSAGES = {
  CARD_DECLINED: 'Your card was declined. Please try a different payment method.',
  INSUFFICIENT_FUNDS: 'Insufficient funds. Please check your account balance.',
  EXPIRED_CARD: 'Your card has expired. Please update your payment method.',
  PROCESSING_ERROR: 'There was an error processing your payment. Please try again.',
  INVALID_CVC: 'Your card\'s security code is invalid.',
  INVALID_EXPIRY: 'Your card\'s expiration date is invalid.',
  GENERIC_ERROR: 'An unexpected error occurred. Please try again or contact support.'
} as const;

// Helper to get user-friendly error message
export function getStripeErrorMessage(error: any): string {
  switch (error.code) {
    case 'card_declined':
      return STRIPE_ERROR_MESSAGES.CARD_DECLINED;
    case 'insufficient_funds':
      return STRIPE_ERROR_MESSAGES.INSUFFICIENT_FUNDS;
    case 'expired_card':
      return STRIPE_ERROR_MESSAGES.EXPIRED_CARD;
    case 'processing_error':
      return STRIPE_ERROR_MESSAGES.PROCESSING_ERROR;
    case 'incorrect_cvc':
      return STRIPE_ERROR_MESSAGES.INVALID_CVC;
    case 'invalid_expiry_month':
    case 'invalid_expiry_year':
      return STRIPE_ERROR_MESSAGES.INVALID_EXPIRY;
    default:
      return STRIPE_ERROR_MESSAGES.GENERIC_ERROR;
  }
}
