import { devLog } from '@/lib/console-cleaner';
/**
 * Scheduled Summaries System
 * 
 * Automated summary generation with cron scheduling and smart timing
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';

export interface ScheduledSummary {
  id: string;
  organizationId: string;
  userId: string;
  scheduleType: 'daily' | 'weekly' | 'custom';
  frequency: ScheduleFrequency;
  workspaceFilters: WorkspaceFilters;
  lastRunAt?: string;
  nextRunAt: string;
  enabled: boolean;
  createdAt: string;
}

export interface ScheduleFrequency {
  // For daily: { hour: 9, minute: 0 }
  // For weekly: { dayOfWeek: 1, hour: 9, minute: 0 } (Monday = 1)
  // For custom: { cron: "0 9 * * 1,3,5" }
  hour?: number;
  minute?: number;
  dayOfWeek?: number;
  cron?: string;
}

export interface WorkspaceFilters {
  workspaceIds?: string[];
  channels?: string[];
  excludeChannels?: string[];
  keywords?: string[];
  minMessages?: number;
}

export interface SummaryGenerationResult {
  success: boolean;
  summaryId?: string;
  error?: string;
  metadata?: {
    messagesProcessed: number;
    wordCount: number;
    processingTime: number;
  };
}

/**
 * Schedule a recurring summary
 */
export async function scheduleRecurringSummary(
  schedule: Omit<ScheduledSummary, 'id' | 'lastRunAt' | 'nextRunAt' | 'createdAt'>
): Promise<{ success: boolean; scheduleId?: string; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Calculate next run time
    const nextRunAt = calculateNextRunTime(schedule.scheduleType, schedule.frequency);
    
    const { data, error } = await supabase
      .from('scheduled_summaries')
      .insert({
        organization_id: schedule.organizationId,
        user_id: schedule.userId,
        schedule_type: schedule.scheduleType,
        frequency: schedule.frequency,
        workspace_filters: schedule.workspaceFilters,
        next_run_at: nextRunAt.toISOString(),
        enabled: schedule.enabled,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, scheduleId: data.id };

  } catch (error) {
    console.error('Failed to schedule recurring summary:', error);
    return { success: false, error: 'Failed to schedule summary' };
  }
}

/**
 * Get pending scheduled summaries
 */
export async function getPendingScheduledSummaries(): Promise<ScheduledSummary[]> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { data, error } = await supabase
      .from('scheduled_summaries')
      .select('*')
      .eq('enabled', true)
      .lte('next_run_at', new Date().toISOString())
      .order('next_run_at', { ascending: true });

    if (error) {
      console.error('Failed to get pending summaries:', error);
      return [];
    }

    return data.map(formatScheduledSummary);

  } catch (error) {
    console.error('Failed to get pending summaries:', error);
    return [];
  }
}

/**
 * Process scheduled summary generation
 */
export async function processScheduledSummary(
  scheduleId: string
): Promise<SummaryGenerationResult> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get schedule details
    const { data: schedule, error: scheduleError } = await supabase
      .from('scheduled_summaries')
      .select('*')
      .eq('id', scheduleId)
      .single();

    if (scheduleError || !schedule) {
      return { success: false, error: 'Schedule not found' };
    }

    // Generate summary based on workspace filters
    const result = await generateScheduledSummary(schedule);
    
    // Update schedule with last run time and calculate next run
    const nextRunAt = calculateNextRunTime(
      schedule.schedule_type,
      schedule.frequency
    );

    await supabase
      .from('scheduled_summaries')
      .update({
        last_run_at: new Date().toISOString(),
        next_run_at: nextRunAt.toISOString()
      })
      .eq('id', scheduleId);

    return result;

  } catch (error) {
    console.error('Failed to process scheduled summary:', error);
    return { success: false, error: 'Failed to process summary' };
  }
}

/**
 * Generate summary for scheduled task
 */
async function generateScheduledSummary(
  schedule: any
): Promise<SummaryGenerationResult> {
  try {
    const supabase = await createSupabaseServerClient();
    const startTime = Date.now();
    
    // Get workspace data based on filters
    const workspaceData = await fetchWorkspaceData(schedule);
    
    if (!workspaceData || workspaceData.messages.length === 0) {
      return { 
        success: false, 
        error: 'No messages found for summary generation' 
      };
    }

    // Generate AI summary
    const summaryContent = await generateAISummary(workspaceData);
    
    // Save summary to database
    const { data: summary, error } = await supabase
      .from('summaries')
      .insert({
        organization_id: schedule.organization_id,
        user_id: schedule.user_id,
        title: `Scheduled Summary - ${new Date().toLocaleDateString()}`,
        content: summaryContent,
        summary_type: 'scheduled',
        metadata: {
          scheduleId: schedule.id,
          workspaceFilters: schedule.workspace_filters,
          messagesProcessed: workspaceData.messages.length,
          generatedAt: new Date().toISOString()
        },
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    const processingTime = Date.now() - startTime;

    // Send notification if enabled
    await sendSummaryNotification(schedule, summary);

    return {
      success: true,
      summaryId: summary.id,
      metadata: {
        messagesProcessed: workspaceData.messages.length,
        wordCount: summaryContent.split(' ').length,
        processingTime
      }
    };

  } catch (error) {
    console.error('Failed to generate scheduled summary:', error);
    return { success: false, error: 'Summary generation failed' };
  }
}

/**
 * Fetch workspace data based on filters
 */
async function fetchWorkspaceData(schedule: any): Promise<{
  messages: any[];
  channels: string[];
  timeRange: { start: string; end: string };
} | null> {
  try {
    // Calculate time range based on schedule type
    const timeRange = getTimeRangeForSchedule(schedule.schedule_type);
    
    // This would integrate with your Slack data fetching logic
    // For now, we'll return mock data structure
    return {
      messages: [], // Would contain actual Slack messages
      channels: schedule.workspace_filters.channels || [],
      timeRange
    };

  } catch (error) {
    console.error('Failed to fetch workspace data:', error);
    return null;
  }
}

/**
 * Generate AI summary from workspace data
 */
async function generateAISummary(workspaceData: any): Promise<string> {
  // This would integrate with your AI summary generation logic
  // For now, return a placeholder
  return `Automated summary generated for ${workspaceData.messages.length} messages across ${workspaceData.channels.length} channels.`;
}

/**
 * Send notification for generated summary
 */
async function sendSummaryNotification(schedule: any, summary: any): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get user notification preferences
    const { data: preferences } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', schedule.user_id)
      .single();

    const emailEnabled = preferences?.email_notifications?.scheduled_summaries !== false;
    
    if (emailEnabled) {
      // Send email notification
      await sendEmailNotification(schedule, summary);
    }

    // Create in-app notification
    await supabase
      .from('notifications')
      .insert({
        user_id: schedule.user_id,
        type: 'scheduled_summary',
        title: 'New Scheduled Summary Available',
        message: `Your scheduled summary has been generated with ${summary.metadata?.messagesProcessed || 0} messages processed.`,
        metadata: {
          summaryId: summary.id,
          scheduleId: schedule.id
        },
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('Failed to send summary notification:', error);
  }
}

/**
 * Send email notification for summary
 */
async function sendEmailNotification(schedule: any, summary: any): Promise<void> {
  // This would integrate with your email service (Resend)
  devLog.log('Sending email notification for summary:', summary.id);
}

/**
 * Calculate next run time based on schedule
 */
function calculateNextRunTime(
  scheduleType: string,
  frequency: ScheduleFrequency
): Date {
  const now = new Date();
  const nextRun = new Date(now);

  switch (scheduleType) {
    case 'daily':
      nextRun.setHours(frequency.hour || 9, frequency.minute || 0, 0, 0);
      if (nextRun <= now) {
        nextRun.setDate(nextRun.getDate() + 1);
      }
      break;

    case 'weekly':
      const targetDay = frequency.dayOfWeek || 1; // Monday = 1
      const currentDay = nextRun.getDay() || 7; // Sunday = 7
      const daysUntilTarget = (targetDay - currentDay + 7) % 7;
      
      nextRun.setDate(nextRun.getDate() + (daysUntilTarget || 7));
      nextRun.setHours(frequency.hour || 9, frequency.minute || 0, 0, 0);
      break;

    case 'custom':
      // For custom cron expressions, calculate next run
      // This would use a proper cron parser in production
      nextRun.setHours(nextRun.getHours() + 1);
      break;

    default:
      nextRun.setHours(nextRun.getHours() + 1);
  }

  return nextRun;
}

/**
 * Get time range for schedule type
 */
function getTimeRangeForSchedule(scheduleType: string): { start: string; end: string } {
  const now = new Date();
  const end = now.toISOString();
  
  let start: Date;
  
  switch (scheduleType) {
    case 'daily':
      start = new Date(now);
      start.setDate(start.getDate() - 1);
      break;
    case 'weekly':
      start = new Date(now);
      start.setDate(start.getDate() - 7);
      break;
    default:
      start = new Date(now);
      start.setDate(start.getDate() - 1);
  }
  
  return {
    start: start.toISOString(),
    end
  };
}

/**
 * Format scheduled summary from database
 */
function formatScheduledSummary(data: any): ScheduledSummary {
  return {
    id: data.id,
    organizationId: data.organization_id,
    userId: data.user_id,
    scheduleType: data.schedule_type,
    frequency: data.frequency,
    workspaceFilters: data.workspace_filters,
    lastRunAt: data.last_run_at,
    nextRunAt: data.next_run_at,
    enabled: data.enabled,
    createdAt: data.created_at
  };
}

/**
 * Update scheduled summary
 */
export async function updateScheduledSummary(
  scheduleId: string,
  updates: Partial<ScheduledSummary>
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const updateData: any = {};
    
    if (updates.scheduleType) updateData.schedule_type = updates.scheduleType;
    if (updates.frequency) updateData.frequency = updates.frequency;
    if (updates.workspaceFilters) updateData.workspace_filters = updates.workspaceFilters;
    if (updates.enabled !== undefined) updateData.enabled = updates.enabled;
    
    // Recalculate next run time if schedule changed
    if (updates.scheduleType || updates.frequency) {
      const nextRunAt = calculateNextRunTime(
        updates.scheduleType || 'daily',
        updates.frequency || { hour: 9, minute: 0 }
      );
      updateData.next_run_at = nextRunAt.toISOString();
    }

    const { error } = await supabase
      .from('scheduled_summaries')
      .update(updateData)
      .eq('id', scheduleId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };

  } catch (error) {
    console.error('Failed to update scheduled summary:', error);
    return { success: false, error: 'Failed to update schedule' };
  }
}

/**
 * Delete scheduled summary
 */
export async function deleteScheduledSummary(
  scheduleId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { error } = await supabase
      .from('scheduled_summaries')
      .delete()
      .eq('id', scheduleId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };

  } catch (error) {
    console.error('Failed to delete scheduled summary:', error);
    return { success: false, error: 'Failed to delete schedule' };
  }
}
