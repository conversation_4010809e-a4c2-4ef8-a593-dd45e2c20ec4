-- =====================================================
-- SLACK SUMMARY SCRIBE - DEMO RLS POLICIES
-- Public Demo Access Policies for Production
-- =====================================================

-- Demo constants
-- Demo user ID: 'demo-user-12345678-1234-1234-1234-123456789012'
-- Demo org ID: 'demo-org-12345678-1234-1234-1234-123456789012'

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "public_demo_organizations_select" ON organizations;
DROP POLICY IF EXISTS "public_demo_users_select" ON users;
DROP POLICY IF EXISTS "public_demo_user_organizations_select" ON user_organizations;
DROP POLICY IF EXISTS "public_demo_summaries_select" ON summaries;
DROP POLICY IF EXISTS "public_demo_summaries_insert" ON summaries;
DROP POLICY IF EXISTS "public_demo_slack_integrations_select" ON slack_integrations;

-- =====================================================
-- PUBLIC DEMO POLICIES - READ ACCESS
-- =====================================================

-- Allow public read access to demo organization
CREATE POLICY "public_demo_organizations_select" ON organizations
  FOR SELECT 
  TO public, anon, authenticated
  USING (id = 'demo-org-12345678-1234-1234-1234-123456789012');

-- Allow public read access to demo user profile
CREATE POLICY "public_demo_users_select" ON users
  FOR SELECT 
  TO public, anon, authenticated
  USING (id = 'demo-user-12345678-1234-1234-1234-123456789012');

-- Allow public read access to demo user-organization relationship
CREATE POLICY "public_demo_user_organizations_select" ON user_organizations
  FOR SELECT 
  TO public, anon, authenticated
  USING (
    user_id = 'demo-user-12345678-1234-1234-1234-123456789012' AND
    organization_id = 'demo-org-12345678-1234-1234-1234-123456789012'
  );

-- Allow public read access to demo summaries
CREATE POLICY "public_demo_summaries_select" ON summaries
  FOR SELECT 
  TO public, anon, authenticated
  USING (
    user_id = 'demo-user-12345678-1234-1234-1234-123456789012' AND
    organization_id = 'demo-org-12345678-1234-1234-1234-123456789012'
  );

-- Allow public insert access for demo summaries (for upload functionality)
CREATE POLICY "public_demo_summaries_insert" ON summaries
  FOR INSERT 
  TO public, anon, authenticated
  WITH CHECK (
    user_id = 'demo-user-12345678-1234-1234-1234-123456789012' AND
    organization_id = 'demo-org-12345678-1234-1234-1234-123456789012'
  );

-- Allow public read access to demo Slack integrations
CREATE POLICY "public_demo_slack_integrations_select" ON slack_integrations
  FOR SELECT 
  TO public, anon, authenticated
  USING (organization_id = 'demo-org-12345678-1234-1234-1234-123456789012');

-- =====================================================
-- SERVICE ROLE POLICIES (for API operations)
-- =====================================================

-- Service role can access all demo data
CREATE POLICY "service_role_demo_full_access" ON organizations
  FOR ALL 
  TO service_role
  USING (id = 'demo-org-12345678-1234-1234-1234-123456789012')
  WITH CHECK (id = 'demo-org-12345678-1234-1234-1234-123456789012');

CREATE POLICY "service_role_demo_users_full_access" ON users
  FOR ALL 
  TO service_role
  USING (id = 'demo-user-12345678-1234-1234-1234-123456789012')
  WITH CHECK (id = 'demo-user-12345678-1234-1234-1234-123456789012');

CREATE POLICY "service_role_demo_summaries_full_access" ON summaries
  FOR ALL 
  TO service_role
  USING (
    user_id = 'demo-user-12345678-1234-1234-1234-123456789012' AND
    organization_id = 'demo-org-12345678-1234-1234-1234-123456789012'
  )
  WITH CHECK (
    user_id = 'demo-user-12345678-1234-1234-1234-123456789012' AND
    organization_id = 'demo-org-12345678-1234-1234-1234-123456789012'
  );

-- =====================================================
-- SECURITY NOTES
-- =====================================================

-- These policies ensure that:
-- 1. Only demo organization data is publicly accessible
-- 2. All other user data remains protected by existing RLS policies
-- 3. Service role can perform full CRUD operations on demo data
-- 4. Anonymous and authenticated users can read demo data
-- 5. Anonymous users can insert new summaries (for upload demo)

-- To verify policies are working:
-- SELECT * FROM organizations WHERE id = 'demo-org-12345678-1234-1234-1234-123456789012';
-- SELECT * FROM summaries WHERE organization_id = 'demo-org-12345678-1234-1234-1234-123456789012';

-- =====================================================
-- CLEANUP COMMANDS (if needed)
-- =====================================================

-- To remove demo policies (uncomment if needed):
-- DROP POLICY IF EXISTS "public_demo_organizations_select" ON organizations;
-- DROP POLICY IF EXISTS "public_demo_users_select" ON users;
-- DROP POLICY IF EXISTS "public_demo_user_organizations_select" ON user_organizations;
-- DROP POLICY IF EXISTS "public_demo_summaries_select" ON summaries;
-- DROP POLICY IF EXISTS "public_demo_summaries_insert" ON summaries;
-- DROP POLICY IF EXISTS "public_demo_slack_integrations_select" ON slack_integrations;
-- DROP POLICY IF EXISTS "service_role_demo_full_access" ON organizations;
-- DROP POLICY IF EXISTS "service_role_demo_users_full_access" ON users;
-- DROP POLICY IF EXISTS "service_role_demo_summaries_full_access" ON summaries;
