import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { getCurrentUser } from '@/lib/user-management';
import { slackTokenManager, SlackTokenData } from '@/lib/slack-token-manager';
import { logAuditEvent } from '@/lib/audit-logger';

export async function GET(request: NextRequest) {
  try {
  devLog.log('🔗 Slack OAuth callback');

    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;

    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    // Handle OAuth errors
    if (error) {
      console.error('Slack OAuth error:', error);
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=oauth_failed`);
    }

    if (!code) {
      console.error('Missing authorization code');
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=missing_code`);
    }

    // Verify state parameter (basic security check)
    if (state && !state.startsWith(userId)) {
      console.error('Invalid state parameter');
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=invalid_state`);
    }
  devLog.log('🔗 Processing OAuth callback for user:', userId);

    // Exchange code for access token
    const slackClientId = process.env.SLACK_CLIENT_ID;
    const slackClientSecret = process.env.SLACK_CLIENT_SECRET;
    const redirectUri = `${process.env.NEXT_PUBLIC_SITE_URL}/api/slack/oauth/callback`;

    if (!slackClientId || !slackClientSecret) {
      console.error('Missing Slack credentials');
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=config_error`);
    }

    const tokenResponse = await fetch('https://slack.com/api/oauth.v2.access', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        code,
        client_id: slackClientId,
        client_secret: slackClientSecret,
        redirect_uri: redirectUri,
      }),
    });

    if (!tokenResponse.ok) {
      console.error('Slack token exchange failed:', await tokenResponse.text());
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=token_exchange_failed`);
    }

    const tokenData = await tokenResponse.json();

    if (!tokenData.ok) {
      console.error('Slack API error:', tokenData.error);
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=slack_api_error`);
    }

    // Extract relevant data
    const {
      access_token,
      refresh_token,
      expires_in,
      team,
      authed_user,
      scope,
      bot_user_id,
      app_id,
      enterprise,
      is_enterprise_install
    } = tokenData;

    // Prepare secure token data
    const slackTokenData: SlackTokenData = {
      access_token,
      refresh_token,
      expires_at: expires_in ? Math.floor(Date.now() / 1000) + expires_in : undefined,
      scope,
      team_id: team.id,
      team_name: team.name,
      user_id: authed_user.id,
      user_name: authed_user.name,
      bot_user_id,
      app_id,
      enterprise_id: enterprise?.id,
      is_enterprise_install
    };

    // Store token securely using the token manager
    try {
      await slackTokenManager.store(userId, slackTokenData);
    } catch (error) {
      console.error('Failed to store Slack token securely:', error);

      await logAuditEvent({
        event_type: 'SLACK_ERROR',
        user_id: userId,
        action: 'Failed to store Slack OAuth token',
        success: false,
        error_message: error instanceof Error ? error.message : 'Unknown error',
        metadata: { team_id: team.id, team_name: team.name }
      });

      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=token_storage_failed`);
    }

    // Create Supabase client for integration metadata
    const supabase = await createSupabaseServerClient();

    // Store integration metadata (non-sensitive data only)
    const { data: integration, error: integrationError } = await supabase
      .from('slack_integrations')
      .upsert({
        user_id: userId,
        team_id: team.id,
        team_name: team.name,
        scope,
        bot_user_id,
        authed_user_id: authed_user.id,
        is_active: true,
        is_enterprise: is_enterprise_install || false,
        enterprise_id: enterprise?.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (integrationError) {
      console.error('Failed to store Slack integration metadata:', integrationError);
      // Don't fail the entire flow for metadata storage issues
    }

    // Create a notification for the user
    await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        type: 'slack_connected',
        title: '🎉 Slack Connected Successfully',
        message: `Your Slack workspace "${team.name}" has been connected. You can now generate summaries from your Slack conversations.`,
        data: {
          team_id: team.id,
          team_name: team.name,
        },
      });

    // Auto-complete onboarding step if applicable
    try {
      await supabase.rpc('complete_onboarding_step', {
        p_user_id: userId,
        p_step_name: 'connect_slack',
        p_step_data: { 
          team_id: team.id,
          team_name: team.name,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.warn('Failed to complete onboarding step:', error);
      // Non-critical error, continue
    }
  devLog.log('✅ Slack integration successful:', {
      userId,
      teamId: team.id,
      teamName: team.name
    });

    // Redirect back to settings with success message
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?success=slack_connected&team=${encodeURIComponent(team.name)}`);

  } catch (error) {
    console.error('Slack OAuth callback error:', error);

    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=internal_error`);
  }
}
