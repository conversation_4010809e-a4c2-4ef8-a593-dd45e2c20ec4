# 🌐 API Ecosystem

## Overview
Comprehensive API documentation, webhook management, and OpenAPI specification generation for enterprise integrations.

## Features
- ✅ Auto-generated OpenAPI specs from Zod schemas
- ✅ Interactive API documentation with Swagger UI
- ✅ Webhook management and validation
- ✅ Rate limiting and authentication
- ✅ API versioning and deprecation handling
- 🟡 GraphQL endpoint (planned)
- 🔴 Public API marketplace (future)

## Module Structure
```
/features/api/
├── README.md                   # This file
├── documentation/
│   ├── openapi-generator.ts   # Auto-generate OpenAPI specs
│   ├── swagger-ui.tsx         # Interactive API docs
│   └── api-reference.md       # Human-readable API docs
├── webhooks/
│   ├── webhook-manager.ts     # Webhook registration & management
│   ├── webhook-validator.ts   # Webhook signature validation
│   ├── handlers/              # Webhook event handlers
│   │   ├── stripe.handler.ts
│   │   ├── slack.handler.ts
│   │   └── notion.handler.ts
│   └── types.ts              # Webhook types and schemas
├── middleware/
│   ├── auth.middleware.ts     # API authentication
│   ├── rate-limit.middleware.ts # Rate limiting
│   ├── validation.middleware.ts # Request validation
│   └── cors.middleware.ts     # CORS configuration
├── versioning/
│   ├── version-manager.ts     # API version management
│   ├── deprecation.ts         # Deprecation warnings
│   └── migration-guide.md     # Version migration guide
├── schemas/
│   ├── api-schemas.ts         # Zod schemas for all endpoints
│   ├── webhook-schemas.ts     # Webhook payload schemas
│   └── response-schemas.ts    # Standard response formats
└── utils/
    ├── response-helpers.ts    # Standardized API responses
    ├── error-handlers.ts      # Error handling utilities
    └── pagination.ts          # Pagination utilities
```

## OpenAPI Specification Generation

### Auto-Generated Specs
```typescript
// features/api/documentation/openapi-generator.ts
import { z } from 'zod';
import { OpenAPIRegistry, OpenApiGeneratorV3 } from '@asteasolutions/zod-to-openapi';

// Import all API schemas
import { 
  CreateSummarySchema,
  UpdateSummarySchema,
  SummaryResponseSchema,
  ListSummariesSchema 
} from '../schemas/api-schemas';

const registry = new OpenAPIRegistry();

// Register summary endpoints
registry.registerPath({
  method: 'post',
  path: '/api/summaries',
  description: 'Create a new summary',
  summary: 'Create Summary',
  tags: ['Summaries'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: CreateSummarySchema,
        },
      },
    },
  },
  responses: {
    201: {
      description: 'Summary created successfully',
      content: {
        'application/json': {
          schema: SummaryResponseSchema,
        },
      },
    },
    400: {
      description: 'Invalid request data',
      content: {
        'application/json': {
          schema: z.object({
            success: z.literal(false),
            error: z.string(),
            details: z.array(z.string()).optional(),
          }),
        },
      },
    },
    401: {
      description: 'Unauthorized',
    },
    429: {
      description: 'Rate limit exceeded',
    },
  },
  security: [{ bearerAuth: [] }],
});

registry.registerPath({
  method: 'get',
  path: '/api/summaries',
  description: 'List user summaries with pagination',
  summary: 'List Summaries',
  tags: ['Summaries'],
  request: {
    query: ListSummariesSchema,
  },
  responses: {
    200: {
      description: 'Summaries retrieved successfully',
      content: {
        'application/json': {
          schema: z.object({
            success: z.literal(true),
            data: z.object({
              summaries: z.array(SummaryResponseSchema),
              pagination: z.object({
                page: z.number(),
                limit: z.number(),
                total: z.number(),
                pages: z.number(),
              }),
            }),
          }),
        },
      },
    },
  },
  security: [{ bearerAuth: [] }],
});

// Security schemes
registry.registerComponent('securitySchemes', 'bearerAuth', {
  type: 'http',
  scheme: 'bearer',
  bearerFormat: 'JWT',
});

export function generateOpenAPISpec() {
  const generator = new OpenApiGeneratorV3(registry.definitions);
  
  return generator.generateDocument({
    openapi: '3.0.0',
    info: {
      version: '1.0.0',
      title: 'Slack Summary Scribe API',
      description: 'Enterprise API for AI-powered Slack summary generation',
      contact: {
        name: 'API Support',
        email: '<EMAIL>',
        url: 'https://slacksummaryscribe.com/support',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: 'https://api.slacksummaryscribe.com',
        description: 'Production server',
      },
      {
        url: 'https://staging-api.slacksummaryscribe.com',
        description: 'Staging server',
      },
    ],
    tags: [
      {
        name: 'Summaries',
        description: 'Summary generation and management',
      },
      {
        name: 'Organizations',
        description: 'Organization and team management',
      },
      {
        name: 'Integrations',
        description: 'Third-party service integrations',
      },
      {
        name: 'Webhooks',
        description: 'Webhook management and events',
      },
    ],
  });
}

// Generate and save OpenAPI spec
export async function saveOpenAPISpec() {
  const spec = generateOpenAPISpec();
  
  // Save to public directory for API docs
  const fs = await import('fs/promises');
  await fs.writeFile(
    './public/api/openapi.json',
    JSON.stringify(spec, null, 2)
  );
  
  return spec;
}
```

### API Schemas
```typescript
// features/api/schemas/api-schemas.ts
import { z } from 'zod';

// Base schemas
export const PaginationSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
});

export const DateRangeSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

// Summary schemas
export const CreateSummarySchema = z.object({
  title: z.string().min(1).max(200),
  content: z.string().min(1),
  sourceType: z.enum(['slack', 'pdf', 'text']),
  sourceData: z.record(z.any()).optional(),
  tags: z.array(z.string()).optional(),
  templateId: z.string().uuid().optional(),
});

export const UpdateSummarySchema = CreateSummarySchema.partial();

export const SummaryResponseSchema = z.object({
  id: z.string().uuid(),
  title: z.string(),
  content: z.string(),
  sourceType: z.string(),
  sourceData: z.record(z.any()),
  tags: z.array(z.string()),
  wordCount: z.number(),
  aiModel: z.string().optional(),
  processingTime: z.number().optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const ListSummariesSchema = PaginationSchema.extend({
  search: z.string().optional(),
  tags: z.array(z.string()).optional(),
  sourceType: z.enum(['slack', 'pdf', 'text']).optional(),
}).merge(DateRangeSchema);

// Organization schemas
export const CreateOrganizationSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
});

export const OrganizationResponseSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  description: z.string().optional(),
  plan: z.enum(['FREE', 'PRO', 'ENTERPRISE']),
  memberCount: z.number(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Integration schemas
export const CreateIntegrationSchema = z.object({
  provider: z.enum(['notion', 'google-drive', 'dropbox', 'slack']),
  configuration: z.record(z.any()),
});

export const IntegrationResponseSchema = z.object({
  id: z.string().uuid(),
  provider: z.string(),
  status: z.enum(['active', 'inactive', 'error']),
  lastSyncAt: z.string().datetime().optional(),
  createdAt: z.string().datetime(),
});

// Webhook schemas
export const WebhookEventSchema = z.object({
  id: z.string().uuid(),
  type: z.string(),
  data: z.record(z.any()),
  timestamp: z.string().datetime(),
});

export const CreateWebhookSchema = z.object({
  url: z.string().url(),
  events: z.array(z.string()),
  secret: z.string().optional(),
});

// Standard API response schemas
export const SuccessResponseSchema = z.object({
  success: z.literal(true),
  data: z.any(),
  metadata: z.record(z.any()).optional(),
});

export const ErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  details: z.array(z.string()).optional(),
  code: z.string().optional(),
});

export const ApiResponseSchema = z.union([
  SuccessResponseSchema,
  ErrorResponseSchema,
]);
```

## Webhook Management System

### Webhook Manager
```typescript
// features/api/webhooks/webhook-manager.ts
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { validateWebhookSignature } from './webhook-validator';

export interface WebhookEndpoint {
  id: string;
  url: string;
  events: string[];
  secret?: string;
  enabled: boolean;
  organizationId: string;
  createdAt: string;
}

export interface WebhookEvent {
  id: string;
  type: string;
  data: Record<string, any>;
  timestamp: string;
  organizationId?: string;
}

export interface WebhookDelivery {
  id: string;
  webhookId: string;
  eventId: string;
  status: 'pending' | 'delivered' | 'failed';
  attempts: number;
  lastAttemptAt?: string;
  nextRetryAt?: string;
  responseCode?: number;
  responseBody?: string;
  errorMessage?: string;
}

/**
 * Register a new webhook endpoint
 */
export async function registerWebhook(
  organizationId: string,
  webhook: Omit<WebhookEndpoint, 'id' | 'organizationId' | 'createdAt'>
): Promise<{ success: boolean; webhookId?: string; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Validate webhook URL is reachable
    const isValid = await validateWebhookUrl(webhook.url);
    if (!isValid) {
      return { success: false, error: 'Webhook URL is not reachable' };
    }

    const { data, error } = await supabase
      .from('webhook_endpoints')
      .insert({
        organization_id: organizationId,
        url: webhook.url,
        events: webhook.events,
        secret: webhook.secret,
        enabled: webhook.enabled,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, webhookId: data.id };

  } catch (error) {
    console.error('Failed to register webhook:', error);
    return { success: false, error: 'Failed to register webhook' };
  }
}

/**
 * Send webhook event to all registered endpoints
 */
export async function sendWebhookEvent(
  event: WebhookEvent
): Promise<{ success: boolean; deliveries: number; errors: string[] }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get all webhook endpoints that should receive this event
    let query = supabase
      .from('webhook_endpoints')
      .select('*')
      .eq('enabled', true)
      .contains('events', [event.type]);

    if (event.organizationId) {
      query = query.eq('organization_id', event.organizationId);
    }

    const { data: webhooks, error } = await query;

    if (error || !webhooks) {
      return { success: false, deliveries: 0, errors: [error?.message || 'No webhooks found'] };
    }

    const deliveries: Promise<any>[] = [];
    const errors: string[] = [];

    // Send to each webhook endpoint
    for (const webhook of webhooks) {
      deliveries.push(
        deliverWebhook(webhook, event).catch(error => {
          errors.push(`Webhook ${webhook.id}: ${error.message}`);
        })
      );
    }

    await Promise.allSettled(deliveries);

    return {
      success: true,
      deliveries: webhooks.length,
      errors
    };

  } catch (error) {
    console.error('Failed to send webhook event:', error);
    return { success: false, deliveries: 0, errors: [error instanceof Error ? error.message : 'Unknown error'] };
  }
}

/**
 * Deliver webhook to specific endpoint
 */
async function deliverWebhook(
  webhook: any,
  event: WebhookEvent
): Promise<void> {
  const supabase = await createSupabaseServerClient();
  
  try {
    // Create delivery record
    const { data: delivery, error: deliveryError } = await supabase
      .from('webhook_deliveries')
      .insert({
        webhook_id: webhook.id,
        event_id: event.id,
        status: 'pending',
        attempts: 0,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (deliveryError) {
      throw new Error(`Failed to create delivery record: ${deliveryError.message}`);
    }

    // Prepare webhook payload
    const payload = {
      id: event.id,
      type: event.type,
      data: event.data,
      timestamp: event.timestamp,
    };

    // Generate signature if secret is provided
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'SlackSummaryScribe-Webhook/1.0',
    };

    if (webhook.secret) {
      const signature = await generateWebhookSignature(
        JSON.stringify(payload),
        webhook.secret
      );
      headers['X-Webhook-Signature'] = signature;
    }

    // Send webhook
    const response = await fetch(webhook.url, {
      method: 'POST',
      headers,
      body: JSON.stringify(payload),
      signal: AbortSignal.timeout(30000), // 30 second timeout
    });

    // Update delivery record
    await supabase
      .from('webhook_deliveries')
      .update({
        status: response.ok ? 'delivered' : 'failed',
        attempts: 1,
        last_attempt_at: new Date().toISOString(),
        response_code: response.status,
        response_body: await response.text().catch(() => ''),
        error_message: response.ok ? null : `HTTP ${response.status}`,
      })
      .eq('id', delivery.id);

    if (!response.ok) {
      throw new Error(`Webhook delivery failed: HTTP ${response.status}`);
    }

  } catch (error) {
    // Update delivery record with error
    await supabase
      .from('webhook_deliveries')
      .update({
        status: 'failed',
        attempts: 1,
        last_attempt_at: new Date().toISOString(),
        error_message: error instanceof Error ? error.message : 'Unknown error',
      })
      .eq('webhook_id', webhook.id)
      .eq('event_id', event.id);

    throw error;
  }
}

/**
 * Validate webhook URL is reachable
 */
async function validateWebhookUrl(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, {
      method: 'HEAD',
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });
    
    return response.status < 500; // Accept any non-server error
  } catch {
    return false;
  }
}

/**
 * Generate webhook signature
 */
async function generateWebhookSignature(
  payload: string,
  secret: string
): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  
  const signature = await crypto.subtle.sign(
    'HMAC',
    key,
    encoder.encode(payload)
  );
  
  const hashArray = Array.from(new Uint8Array(signature));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  
  return `sha256=${hashHex}`;
}

/**
 * List webhook endpoints for organization
 */
export async function listWebhooks(
  organizationId: string
): Promise<{ webhooks: WebhookEndpoint[]; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { data: webhooks, error } = await supabase
      .from('webhook_endpoints')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });

    if (error) {
      return { webhooks: [], error: error.message };
    }

    const formattedWebhooks: WebhookEndpoint[] = webhooks.map(webhook => ({
      id: webhook.id,
      url: webhook.url,
      events: webhook.events,
      secret: webhook.secret ? '***' : undefined, // Hide secret in response
      enabled: webhook.enabled,
      organizationId: webhook.organization_id,
      createdAt: webhook.created_at,
    }));

    return { webhooks: formattedWebhooks };

  } catch (error) {
    console.error('Failed to list webhooks:', error);
    return { webhooks: [], error: 'Failed to list webhooks' };
  }
}

/**
 * Update webhook endpoint
 */
export async function updateWebhook(
  webhookId: string,
  updates: Partial<Pick<WebhookEndpoint, 'url' | 'events' | 'enabled'>>
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { error } = await supabase
      .from('webhook_endpoints')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', webhookId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };

  } catch (error) {
    console.error('Failed to update webhook:', error);
    return { success: false, error: 'Failed to update webhook' };
  }
}

/**
 * Delete webhook endpoint
 */
export async function deleteWebhook(
  webhookId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { error } = await supabase
      .from('webhook_endpoints')
      .delete()
      .eq('id', webhookId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };

  } catch (error) {
    console.error('Failed to delete webhook:', error);
    return { success: false, error: 'Failed to delete webhook' };
  }
}
