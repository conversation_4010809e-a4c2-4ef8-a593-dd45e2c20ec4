'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Zap, Crown, Sparkles } from 'lucide-react';
import { PRICING_PLANS } from '@/lib/stripe-config';
import { getCurrentUserClient } from '@/lib/user-management';
import { toast } from 'sonner';

interface PricingPlansProps {
  currentTier?: 'FREE' | 'PRO' | 'ENTERPRISE';
  onUpgrade?: (tier: 'PRO' | 'ENTERPRISE') => void;
}

export function PricingPlans({ currentTier = 'FREE', onUpgrade }: PricingPlansProps) {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { getCurrentUserClient } = await import('@/lib/user-management-client');
        const currentUser = await getCurrentUserClient();
        setUser(currentUser);
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    };

    fetchUser();
  }, []);

  const handleUpgrade = async (tier: 'PRO' | 'ENTERPRISE') => {
    if (!user) {
      toast.error('Please sign in to upgrade your plan');
      return;
    }

    setLoading(tier);
    
    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tier }),
      });

      const data = await response.json();

      if (data.success && data.checkout_url) {
        // Redirect to Stripe Checkout
        window.location.href = data.checkout_url;
      } else {
        toast.error(data.error || 'Failed to create checkout session');
      }
    } catch (error) {
      console.error('Upgrade error:', error);
      toast.error('Failed to start upgrade process');
    } finally {
      setLoading(null);
    }

    if (onUpgrade) {
      onUpgrade(tier);
    }
  };

  const plans = [
    {
      key: 'FREE' as const,
      ...PRICING_PLANS.FREE,
      icon: <Sparkles className="h-6 w-6" />,
      popular: false,
      buttonText: 'Current Plan',
      buttonVariant: 'outline' as const,
    },
    {
      key: 'PRO' as const,
      ...PRICING_PLANS.PRO,
      icon: <Zap className="h-6 w-6" />,
      popular: true,
      buttonText: 'Upgrade to Pro',
      buttonVariant: 'default' as const,
    },
    {
      key: 'ENTERPRISE' as const,
      ...PRICING_PLANS.ENTERPRISE,
      icon: <Crown className="h-6 w-6" />,
      popular: false,
      buttonText: 'Upgrade to Enterprise',
      buttonVariant: 'outline' as const,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
      {plans.map((plan) => {
        const isCurrentPlan = currentTier === plan.key;
        const isDowngrade = currentTier === 'ENTERPRISE' && plan.key !== 'ENTERPRISE';
        const isUpgrade = (currentTier === 'FREE' && plan.key !== 'FREE') || 
                         (currentTier === 'PRO' && plan.key === 'ENTERPRISE');

        return (
          <Card 
            key={plan.key} 
            className={`relative ${plan.popular ? 'border-primary shadow-lg scale-105' : ''} ${isCurrentPlan ? 'ring-2 ring-primary' : ''}`}
          >
            {plan.popular && (
              <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-primary">
                Most Popular
              </Badge>
            )}
            
            <CardHeader className="text-center pb-4">
              <div className="flex items-center justify-center mb-2">
                {plan.icon}
              </div>
              <CardTitle className="text-2xl">{plan.name}</CardTitle>
              <CardDescription>{plan.features[0]}</CardDescription>
              <div className="mt-4">
                <span className="text-4xl font-bold">${plan.price}</span>
                <span className="text-muted-foreground">/{plan.interval}</span>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Features:</h4>
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Limits:</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>
                    Monthly Summaries: {plan.limits.monthlySummaries === -1 ? 'Unlimited' : plan.limits.monthlySummaries}
                  </li>
                  <li>
                    AI Models: {plan.limits.aiModels.join(', ')}
                  </li>
                </ul>
              </div>
            </CardContent>

            <CardFooter>
              <Button
                className="w-full"
                variant={isCurrentPlan ? 'outline' : plan.buttonVariant}
                disabled={isCurrentPlan || isDowngrade || loading === plan.key}
                onClick={() => !isCurrentPlan && isUpgrade && handleUpgrade(plan.key as 'PRO' | 'ENTERPRISE')}
              >
                {loading === plan.key ? (
                  'Processing...'
                ) : isCurrentPlan ? (
                  'Current Plan'
                ) : isDowngrade ? (
                  'Downgrade'
                ) : (
                  plan.buttonText
                )}
              </Button>
            </CardFooter>
          </Card>
        );
      })}
    </div>
  );
}

export default PricingPlans;
