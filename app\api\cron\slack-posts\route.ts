import { devLog } from '@/lib/console-cleaner';
/**
 * Slack Posts Cron Job API Route
 * Processes scheduled Slack posts (called by Vercel Cron or external scheduler)
 */

import { NextRequest, NextResponse } from 'next/server';
import { processDuePosts, healthCheck } from '@/lib/slack-post-processor';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * POST /api/cron/slack-posts
 * Process due scheduled posts
 */
export async function POST(request: NextRequest) {
  try {
    // Verify cron secret for security
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      console.warn('Unauthorized cron job attempt');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
  devLog.log('🚀 Starting scheduled posts processing...');
    const startTime = Date.now();

    // Process due posts
    const results = await processDuePosts();
    
    const processingTime = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;
  devLog.log(`✅ Cron job completed in ${processingTime}ms: ${successCount} successful, ${failureCount} failed`);

    // Log to Sentry for monitoring
    SentryTracker.addAPIBreadcrumb('POST', '/api/cron/slack-posts', {
      totalPosts: results.length,
      successCount,
      failureCount,
      processingTime,
    });

    return NextResponse.json({
      success: true,
      message: 'Scheduled posts processed successfully',
      stats: {
        total_posts: results.length,
        successful: successCount,
        failed: failureCount,
        processing_time_ms: processingTime,
      },
      results: results.map(r => ({
        post_id: r.postId,
        success: r.success,
        message_ts: r.messageTs,
        error: r.error,
      })),
    });

  } catch (error) {
    console.error('Cron job error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { 
        error: 'Cron job failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/cron/slack-posts
 * Health check for the cron job
 */
export async function GET() {
  try {
    const health = await healthCheck();
    
    return NextResponse.json({
      success: true,
      healthy: health.healthy,
      message: health.message,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Health check error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { 
        success: false,
        healthy: false,
        message: error instanceof Error ? error.message : 'Health check failed',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
