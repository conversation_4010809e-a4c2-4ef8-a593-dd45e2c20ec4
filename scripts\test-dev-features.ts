#!/usr/bin/env tsx

/**
 * Development Feature Testing Script
 * Tests all SaaS features in local development environment
 */

import fs from 'fs';
import path from 'path';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  duration?: number;
}

/**
 * Test API endpoint
 */
async function testAPIEndpoint(
  name: string,
  url: string,
  options: RequestInit = {},
  expectedStatus: number = 200
): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });
    
    const duration = Date.now() - startTime;
    
    if (response.status === expectedStatus) {
      return {
        name,
        passed: true,
        message: `✅ ${name} (${duration}ms)`,
        duration,
      };
    } else {
      return {
        name,
        passed: false,
        message: `❌ ${name} - Expected ${expectedStatus}, got ${response.status}`,
        duration,
      };
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    return {
      name,
      passed: false,
      message: `❌ ${name} - ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration,
    };
  }
}

/**
 * Test dashboard data loading
 */
async function testDashboardData(baseUrl: string): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${baseUrl}/api/dashboard`);
    const data = await response.json();
    const duration = Date.now() - startTime;
    
    if (response.ok && data.summaries !== undefined && data.workspaces !== undefined) {
      return {
        name: 'Dashboard Data',
        passed: true,
        message: `✅ Dashboard Data - ${data.summaries.length} summaries, ${data.workspaces.length} workspaces (${duration}ms)`,
        duration,
      };
    } else {
      return {
        name: 'Dashboard Data',
        passed: false,
        message: `❌ Dashboard Data - Invalid response structure`,
        duration,
      };
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    return {
      name: 'Dashboard Data',
      passed: false,
      message: `❌ Dashboard Data - ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration,
    };
  }
}

/**
 * Test AI summarization
 */
async function testAISummarization(baseUrl: string): Promise<TestResult> {
  const startTime = Date.now();
  
  const testTranscript = `
    John: Hey team, let's discuss the Q4 roadmap.
    Sarah: I think we should focus on the new dashboard features.
    Mike: Agreed, and we need to improve the mobile experience.
    John: Great points. Let's prioritize those items.
  `;
  
  try {
    const response = await fetch(`${baseUrl}/api/summarize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        transcript: testTranscript,
        title: 'Q4 Roadmap Discussion',
      }),
    });
    
    const data = await response.json();
    const duration = Date.now() - startTime;
    
    if (response.ok && data.summary) {
      return {
        name: 'AI Summarization',
        passed: true,
        message: `✅ AI Summarization - Generated summary (${duration}ms)`,
        duration,
      };
    } else {
      return {
        name: 'AI Summarization',
        passed: false,
        message: `❌ AI Summarization - ${data.error || 'No summary generated'}`,
        duration,
      };
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    return {
      name: 'AI Summarization',
      passed: false,
      message: `❌ AI Summarization - ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration,
    };
  }
}

/**
 * Test file upload endpoint
 */
async function testFileUpload(baseUrl: string): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    // Create a simple test file
    const testContent = 'This is a test document for upload functionality.';
    const blob = new Blob([testContent], { type: 'text/plain' });
    const formData = new FormData();
    formData.append('file', blob, 'test.txt');
    
    const response = await fetch(`${baseUrl}/api/upload`, {
      method: 'POST',
      body: formData,
    });
    
    const data = await response.json();
    const duration = Date.now() - startTime;
    
    if (response.ok && data.id) {
      return {
        name: 'File Upload',
        passed: true,
        message: `✅ File Upload - Upload ID: ${data.id} (${duration}ms)`,
        duration,
      };
    } else {
      return {
        name: 'File Upload',
        passed: false,
        message: `❌ File Upload - ${data.error || 'Upload failed'}`,
        duration,
      };
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    return {
      name: 'File Upload',
      passed: false,
      message: `❌ File Upload - ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration,
    };
  }
}

/**
 * Test export functionality
 */
async function testExportFunctionality(baseUrl: string): Promise<TestResult[]> {
  const results: TestResult[] = [];
  const exportFormats = ['pdf', 'excel', 'notion'];
  
  for (const format of exportFormats) {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${baseUrl}/api/export/${format}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          summaryId: 'test-summary-id',
          title: 'Test Export',
          content: 'This is a test export.',
        }),
      });
      
      const duration = Date.now() - startTime;
      
      if (response.ok) {
        results.push({
          name: `${format.toUpperCase()} Export`,
          passed: true,
          message: `✅ ${format.toUpperCase()} Export (${duration}ms)`,
          duration,
        });
      } else {
        results.push({
          name: `${format.toUpperCase()} Export`,
          passed: false,
          message: `❌ ${format.toUpperCase()} Export - Status ${response.status}`,
          duration,
        });
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      results.push({
        name: `${format.toUpperCase()} Export`,
        passed: false,
        message: `❌ ${format.toUpperCase()} Export - ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration,
      });
    }
  }
  
  return results;
}

/**
 * Main testing function
 */
async function main() {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  
  log('\n' + '='.repeat(60), 'cyan');
  log('🧪 DEVELOPMENT FEATURE TESTING', 'cyan');
  log('='.repeat(60), 'cyan');
  log(`Testing against: ${baseUrl}\n`, 'bright');
  
  const allResults: TestResult[] = [];
  
  // 1. Basic API Health Check
  log('🔍 Testing Basic API Health...', 'blue');
  const healthResult = await testAPIEndpoint('Health Check', `${baseUrl}/api/health`);
  allResults.push(healthResult);
  log(`  ${healthResult.message}`);
  
  // 2. Dashboard Data
  log('\n📊 Testing Dashboard Data...', 'blue');
  const dashboardResult = await testDashboardData(baseUrl);
  allResults.push(dashboardResult);
  log(`  ${dashboardResult.message}`);
  
  // 3. AI Summarization
  log('\n🤖 Testing AI Summarization...', 'blue');
  const aiResult = await testAISummarization(baseUrl);
  allResults.push(aiResult);
  log(`  ${aiResult.message}`);
  
  // 4. File Upload
  log('\n📁 Testing File Upload...', 'blue');
  const uploadResult = await testFileUpload(baseUrl);
  allResults.push(uploadResult);
  log(`  ${uploadResult.message}`);
  
  // 5. Export Functionality
  log('\n📤 Testing Export Functionality...', 'blue');
  const exportResults = await testExportFunctionality(baseUrl);
  allResults.push(...exportResults);
  exportResults.forEach(result => log(`  ${result.message}`));
  
  // 6. Additional API Endpoints
  log('\n🔌 Testing Additional API Endpoints...', 'blue');
  const additionalTests = [
    await testAPIEndpoint('Summaries API', `${baseUrl}/api/summaries`),
    await testAPIEndpoint('Analytics API', `${baseUrl}/api/analytics`),
    await testAPIEndpoint('Notifications API', `${baseUrl}/api/notifications`),
  ];
  allResults.push(...additionalTests);
  additionalTests.forEach(result => log(`  ${result.message}`));
  
  // Summary
  const passed = allResults.filter(r => r.passed).length;
  const failed = allResults.filter(r => !r.passed).length;
  const avgDuration = allResults.reduce((sum, r) => sum + (r.duration || 0), 0) / allResults.length;
  
  log('\n' + '='.repeat(60), 'cyan');
  log('📋 FEATURE TESTING SUMMARY', 'cyan');
  log('='.repeat(60), 'cyan');
  
  log(`✅ Passed: ${passed}`, 'green');
  log(`❌ Failed: ${failed}`, failed > 0 ? 'red' : 'green');
  log(`⏱️  Average Response Time: ${Math.round(avgDuration)}ms`, 'blue');
  
  if (failed === 0) {
    log('\n🎉 ALL FEATURES WORKING!', 'green');
    log('✅ Development environment is production-ready', 'green');
    log('🚀 Ready for full SaaS development and testing', 'cyan');
    process.exit(0);
  } else {
    log('\n🚨 SOME FEATURES NEED ATTENTION', 'yellow');
    log(`❌ ${failed} features failed testing`, 'red');
    log('\n🔧 Check the failed tests above and ensure:', 'yellow');
    log('   1. Development server is running', 'yellow');
    log('   2. All environment variables are configured', 'yellow');
    log('   3. External services are accessible', 'yellow');
    log('   4. Database is properly connected', 'yellow');
    process.exit(1);
  }
}

// Execute testing
main().catch((error) => {
  console.error('Feature testing failed:', error);
  process.exit(1);
});
