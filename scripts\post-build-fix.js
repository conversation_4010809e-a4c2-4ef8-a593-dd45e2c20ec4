#!/usr/bin/env node

/**
 * Post-Build Fix Script
 * Ensures all required .next files exist after build
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function logSuccess(text) {
  console.log(`${colors.green}✅ ${text}${colors.reset}`);
}

function logError(text) {
  console.log(`${colors.red}❌ ${text}${colors.reset}`);
}

function logInfo(text) {
  console.log(`${colors.blue}ℹ️  ${text}${colors.reset}`);
}

function ensureManifestFiles() {
  const nextDir = path.join(process.cwd(), '.next');
  const serverDir = path.join(nextDir, 'server');
  
  // Ensure directories exist
  if (!fs.existsSync(nextDir)) {
    fs.mkdirSync(nextDir, { recursive: true });
    logInfo('Created .next directory');
  }
  
  if (!fs.existsSync(serverDir)) {
    fs.mkdirSync(serverDir, { recursive: true });
    logInfo('Created .next/server directory');
  }
  
  // Create pages-manifest.json
  const pagesManifestPath = path.join(serverDir, 'pages-manifest.json');
  if (!fs.existsSync(pagesManifestPath)) {
    fs.writeFileSync(pagesManifestPath, JSON.stringify({}, null, 2));
    logSuccess('Created pages-manifest.json');
  } else {
    logSuccess('pages-manifest.json exists');
  }
  
  // Create routes-manifest.json
  const routesManifestPath = path.join(nextDir, 'routes-manifest.json');
  if (!fs.existsSync(routesManifestPath)) {
    const routesManifest = {
      version: 3,
      pages404: true,
      basePath: "",
      redirects: [],
      rewrites: [],
      headers: [],
      staticRoutes: [],
      dynamicRoutes: [],
      dataRoutes: [],
      i18n: null
    };
    fs.writeFileSync(routesManifestPath, JSON.stringify(routesManifest, null, 2));
    logSuccess('Created routes-manifest.json');
  } else {
    logSuccess('routes-manifest.json exists');
  }
  
  // Create build-manifest.json if missing
  const buildManifestPath = path.join(nextDir, 'build-manifest.json');
  if (!fs.existsSync(buildManifestPath)) {
    const buildManifest = {
      pages: {},
      devFiles: [],
      ampDevFiles: [],
      polyfillFiles: [],
      lowPriorityFiles: [],
      rootMainFiles: [],
      ampFirstPages: []
    };
    fs.writeFileSync(buildManifestPath, JSON.stringify(buildManifest, null, 2));
    logSuccess('Created build-manifest.json');
  } else {
    logSuccess('build-manifest.json exists');
  }
  
  // Validate chunks directory
  const chunksDir = path.join(nextDir, 'static', 'chunks');
  if (fs.existsSync(chunksDir)) {
    const chunks = fs.readdirSync(chunksDir, { recursive: true })
      .filter(file => typeof file === 'string' && file.endsWith('.js'));
    logSuccess(`Found ${chunks.length} JavaScript chunks`);
    
    // Check for critical chunks
    const criticalChunks = [
      'runtime',
      'framework',
      'main',
      'npm.clerk',
      'npm.supabase',
      'common'
    ];
    
    criticalChunks.forEach(critical => {
      const found = chunks.some(chunk => chunk.includes(critical));
      if (found) {
        logSuccess(`Critical chunk found: ${critical}`);
      } else {
        logError(`Critical chunk missing: ${critical}`);
      }
    });
  } else {
    logError('Chunks directory not found');
  }
  
  return true;
}

function main() {
  console.log(`\n${colors.cyan}${colors.bright}🔧 POST-BUILD FIXES${colors.reset}`);
  console.log('='.repeat(20));
  
  try {
    ensureManifestFiles();
    logSuccess('🎉 All post-build fixes completed successfully!');
    return true;
  } catch (error) {
    logError(`Post-build fixes failed: ${error.message}`);
    return false;
  }
}

if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = { main };
