/**
 * Summary Form Component
 * 
 * Form for creating new summaries from meeting transcripts
 */

'use client';

import { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  MessageSquare, 
  Download, 
  Share2, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Zap,
  Users,
  Target,
  AlertTriangle,
  Star
} from 'lucide-react';
import { toast } from 'sonner';

interface SummaryFormProps {
  onSummaryCreated?: (summary: any) => void;
  onError?: (error: string) => void;
  placeholder?: string;
  maxLength?: number;
}

interface SummaryResult {
  id: string;
  summary: string;
  keyPoints: string[];
  actionItems: string[];
  redFlags: string[];
  skills: string[];
  speakers: string[];
  tags: string[];
  model: string;
  cost: number;
  tokens: number;
  processingTime: number;
}

export function SummaryForm({ 
  onSummaryCreated, 
  onError, 
  placeholder = "Paste your meeting transcript, conversation, or any text you'd like to summarize...",
  maxLength = 10000 
}: SummaryFormProps) {
  const [content, setContent] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState('');
  const [summary, setSummary] = useState<SummaryResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isExporting, setIsExporting] = useState(false);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim()) {
      toast.error('Please enter some content to summarize');
      return;
    }

    if (content.length > maxLength) {
      toast.error(`Content is too long. Maximum ${maxLength} characters allowed.`);
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setProgressMessage('Starting summarization...');
    setError(null);
    setSummary(null);

    try {
      const response = await fetch('/api/summarize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transcriptText: content,
          type: 'slack',
          context: 'meeting_summary'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate summary');
      }

      const data = await response.json();
      
      if (data.success && data.summary) {
        const summaryResult: SummaryResult = {
          id: data.summaryId,
          summary: data.summary.summary_text,
          keyPoints: data.summary.summary.keyPoints || [],
          actionItems: data.summary.summary.actions || [],
          redFlags: data.summary.summary.redFlags || [],
          skills: data.summary.summary.skills || [],
          speakers: data.summary.summary.participants || [],
          tags: [],
          model: data.model,
          cost: data.cost,
          tokens: data.tokens,
          processingTime: data.processingTime
        };

        setSummary(summaryResult);
        onSummaryCreated?.(summaryResult);
        
        // Send notification
        try {
          await fetch('/api/notifications', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              type: 'summary_complete',
              title: 'Summary Generated',
              message: `Successfully generated summary using ${data.model}`,
              userId: 'anonymous',
              summaryId: data.summaryId
            })
          });
        } catch (notificationError) {
          console.warn('Failed to send notification:', notificationError);
        }

        toast.success('Summary generated successfully!');
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate summary';
      setError(errorMessage);
      onError?.(errorMessage);
      
      // Send error notification
      try {
        await fetch('/api/notifications', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'summary_error',
            title: 'Summary Generation Failed',
            message: errorMessage,
            userId: 'anonymous',
            summaryId: 'unknown'
          })
        });
      } catch (notificationError) {
        console.warn('Failed to send error notification:', notificationError);
      }
    } finally {
      setIsProcessing(false);
      setProgress(100);
      setProgressMessage('Complete');
    }
  }, [content, maxLength, onSummaryCreated, onError]);

  const handleExport = useCallback(async (format: 'pdf' | 'excel' | 'notion') => {
    if (!summary) return;

    setIsExporting(true);
    
    try {
      if (format === 'notion') {
        toast.info('Notion export requires setup. Please configure your Notion integration.');
        return;
      }

      const response = await fetch('/api/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          summary,
          format,
          fileName: `summary-${Date.now()}`
        }),
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      if (format === 'pdf' || format === 'excel') {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `summary.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }

      toast.success(`${format.toUpperCase()} export completed!`);
      
      // Send export notification
      try {
        await fetch('/api/notifications', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'export_complete',
            title: 'Export Complete',
            message: `Successfully exported summary to ${format.toUpperCase()}`,
            userId: 'anonymous',
            summaryId: summary.id
          })
        });
      } catch (notificationError) {
        console.warn('Failed to send export notification:', notificationError);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Export failed';
      toast.error(errorMessage);
      
      // Send export error notification
      try {
        await fetch('/api/notifications', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'export_error',
            title: 'Export Failed',
            message: errorMessage,
            userId: 'anonymous',
            summaryId: summary.id
          })
        });
      } catch (notificationError) {
        console.warn('Failed to send export error notification:', notificationError);
      }
    } finally {
      setIsExporting(false);
    }
  }, [summary]);

  const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
  const charCount = content.length;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            AI Summary Generator
          </CardTitle>
          <CardDescription>
            Paste your content below and get an AI-powered summary with key insights, action items, and more.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder={placeholder}
                className="min-h-[200px] resize-none"
                disabled={isProcessing}
              />
              <div className="flex justify-between text-sm text-gray-500">
                <span>{wordCount} words</span>
                <span>{charCount}/{maxLength} characters</span>
              </div>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {isProcessing && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4 animate-spin" />
                  {progressMessage}
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            )}

            <Button 
              type="submit" 
              disabled={isProcessing || !content.trim()}
              className="w-full"
            >
              {isProcessing ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Generating Summary...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Generate Summary
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {summary && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Summary Generated
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{summary.model}</Badge>
                <Badge variant="outline">{summary.processingTime}ms</Badge>
              </div>
            </div>
            <CardDescription>
              AI-powered summary with key insights and actionable items
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="summary" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="insights">Insights</TabsTrigger>
                <TabsTrigger value="actions">Actions</TabsTrigger>
                <TabsTrigger value="export">Export</TabsTrigger>
              </TabsList>

              <TabsContent value="summary" className="space-y-4">
                <div className="prose max-w-none">
                  <p className="text-gray-700 leading-relaxed">{summary.summary}</p>
                </div>
              </TabsContent>

              <TabsContent value="insights" className="space-y-4">
                <div className="grid gap-4">
                  {summary.keyPoints.length > 0 && (
                    <div>
                      <h4 className="font-semibold flex items-center gap-2 mb-2">
                        <Star className="h-4 w-4" />
                        Key Points
                      </h4>
                      <ul className="space-y-1">
                        {summary.keyPoints.map((point, index) => (
                          <li key={index} className="text-sm text-gray-600">• {point}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {summary.skills.length > 0 && (
                    <div>
                      <h4 className="font-semibold flex items-center gap-2 mb-2">
                        <Users className="h-4 w-4" />
                        Skills Mentioned
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {summary.skills.map((skill, index) => (
                          <Badge key={index} variant="secondary">{skill}</Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {summary.redFlags.length > 0 && (
                    <div>
                      <h4 className="font-semibold flex items-center gap-2 mb-2">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        Red Flags
                      </h4>
                      <ul className="space-y-1">
                        {summary.redFlags.map((flag, index) => (
                          <li key={index} className="text-sm text-red-600">• {flag}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="actions" className="space-y-4">
                {summary.actionItems.length > 0 ? (
                  <div>
                    <h4 className="font-semibold flex items-center gap-2 mb-2">
                      <Target className="h-4 w-4" />
                      Action Items
                    </h4>
                    <ul className="space-y-2">
                      {summary.actionItems.map((item, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="w-4 h-4 rounded-full border-2 border-gray-300 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No action items identified</p>
                )}
              </TabsContent>

              <TabsContent value="export" className="space-y-4">
                <div>
                  <h4 className="font-semibold flex items-center gap-2 mb-4">
                    <Download className="h-4 w-4" />
                    Export Summary
                  </h4>
                  <div className="grid gap-2">
                    <Button
                      onClick={() => handleExport('pdf')}
                      disabled={isExporting}
                      variant="outline"
                      className="justify-start"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Export as PDF
                    </Button>
                    <Button
                      onClick={() => handleExport('excel')}
                      disabled={isExporting}
                      variant="outline"
                      className="justify-start"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Export as Excel
                    </Button>
                    <Button
                      onClick={() => handleExport('notion')}
                      disabled={isExporting}
                      variant="outline"
                      className="justify-start"
                    >
                      <Share2 className="h-4 w-4 mr-2" />
                      Export to Notion
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
