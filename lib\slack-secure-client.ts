/**
 * Secure Slack API Client
 * 
 * Provides secure access to Slack APIs using encrypted token storage
 * with automatic token refresh and comprehensive error handling.
 */

import { slackTokenManager, SlackTokenData } from './slack-token-manager';
import { logAuditEvent } from './audit-logger';

export interface SlackAPIResponse<T = any> {
  ok: boolean;
  error?: string;
  data?: T;
  warning?: string;
  response_metadata?: {
    next_cursor?: string;
    warnings?: string[];
  };
}

export interface SlackChannel {
  id: string;
  name: string;
  is_channel: boolean;
  is_group: boolean;
  is_im: boolean;
  is_mpim: boolean;
  is_private: boolean;
  created: number;
  is_archived: boolean;
  is_general: boolean;
  unlinked: number;
  name_normalized: string;
  is_shared: boolean;
  is_ext_shared: boolean;
  is_org_shared: boolean;
  pending_shared: string[];
  pending_connected_team_ids: string[];
  is_pending_ext_shared: boolean;
  is_member: boolean;
  is_open: boolean;
  topic: {
    value: string;
    creator: string;
    last_set: number;
  };
  purpose: {
    value: string;
    creator: string;
    last_set: number;
  };
  num_members?: number;
}

export interface SlackMessage {
  type: string;
  user?: string;
  text: string;
  ts: string;
  thread_ts?: string;
  reply_count?: number;
  replies?: Array<{
    user: string;
    ts: string;
  }>;
  reactions?: Array<{
    name: string;
    users: string[];
    count: number;
  }>;
  files?: Array<{
    id: string;
    name: string;
    title: string;
    mimetype: string;
    filetype: string;
    pretty_type: string;
    user: string;
    size: number;
    url_private: string;
    url_private_download: string;
    permalink: string;
    permalink_public: string;
  }>;
}

export interface SlackUser {
  id: string;
  team_id: string;
  name: string;
  deleted: boolean;
  color: string;
  real_name: string;
  tz: string;
  tz_label: string;
  tz_offset: number;
  profile: {
    title: string;
    phone: string;
    skype: string;
    real_name: string;
    real_name_normalized: string;
    display_name: string;
    display_name_normalized: string;
    fields: any;
    status_text: string;
    status_emoji: string;
    status_expiration: number;
    avatar_hash: string;
    image_original: string;
    is_custom_image: boolean;
    email: string;
    first_name: string;
    last_name: string;
    image_24: string;
    image_32: string;
    image_48: string;
    image_72: string;
    image_192: string;
    image_512: string;
    image_1024: string;
    status_text_canonical: string;
    team: string;
  };
  is_admin: boolean;
  is_owner: boolean;
  is_primary_owner: boolean;
  is_restricted: boolean;
  is_ultra_restricted: boolean;
  is_bot: boolean;
  is_app_user: boolean;
  updated: number;
  is_email_confirmed: boolean;
  who_can_share_contact_card: string;
}

/**
 * Secure Slack API Client
 */
export class SlackSecureClient {
  private userId: string;
  private teamId?: string;
  private tokenData?: SlackTokenData;
  
  constructor(userId: string, teamId?: string) {
    this.userId = userId;
    this.teamId = teamId;
  }
  
  /**
   * Initialize client with token
   */
  private async initialize(): Promise<void> {
    if (this.tokenData) {
      return; // Already initialized
    }
    
    try {
      this.tokenData = await slackTokenManager.get(this.userId, this.teamId);
      
      if (!this.tokenData) {
        throw new Error('No Slack token found for user');
      }
      
      // Validate token
      const isValid = await slackTokenManager.validate(this.userId, this.tokenData);
      if (!isValid) {
        throw new Error('Slack token is invalid');
      }
      
    } catch (error) {
      await logAuditEvent({
        event_type: 'SLACK_ERROR',
        user_id: this.userId,
        action: 'Failed to initialize Slack client',
        success: false,
        error_message: error instanceof Error ? error.message : 'Unknown error',
        metadata: { team_id: this.teamId }
      });
      
      throw error;
    }
  }
  
  /**
   * Make authenticated API request to Slack
   */
  private async makeRequest<T = any>(
    endpoint: string,
    options: {
      method?: string;
      body?: any;
      params?: Record<string, string>;
    } = {}
  ): Promise<SlackAPIResponse<T>> {
    await this.initialize();
    
    if (!this.tokenData) {
      throw new Error('Slack client not initialized');
    }
    
    try {
      const url = new URL(`https://slack.com/api/${endpoint}`);
      
      // Add query parameters
      if (options.params) {
        Object.entries(options.params).forEach(([key, value]) => {
          url.searchParams.append(key, value);
        });
      }
      
      const requestOptions: RequestInit = {
        method: options.method || 'GET',
        headers: {
          'Authorization': `Bearer ${this.tokenData.access_token}`,
          'Content-Type': 'application/json',
          'User-Agent': 'SlackSummaryScribe/1.0'
        }
      };
      
      if (options.body && (options.method === 'POST' || options.method === 'PUT')) {
        requestOptions.body = JSON.stringify(options.body);
      }
      
      const response = await fetch(url.toString(), requestOptions);
      const data = await response.json();
      
      // Handle rate limiting
      if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After');
        await logAuditEvent({
          event_type: 'SLACK_ERROR',
          user_id: this.userId,
          action: 'Slack API rate limit exceeded',
          success: false,
          metadata: { 
            endpoint,
            retry_after: retryAfter,
            team_id: this.tokenData.team_id
          }
        });
        
        throw new Error(`Rate limited. Retry after ${retryAfter} seconds`);
      }
      
      // Handle token errors
      if (!data.ok && (data.error === 'invalid_auth' || data.error === 'token_revoked')) {
        await logAuditEvent({
          event_type: 'SLACK_ERROR',
          user_id: this.userId,
          action: 'Slack token invalid or revoked',
          success: false,
          metadata: { 
            endpoint,
            error: data.error,
            team_id: this.tokenData.team_id
          }
        });
        
        // Remove invalid token
        await slackTokenManager.revoke(this.userId, this.tokenData.team_id);
        throw new Error('Slack token is invalid or revoked');
      }
      
      return {
        ok: data.ok,
        error: data.error,
        data: data.ok ? data : undefined,
        warning: data.warning,
        response_metadata: data.response_metadata
      };
      
    } catch (error) {
      await logAuditEvent({
        event_type: 'SLACK_ERROR',
        user_id: this.userId,
        action: 'Slack API request failed',
        success: false,
        error_message: error instanceof Error ? error.message : 'Unknown error',
        metadata: { 
          endpoint,
          team_id: this.tokenData?.team_id
        }
      });
      
      throw error;
    }
  }
  
  /**
   * Get list of channels
   */
  async getChannels(options: {
    excludeArchived?: boolean;
    types?: string;
    limit?: number;
    cursor?: string;
  } = {}): Promise<SlackAPIResponse<{ channels: SlackChannel[] }>> {
    const params: Record<string, string> = {
      exclude_archived: (options.excludeArchived ?? true).toString(),
      types: options.types || 'public_channel,private_channel',
      limit: (options.limit || 100).toString()
    };
    
    if (options.cursor) {
      params.cursor = options.cursor;
    }
    
    return this.makeRequest<{ channels: SlackChannel[] }>('conversations.list', {
      params
    });
  }
  
  /**
   * Get channel history
   */
  async getChannelHistory(
    channelId: string,
    options: {
      oldest?: string;
      latest?: string;
      limit?: number;
      cursor?: string;
      inclusive?: boolean;
    } = {}
  ): Promise<SlackAPIResponse<{ messages: SlackMessage[] }>> {
    const params: Record<string, string> = {
      channel: channelId,
      limit: (options.limit || 100).toString()
    };
    
    if (options.oldest) params.oldest = options.oldest;
    if (options.latest) params.latest = options.latest;
    if (options.cursor) params.cursor = options.cursor;
    if (options.inclusive !== undefined) params.inclusive = options.inclusive.toString();
    
    return this.makeRequest<{ messages: SlackMessage[] }>('conversations.history', {
      params
    });
  }
  
  /**
   * Get thread replies
   */
  async getThreadReplies(
    channelId: string,
    threadTs: string,
    options: {
      limit?: number;
      cursor?: string;
    } = {}
  ): Promise<SlackAPIResponse<{ messages: SlackMessage[] }>> {
    const params: Record<string, string> = {
      channel: channelId,
      ts: threadTs,
      limit: (options.limit || 100).toString()
    };
    
    if (options.cursor) params.cursor = options.cursor;
    
    return this.makeRequest<{ messages: SlackMessage[] }>('conversations.replies', {
      params
    });
  }
  
  /**
   * Get user information
   */
  async getUser(userId: string): Promise<SlackAPIResponse<{ user: SlackUser }>> {
    return this.makeRequest<{ user: SlackUser }>('users.info', {
      params: { user: userId }
    });
  }
  
  /**
   * Get multiple users
   */
  async getUsers(options: {
    limit?: number;
    cursor?: string;
  } = {}): Promise<SlackAPIResponse<{ members: SlackUser[] }>> {
    const params: Record<string, string> = {
      limit: (options.limit || 100).toString()
    };
    
    if (options.cursor) params.cursor = options.cursor;
    
    return this.makeRequest<{ members: SlackUser[] }>('users.list', {
      params
    });
  }
  
  /**
   * Test authentication
   */
  async testAuth(): Promise<SlackAPIResponse<{
    url: string;
    team: string;
    user: string;
    team_id: string;
    user_id: string;
    bot_id?: string;
  }>> {
    return this.makeRequest('auth.test');
  }
  
  /**
   * Get team information
   */
  async getTeamInfo(): Promise<SlackAPIResponse<{
    team: {
      id: string;
      name: string;
      domain: string;
      email_domain: string;
      icon: {
        image_34: string;
        image_44: string;
        image_68: string;
        image_88: string;
        image_102: string;
        image_132: string;
        image_230: string;
        image_default: boolean;
      };
      enterprise_id?: string;
      enterprise_name?: string;
    };
  }>> {
    return this.makeRequest('team.info');
  }
}

/**
 * Factory function to create secure Slack client
 */
export function createSlackClient(userId: string, teamId?: string): SlackSecureClient {
  return new SlackSecureClient(userId, teamId);
}

/**
 * Convenience functions for common Slack operations
 */
export const slackAPI = {
  /**
   * Get channels for user
   */
  getChannels: async (userId: string, teamId?: string, options?: any) => {
    const client = createSlackClient(userId, teamId);
    return client.getChannels(options);
  },
  
  /**
   * Get channel messages
   */
  getMessages: async (userId: string, channelId: string, teamId?: string, options?: any) => {
    const client = createSlackClient(userId, teamId);
    return client.getChannelHistory(channelId, options);
  },
  
  /**
   * Test user's Slack connection
   */
  testConnection: async (userId: string, teamId?: string) => {
    const client = createSlackClient(userId, teamId);
    return client.testAuth();
  }
};
