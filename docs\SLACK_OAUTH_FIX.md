# 🔧 Slack OAuth Distribution Fix Guide

## ❌ **Current Error**: `invalid_team_for_non_distributed_app`

This error occurs because your Slack app is not publicly distributed, meaning it can only be installed in the workspace where it was originally created.

---

## ✅ **SOLUTION: Enable Public Distribution**

### **Step 1: Access Slack App Management**
1. Go to **https://api.slack.com/apps**
2. Sign in with your Slack account
3. Click on your **"Slack Summary Scribe"** app (Client ID: *************.*************)

### **Step 2: Configure OAuth & Permissions**
1. In the left sidebar, click **"OAuth & Permissions"**
2. **Add Redirect URLs**:
   ```
   http://localhost:3000/auth/slack/callback
   http://localhost:3001/auth/slack/callback
   https://your-production-domain.com/auth/slack/callback
   ```

3. **Verify Bot Token Scopes**:
   - `channels:read` - View basic information about public channels
   - `chat:write` - Send messages as the app
   - `files:read` - View files shared in channels
   - `users:read` - View people in the workspace

4. **Verify User Token Scopes**:
   - `channels:read` - View basic information about public channels
   - `files:read` - View files shared in channels

### **Step 3: Enable Public Distribution**
1. In the left sidebar, click **"Manage Distribution"**
2. Review the checklist items:
   - ✅ App name and description are set
   - ✅ OAuth scopes are configured
   - ✅ Redirect URLs are set
   - ✅ App icon is uploaded (optional but recommended)

3. **Check the box**: "I've reviewed and completed the steps above"
4. Click **"Activate Public Distribution"**

### **Step 3: Update OAuth Settings (If Needed)**
1. Go to **"OAuth & Permissions"** in the sidebar
2. Verify **Redirect URLs** include:
   ```
   https://your-domain.com/auth/slack/callback
   http://localhost:3001/auth/slack/callback  (for development)
   ```

3. Verify **OAuth Scopes** include:
   ```
   Bot Token Scopes:
   - channels:read
   - chat:write
   - files:read
   - users:read
   
   User Token Scopes:
   - channels:read
   - files:read
   ```

### **Step 4: Get Updated Credentials**
After enabling distribution, you may get new credentials:

1. Copy the **Client ID** from "Basic Information"
2. Copy the **Client Secret** from "Basic Information"
3. Copy the **Signing Secret** from "Basic Information"

### **Step 5: Update Environment Variables**
Update your `.env.local` file with any new credentials:

```bash
# Slack OAuth Configuration (Updated after distribution)
NEXT_PUBLIC_SLACK_CLIENT_ID=your_new_client_id
SLACK_CLIENT_ID=your_new_client_id
SLACK_CLIENT_SECRET=your_new_client_secret
SLACK_SIGNING_SECRET=your_new_signing_secret
```

---

## 🧪 **Testing the Fix**

1. **Restart your development server**:
   ```bash
   npm run dev
   ```

2. **Test OAuth flow**:
   - Go to http://localhost:3001
   - Click "Add to Slack" button
   - Should redirect to Slack authorization page
   - After approval, should redirect back successfully

3. **Test in different workspace**:
   - Try installing the app in a different Slack workspace
   - Should work without the `invalid_team_for_non_distributed_app` error

---

## 🚀 **Production Deployment Notes**

### **Update Redirect URLs for Production**
When deploying to production, add your production domain:

```
https://your-production-domain.com/auth/slack/callback
```

### **Webhook URLs**
Update webhook URLs in Slack app settings to point to your production domain:

```
https://your-production-domain.com/api/slack/events
```

---

## 🔍 **Troubleshooting**

### **If distribution activation fails:**
1. Ensure app name and description are filled
2. Upload an app icon (recommended)
3. Verify all OAuth scopes are properly set
4. Check that redirect URLs are valid HTTPS URLs

### **If OAuth still fails after distribution:**
1. Clear browser cache and cookies
2. Try OAuth flow in incognito/private mode
3. Verify environment variables are correctly set
4. Check server logs for detailed error messages

### **Common Issues:**
- **Invalid redirect URI**: Update redirect URLs in Slack app settings
- **Scope mismatch**: Verify OAuth scopes match your app requirements
- **Expired tokens**: Regenerate client secret if needed

---

## ✅ **Success Indicators**

After fixing, you should see:
- ✅ No `invalid_team_for_non_distributed_app` errors
- ✅ Successful OAuth redirects
- ✅ App can be installed in any Slack workspace
- ✅ Proper token exchange and API access

The Slack integration should now work seamlessly across all workspaces!
