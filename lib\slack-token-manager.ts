import { devLog } from '@/lib/console-cleaner';
/**
 * Secure Slack Token Management System
 * 
 * Handles secure storage, encryption, refresh, and management
 * of Slack OAuth tokens with enterprise-grade security.
 */

import { createClient } from '@supabase/supabase-js';
import { logAuditEvent } from './audit-logger';

// Token encryption configuration
const ENCRYPTION_CONFIG = {
  ALGORITHM: 'aes-256-gcm',
  KEY_LENGTH: 32,
  IV_LENGTH: 16,
  TAG_LENGTH: 16,
  SALT_LENGTH: 32
};

// Token refresh configuration
const REFRESH_CONFIG = {
  REFRESH_THRESHOLD_HOURS: 24, // Refresh if token expires within 24 hours
  MAX_REFRESH_ATTEMPTS: 3,
  REFRESH_RETRY_DELAY_MS: 5000,
  TOKEN_VALIDATION_INTERVAL_MS: 60 * 60 * 1000 // 1 hour
};

export interface SlackTokenData {
  access_token: string;
  refresh_token?: string;
  expires_at?: number;
  scope: string;
  team_id: string;
  team_name: string;
  user_id: string;
  user_name?: string;
  bot_user_id?: string;
  app_id?: string;
  enterprise_id?: string;
  is_enterprise_install?: boolean;
}

export interface EncryptedTokenData {
  encrypted_data: string;
  iv: string;
  tag: string;
  salt: string;
  created_at: string;
  updated_at: string;
  expires_at?: string;
  metadata: {
    team_id: string;
    user_id: string;
    scope: string;
    last_validated?: string;
    refresh_count: number;
  };
}

/**
 * Slack Token Manager with encryption and secure storage
 */
export class SlackTokenManager {
  private static instance: SlackTokenManager;
  private supabase: any;
  private encryptionKey: Buffer;
  private validationInterval: NodeJS.Timeout | null = null;
  
  private constructor() {
    // Initialize Supabase client
    if (process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      this.supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
      );
    }
    
    // Initialize encryption key
    this.encryptionKey = this.deriveEncryptionKey();
    
    // Start token validation interval
    this.startTokenValidation();
  }
  
  static getInstance(): SlackTokenManager {
    if (!SlackTokenManager.instance) {
      SlackTokenManager.instance = new SlackTokenManager();
    }
    return SlackTokenManager.instance;
  }
  
  /**
   * Derive encryption key from environment
   */
  private deriveEncryptionKey(): Buffer {
    const secret = process.env.SLACK_TOKEN_ENCRYPTION_KEY ||
                  process.env.NEXTAUTH_SECRET ||
                  'default-development-key-change-in-production';

    // Use PBKDF2 to derive a proper encryption key
    const crypto = require('crypto');
    return crypto.pbkdf2Sync(secret, 'slack-tokens', 100000, ENCRYPTION_CONFIG.KEY_LENGTH, 'sha256');
  }
  
  /**
   * Encrypt token data
   */
  private encryptTokenData(tokenData: SlackTokenData): {
    encrypted: string;
    iv: string;
    tag: string;
    salt: string;
  } {
    const crypto = require('crypto');
    
    // Generate random IV and salt
    const iv = crypto.randomBytes(ENCRYPTION_CONFIG.IV_LENGTH);
    const salt = crypto.randomBytes(ENCRYPTION_CONFIG.SALT_LENGTH);
    
    // Create cipher
    const cipher = crypto.createCipher(ENCRYPTION_CONFIG.ALGORITHM, this.encryptionKey);
    cipher.setAAD(salt); // Additional authenticated data
    
    // Encrypt data
    const jsonData = JSON.stringify(tokenData);
    let encrypted = cipher.update(jsonData, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Get authentication tag
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
      salt: salt.toString('hex')
    };
  }
  
  /**
   * Decrypt token data
   */
  private decryptTokenData(encryptedData: {
    encrypted: string;
    iv: string;
    tag: string;
    salt: string;
  }): SlackTokenData {
    const crypto = require('crypto');
    
    try {
      // Create decipher
      const decipher = crypto.createDecipher(ENCRYPTION_CONFIG.ALGORITHM, this.encryptionKey);
      decipher.setAAD(Buffer.from(encryptedData.salt, 'hex'));
      decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));
      
      // Decrypt data
      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return JSON.parse(decrypted);
      
    } catch (error) {
      console.error('Failed to decrypt token data:', error);
      throw new Error('Token decryption failed');
    }
  }
  
  /**
   * Store encrypted Slack token
   */
  async storeToken(userId: string, tokenData: SlackTokenData): Promise<void> {
    if (!this.supabase) {
      throw new Error('Supabase not initialized');
    }
    
    try {
      // Encrypt token data
      const encrypted = this.encryptTokenData(tokenData);
      
      // Prepare database record
      const record: EncryptedTokenData = {
        encrypted_data: encrypted.encrypted,
        iv: encrypted.iv,
        tag: encrypted.tag,
        salt: encrypted.salt,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        expires_at: tokenData.expires_at ? new Date(tokenData.expires_at * 1000).toISOString() : undefined,
        metadata: {
          team_id: tokenData.team_id,
          user_id: tokenData.user_id,
          scope: tokenData.scope,
          refresh_count: 0
        }
      };
      
      // Store in database
      const { error } = await this.supabase
        .from('slack_tokens')
        .upsert({
          user_id: userId,
          team_id: tokenData.team_id,
          ...record
        }, {
          onConflict: 'user_id,team_id'
        });
      
      if (error) {
        throw error;
      }
      
      // Log audit event
      await logAuditEvent({
        event_type: 'SLACK_CONNECTED',
        user_id: userId,
        action: 'Slack token stored securely',
        resource_type: 'slack_token',
        resource_id: tokenData.team_id,
        metadata: {
          team_name: tokenData.team_name,
          scope: tokenData.scope,
          encrypted: true
        }
      });
  devLog.log(`Slack token stored securely for user ${userId}, team ${tokenData.team_id}`);
      
    } catch (error) {
      console.error('Failed to store Slack token:', error);
      
      await logAuditEvent({
        event_type: 'SLACK_ERROR',
        user_id: userId,
        action: 'Failed to store Slack token',
        success: false,
        error_message: error instanceof Error ? error.message : 'Unknown error',
        metadata: { team_id: tokenData.team_id }
      });
      
      throw error;
    }
  }
  
  /**
   * Retrieve and decrypt Slack token
   */
  async getToken(userId: string, teamId?: string): Promise<SlackTokenData | null> {
    if (!this.supabase) {
      throw new Error('Supabase not initialized');
    }
    
    try {
      let query = this.supabase
        .from('slack_tokens')
        .select('*')
        .eq('user_id', userId);
      
      if (teamId) {
        query = query.eq('team_id', teamId);
      }
      
      query = query.order('updated_at', { ascending: false }).limit(1);
      
      const { data, error } = await query;
      
      if (error) {
        throw error;
      }
      
      if (!data || data.length === 0) {
        return null;
      }
      
      const record = data[0];
      
      // Decrypt token data
      const tokenData = this.decryptTokenData({
        encrypted: record.encrypted_data,
        iv: record.iv,
        tag: record.tag,
        salt: record.salt
      });
      
      // Check if token needs refresh
      if (this.shouldRefreshToken(tokenData)) {
        return await this.refreshToken(userId, tokenData, record);
      }
      
      return tokenData;
      
    } catch (error) {
      console.error('Failed to retrieve Slack token:', error);
      
      await logAuditEvent({
        event_type: 'SLACK_ERROR',
        user_id: userId,
        action: 'Failed to retrieve Slack token',
        success: false,
        error_message: error instanceof Error ? error.message : 'Unknown error',
        metadata: { team_id: teamId }
      });
      
      return null;
    }
  }
  
  /**
   * Check if token should be refreshed
   */
  private shouldRefreshToken(tokenData: SlackTokenData): boolean {
    if (!tokenData.expires_at || !tokenData.refresh_token) {
      return false;
    }
    
    const now = Math.floor(Date.now() / 1000);
    const expiresIn = tokenData.expires_at - now;
    const thresholdSeconds = REFRESH_CONFIG.REFRESH_THRESHOLD_HOURS * 60 * 60;
    
    return expiresIn < thresholdSeconds;
  }
  
  /**
   * Refresh Slack token
   */
  private async refreshToken(
    userId: string, 
    tokenData: SlackTokenData, 
    record: any
  ): Promise<SlackTokenData> {
    if (!tokenData.refresh_token) {
      throw new Error('No refresh token available');
    }
    
    try {
      // Call Slack OAuth refresh endpoint
      const response = await fetch('https://slack.com/api/oauth.v2.access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: process.env.SLACK_CLIENT_ID!,
          client_secret: process.env.SLACK_CLIENT_SECRET!,
          grant_type: 'refresh_token',
          refresh_token: tokenData.refresh_token
        })
      });
      
      const refreshData = await response.json();
      
      if (!refreshData.ok) {
        throw new Error(`Token refresh failed: ${refreshData.error}`);
      }
      
      // Update token data
      const newTokenData: SlackTokenData = {
        ...tokenData,
        access_token: refreshData.access_token,
        refresh_token: refreshData.refresh_token || tokenData.refresh_token,
        expires_at: refreshData.expires_in ? 
          Math.floor(Date.now() / 1000) + refreshData.expires_in : 
          tokenData.expires_at
      };
      
      // Store updated token
      await this.storeToken(userId, newTokenData);
      
      // Log audit event
      await logAuditEvent({
        event_type: 'SLACK_CONNECTED',
        user_id: userId,
        action: 'Slack token refreshed',
        resource_type: 'slack_token',
        resource_id: tokenData.team_id,
        metadata: {
          refresh_count: (record.metadata?.refresh_count || 0) + 1,
          previous_expires_at: tokenData.expires_at,
          new_expires_at: newTokenData.expires_at
        }
      });
      
      return newTokenData;
      
    } catch (error) {
      console.error('Failed to refresh Slack token:', error);
      
      await logAuditEvent({
        event_type: 'SLACK_ERROR',
        user_id: userId,
        action: 'Failed to refresh Slack token',
        success: false,
        error_message: error instanceof Error ? error.message : 'Unknown error',
        metadata: { 
          team_id: tokenData.team_id,
          refresh_attempt: true
        }
      });
      
      throw error;
    }
  }
  
  /**
   * Validate token with Slack API
   */
  async validateToken(userId: string, tokenData: SlackTokenData): Promise<boolean> {
    try {
      const response = await fetch('https://slack.com/api/auth.test', {
        headers: {
          'Authorization': `Bearer ${tokenData.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      const result = await response.json();
      
      if (result.ok) {
        // Update last validated timestamp
        await this.updateTokenMetadata(userId, tokenData.team_id, {
          last_validated: new Date().toISOString()
        });
        
        return true;
      }
      
      return false;
      
    } catch (error) {
      console.error('Token validation failed:', error);
      return false;
    }
  }
  
  /**
   * Update token metadata
   */
  private async updateTokenMetadata(
    userId: string, 
    teamId: string, 
    updates: Partial<EncryptedTokenData['metadata']>
  ): Promise<void> {
    if (!this.supabase) return;
    
    try {
      const { error } = await this.supabase
        .from('slack_tokens')
        .update({
          metadata: updates,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('team_id', teamId);
      
      if (error) {
        console.error('Failed to update token metadata:', error);
      }
      
    } catch (error) {
      console.error('Failed to update token metadata:', error);
    }
  }
  
  /**
   * Revoke and delete token
   */
  async revokeToken(userId: string, teamId: string): Promise<void> {
    try {
      // Get token first
      const tokenData = await this.getToken(userId, teamId);
      
      if (tokenData) {
        // Revoke with Slack API
        try {
          await fetch('https://slack.com/api/auth.revoke', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${tokenData.access_token}`,
              'Content-Type': 'application/json'
            }
          });
        } catch (error) {
          console.error('Failed to revoke token with Slack:', error);
          // Continue with local deletion even if revocation fails
        }
      }
      
      // Delete from database
      if (this.supabase) {
        const { error } = await this.supabase
          .from('slack_tokens')
          .delete()
          .eq('user_id', userId)
          .eq('team_id', teamId);
        
        if (error) {
          throw error;
        }
      }
      
      // Log audit event
      await logAuditEvent({
        event_type: 'SLACK_DISCONNECTED',
        user_id: userId,
        action: 'Slack token revoked and deleted',
        resource_type: 'slack_token',
        resource_id: teamId,
        metadata: { revoked: true }
      });
      
    } catch (error) {
      console.error('Failed to revoke Slack token:', error);
      throw error;
    }
  }
  
  /**
   * Start periodic token validation
   */
  private startTokenValidation(): void {
    this.validationInterval = setInterval(async () => {
      await this.validateAllTokens();
    }, REFRESH_CONFIG.TOKEN_VALIDATION_INTERVAL_MS);
  }
  
  /**
   * Validate all stored tokens
   */
  private async validateAllTokens(): Promise<void> {
    if (!this.supabase) return;
    
    try {
      const { data, error } = await this.supabase
        .from('slack_tokens')
        .select('user_id, team_id, metadata')
        .lt('metadata->last_validated', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());
      
      if (error || !data) return;
      
      for (const record of data) {
        try {
          const tokenData = await this.getToken(record.user_id, record.team_id);
          if (tokenData) {
            await this.validateToken(record.user_id, tokenData);
          }
        } catch (error) {
          console.error(`Failed to validate token for user ${record.user_id}:`, error);
        }
      }
      
    } catch (error) {
      console.error('Failed to validate tokens:', error);
    }
  }
  
  /**
   * Cleanup expired tokens
   */
  async cleanupExpiredTokens(): Promise<void> {
    if (!this.supabase) return;
    
    try {
      const { error } = await this.supabase
        .from('slack_tokens')
        .delete()
        .lt('expires_at', new Date().toISOString());
      
      if (error) {
        console.error('Failed to cleanup expired tokens:', error);
      } else {
  devLog.log('Expired Slack tokens cleaned up');
      }
      
    } catch (error) {
      console.error('Failed to cleanup expired tokens:', error);
    }
  }
  
  /**
   * Destroy the token manager
   */
  destroy(): void {
    if (this.validationInterval) {
      clearInterval(this.validationInterval);
      this.validationInterval = null;
    }
  }
}

/**
 * Convenience functions for Slack token management
 */
export const slackTokenManager = {
  store: (userId: string, tokenData: SlackTokenData) => 
    SlackTokenManager.getInstance().storeToken(userId, tokenData),
  
  get: (userId: string, teamId?: string) => 
    SlackTokenManager.getInstance().getToken(userId, teamId),
  
  validate: (userId: string, tokenData: SlackTokenData) => 
    SlackTokenManager.getInstance().validateToken(userId, tokenData),
  
  revoke: (userId: string, teamId: string) => 
    SlackTokenManager.getInstance().revokeToken(userId, teamId),
  
  cleanup: () => 
    SlackTokenManager.getInstance().cleanupExpiredTokens()
};
