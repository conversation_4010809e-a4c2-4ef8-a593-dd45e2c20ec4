name: 🚀 Continuous Integration

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # Code Quality & Linting
  lint-and-format:
    name: 🧹 Lint & Format
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🧹 Run ESLint
        run: npm run lint

      - name: 💅 Check Prettier formatting
        run: npm run format:check

      - name: 🔍 TypeScript type check
        run: npm run type-check

      - name: 📊 Upload lint results
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: lint-results
          path: |
            eslint-report.json
            prettier-report.txt

  # Security Scanning
  security-scan:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔍 Run npm audit
        run: npm audit --audit-level=moderate

      - name: 🛡️ Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

      - name: 📊 Upload Snyk results
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: snyk-results
          path: snyk-report.json

  # Unit Tests
  unit-tests:
    name: 🧪 Unit Tests
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🧪 Run unit tests
        run: npm run test:unit
        env:
          CI: true

      - name: 📊 Generate coverage report
        run: npm run test:coverage

      - name: 📈 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/coverage-final.json
          flags: unittests
          name: codecov-umbrella

      - name: 📊 Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: unit-test-results
          path: |
            coverage/
            test-results.xml

  # Integration Tests
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🗄️ Setup test database
        run: npm run test:db:setup
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

      - name: 🔗 Run integration tests
        run: npm run test:integration
        env:
          CI: true
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          SUPABASE_TEST_URL: ${{ secrets.SUPABASE_TEST_URL }}
          SUPABASE_TEST_ANON_KEY: ${{ secrets.SUPABASE_TEST_ANON_KEY }}
          STRIPE_TEST_SECRET_KEY: ${{ secrets.STRIPE_TEST_SECRET_KEY }}

      - name: 📊 Upload integration test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: integration-test-results
          path: test-results/

  # Build Application
  build:
    name: 🏗️ Build Application
    runs-on: ubuntu-latest
    needs: [lint-and-format, security-scan]
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🏗️ Build application
        run: npm run build
        env:
          NEXT_TELEMETRY_DISABLED: 1

      - name: 📦 Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            .next/
            public/
          retention-days: 1

  # Database Migration Validation
  migration-check:
    name: 🗄️ Migration Check
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔍 Check for migration changes
        id: migration-check
        run: |
          if git diff --name-only origin/main...HEAD | grep -q "migrations/\|schema\.sql"; then
            echo "migrations_changed=true" >> $GITHUB_OUTPUT
          else
            echo "migrations_changed=false" >> $GITHUB_OUTPUT
          fi

      - name: 🗄️ Validate migrations
        if: steps.migration-check.outputs.migrations_changed == 'true'
        run: npm run migration:validate
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_STAGING_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_STAGING_SERVICE_KEY }}

      - name: 📝 Comment on PR
        if: steps.migration-check.outputs.migrations_changed == 'true'
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '🗄️ **Database migrations detected!**\n\nPlease ensure:\n- [ ] Migrations are backward compatible\n- [ ] Production backup is scheduled\n- [ ] Migration rollback plan is documented'
            })

  # End-to-End Tests
  e2e-tests:
    name: 🎭 E2E Tests
    runs-on: ubuntu-latest
    needs: [build]
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 📦 Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: 🎭 Install Playwright
        run: npx playwright install --with-deps ${{ matrix.browser }}

      - name: 🎭 Run E2E tests
        run: npm run test:e2e:${{ matrix.browser }}
        env:
          CI: true
          PLAYWRIGHT_BASE_URL: http://localhost:3000
          TEST_USER_EMAIL: ${{ secrets.TEST_USER_EMAIL }}
          TEST_USER_PASSWORD: ${{ secrets.TEST_USER_PASSWORD }}

      - name: 📊 Upload E2E test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: e2e-results-${{ matrix.browser }}
          path: |
            playwright-report/
            test-results/

  # Performance Tests
  performance-tests:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'pull_request'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 📦 Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: ⚡ Run Lighthouse CI
        run: npm run lighthouse:ci
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

      - name: 📊 Upload Lighthouse results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: lighthouse-results
          path: .lighthouseci/

  # Deployment Preview (for PRs)
  deploy-preview:
    name: 🚀 Deploy Preview
    runs-on: ubuntu-latest
    needs: [build, unit-tests]
    if: github.event_name == 'pull_request'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Vercel Preview
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          github-comment: true
          working-directory: ./

  # Notify on Success/Failure
  notify:
    name: 📢 Notify Results
    runs-on: ubuntu-latest
    needs: [lint-and-format, security-scan, unit-tests, integration-tests, build, e2e-tests]
    if: always()
    steps:
      - name: 📢 Slack Notification - Success
        if: needs.lint-and-format.result == 'success' && needs.security-scan.result == 'success' && needs.unit-tests.result == 'success' && needs.integration-tests.result == 'success' && needs.build.result == 'success' && needs.e2e-tests.result == 'success'
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#dev-notifications'
          text: '✅ All CI checks passed for ${{ github.ref }}'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: 📢 Slack Notification - Failure
        if: needs.lint-and-format.result == 'failure' || needs.security-scan.result == 'failure' || needs.unit-tests.result == 'failure' || needs.integration-tests.result == 'failure' || needs.build.result == 'failure' || needs.e2e-tests.result == 'failure'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#dev-notifications'
          text: '❌ CI checks failed for ${{ github.ref }}'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Cleanup
  cleanup:
    name: 🧹 Cleanup
    runs-on: ubuntu-latest
    needs: [notify]
    if: always()
    steps:
      - name: 🧹 Delete old artifacts
        uses: actions/github-script@v6
        with:
          script: |
            const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
              owner: context.repo.owner,
              repo: context.repo.repo,
              run_id: context.runId,
            });
            
            // Keep only the latest 5 artifacts
            const oldArtifacts = artifacts.data.artifacts.slice(5);
            
            for (const artifact of oldArtifacts) {
              await github.rest.actions.deleteArtifact({
                owner: context.repo.owner,
                repo: context.repo.repo,
                artifact_id: artifact.id,
              });
            }
