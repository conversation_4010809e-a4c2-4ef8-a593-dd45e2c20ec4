# 🔐 Environment Variables for Public SaaS Mode

## 📋 Overview

This document provides the complete environment configuration for running Slack Summary Scribe in **public mode** without authentication requirements.

## 🚀 **MINIMUM REQUIRED CONFIGURATION**

Create a `.env.local` file with these essential variables:

```bash
# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_SITE_URL=http://localhost:3001
NODE_ENV=development

# =============================================================================
# AI MODELS & SUMMARIZATION (CRITICAL)
# =============================================================================

# OpenAI API (Primary AI Provider)
OPENAI_API_KEY=sk-your_openai_api_key_here

# OpenRouter API (Fallback AI Provider)
OPENROUTER_API_KEY=sk-or-your_openrouter_api_key_here

# =============================================================================
# DATABASE & STORAGE
# =============================================================================

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =============================================================================
# ANALYTICS & MONITORING
# =============================================================================

# PostHog Analytics
NEXT_PUBLIC_POSTHOG_KEY=phc_your_posthog_key_here
POSTHOG_API_KEY=phx_your_posthog_api_key_here
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# =============================================================================
# NOTIFICATIONS & INTEGRATIONS
# =============================================================================

# Slack Integration
SLACK_BOT_TOKEN=xoxb-your_slack_bot_token_here
SLACK_APP_TOKEN=xapp-your_slack_app_token_here
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url

# =============================================================================
# EXPORT & INTEGRATION SERVICES
# =============================================================================

# Notion Integration
NOTION_CLIENT_ID=your_notion_client_id_here
NOTION_CLIENT_SECRET=your_notion_client_secret_here

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

NEXT_PUBLIC_FETCH_TIMEOUT=15000
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_DEMO_MODE=true
```

## 🎯 **QUICK START GUIDE**

### 1. **Get OpenAI API Key**
1. Go to [OpenAI Platform](https://platform.openai.com)
2. Create an account and add billing
3. Generate an API key with GPT-4o access
4. Copy the key to `OPENAI_API_KEY`

### 2. **Set up Supabase Database**
1. Go to [Supabase](https://supabase.com)
2. Create a new project
3. Go to Settings → API
4. Copy the URL and anon key
5. Update `NEXT_PUBLIC_SUPABASE_URL` and `NEXT_PUBLIC_SUPABASE_ANON_KEY`

### 3. **Set up PostHog Analytics**
1. Go to [PostHog](https://app.posthog.com)
2. Create account and project
3. Copy the project API key
4. Update `NEXT_PUBLIC_POSTHOG_KEY`

### 4. **Optional: Slack Integration**
1. Go to [Slack API](https://api.slack.com/apps)
2. Create a new app
3. Add bot token and webhook URL
4. Update `SLACK_BOT_TOKEN` and `SLACK_WEBHOOK_URL`

## 🔧 **FEATURE CONFIGURATION**

### **AI Models Available**
- **Free Tier**: DeepSeek R1 (via OpenRouter)
- **Premium**: GPT-4o Mini, GPT-4o (via OpenAI)
- **Enterprise**: Claude 3.5 Sonnet (via Anthropic)

### **Export Options**
- **PDF**: Built-in (no API key required)
- **Excel**: Built-in (no API key required)
- **Notion**: Requires Notion API setup
- **Slack**: Requires Slack bot setup

### **Analytics Tracking**
- **Anonymous Usage**: PostHog tracks without user identification
- **Summary Analytics**: Processing time, model usage, export counts
- **Error Monitoring**: Automatic error tracking and reporting

## 🚨 **IMPORTANT NOTES FOR PUBLIC MODE**

### **No Authentication Required**
- All routes work without login
- Anonymous users can access all features
- No user accounts or sessions needed

### **Data Storage**
- Summaries stored with anonymous IDs
- No personal data collection
- Temporary storage for demo purposes

### **Rate Limiting**
- Built-in rate limiting to prevent abuse
- Configurable limits per IP address
- Graceful degradation under load

### **Security**
- API keys stored server-side only
- No client-side exposure of sensitive data
- CORS properly configured for public access

## 🎯 **DEPLOYMENT CHECKLIST**

### **Before Deploying to Production:**

1. ✅ **Replace all placeholder API keys with real ones**
2. ✅ **Update URLs from localhost to production domain**
3. ✅ **Set up production Supabase database**
4. ✅ **Configure production PostHog project**
5. ✅ **Set up Slack webhook for production**
6. ✅ **Test all features in production environment**

### **Production Environment Variables:**

```bash
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
NODE_ENV=production
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_DEMO_MODE=false
```

## 🔍 **TROUBLESHOOTING**

### **Common Issues:**

1. **"AI model not available"**
   - Check OpenAI API key is valid
   - Ensure billing is set up on OpenAI account
   - Verify OpenRouter API key as fallback

2. **"Database connection failed"**
   - Verify Supabase URL and keys
   - Check database is online
   - Ensure RLS policies allow anonymous access

3. **"Analytics not working"**
   - Check PostHog API key
   - Verify PostHog project is active
   - Check browser console for errors

4. **"Export features not working"**
   - PDF/Excel: No API keys needed
   - Notion: Requires Notion API setup
   - Slack: Requires Slack bot configuration

## 📞 **SUPPORT**

For help with configuration:

1. **OpenAI**: [OpenAI API Documentation](https://platform.openai.com/docs)
2. **Supabase**: [Supabase Documentation](https://supabase.com/docs)
3. **PostHog**: [PostHog Documentation](https://posthog.com/docs)
4. **Slack**: [Slack API Documentation](https://api.slack.com)

---

**🎉 Ready to launch!** With just the minimum configuration, your Slack Summary Scribe will be fully functional in public mode. 