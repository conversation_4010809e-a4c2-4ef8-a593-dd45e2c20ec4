# 🔐 Environment Variables Configuration Guide

## 📋 Overview

This document outlines all environment variables required for the Slack Summary Scribe SaaS application with enterprise features.

## 🚨 MISSING ENVIRONMENT VARIABLES

Based on your current `.env.local` file, here are the **CRITICAL** environment variables you need to add:

### 🔴 **IMMEDIATE PRIORITY - Required for Core Features**

```bash
# Stripe Payment Processing (CRITICAL)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_actual_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_actual_stripe_webhook_secret
STRIPE_PRO_PRICE_ID=price_your_actual_pro_price_id
STRIPE_ENTERPRISE_PRICE_ID=price_your_actual_enterprise_price_id

# OpenAI API for GPT-4o Models (CRITICAL)
OPENAI_API_KEY=sk-your_actual_openai_api_key_here

# Slack Bot Token for Auto-Posting (CRITICAL)
SLACK_BOT_TOKEN=xoxb-your-actual-slack-bot-token
SLACK_APP_TOKEN=xapp-your-actual-slack-app-token

# PostHog Analytics (CRITICAL)
NEXT_PUBLIC_POSTHOG_KEY=phc_your_actual_posthog_key_here
```

### 🟡 **HIGH PRIORITY - Required for Enterprise Features**

```bash
# CRM Integrations
HUBSPOT_CLIENT_ID=your_hubspot_client_id
HUBSPOT_CLIENT_SECRET=your_hubspot_client_secret
NOTION_CLIENT_ID=your_notion_oauth_client_id
NOTION_CLIENT_SECRET=your_notion_oauth_client_secret

# Cashfree Payment Gateway (Fallback)
CASHFREE_APP_ID=your_cashfree_app_id
CASHFREE_SECRET_KEY=your_cashfree_secret_key
CASHFREE_WEBHOOK_SECRET=your_cashfree_webhook_secret

# Redis for Background Jobs & Caching
REDIS_URL=redis://localhost:6379
```

### 🟢 **MEDIUM PRIORITY - Optional but Recommended**

```bash
# Enhanced Security
ENCRYPTION_KEY=your_32_character_encryption_key_here
API_SECRET_KEY=your_api_secret_key_here

# File Storage (AWS S3)
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET=slack-summary-scribe-uploads

# Additional AI Models
ANTHROPIC_API_KEY=sk-ant-your_anthropic_api_key_here
```

## 🛠️ **HOW TO GET THESE KEYS**

### 1. **Stripe Setup** (CRITICAL)
1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Get your **Publishable Key** and **Secret Key** from API Keys section
3. Create products for Pro ($29) and Enterprise ($99) plans
4. Copy the **Price IDs** for each plan
5. Set up webhook endpoint: `https://yourdomain.com/api/stripe/webhook`
6. Copy the **Webhook Secret**

### 2. **OpenAI API** (CRITICAL)
1. Go to [OpenAI Platform](https://platform.openai.com)
2. Create an API key with GPT-4o access
3. Add billing information to enable GPT-4o models

### 3. **Slack Bot Setup** (CRITICAL)
1. Go to [Slack API](https://api.slack.com/apps)
2. Create a new app or use existing
3. Go to "OAuth & Permissions" → Copy **Bot User OAuth Token**
4. Go to "Basic Information" → Copy **App-Level Token**
5. Add required scopes: `chat:write`, `channels:read`, `groups:read`

### 4. **PostHog Analytics** (CRITICAL)
1. Go to [PostHog](https://app.posthog.com)
2. Create account and project
3. Copy **Project API Key** from Settings

### 5. **HubSpot CRM Integration**
1. Go to [HubSpot Developers](https://developers.hubspot.com)
2. Create a new app
3. Copy **Client ID** and **Client Secret**
4. Set redirect URI: `http://localhost:3000/api/crm/hubspot/callback`

### 6. **Notion Integration**
1. Go to [Notion Developers](https://developers.notion.com)
2. Create a new integration
3. Copy **Client ID** and **Client Secret**
4. Set redirect URI: `http://localhost:3000/api/crm/notion/callback`

### 7. **Cashfree Payment Gateway**
1. Go to [Cashfree Dashboard](https://merchant.cashfree.com)
2. Get **App ID** and **Secret Key** from API section
3. Set up webhook endpoint for payment notifications

### 8. **Redis Setup** (for Background Jobs)
```bash
# Local Redis installation
brew install redis  # macOS
sudo apt install redis-server  # Ubuntu

# Or use Redis Cloud
# Go to https://redis.com/try-free/
```

## 🔧 **QUICK SETUP COMMANDS**

### 1. **Copy Environment Template**
```bash
cp .env.example .env.local
```

### 2. **Install Required Services**
```bash
# Install Redis locally
brew install redis
redis-server

# Or use Docker
docker run -d -p 6379:6379 redis:alpine
```

### 3. **Validate Environment**
```bash
npm run validate-env
```

## 🚀 **DEPLOYMENT CHECKLIST**

### ✅ **Before Deploying to Production:**

1. **Replace all test keys with production keys**
2. **Update all URLs from localhost to production domain**
3. **Set up production database**
4. **Configure production Redis instance**
5. **Set up production Stripe webhooks**
6. **Configure production Slack app**
7. **Set up monitoring and alerts**

### 🔒 **Security Checklist:**

1. **Never commit `.env.local` to git**
2. **Use different keys for development/staging/production**
3. **Rotate keys regularly**
4. **Use environment-specific Stripe accounts**
5. **Enable webhook signature verification**
6. **Set up proper CORS policies**

## 📞 **SUPPORT**

If you need help setting up any of these integrations:

1. **Stripe**: [Stripe Documentation](https://stripe.com/docs)
2. **OpenAI**: [OpenAI API Documentation](https://platform.openai.com/docs)
3. **Slack**: [Slack API Documentation](https://api.slack.com)
4. **HubSpot**: [HubSpot API Documentation](https://developers.hubspot.com)
5. **Notion**: [Notion API Documentation](https://developers.notion.com)

## 🎯 **NEXT STEPS**

1. **Set up the CRITICAL environment variables first**
2. **Test payment flows with Stripe test mode**
3. **Configure Slack bot and test auto-posting**
4. **Set up analytics tracking with PostHog**
5. **Test CRM integrations**
6. **Deploy to staging environment**
7. **Run end-to-end tests**
8. **Deploy to production**

---

**⚠️ IMPORTANT**: Make sure to replace all placeholder values with your actual API keys and secrets before running the application.
