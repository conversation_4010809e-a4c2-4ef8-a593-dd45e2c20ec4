/**
 * Queue Service Configuration
 */

export const config = {
  port: parseInt(process.env.PORT || '3001'),
  nodeEnv: process.env.NODE_ENV || 'development',
  logLevel: process.env.LOG_LEVEL || 'info',
  
  // Redis configuration
  redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0')
  },
  
  // Supabase configuration
  supabase: {
    url: process.env.SUPABASE_URL!,
    serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY!
  },
  
  // Queue configuration
  queue: {
    concurrency: parseInt(process.env.QUEUE_CONCURRENCY || '5'),
    maxRetries: parseInt(process.env.QUEUE_MAX_RETRIES || '3'),
    retryDelay: parseInt(process.env.QUEUE_RETRY_DELAY || '2000'),
    removeOnComplete: parseInt(process.env.QUEUE_REMOVE_ON_COMPLETE || '100'),
    removeOnFail: parseInt(process.env.QUEUE_REMOVE_ON_FAIL || '50')
  },
  
  // Security
  allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  apiKey: process.env.QUEUE_SERVICE_API_KEY,
  
  // Monitoring
  monitoring: {
    enabled: process.env.MONITORING_ENABLED === 'true',
    endpoint: process.env.MONITORING_ENDPOINT,
    apiKey: process.env.MONITORING_API_KEY
  },
  
  // Health checks
  healthCheck: {
    interval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000'),
    timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT || '5000')
  }
};
