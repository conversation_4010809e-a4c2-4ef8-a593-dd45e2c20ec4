import { devLog } from '@/lib/console-cleaner';
/**
 * Authentication Protection Utilities
 * 
 * Provides comprehensive authentication and authorization utilities
 * for protecting routes, API endpoints, and user actions.
 */

import { auth, currentUser } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { redirect } from 'next/navigation';
import { rbac, Permission, ResourceType, UserRole as RBACUserRole } from './rbac';
import { createSecureApiRoute } from './api-security';

// User roles for role-based access control (keeping for backward compatibility)
export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

// Export RBAC types for convenience
export { Permission, ResourceType, RBACUserRole };

// Subscription tiers
export enum SubscriptionTier {
  FREE = 'free',
  PRO = 'pro',
  ENTERPRISE = 'enterprise'
}

// Authentication result interface
export interface AuthResult {
  success: boolean;
  user?: any;
  userId?: string;
  role?: UserRole;
  subscriptionTier?: SubscriptionTier;
  error?: string;
}

/**
 * Get current authenticated user with role and subscription info
 */
export async function getCurrentAuthenticatedUser(): Promise<AuthResult> {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return {
        success: false,
        error: 'Not authenticated'
      };
    }

    const user = await currentUser();
    
    if (!user) {
      return {
        success: false,
        error: 'User not found'
      };
    }

    // Extract role from user metadata
    const role = (user.publicMetadata?.role as UserRole) || UserRole.USER;
    const subscriptionTier = (user.publicMetadata?.subscriptionTier as SubscriptionTier) || SubscriptionTier.FREE;

    return {
      success: true,
      user,
      userId,
      role,
      subscriptionTier
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return {
      success: false,
      error: 'Authentication failed'
    };
  }
}

/**
 * Require authentication for server components
 */
export async function requireAuth(): Promise<AuthResult> {
  const authResult = await getCurrentAuthenticatedUser();
  
  if (!authResult.success) {
    redirect('/sign-in');
  }
  
  return authResult;
}

/**
 * Require specific role for server components
 */
export async function requireRole(requiredRole: UserRole): Promise<AuthResult> {
  const authResult = await requireAuth();
  
  if (authResult.role !== requiredRole && authResult.role !== UserRole.SUPER_ADMIN) {
    redirect('/dashboard?error=insufficient_permissions');
  }
  
  return authResult;
}

/**
 * Require admin role for server components
 */
export async function requireAdmin(): Promise<AuthResult> {
  const authResult = await requireAuth();
  
  if (authResult.role !== UserRole.ADMIN && authResult.role !== UserRole.SUPER_ADMIN) {
    redirect('/dashboard?error=admin_required');
  }
  
  return authResult;
}

/**
 * Check if user has required subscription tier
 */
export function hasSubscriptionTier(userTier: SubscriptionTier, requiredTier: SubscriptionTier): boolean {
  const tierHierarchy = {
    [SubscriptionTier.FREE]: 0,
    [SubscriptionTier.PRO]: 1,
    [SubscriptionTier.ENTERPRISE]: 2
  };
  
  return tierHierarchy[userTier] >= tierHierarchy[requiredTier];
}

/**
 * API route authentication wrapper
 */
export async function withAuth(
  handler: (req: NextRequest, authResult: AuthResult) => Promise<NextResponse>,
  options: {
    requiredRole?: UserRole;
    requiredSubscription?: SubscriptionTier;
  } = {}
) {
  return async (req: NextRequest) => {
    try {
      const authResult = await getCurrentAuthenticatedUser();
      
      if (!authResult.success) {
        return NextResponse.json(
          { 
            error: 'Authentication required',
            message: 'Please sign in to access this endpoint',
            code: 'AUTH_REQUIRED'
          },
          { status: 401 }
        );
      }

      // Check role requirements
      if (options.requiredRole) {
        if (authResult.role !== options.requiredRole && authResult.role !== UserRole.SUPER_ADMIN) {
          return NextResponse.json(
            { 
              error: 'Insufficient permissions',
              message: `This endpoint requires ${options.requiredRole} role`,
              code: 'INSUFFICIENT_PERMISSIONS'
            },
            { status: 403 }
          );
        }
      }

      // Check subscription requirements
      if (options.requiredSubscription && authResult.subscriptionTier) {
        if (!hasSubscriptionTier(authResult.subscriptionTier, options.requiredSubscription)) {
          return NextResponse.json(
            { 
              error: 'Subscription upgrade required',
              message: `This feature requires ${options.requiredSubscription} subscription`,
              code: 'SUBSCRIPTION_REQUIRED',
              requiredTier: options.requiredSubscription,
              currentTier: authResult.subscriptionTier
            },
            { status: 402 }
          );
        }
      }

      return await handler(req, authResult);
    } catch (error) {
      console.error('API authentication error:', error);
      return NextResponse.json(
        { 
          error: 'Authentication failed',
          message: 'An error occurred during authentication',
          code: 'AUTH_ERROR'
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Check if user is authenticated (for client components)
 */
export function useAuthGuard() {
  const { userId } = auth();
  return {
    isAuthenticated: !!userId,
    userId
  };
}

/**
 * Rate limiting by user ID
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  userId: string, 
  maxRequests: number = 100, 
  windowMs: number = 60 * 1000 // 1 minute
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const userLimit = rateLimitMap.get(userId);
  
  if (!userLimit || now > userLimit.resetTime) {
    // Reset or initialize rate limit
    const resetTime = now + windowMs;
    rateLimitMap.set(userId, { count: 1, resetTime });
    return { allowed: true, remaining: maxRequests - 1, resetTime };
  }
  
  if (userLimit.count >= maxRequests) {
    return { allowed: false, remaining: 0, resetTime: userLimit.resetTime };
  }
  
  userLimit.count++;
  return { 
    allowed: true, 
    remaining: maxRequests - userLimit.count, 
    resetTime: userLimit.resetTime 
  };
}

/**
 * API route with rate limiting
 */
export async function withRateLimit(
  handler: (req: NextRequest, authResult: AuthResult) => Promise<NextResponse>,
  options: {
    maxRequests?: number;
    windowMs?: number;
    requiredRole?: UserRole;
    requiredSubscription?: SubscriptionTier;
  } = {}
) {
  return withAuth(async (req: NextRequest, authResult: AuthResult) => {
    const { maxRequests = 100, windowMs = 60 * 1000 } = options;
    
    // Check rate limit
    const rateLimit = checkRateLimit(authResult.userId!, maxRequests, windowMs);
    
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          message: `Too many requests. Try again after ${new Date(rateLimit.resetTime).toISOString()}`,
          code: 'RATE_LIMIT_EXCEEDED',
          resetTime: rateLimit.resetTime
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': maxRequests.toString(),
            'X-RateLimit-Remaining': rateLimit.remaining.toString(),
            'X-RateLimit-Reset': rateLimit.resetTime.toString()
          }
        }
      );
    }
    
    // Add rate limit headers to successful responses
    const response = await handler(req, authResult);
    response.headers.set('X-RateLimit-Limit', maxRequests.toString());
    response.headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString());
    response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toString());
    
    return response;
  }, options);
}

/**
 * Audit log for security events
 */
export interface AuditLogEntry {
  userId: string;
  action: string;
  resource: string;
  details?: any;
  ip?: string;
  userAgent?: string;
  timestamp: Date;
}

const auditLogs: AuditLogEntry[] = [];

export function logSecurityEvent(entry: Omit<AuditLogEntry, 'timestamp'>) {
  auditLogs.push({
    ...entry,
    timestamp: new Date()
  });
  
  // In production, you would send this to a logging service
  devLog.log('Security Event:', entry);
}

/**
 * Get audit logs (admin only)
 */
export async function getAuditLogs(limit: number = 100): Promise<AuditLogEntry[]> {
  const authResult = await requireAdmin();
  
  if (!authResult.success) {
    throw new Error('Admin access required');
  }
  
  return auditLogs.slice(-limit);
}

/**
 * RBAC Protection Middleware
 */
export function createRBACProtectedRoute(
  handler: (request: NextRequest, authResult: any) => Promise<NextResponse>,
  options: {
    requiredPermission: Permission;
    resourceType?: ResourceType;
    getResourceId?: (request: NextRequest) => string | undefined;
    getOrganizationId?: (request: NextRequest) => string | undefined;
    requireAuth?: boolean;
    rateLimit?: number;
    auditLog?: boolean;
    allowedMethods?: string[];
  }
) {
  return createSecureApiRoute(async (request: NextRequest, authResult: any) => {
    const { userId } = authResult;

    // Get resource and organization IDs if provided
    const resourceId = options.getResourceId?.(request);
    const organizationId = options.getOrganizationId?.(request);

    // Check permission
    const hasPermission = await rbac.hasPermission(
      userId,
      options.requiredPermission,
      options.resourceType,
      resourceId,
      organizationId
    );

    if (!hasPermission) {
      // Log unauthorized access attempt
      await logSecurityEvent({
        userId,
        action: 'UNAUTHORIZED_ACCESS_ATTEMPT',
        resource: request.nextUrl.pathname,
        details: {
          required_permission: options.requiredPermission,
          resource_type: options.resourceType,
          resource_id: resourceId,
          organization_id: organizationId
        },
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
        userAgent: request.headers.get('user-agent') || undefined
      });

      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    return handler(request, authResult);
  }, {
    requireAuth: options.requireAuth ?? true,
    rateLimit: options.rateLimit,
    auditLog: options.auditLog,
    allowedMethods: options.allowedMethods
  });
}

/**
 * Check if user has admin role
 */
export async function isAdmin(userId: string, organizationId?: string): Promise<boolean> {
  const userPermissions = await rbac.getUserPermissions(userId, organizationId);
  return userPermissions?.role === RBACUserRole.ADMIN ||
         userPermissions?.role === RBACUserRole.SUPER_ADMIN;
}

/**
 * Check if user has super admin role
 */
export async function isSuperAdmin(userId: string): Promise<boolean> {
  const userPermissions = await rbac.getUserPermissions(userId);
  return userPermissions?.role === RBACUserRole.SUPER_ADMIN;
}

/**
 * Require admin access for API route
 */
export async function requireAdminAccess(userId: string, organizationId?: string): Promise<void> {
  const hasAdminAccess = await isAdmin(userId, organizationId);

  if (!hasAdminAccess) {
    await logSecurityEvent({
      userId,
      action: 'ADMIN_ACCESS_DENIED',
      resource: 'admin_route',
      details: { organization_id: organizationId }
    });

    throw new Error('Admin access required');
  }
}

/**
 * Require specific permission for action
 */
export async function requirePermission(
  userId: string,
  permission: Permission,
  resourceType?: ResourceType,
  resourceId?: string,
  organizationId?: string
): Promise<void> {
  const hasPermission = await rbac.hasPermission(
    userId,
    permission,
    resourceType,
    resourceId,
    organizationId
  );

  if (!hasPermission) {
    await logSecurityEvent({
      userId,
      action: 'PERMISSION_DENIED',
      resource: `${resourceType}:${resourceId}`,
      details: {
        required_permission: permission,
        resource_type: resourceType,
        resource_id: resourceId,
        organization_id: organizationId
      }
    });

    throw new Error(`Permission denied: ${permission}`);
  }
}
