name: 🚀 Production Deployment

on:
  push:
    branches: [main]
    tags: ['v*']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging
      skip_tests:
        description: 'Skip tests (emergency deployment)'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  DEPLOYMENT_TIMEOUT: '600' # 10 minutes

jobs:
  # Pre-deployment validation
  pre-deployment:
    name: 🔍 Pre-deployment Validation
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.determine-env.outputs.environment }}
      version: ${{ steps.version.outputs.version }}
      skip_tests: ${{ steps.determine-env.outputs.skip_tests }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🎯 Determine environment
        id: determine-env
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
            echo "skip_tests=${{ github.event.inputs.skip_tests }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "environment=production" >> $GITHUB_OUTPUT
            echo "skip_tests=false" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == refs/tags/* ]]; then
            echo "environment=production" >> $GITHUB_OUTPUT
            echo "skip_tests=false" >> $GITHUB_OUTPUT
          else
            echo "environment=staging" >> $GITHUB_OUTPUT
            echo "skip_tests=false" >> $GITHUB_OUTPUT
          fi

      - name: 📋 Extract version
        id: version
        run: |
          if [[ "${{ github.ref }}" == refs/tags/* ]]; then
            echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          else
            echo "version=$(date +%Y%m%d)-${GITHUB_SHA:0:7}" >> $GITHUB_OUTPUT
          fi

      - name: 📊 Environment summary
        run: |
          echo "🎯 **Deployment Summary**" >> $GITHUB_STEP_SUMMARY
          echo "- Environment: ${{ steps.determine-env.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "- Version: ${{ steps.version.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "- Skip Tests: ${{ steps.determine-env.outputs.skip_tests }}" >> $GITHUB_STEP_SUMMARY
          echo "- Commit: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY

  # Run tests (unless skipped)
  tests:
    name: 🧪 Run Tests
    runs-on: ubuntu-latest
    needs: [pre-deployment]
    if: needs.pre-deployment.outputs.skip_tests == 'false'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🧪 Run critical tests
        run: |
          npm run test:unit -- --testPathPattern="critical"
          npm run test:integration -- --testPathPattern="critical"
        env:
          CI: true

  # Database migration (production only)
  database-migration:
    name: 🗄️ Database Migration
    runs-on: ubuntu-latest
    needs: [pre-deployment, tests]
    if: always() && (needs.tests.result == 'success' || needs.pre-deployment.outputs.skip_tests == 'true') && needs.pre-deployment.outputs.environment == 'production'
    environment: 
      name: production-db
      url: https://supabase.com/dashboard/project/${{ secrets.SUPABASE_PROJECT_ID }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🗄️ Create database backup
        run: npm run db:backup
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

      - name: 🔄 Run migrations
        run: npm run migration:up
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

      - name: ✅ Verify migration
        run: npm run migration:verify
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

  # Deploy to Vercel
  deploy:
    name: 🚀 Deploy to ${{ needs.pre-deployment.outputs.environment }}
    runs-on: ubuntu-latest
    needs: [pre-deployment, tests, database-migration]
    if: always() && (needs.tests.result == 'success' || needs.pre-deployment.outputs.skip_tests == 'true') && (needs.database-migration.result == 'success' || needs.database-migration.result == 'skipped')
    environment: 
      name: ${{ needs.pre-deployment.outputs.environment }}
      url: ${{ steps.deploy.outputs.preview-url }}
    outputs:
      deployment-url: ${{ steps.deploy.outputs.preview-url }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Vercel
        id: deploy
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: ${{ needs.pre-deployment.outputs.environment == 'production' && '--prod' || '' }}
          github-comment: false
          working-directory: ./

      - name: 📊 Deployment summary
        run: |
          echo "🚀 **Deployment Successful**" >> $GITHUB_STEP_SUMMARY
          echo "- URL: ${{ steps.deploy.outputs.preview-url }}" >> $GITHUB_STEP_SUMMARY
          echo "- Environment: ${{ needs.pre-deployment.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "- Version: ${{ needs.pre-deployment.outputs.version }}" >> $GITHUB_STEP_SUMMARY

  # Post-deployment health checks
  health-checks:
    name: 🏥 Health Checks
    runs-on: ubuntu-latest
    needs: [pre-deployment, deploy]
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🏥 Run health checks
        run: npm run health-check
        env:
          HEALTH_CHECK_URL: ${{ needs.deploy.outputs.deployment-url }}
          HEALTH_CHECK_TIMEOUT: 30000

      - name: 🔍 API endpoint tests
        run: npm run test:api-endpoints
        env:
          API_BASE_URL: ${{ needs.deploy.outputs.deployment-url }}
          API_TEST_TOKEN: ${{ secrets.API_TEST_TOKEN }}

      - name: 📊 Performance check
        run: npm run lighthouse:production
        env:
          LIGHTHOUSE_URL: ${{ needs.deploy.outputs.deployment-url }}

  # Smoke tests
  smoke-tests:
    name: 💨 Smoke Tests
    runs-on: ubuntu-latest
    needs: [pre-deployment, deploy, health-checks]
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🎭 Install Playwright
        run: npx playwright install --with-deps chromium

      - name: 💨 Run smoke tests
        run: npm run test:smoke
        env:
          PLAYWRIGHT_BASE_URL: ${{ needs.deploy.outputs.deployment-url }}
          TEST_USER_EMAIL: ${{ secrets.PROD_TEST_USER_EMAIL }}
          TEST_USER_PASSWORD: ${{ secrets.PROD_TEST_USER_PASSWORD }}

      - name: 📊 Upload smoke test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: smoke-test-results
          path: |
            playwright-report/
            test-results/

  # Update monitoring and alerts
  monitoring-setup:
    name: 📊 Setup Monitoring
    runs-on: ubuntu-latest
    needs: [pre-deployment, deploy, health-checks]
    if: needs.pre-deployment.outputs.environment == 'production'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📊 Update Sentry release
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
        with:
          environment: ${{ needs.pre-deployment.outputs.environment }}
          version: ${{ needs.pre-deployment.outputs.version }}

      - name: 📈 Update PostHog deployment
        run: |
          curl -X POST https://app.posthog.com/api/projects/${{ secrets.POSTHOG_PROJECT_ID }}/annotations/ \
            -H "Authorization: Bearer ${{ secrets.POSTHOG_API_KEY }}" \
            -H "Content-Type: application/json" \
            -d '{
              "content": "Production deployment: ${{ needs.pre-deployment.outputs.version }}",
              "date_marker": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'",
              "creation_type": "USR"
            }'

      - name: 🔔 Setup alerts
        run: npm run setup-alerts
        env:
          DEPLOYMENT_VERSION: ${{ needs.pre-deployment.outputs.version }}
          DEPLOYMENT_URL: ${{ needs.deploy.outputs.deployment-url }}

  # Rollback capability
  rollback:
    name: 🔄 Rollback (Manual)
    runs-on: ubuntu-latest
    needs: [pre-deployment, deploy]
    if: failure() && needs.pre-deployment.outputs.environment == 'production'
    environment: 
      name: production-rollback
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔄 Rollback deployment
        run: |
          echo "🔄 Initiating rollback procedure..."
          # This would trigger your rollback mechanism
          # Could be a Vercel rollback, database migration rollback, etc.

      - name: 📢 Notify team of rollback
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#critical-alerts'
          text: '🚨 Production deployment failed and rollback initiated for ${{ github.ref }}'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Success notifications
  notify-success:
    name: 📢 Success Notifications
    runs-on: ubuntu-latest
    needs: [pre-deployment, deploy, health-checks, smoke-tests, monitoring-setup]
    if: success()
    steps:
      - name: 📢 Slack notification
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#deployments'
          text: |
            ✅ **Production Deployment Successful**
            
            🎯 Environment: ${{ needs.pre-deployment.outputs.environment }}
            📦 Version: ${{ needs.pre-deployment.outputs.version }}
            🔗 URL: ${{ needs.deploy.outputs.deployment-url }}
            ⏱️ Duration: ${{ github.event.head_commit.timestamp }}
            
            All health checks and smoke tests passed! 🎉
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: 📧 Email notification (production only)
        if: needs.pre-deployment.outputs.environment == 'production'
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: smtp.gmail.com
          server_port: 587
          username: ${{ secrets.EMAIL_USERNAME }}
          password: ${{ secrets.EMAIL_PASSWORD }}
          subject: '✅ Production Deployment Successful - ${{ needs.pre-deployment.outputs.version }}'
          to: ${{ secrets.DEPLOYMENT_NOTIFICATION_EMAIL }}
          from: '<EMAIL>'
          body: |
            Production deployment completed successfully!
            
            Version: ${{ needs.pre-deployment.outputs.version }}
            URL: ${{ needs.deploy.outputs.deployment-url }}
            Commit: ${{ github.sha }}
            
            All systems operational.

  # Failure notifications
  notify-failure:
    name: 📢 Failure Notifications
    runs-on: ubuntu-latest
    needs: [pre-deployment, deploy, health-checks, smoke-tests]
    if: failure()
    steps:
      - name: 📢 Critical Slack alert
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#critical-alerts'
          text: |
            🚨 **Production Deployment Failed**
            
            🎯 Environment: ${{ needs.pre-deployment.outputs.environment }}
            📦 Version: ${{ needs.pre-deployment.outputs.version }}
            💥 Failed at: ${{ job.status }}
            
            Immediate attention required! 🆘
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: 📧 Critical email alert
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: smtp.gmail.com
          server_port: 587
          username: ${{ secrets.EMAIL_USERNAME }}
          password: ${{ secrets.EMAIL_PASSWORD }}
          subject: '🚨 CRITICAL: Production Deployment Failed - ${{ needs.pre-deployment.outputs.version }}'
          to: ${{ secrets.CRITICAL_ALERT_EMAIL }}
          from: '<EMAIL>'
          body: |
            CRITICAL: Production deployment has failed!
            
            Version: ${{ needs.pre-deployment.outputs.version }}
            Environment: ${{ needs.pre-deployment.outputs.environment }}
            Commit: ${{ github.sha }}
            
            Please investigate immediately.
            
            GitHub Actions: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
