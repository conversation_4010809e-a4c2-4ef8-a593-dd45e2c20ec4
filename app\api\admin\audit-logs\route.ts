import { NextRequest, NextResponse } from 'next/server';
import { createRBACProtectedRoute, Permission } from '@/lib/auth-protection';
import { createSupabaseServerClient } from '@/lib/supabase-server';

/**
 * Admin Audit Logs API
 * 
 * Provides audit log data for admin dashboard
 */
export const GET = createRBACProtectedRoute(
  async (request: NextRequest, authResult) => {
    try {
      const supabase = await createSupabaseServerClient();
      const url = new URL(request.url);
      
      // Parse query parameters
      const limit = Math.min(parseInt(url.searchParams.get('limit') || '50'), 100);
      const offset = parseInt(url.searchParams.get('offset') || '0');
      const action = url.searchParams.get('action');
      const userId = url.searchParams.get('userId');
      const riskLevel = url.searchParams.get('riskLevel');
      
      // Build query
      let query = supabase
        .from('audit_logs')
        .select(`
          id,
          user_id,
          action,
          resource,
          risk_level,
          ip_address,
          user_agent,
          details,
          created_at
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);
      
      // Apply filters
      if (action) {
        query = query.eq('action', action);
      }
      
      if (userId) {
        query = query.eq('user_id', userId);
      }
      
      if (riskLevel) {
        query = query.eq('risk_level', riskLevel);
      }
      
      const { data: auditLogs, error: auditError } = await query;
      
      if (auditError) {
        console.error('Failed to fetch audit logs:', auditError);
        return NextResponse.json(
          { error: 'Failed to load audit logs' },
          { status: 500 }
        );
      }
      
      // Get total count for pagination
      let countQuery = supabase
        .from('audit_logs')
        .select('id', { count: 'exact', head: true });
      
      // Apply same filters to count
      if (action) {
        countQuery = countQuery.eq('action', action);
      }
      
      if (userId) {
        countQuery = countQuery.eq('user_id', userId);
      }
      
      if (riskLevel) {
        countQuery = countQuery.eq('risk_level', riskLevel);
      }
      
      const { count, error: countError } = await countQuery;
      
      if (countError) {
        console.error('Failed to count audit logs:', countError);
      }
      
      // Enrich logs with user information
      const enrichedLogs = auditLogs?.map(log => ({
        ...log,
        timestamp: log.created_at,
        severity: log.risk_level,
        description: getActionDescription(log.action, log.resource, log.details)
      })) || [];
      
      return NextResponse.json({
        logs: enrichedLogs,
        pagination: {
          total: count || 0,
          limit,
          offset,
          hasMore: (count || 0) > offset + limit
        },
        filters: {
          action,
          userId,
          riskLevel
        }
      });
      
    } catch (error) {
      console.error('Failed to get audit logs:', error);
      return NextResponse.json(
        { error: 'Failed to load audit logs' },
        { status: 500 }
      );
    }
  },
  {
    requiredPermission: Permission.AUDIT_VIEW,
    requireAuth: true,
    rateLimit: 30, // 30 requests per minute
    auditLog: true,
    allowedMethods: ['GET']
  }
);

/**
 * Generate human-readable description for audit log actions
 */
function getActionDescription(action: string, resource: string, details: any): string {
  const detailsObj = typeof details === 'string' ? JSON.parse(details || '{}') : details || {};
  
  switch (action) {
    case 'USER_LOGIN':
      return `User logged in from ${detailsObj.ip_address || 'unknown IP'}`;
    case 'USER_LOGOUT':
      return 'User logged out';
    case 'ROLE_ASSIGNED':
      return `Role ${detailsObj.role} assigned to user`;
    case 'ROLE_REVOKED':
      return `Role revoked from user`;
    case 'PAYMENT_CREATED':
      return `Payment of $${detailsObj.amount || 0} created`;
    case 'SUBSCRIPTION_CREATED':
      return `Subscription ${detailsObj.plan_id} created`;
    case 'SLACK_TOKEN_STORED':
      return 'Slack token securely stored';
    case 'SLACK_TOKEN_REFRESHED':
      return 'Slack token refreshed';
    case 'ADMIN_ACCESS_GRANTED':
      return `Admin access granted for ${resource}`;
    case 'ADMIN_ACCESS_DENIED':
      return `Admin access denied for ${resource}`;
    case 'RATE_LIMIT_EXCEEDED':
      return `Rate limit exceeded for ${resource}`;
    case 'SECURITY_VIOLATION':
      return `Security violation detected: ${detailsObj.violation_type || 'unknown'}`;
    default:
      return `${action.replace(/_/g, ' ').toLowerCase()} on ${resource}`;
  }
}
