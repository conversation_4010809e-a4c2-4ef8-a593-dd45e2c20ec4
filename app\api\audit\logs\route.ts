/**
 * Audit Logs API Route
 * Enterprise audit logging and compliance
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import {
  getAuditLogs,
  logAuditEvent,
  type AuditFilters,
} from '@/lib/audit-logging';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
// Removed: import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/audit/logs
 * Get audit logs with filtering
 */
export async function GET(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      // Audit logs require Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'Audit logs require Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const { searchParams } = new URL(req.url);
      
      const filters: AuditFilters = {
        organization_id: searchParams.get('organization_id') || user.id,
        user_id: searchParams.get('user_id') || undefined,
        action: searchParams.get('action') || undefined,
        resource_type: searchParams.get('resource_type') || undefined,
        risk_level: (searchParams.get('risk_level') as any) || undefined,
        status: (searchParams.get('status') as any) || undefined,
        start_date: searchParams.get('start_date') || undefined,
        end_date: searchParams.get('end_date') || undefined,
        limit: parseInt(searchParams.get('limit') || '50'),
        offset: parseInt(searchParams.get('offset') || '0'),
      };

      const result = await getAuditLogs(filters);

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      // Log the audit log access
      await logAuditEvent(
        user.id,
        context.userId,
        user.email,
        'audit.logs_accessed',
        'audit_logs',
        { filters },
        {
          ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || undefined,
          userAgent: req.headers.get('user-agent') || undefined,
          riskLevel: 'medium',
        }
      );

      return NextResponse.json({
        success: true,
        logs: result.logs,
        total: result.total,
        filters,
      });

    } catch (error) {
      console.error('Audit logs API error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to fetch audit logs' },
        { status: 500 }
      );
    }
  });
}

/**
 * POST /api/audit/logs
 * Create audit log entry (for manual logging)
 */
export async function POST(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      // Audit logs require Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'Audit logging requires Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const body = await req.json();
      const { 
        action, 
        resource_type, 
        resource_id, 
        details, 
        risk_level, 
        status,
        metadata 
      } = body;

      if (!action || !resource_type || !details) {
        return NextResponse.json(
          { error: 'Action, resource_type, and details are required' },
          { status: 400 }
        );
      }

      const result = await logAuditEvent(
        user.id,
        context.userId,
        user.email,
        action,
        resource_type,
        details,
        {
          resourceId: resource_id,
          ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || undefined,
          userAgent: req.headers.get('user-agent') || undefined,
          riskLevel: risk_level,
          status: status,
          metadata,
        }
      );

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        log_id: result.logId,
        message: 'Audit log created successfully',
      });

    } catch (error) {
      console.error('Audit log creation error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to create audit log' },
        { status: 500 }
      );
    }
  });
}
