#!/bin/bash

# =============================================================================
# SLACK SUMMARY SCRIBE - PRODUCTION DEPLOYMENT SCRIPT
# =============================================================================

set -e  # Exit on any error

echo "🚀 Starting production deployment for Slack Summary Scribe..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    print_error "Vercel CLI not found. Please install it with: npm i -g vercel"
    exit 1
fi

print_status "Validating environment..."

# Check for required environment variables
REQUIRED_VARS=(
    "NEXT_PUBLIC_SUPABASE_URL"
    "NEXT_PUBLIC_SUPABASE_ANON_KEY"
    "SUPABASE_SERVICE_ROLE_KEY"
    "OPENROUTER_API_KEY"
    "NEXT_PUBLIC_POSTHOG_KEY"
    "RESEND_API_KEY"
    "STRIPE_SECRET_KEY"
    "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"
)

print_status "Checking required environment variables..."
for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        print_warning "Environment variable $var is not set"
    else
        print_success "✓ $var is configured"
    fi
done

print_status "Running pre-deployment checks..."

# Clean install dependencies
print_status "Installing dependencies..."
npm ci

# Run type checking
print_status "Running TypeScript type check..."
npm run type-check || {
    print_error "TypeScript type check failed"
    exit 1
}

# Run linting
print_status "Running ESLint..."
npm run lint || {
    print_warning "ESLint found issues, but continuing..."
}

# Build the application
print_status "Building application..."
npm run build || {
    print_error "Build failed"
    exit 1
}

print_success "Build completed successfully!"

# Deploy to Vercel
print_status "Deploying to Vercel..."

# Check if this is a production deployment
if [ "$1" = "--production" ] || [ "$1" = "-p" ]; then
    print_status "Deploying to production..."
    vercel --prod --yes
else
    print_status "Deploying to preview..."
    vercel --yes
fi

print_success "Deployment completed!"

# Post-deployment checks
print_status "Running post-deployment checks..."

# Get the deployment URL
DEPLOYMENT_URL=$(vercel ls --limit 1 | grep -o 'https://[^ ]*' | head -1)

if [ -n "$DEPLOYMENT_URL" ]; then
    print_success "Deployment URL: $DEPLOYMENT_URL"
    
    # Basic health check
    print_status "Running health check..."
    if curl -f -s "$DEPLOYMENT_URL/api/health" > /dev/null; then
        print_success "Health check passed!"
    else
        print_warning "Health check failed - please verify manually"
    fi
    
    # Check if the main page loads
    print_status "Checking main page..."
    if curl -f -s "$DEPLOYMENT_URL" > /dev/null; then
        print_success "Main page loads successfully!"
    else
        print_warning "Main page check failed - please verify manually"
    fi
else
    print_warning "Could not determine deployment URL"
fi

print_success "🎉 Production deployment completed!"
print_status "Next steps:"
echo "  1. Verify the application at: $DEPLOYMENT_URL"
echo "  2. Test key features: upload, summarization, export"
echo "  3. Check analytics and monitoring dashboards"
echo "  4. Update DNS records if needed"
echo "  5. Announce the launch! 🚀"
