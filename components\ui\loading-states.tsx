'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { RefreshCw, Wifi, WifiOff, Clock, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SmartErrorMessage, ErrorMessageCard } from '@/components/ui/contextual-messages';

interface LoadingStateProps {
  isLoading: boolean;
  isSlowLoading: boolean;
  error: string | null;
  onRetry: () => void;
  responseTime?: number | null;
  lastRequestId?: string | null;
  children: React.ReactNode;
}

/**
 * Progressive Loading States Component
 * 
 * Provides different UI states based on loading progress:
 * - 0-1s: Skeleton loaders
 * - 1-5s: Spinner with progress
 * - 5-8s: Slow loading warning
 * - 8s+: Error state with retry
 */
export function ProgressiveLoadingState({
  isLoading,
  isSlowLoading,
  error,
  onRetry,
  responseTime,
  lastRequestId,
  children
}: LoadingStateProps) {
  if (error) {
    return (
      <ErrorState
        error={error}
        onRetry={onRetry}
        lastRequestId={lastRequestId}
      />
    );
  }

  if (isLoading) {
    return (
      <LoadingState
        isSlowLoading={isSlowLoading}
        responseTime={responseTime}
      />
    );
  }

  return (
    <div className="relative">
      {responseTime && (
        <StatusIndicator responseTime={responseTime} />
      )}
      {children}
    </div>
  );
}

/**
 * Dashboard Skeleton Loader
 */
export function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-32" />
        </div>
        <Skeleton className="h-10 w-24" />
      </div>

      {/* Stats cards skeleton */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main content skeleton */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-2/3" />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-48 w-full" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

/**
 * Loading State with Progressive Feedback
 */
function LoadingState({
  isSlowLoading,
  responseTime
}: {
  isSlowLoading: boolean;
  responseTime?: number | null;
}) {
  return (
    <div className="space-y-6">
      {isSlowLoading && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <Clock className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            This is taking longer than usual. Please wait while we load your data...
          </AlertDescription>
        </Alert>
      )}
      
      <DashboardSkeleton />
      
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center space-x-3 text-muted-foreground">
          <RefreshCw className="h-5 w-5 animate-spin" />
          <span>Loading your dashboard...</span>
        </div>
      </div>
    </div>
  );
}

/**
 * Error State with Contextual Messages and Retry
 */
function ErrorState({
  error,
  onRetry,
  lastRequestId
}: {
  error: string;
  onRetry: () => void;
  lastRequestId?: string | null;
}) {
  return (
    <div className="flex items-center justify-center min-h-[400px] p-4">
      <div className="w-full max-w-md space-y-4">
        <ErrorMessageCard
          error={error}
          onRetry={onRetry}
          onSignIn={() => {
            if (typeof window !== 'undefined') {
              window.location.href = '/login'
            }
          }}
        />

        {lastRequestId && process.env.NODE_ENV === 'development' && (
          <div className="text-xs text-muted-foreground text-center pt-2 border-t">
            Request ID: {lastRequestId}
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Status Indicator showing response time and connection status
 */
function StatusIndicator({ responseTime }: { responseTime: number }) {
  const getStatusColor = () => {
    if (responseTime < 500) return 'text-green-600';
    if (responseTime < 2000) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = () => {
    if (responseTime < 2000) return <Wifi className="h-3 w-3" />;
    return <WifiOff className="h-3 w-3" />;
  };

  return (
    <div className="absolute top-0 right-0 z-10">
      <div className={cn(
        "flex items-center space-x-1 text-xs px-2 py-1 rounded-bl-md bg-background/80 backdrop-blur-sm border-l border-b",
        getStatusColor()
      )}>
        {getStatusIcon()}
        <span>{responseTime}ms</span>
      </div>
    </div>
  );
}

/**
 * Retry Button Component
 */
export function RetryButton({
  onRetry,
  isLoading = false,
  className
}: {
  onRetry: () => void;
  isLoading?: boolean;
  className?: string;
}) {
  return (
    <Button
      onClick={onRetry}
      disabled={isLoading}
      variant="outline"
      size="sm"
      className={cn("gap-2", className)}
    >
      <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
      {isLoading ? 'Refreshing...' : 'Refresh'}
    </Button>
  );
}
