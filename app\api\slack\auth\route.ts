import { NextRequest, NextResponse } from 'next/server';
import { getSlackOAuthUrl } from '@/lib/slack';

export async function GET(request: NextRequest) {
  try {
    // No auth mode - using demo user
    const user = { id: 'demo-user', email: '<EMAIL>', name: 'Demo User' };

    // Get organization ID from query params
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id') || 'demo-org';

    // No auth mode - allow all access

    // Generate Slack OAuth URL
    const oauthUrl = getSlackOAuthUrl(
      process.env.SLACK_CLIENT_ID!,
      `${process.env.NEXT_PUBLIC_SITE_URL}/api/slack/callback`,
      organizationId
    );

    return NextResponse.json({
      success: true,
      oauth_url: oauthUrl,
    });

  } catch (error) {
    console.error('Slack auth error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
