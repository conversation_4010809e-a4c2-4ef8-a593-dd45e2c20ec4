import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { uploadStatusTracker } from '@/lib/upload-status-tracker';
import { parsePDF, parseDOCX, parseTextFile } from '@/lib/file-parser';
import { routeAIRequest } from '@/lib/ai-routing-service';
import { trackPublicFileUpload, trackPublicSummaryGenerated } from '@/lib/analytics';
import {
  createSecureApiRoute,
  createSuccessResponse,
  createErrorResponse,
  validateFileUpload,
  SECURITY_CONFIG
} from '@/lib/api-security';
import { auth } from '@clerk/nextjs/server';
import { withDemoLimitCheck } from '@/lib/demo-middleware';

// Apply demo limit check middleware
export const POST = withDemoLimitCheck('fileUploads')(async (request: NextRequest, context) => {
    try {
  devLog.log('📁 Upload API called');

      // Get authenticated user
      const { userId } = auth();

      if (!userId) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }
  devLog.log(`📁 Processing file upload for user: ${userId} (Demo: ${context.isInDemo})`);

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const fileId = formData.get('fileId') as string || `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  devLog.log('📁 File received:', file?.name, file?.size, file?.type);

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Initialize upload tracking and start timer
    const startTime = Date.now();
    uploadStatusTracker.initializeUpload(fileId, file.name, file.size);

    // Validate file using security utility
    const fileValidation = validateFileUpload(file);
    if (!fileValidation.valid) {
      return createErrorResponse(
        'Invalid file',
        fileValidation.error!,
        400,
        'FILE_VALIDATION_ERROR'
      );
    }

    // TODO: Add database record creation for file tracking
  devLog.log('📁 Processing file:', fileId);

    // Convert file to buffer for processing
    const buffer = Buffer.from(await file.arrayBuffer());
  devLog.log('📁 Starting file processing for:', fileId);

    // Process file in background
    processFileInBackground(fileId, buffer, file.type, userId, file.name);

    // Return success response
    return createSuccessResponse({
      fileId: fileId,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      uploadStatus: 'completed',
      processingStatus: 'processing',
      estimatedProcessingTime: '30-60 seconds'
    }, 'File uploaded and processing started');

  } catch (error) {
    console.error('Upload API error:', error);
    return createErrorResponse(
      'Upload failed',
      'An error occurred while processing your file upload',
      500,
      'UPLOAD_ERROR'
    );
  }
}, {
  requireAuth: true,
  rateLimit: SECURITY_CONFIG.UPLOAD_RATE_LIMIT, // 10 uploads per minute
  auditLog: true,
  allowedMethods: ['POST']
});

// Real file processing function with AI integration
async function processFileInBackground(fileId: string, buffer: Buffer, mimeType: string, userId: string, fileName: string) {
  const startTime = Date.now();

  try {
  devLog.log('📁 Processing file for upload:', fileId);

    // Update status to processing
    uploadStatusTracker.startProcessing(fileId);

    // Extract text content from file using actual parsers
    let parseResult;

    try {
      if (mimeType === 'application/pdf') {
  devLog.log('📁 Parsing PDF file:', fileName);
        parseResult = await parsePDF(buffer);
      } else if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
  devLog.log('📁 Parsing DOCX file:', fileName);
        parseResult = await parseDOCX(buffer);
      } else if (mimeType === 'text/plain') {
  devLog.log('📁 Parsing text file:', fileName);
        parseResult = await parseTextFile(buffer);
      } else {
        throw new Error(`Unsupported file type: ${mimeType}`);
      }

      // Check if parsing was successful
      if ('error' in parseResult) {
        throw new Error(parseResult.error);
      }

      const extractedText = parseResult.text;
  devLog.log('📁 Text extracted successfully, length:', extractedText.length, 'words:', parseResult.metadata.wordCount);

      // Update status
      uploadStatusTracker.updateProcessingStep(fileId, 'ai_processing', 'processing', 'Generating AI summary...', 30);

      // Generate AI summary using direct API call (bypassing routing service for public mode)
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
          'X-Title': 'Slack Summary Scribe'
        },
        body: JSON.stringify({
          model: 'tngtech/deepseek-r1t2-chimera:free',
          messages: [
            {
              role: 'system',
              content: `You are an expert document summarizer. Analyze the following document and provide a comprehensive summary with:

1. **Main Summary**: 2-3 sentences capturing the key points
2. **Key Skills/Topics**: List important skills, technologies, or topics mentioned
3. **Red Flags**: Any concerns, issues, or problems identified
4. **Suggested Actions**: Recommended next steps or actions

Format your response as JSON:
{
  "summary": "Main summary text",
  "keySkills": ["skill1", "skill2"],
  "redFlags": ["concern1", "concern2"],
  "suggestedActions": ["action1", "action2"]
}`
            },
            {
              role: 'user',
              content: extractedText
            }
          ],
          max_tokens: 1000,
          temperature: 0.3
        })
      });

      if (!response.ok) {
        throw new Error(`AI API error: ${response.status}`);
      }

      const aiData = await response.json();
      const aiContent = aiData.choices?.[0]?.message?.content || 'No summary generated';

      const summaryResponse = {
        text: aiContent,
        model: 'deepseek-r1',
        cost: 0,
        tokens: aiData.usage?.total_tokens || 0
      };

      // Update status
      uploadStatusTracker.updateProcessingStep(fileId, 'summary_generation', 'processing', 'Finalizing summary...', 80);

      // Create summary object
      const summaryId = `file-summary-${fileId}-${Date.now()}`;
      const summary = {
        id: summaryId,
        user_id: userId,
        title: `Summary of ${fileName}`,
        summary_text: summaryResponse.text,
        source_type: 'file_upload',
        ai_model: summaryResponse.model,
        ai_cost: summaryResponse.cost,
        ai_tokens: summaryResponse.tokens,
        created_at: new Date().toISOString(),
        metadata: {
          fileName,
          fileType: mimeType,
          fileSize: buffer.length,
          wordCount: parseResult.metadata.wordCount,
          characterCount: parseResult.metadata.characterCount,
          pages: parseResult.metadata.pages
        }
      };

      // Update status to completed
      uploadStatusTracker.markCompleted(fileId, summaryId);

      // Track analytics for file upload and summary generation
      const processingTime = (Date.now() - startTime) / 1000;
      trackPublicFileUpload(fileName, buffer.length, mimeType);
      trackPublicSummaryGenerated(summaryId, processingTime, summaryResponse.model, parseResult.text.length);
  devLog.log('📁 File processing completed successfully for:', fileId);
  devLog.log('📁 Generated summary ID:', summary.id);
  devLog.log('📁 Summary length:', summaryResponse.text.length, 'characters');
  devLog.log('📊 Analytics tracked: file upload and summary generation');

    } catch (error) {
      console.error('File processing error:', error);

      // Update status to error
      uploadStatusTracker.markError(
        fileId,
        error instanceof Error ? error.message : 'Unknown processing error'
      );
    }

  } catch (error) {
    console.error('Background processing error:', error);
  }
}





