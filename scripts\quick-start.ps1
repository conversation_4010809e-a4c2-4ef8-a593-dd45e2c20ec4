# Slack Summary Scribe - Public SaaS Quick Start Script (PowerShell)
# This script helps you get the application running quickly in public mode

param(
    [switch]$SkipChecks
)

Write-Host "🚀 Slack Summary Scribe - Public SaaS Quick Start" -ForegroundColor Green
Write-Host "==============================================" -ForegroundColor Green
Write-Host ""

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js $nodeVersion detected" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js 18+ first." -ForegroundColor Red
    Write-Host "   Visit: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Check Node.js version
$nodeMajorVersion = (node --version).Split('.')[0].TrimStart('v')
if ([int]$nodeMajorVersion -lt 18) {
    Write-Host "❌ Node.js version 18+ is required. Current version: $(node --version)" -ForegroundColor Red
    Write-Host "   Please upgrade Node.js to version 18 or higher." -ForegroundColor Yellow
    exit 1
}

# Check if npm is installed
try {
    $npmVersion = npm --version
    Write-Host "✅ npm $npmVersion detected" -ForegroundColor Green
} catch {
    Write-Host "❌ npm is not installed. Please install npm first." -ForegroundColor Red
    exit 1
}

# Check if .env.local exists
if (-not (Test-Path ".env.local")) {
    Write-Host "⚠️  .env.local file not found. Creating template..." -ForegroundColor Yellow
    
    $envContent = @"
# Slack Summary Scribe - Public SaaS Mode Configuration
# Copy this file to .env.local and fill in your actual API keys

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_SITE_URL=http://localhost:3001
NODE_ENV=development

# =============================================================================
# AI MODELS & SUMMARIZATION (CRITICAL)
# =============================================================================

# OpenAI API (Primary AI Provider)
OPENAI_API_KEY=sk-your_openai_api_key_here

# OpenRouter API (Fallback AI Provider)
OPENROUTER_API_KEY=sk-or-your_openrouter_api_key_here

# =============================================================================
# DATABASE & STORAGE
# =============================================================================

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =============================================================================
# ANALYTICS & MONITORING
# =============================================================================

# PostHog Analytics
NEXT_PUBLIC_POSTHOG_KEY=phc_your_posthog_key_here
POSTHOG_API_KEY=phx_your_posthog_api_key_here
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# =============================================================================
# NOTIFICATIONS & INTEGRATIONS
# =============================================================================

# Slack Integration
SLACK_BOT_TOKEN=xoxb-your_slack_bot_token_here
SLACK_APP_TOKEN=xapp-your_slack_app_token_here
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url

# =============================================================================
# EXPORT & INTEGRATION SERVICES
# =============================================================================

# Notion Integration
NOTION_CLIENT_ID=your_notion_client_id_here
NOTION_CLIENT_SECRET=your_notion_client_secret_here

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

NEXT_PUBLIC_FETCH_TIMEOUT=15000
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_DEMO_MODE=true
"@

    $envContent | Out-File -FilePath ".env.local" -Encoding UTF8
    Write-Host "✅ Created .env.local template" -ForegroundColor Green
    Write-Host ""
    Write-Host "⚠️  IMPORTANT: Please edit .env.local and add your actual API keys before continuing." -ForegroundColor Yellow
    Write-Host "   At minimum, you need:" -ForegroundColor Yellow
    Write-Host "   - OPENAI_API_KEY (or OPENROUTER_API_KEY)" -ForegroundColor Yellow
    Write-Host "   - NEXT_PUBLIC_SUPABASE_URL" -ForegroundColor Yellow
    Write-Host "   - NEXT_PUBLIC_SUPABASE_ANON_KEY" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "   Press Enter when you've updated .env.local, or Ctrl+C to exit..." -ForegroundColor Yellow
    Read-Host
} else {
    Write-Host "✅ .env.local file found" -ForegroundColor Green
}

# Install dependencies
Write-Host ""
Write-Host "📦 Installing dependencies..." -ForegroundColor Cyan
npm install

# Check if build is needed
if (-not (Test-Path ".next")) {
    Write-Host ""
    Write-Host "🔨 Building application..." -ForegroundColor Cyan
    npm run build
} else {
    Write-Host "✅ Application already built" -ForegroundColor Green
}

# Check environment variables
Write-Host ""
Write-Host "🔍 Checking environment configuration..." -ForegroundColor Cyan

# Check for required variables
$requiredVars = @("OPENAI_API_KEY", "NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY")
$missingVars = @()

foreach ($var in $requiredVars) {
    $envContent = Get-Content ".env.local" -Raw
    if ($envContent -notmatch "^${var}=" -or $envContent -match "^${var}=.*your_.*_here") {
        $missingVars += $var
    }
}

if ($missingVars.Count -gt 0) {
    Write-Host "⚠️  Warning: Some required environment variables are missing or have placeholder values:" -ForegroundColor Yellow
    foreach ($var in $missingVars) {
        Write-Host "   - $var" -ForegroundColor Yellow
    }
    Write-Host ""
    Write-Host "   The app will work with limited functionality. For full features, please configure all required variables." -ForegroundColor Yellow
    Write-Host ""
}

# Check for optional variables
$optionalVars = @("NEXT_PUBLIC_POSTHOG_KEY", "SLACK_WEBHOOK_URL", "NOTION_CLIENT_ID")
$optionalMissing = @()

foreach ($var in $optionalVars) {
    $envContent = Get-Content ".env.local" -Raw
    if ($envContent -notmatch "^${var}=" -or $envContent -match "^${var}=.*your_.*_here") {
        $optionalMissing += $var
    }
}

if ($optionalMissing.Count -gt 0) {
    Write-Host "ℹ️  Optional features not configured:" -ForegroundColor Blue
    foreach ($var in $optionalMissing) {
        switch ($var) {
            "NEXT_PUBLIC_POSTHOG_KEY" { Write-Host "   - Analytics tracking (PostHog)" -ForegroundColor Blue }
            "SLACK_WEBHOOK_URL" { Write-Host "   - Slack notifications" -ForegroundColor Blue }
            "NOTION_CLIENT_ID" { Write-Host "   - Notion export" -ForegroundColor Blue }
        }
    }
    Write-Host ""
}

# Start the application
Write-Host ""
Write-Host "🚀 Starting Slack Summary Scribe in public mode..." -ForegroundColor Green
Write-Host ""
Write-Host "📱 The application will be available at: http://localhost:3001" -ForegroundColor Cyan
Write-Host "🎯 Features available:" -ForegroundColor Cyan
Write-Host "   - AI-powered summarization" -ForegroundColor White
Write-Host "   - PDF and Excel export" -ForegroundColor White
Write-Host "   - Dashboard with analytics" -ForegroundColor White
Write-Host "   - File upload support" -ForegroundColor White
Write-Host "   - No authentication required" -ForegroundColor White
Write-Host ""
Write-Host "🛑 Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Start the development server
npm run dev 