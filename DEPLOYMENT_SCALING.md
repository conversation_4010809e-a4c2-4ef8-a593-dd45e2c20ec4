# 🚀 Deployment & Scaling Guide

## Overview

This guide covers deploying and scaling your Slack Summary Scribe SaaS platform from development to enterprise-grade production.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   CDN (Vercel)  │    │   Monitoring    │
│   (Nginx/ALB)   │    │   Global Edge   │    │ (Sentry/PostHog)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │    │  Queue Service  │    │   Redis Cache   │
│   (Vercel)      │    │ (Microservice)  │    │   (Upstash)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Supabase DB   │    │   File Storage  │    │   External APIs │
│   (PostgreSQL)  │    │   (Supabase)    │    │ (OpenAI/Slack)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🌍 Multi-Region Deployment

### Primary Regions
- **US East (Virginia)**: Primary region for US customers
- **EU West (Frankfurt)**: GDPR-compliant region for EU customers
- **Asia Pacific (Tokyo)**: Low-latency for Asian markets

### Configuration
```bash
# Vercel regions
vercel --regions iad1,fra1,hnd1

# Environment-specific deployments
vercel --prod --env production
vercel --env staging
```

## 📊 Scaling Strategies

### 1. Horizontal Scaling

#### Next.js Application
```yaml
# vercel.json
{
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30,
      "memory": 1024
    }
  },
  "regions": ["iad1", "fra1", "hnd1"]
}
```

#### Queue Service Scaling
```yaml
# docker-compose.production.yml
services:
  queue-service:
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
```

### 2. Database Scaling

#### Read Replicas
```sql
-- Supabase read replica configuration
CREATE PUBLICATION supabase_realtime FOR ALL TABLES;

-- Connection pooling
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
```

#### Connection Pooling
```typescript
// lib/supabase-server.ts
const supabase = createClient(url, key, {
  db: {
    schema: 'public',
  },
  auth: {
    autoRefreshToken: false,
    persistSession: false
  },
  global: {
    headers: { 'x-my-custom-header': 'my-app-name' },
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});
```

### 3. Caching Strategy

#### Multi-Layer Caching
```typescript
// lib/cache/strategy.ts
export class CacheStrategy {
  // L1: In-memory cache (Node.js)
  private memoryCache = new Map();
  
  // L2: Redis cache (distributed)
  private redisCache = new Redis(process.env.REDIS_URL);
  
  // L3: CDN cache (Vercel Edge)
  private cdnCache = {
    'Cache-Control': 'public, max-age=3600, s-maxage=86400'
  };
}
```

#### Cache Invalidation
```typescript
// lib/cache/invalidation.ts
export async function invalidateCache(pattern: string) {
  // Invalidate Redis
  const keys = await redis.keys(pattern);
  if (keys.length > 0) {
    await redis.del(...keys);
  }
  
  // Invalidate CDN
  await fetch('/api/revalidate', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${process.env.REVALIDATE_TOKEN}` },
    body: JSON.stringify({ pattern })
  });
}
```

## 🔧 Performance Optimization

### 1. Bundle Optimization

#### Next.js Configuration
```javascript
// next.config.js
const nextConfig = {
  experimental: {
    optimizeCss: true,
    optimizeServerReact: true,
    turbotrace: {
      logLevel: 'error'
    }
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production'
  },
  images: {
    domains: ['supabase.co', 'avatars.githubusercontent.com'],
    formats: ['image/avif', 'image/webp']
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false
      };
    }
    return config;
  }
};
```

### 2. Database Optimization

#### Query Optimization
```sql
-- Add indexes for common queries
CREATE INDEX CONCURRENTLY idx_summaries_user_created 
ON summaries(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_summaries_organization_status 
ON summaries(organization_id, status) 
WHERE status IN ('completed', 'processing');

-- Partial indexes for better performance
CREATE INDEX CONCURRENTLY idx_active_oauth_tokens 
ON oauth_tokens(user_id, provider) 
WHERE status = 'active';
```

#### Connection Pooling
```typescript
// lib/db/pool.ts
import { Pool } from 'pg';

export const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});
```

### 3. API Optimization

#### Response Compression
```typescript
// middleware.ts
import { NextResponse } from 'next/server';

export function middleware(request: Request) {
  const response = NextResponse.next();
  
  // Enable compression
  response.headers.set('Content-Encoding', 'gzip');
  
  // Cache static assets
  if (request.url.includes('/_next/static/')) {
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
  }
  
  return response;
}
```

## 📈 Monitoring & Observability

### 1. Application Monitoring

#### Sentry Configuration
```typescript
// lib/sentry.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  beforeSend(event) {
    // Filter out noise
    if (event.exception) {
      const error = event.exception.values?.[0];
      if (error?.type === 'ChunkLoadError') {
        return null;
      }
    }
    return event;
  }
});
```

#### PostHog Analytics
```typescript
// lib/posthog.ts
import { PostHog } from 'posthog-node';

export const posthog = new PostHog(
  process.env.NEXT_PUBLIC_POSTHOG_KEY!,
  {
    host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
    flushAt: 20,
    flushInterval: 10000
  }
);
```

### 2. Infrastructure Monitoring

#### Health Checks
```typescript
// app/api/health/detailed/route.ts
export async function GET() {
  const checks = await Promise.allSettled([
    checkDatabase(),
    checkRedis(),
    checkExternalAPIs(),
    checkQueueService()
  ]);
  
  const health = {
    status: checks.every(c => c.status === 'fulfilled') ? 'healthy' : 'degraded',
    checks: checks.map((check, index) => ({
      name: ['database', 'redis', 'external', 'queue'][index],
      status: check.status,
      ...(check.status === 'rejected' && { error: check.reason })
    })),
    timestamp: new Date().toISOString()
  };
  
  return Response.json(health);
}
```

### 3. Performance Metrics

#### Custom Metrics
```typescript
// lib/metrics.ts
import { register, Counter, Histogram, Gauge } from 'prom-client';

export const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

export const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route']
});

export const activeConnections = new Gauge({
  name: 'active_connections',
  help: 'Number of active connections'
});
```

## 🔒 Security & Compliance

### 1. Security Headers
```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  
  // Security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  // HSTS for production
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }
  
  return response;
}
```

### 2. Rate Limiting
```typescript
// lib/rate-limit.ts
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!
});

export const ratelimit = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(100, '1 h'),
  analytics: true
});
```

## 🚀 Deployment Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] SSL certificates installed
- [ ] CDN configured
- [ ] Monitoring setup
- [ ] Backup strategy implemented

### Deployment Steps
1. **Build & Test**
   ```bash
   npm run build
   npm run test
   npm run lint
   ```

2. **Deploy to Staging**
   ```bash
   vercel --env staging
   ```

3. **Run E2E Tests**
   ```bash
   npm run test:e2e
   ```

4. **Deploy to Production**
   ```bash
   vercel --prod
   ```

5. **Post-Deployment Verification**
   ```bash
   curl https://your-domain.com/health
   npm run test:smoke
   ```

### Post-Deployment
- [ ] Health checks passing
- [ ] Monitoring alerts configured
- [ ] Performance metrics baseline
- [ ] Error tracking active
- [ ] Backup verification

## 📊 Scaling Milestones

### 1K Users
- Single region deployment
- Basic monitoring
- Manual scaling

### 10K Users
- Multi-region deployment
- Auto-scaling enabled
- Advanced monitoring

### 100K Users
- Microservices architecture
- Database sharding
- Advanced caching

### 1M+ Users
- Multi-cloud deployment
- Edge computing
- AI-powered optimization

## 🔧 Troubleshooting

### Common Issues

#### High Response Times
```bash
# Check database performance
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

# Check Redis performance
redis-cli --latency-history
```

#### Memory Leaks
```bash
# Monitor memory usage
node --inspect app.js
# Use Chrome DevTools for heap analysis
```

#### Database Connection Issues
```bash
# Check connection pool
SELECT count(*) as active_connections 
FROM pg_stat_activity 
WHERE state = 'active';
```

This comprehensive deployment and scaling guide ensures your Slack Summary Scribe can grow from startup to enterprise scale while maintaining performance, security, and reliability.
