#!/usr/bin/env tsx

/**
 * Enhanced Development Startup Script for Slack Summary Scribe SaaS
 *
 * Features:
 * - Intelligent port conflict resolution
 * - Production-grade environment validation
 * - Live service connectivity testing
 * - Automatic environment variable updates
 * - Comprehensive health checks
 * - Zero-regression development environment
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import spawn from 'cross-spawn';
import net from 'net';

// Load environment variables from .env.local
function loadEnvironmentVariables() {
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
        const [key, ...valueParts] = trimmed.split('=');
        const value = valueParts.join('=');
        if (key && value && !process.env[key]) {
          process.env[key] = value;
        }
      }
    }
  }
}

// Load environment variables at startup
loadEnvironmentVariables();

// Configuration
const DEFAULT_PORT = 3000;
const MAX_PORT = 3010;
const ENV_FILE = path.resolve(__dirname, '../.env.local');
const HEALTH_CHECK_TIMEOUT = 5000;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function isWindows() {
  return process.platform === 'win32';
}

/**
 * Enhanced port checking using native Node.js net module
 * More reliable than shell commands
 */
function checkPortAvailable(port: number): Promise<boolean> {
  return new Promise((resolve) => {
    const server = net.createServer();

    server.listen(port, () => {
      server.once('close', () => {
        resolve(true); // Port is available
      });
      server.close();
    });

    server.on('error', () => {
      resolve(false); // Port is in use
    });
  });
}

/**
 * Intelligent port conflict resolution
 * Only kills processes if they're actually blocking development
 */
async function resolvePortConflict(port: number): Promise<boolean> {
  try {
    if (isWindows()) {
      const output = execSync(`netstat -ano | findstr :${port}`, { encoding: 'utf8' });
      const lines = output.split('\n').filter(Boolean);

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        const pid = parts[parts.length - 1];

        if (pid && pid !== '0') {
          // Check if this is a Next.js process we can safely kill
          try {
            const processInfo = execSync(`tasklist /FI "PID eq ${pid}" /FO CSV`, { encoding: 'utf8' });
            if (processInfo.includes('node.exe') || processInfo.includes('next')) {
              log(`  Killing Next.js process (PID: ${pid})`, 'yellow');
              execSync(`taskkill /PID ${pid} /F`, { stdio: 'ignore' });
              return true;
            }
          } catch {
            // Process might have already exited
          }
        }
      }
    } else {
      // Unix-like systems
      try {
        const pids = execSync(`lsof -ti :${port}`, { encoding: 'utf8' }).trim().split('\n');
        for (const pid of pids) {
          if (pid) {
            const processInfo = execSync(`ps -p ${pid} -o comm=`, { encoding: 'utf8' }).trim();
            if (processInfo.includes('node') || processInfo.includes('next')) {
              log(`  Killing Next.js process (PID: ${pid})`, 'yellow');
              execSync(`kill -9 ${pid}`, { stdio: 'ignore' });
              return true;
            }
          }
        }
      } catch {
        // No processes found or already killed
      }
    }

    return false;
  } catch (error) {
    log(`  Warning: Could not resolve port conflict: ${error}`, 'yellow');
    return false;
  }
}

/**
 * Find the next available port in range
 */
async function findAvailablePort(start: number, end: number): Promise<number> {
  for (let port = start; port <= end; port++) {
    if (await checkPortAvailable(port)) {
      return port;
    }
  }
  throw new Error(`No available ports found in range ${start}-${end}`);
}

/**
 * Update environment variables for development
 */
function updateEnvironmentVariables(port: number) {
  try {
    let envContent = fs.readFileSync(ENV_FILE, 'utf8');

    // Update development-specific variables
    const updates = [
      { regex: /^NODE_ENV=.*$/m, value: 'NODE_ENV=development' },
      { regex: /^NEXT_PUBLIC_SITE_URL=.*$/m, value: `NEXT_PUBLIC_SITE_URL=http://localhost:${port}` },
      { regex: /^NEXT_PUBLIC_APP_URL=.*$/m, value: `NEXT_PUBLIC_APP_URL=http://localhost:${port}` },
    ];

    for (const { regex, value } of updates) {
      if (regex.test(envContent)) {
        envContent = envContent.replace(regex, value);
      } else {
        envContent += `\n${value}\n`;
      }
    }

    fs.writeFileSync(ENV_FILE, envContent, 'utf8');
    log(`  Environment variables updated for port ${port}`, 'green');
  } catch (error) {
    log(`  Warning: Could not update environment variables: ${error}`, 'yellow');
  }
}

/**
 * Validate critical environment variables
 */
function validateEnvironment(): boolean {
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'OPENROUTER_API_KEY',
    'SLACK_CLIENT_ID',
    'SLACK_CLIENT_SECRET',
  ];

  const missing = requiredVars.filter(varName => {
    const value = process.env[varName];
    return !value || value.includes('your_') || value.includes('placeholder');
  });

  if (missing.length > 0) {
    log('❌ Missing or invalid environment variables:', 'red');
    missing.forEach(varName => log(`   - ${varName}`, 'red'));
    return false;
  }

  log('✅ All critical environment variables configured', 'green');
  return true;
}

/**
 * Test connectivity to external services
 */
async function testServiceConnectivity(): Promise<boolean> {
  const tests = [
    {
      name: 'Supabase Database',
      test: async () => {
        const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`, {
          headers: {
            'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
            'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          },
        });
        return response.ok;
      }
    },
    {
      name: 'OpenRouter AI API',
      test: async () => {
        const response = await fetch('https://openrouter.ai/api/v1/models', {
          headers: {
            'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          },
        });
        return response.ok;
      }
    }
  ];

  let allPassed = true;

  for (const { name, test } of tests) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), HEALTH_CHECK_TIMEOUT);

      const passed = await test();
      clearTimeout(timeoutId);

      if (passed) {
        log(`  ✅ ${name} connection successful`, 'green');
      } else {
        log(`  ⚠️  ${name} connection failed (non-blocking)`, 'yellow');
      }
    } catch (error) {
      log(`  ⚠️  ${name} connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'yellow');
    }
  }

  return allPassed;
}

/**
 * Display startup banner with system information
 */
function displayStartupBanner() {
  log('\n' + '='.repeat(60), 'cyan');
  log('🚀 SLACK SUMMARY SCRIBE - DEVELOPMENT SERVER', 'cyan');
  log('='.repeat(60), 'cyan');
  log('Production-Grade Local Development Environment', 'bright');
  log('Live Data • Real APIs • Full SaaS Functionality\n', 'bright');
}

/**
 * Display success message with important information
 */
function displaySuccessMessage(port: number) {
  log('\n' + '='.repeat(60), 'green');
  log('✅ DEVELOPMENT SERVER READY', 'green');
  log('='.repeat(60), 'green');
  log(`🌐 Application: http://localhost:${port}`, 'bright');
  log(`📊 Dashboard: http://localhost:${port}/dashboard`, 'bright');
  log(`📝 Upload: http://localhost:${port}/upload`, 'bright');
  log(`⚙️  API Health: http://localhost:${port}/api/health`, 'bright');
  log('\n🔥 Features Ready:', 'yellow');
  log('   • Live Supabase Database Connection', 'green');
  log('   • AI Summarization (DeepSeek + GPT-4o-mini)', 'green');
  log('   • Slack OAuth Integration', 'green');
  log('   • File Upload & Processing', 'green');
  log('   • Export Functions (PDF, Excel, Notion)', 'green');
  log('   • Real-time Dashboard Analytics', 'green');
  log('   • Error Tracking & Monitoring', 'green');
  log('\n💡 Ready for production-grade development!', 'cyan');
  log('='.repeat(60) + '\n', 'green');
}

/**
 * Enhanced main function with comprehensive startup sequence
 */
async function main() {
  try {
    displayStartupBanner();

    // Step 1: Environment validation
    log('🔍 Validating Environment Configuration...', 'blue');
    if (!validateEnvironment()) {
      log('\n❌ Environment validation failed. Please check your .env.local file.', 'red');
      process.exit(1);
    }

    // Step 2: Port resolution
    log('\n🔌 Resolving Port Configuration...', 'blue');
    let port = DEFAULT_PORT;

    const isPortAvailable = await checkPortAvailable(port);
    if (!isPortAvailable) {
      log(`  Port ${port} is in use, attempting intelligent resolution...`, 'yellow');

      const resolved = await resolvePortConflict(port);
      if (resolved) {
        // Wait a moment for the port to be freed
        await new Promise(resolve => setTimeout(resolve, 2000));

        if (await checkPortAvailable(port)) {
          log(`  ✅ Successfully freed port ${port}`, 'green');
        } else {
          port = await findAvailablePort(DEFAULT_PORT + 1, MAX_PORT);
          log(`  ✅ Using alternative port: ${port}`, 'green');
        }
      } else {
        port = await findAvailablePort(DEFAULT_PORT + 1, MAX_PORT);
        log(`  ✅ Using alternative port: ${port}`, 'green');
      }
    } else {
      log(`  ✅ Port ${port} is available`, 'green');
    }

    // Step 3: Update environment variables
    log('\n⚙️  Updating Environment Variables...', 'blue');
    updateEnvironmentVariables(port);

    // Step 4: Test service connectivity
    log('\n🌐 Testing Service Connectivity...', 'blue');
    await testServiceConnectivity();

    // Step 5: Start development server
    log('\n🚀 Starting Next.js Development Server...', 'blue');

    const bin = isWindows() ? 'npx.cmd' : 'npx';
    const child = spawn(bin, ['next', 'dev', '-p', String(port)], {
      stdio: 'inherit',
      env: {
        ...process.env,
        PORT: String(port),
        NODE_ENV: 'development',
        NEXT_PUBLIC_SITE_URL: `http://localhost:${port}`,
        NEXT_PUBLIC_APP_URL: `http://localhost:${port}`,
      },
      cwd: path.resolve(__dirname, '..'),
    });

    // Display success message after a brief delay to let Next.js start
    setTimeout(() => {
      displaySuccessMessage(port);
    }, 3000);

    // Handle process events
    child.on('exit', (code) => {
      log('\n👋 Development server stopped.', 'yellow');
      process.exit(code || 0);
    });

    child.on('error', (err) => {
      log(`\n❌ Failed to start Next.js development server: ${err.message}`, 'red');
      process.exit(1);
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      log('\n\n🛑 Shutting down development server...', 'yellow');
      child.kill('SIGINT');
    });

    process.on('SIGTERM', () => {
      log('\n\n🛑 Shutting down development server...', 'yellow');
      child.kill('SIGTERM');
    });

  } catch (error) {
    log(`\n❌ Startup failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'red');
    log('\n🔧 Troubleshooting:', 'yellow');
    log('   1. Check your .env.local file configuration', 'yellow');
    log('   2. Ensure all required services are accessible', 'yellow');
    log('   3. Verify no other processes are blocking ports 3000-3010', 'yellow');
    log('   4. Try running: npm run dev:clean', 'yellow');
    process.exit(1);
  }
}

// Execute main function
main().catch((error) => {
  console.error('Unhandled error:', error);
  process.exit(1);
});