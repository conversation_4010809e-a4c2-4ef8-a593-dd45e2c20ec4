'use client';

import { useEffect } from 'react';
import Head from 'next/head';
import { usePathname } from 'next/navigation';

interface SEOOptimizerProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  noIndex?: boolean;
  canonicalUrl?: string;
  structuredData?: object;
  breadcrumbs?: Array<{ name: string; url: string }>;
}

export default function SEOOptimizer({
  title,
  description,
  keywords = [],
  image,
  noIndex = false,
  canonicalUrl,
  structuredData,
  breadcrumbs
}: SEOOptimizerProps) {
  const pathname = usePathname();
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://slack-summary-scribe.vercel.app';
  const fullUrl = `${baseUrl}${pathname}`;
  
  // Default values
  const defaultTitle = 'Slack Summary Scribe - Enterprise AI Summarization SaaS Platform';
  const defaultDescription = 'Enterprise-grade AI summarization platform for Slack conversations, documents, and meeting transcripts. Secure authentication, role-based access, payment processing, and advanced analytics for teams.';
  const defaultImage = `${baseUrl}/og-image.png`;
  
  const finalTitle = title || defaultTitle;
  const finalDescription = description || defaultDescription;
  const finalImage = image || defaultImage;
  const finalCanonicalUrl = canonicalUrl || fullUrl;

  // Generate page-specific structured data
  const generateStructuredData = () => {
    const baseStructuredData = {
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      name: 'Slack Summary Scribe',
      description: finalDescription,
      url: baseUrl,
      applicationCategory: 'BusinessApplication',
      operatingSystem: 'Web Browser',
      offers: {
        '@type': 'Offer',
        price: '29.00',
        priceCurrency: 'USD',
        priceValidUntil: '2025-12-31',
        availability: 'https://schema.org/InStock'
      },
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: '4.8',
        ratingCount: '150',
        bestRating: '5',
        worstRating: '1'
      },
      author: {
        '@type': 'Organization',
        name: 'Slack Summary Scribe Team',
        url: baseUrl
      },
      publisher: {
        '@type': 'Organization',
        name: 'Slack Summary Scribe',
        url: baseUrl,
        logo: {
          '@type': 'ImageObject',
          url: `${baseUrl}/logo.png`
        }
      }
    };

    // Add breadcrumbs if provided
    if (breadcrumbs && breadcrumbs.length > 0) {
      const breadcrumbStructuredData = {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: breadcrumbs.map((crumb, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: crumb.name,
          item: `${baseUrl}${crumb.url}`
        }))
      };
      
      return [baseStructuredData, breadcrumbStructuredData];
    }

    // Merge with custom structured data
    if (structuredData) {
      return { ...baseStructuredData, ...structuredData };
    }

    return baseStructuredData;
  };

  // Page-specific optimizations
  useEffect(() => {
    // Track page view for SEO analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        page_title: finalTitle,
        page_location: fullUrl
      });
    }

    // PostHog page tracking
    if (typeof window !== 'undefined' && (window as any).posthog) {
      (window as any).posthog.capture('$pageview', {
        title: finalTitle,
        url: fullUrl,
        path: pathname
      });
    }
  }, [finalTitle, fullUrl, pathname]);

  const structuredDataJson = JSON.stringify(generateStructuredData());

  return (
    <Head>
      {/* Primary Meta Tags */}
      <title>{finalTitle}</title>
      <meta name="title" content={finalTitle} />
      <meta name="description" content={finalDescription} />
      {keywords.length > 0 && <meta name="keywords" content={keywords.join(', ')} />}
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalCanonicalUrl} />
      
      {/* Robots */}
      <meta name="robots" content={noIndex ? 'noindex,nofollow' : 'index,follow'} />
      <meta name="googlebot" content={noIndex ? 'noindex,nofollow' : 'index,follow'} />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content="website" />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={finalImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="Slack Summary Scribe" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={fullUrl} />
      <meta property="twitter:title" content={finalTitle} />
      <meta property="twitter:description" content={finalDescription} />
      <meta property="twitter:image" content={finalImage} />
      <meta property="twitter:creator" content="@SlackSummaryScribe" />
      
      {/* Additional Meta Tags */}
      <meta name="author" content="Slack Summary Scribe Team" />
      <meta name="publisher" content="Slack Summary Scribe" />
      <meta name="copyright" content="© 2024 Slack Summary Scribe. All rights reserved." />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
      <meta name="distribution" content="global" />
      <meta name="rating" content="general" />
      
      {/* Mobile Optimization */}
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="Slack Summary Scribe" />
      
      {/* Theme Colors */}
      <meta name="theme-color" content="#3b82f6" />
      <meta name="msapplication-TileColor" content="#3b82f6" />
      <meta name="msapplication-navbutton-color" content="#3b82f6" />
      
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: structuredDataJson }}
      />
      
      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="dns-prefetch" href="https://api.openrouter.ai" />
      <link rel="dns-prefetch" href="https://clerk.dev" />
      
      {/* Favicon and Icons */}
      <link rel="icon" href="/favicon.ico" sizes="any" />
      <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
      <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/manifest.json" />
      
      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
      
      {/* Performance Hints */}
      <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
    </Head>
  );
}

// Hook for dynamic SEO optimization
export function useSEO() {
  const pathname = usePathname();
  
  const generatePageSEO = (pageType: string, data?: any) => {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://slack-summary-scribe.vercel.app';
    
    switch (pageType) {
      case 'dashboard':
        return {
          title: 'Dashboard - Slack Summary Scribe',
          description: 'View your AI-generated summaries, analytics, and manage your Slack integrations from your personalized dashboard.',
          keywords: ['dashboard', 'ai summaries', 'slack analytics', 'team insights']
        };
        
      case 'upload':
        return {
          title: 'Upload Files - Slack Summary Scribe',
          description: 'Upload documents, meeting transcripts, and files for AI-powered summarization and analysis.',
          keywords: ['file upload', 'document analysis', 'ai summarization', 'meeting transcripts']
        };
        
      case 'summary':
        return {
          title: data?.title ? `${data.title} - Summary` : 'AI Summary - Slack Summary Scribe',
          description: data?.description || 'View detailed AI-generated summary with key insights, action items, and analysis.',
          keywords: ['ai summary', 'conversation analysis', 'meeting insights', 'action items']
        };
        
      case 'pricing':
        return {
          title: 'Pricing Plans - Slack Summary Scribe',
          description: 'Choose the perfect plan for your team. Enterprise-grade AI summarization with flexible pricing options.',
          keywords: ['pricing', 'subscription plans', 'enterprise ai', 'team collaboration']
        };
        
      case 'billing':
        return {
          title: 'Billing & Subscription - Slack Summary Scribe',
          description: 'Manage your subscription, view billing history, and update payment methods for your AI summarization platform.',
          keywords: ['billing', 'subscription management', 'payment', 'account settings']
        };
        
      default:
        return {
          title: 'Slack Summary Scribe - Enterprise AI Summarization SaaS Platform',
          description: 'Enterprise-grade AI summarization platform for Slack conversations, documents, and meeting transcripts.',
          keywords: ['slack ai', 'enterprise saas', 'conversation summaries', 'team productivity']
        };
    }
  };
  
  const generateBreadcrumbs = (path: string) => {
    const segments = path.split('/').filter(Boolean);
    const breadcrumbs = [{ name: 'Home', url: '/' }];
    
    let currentPath = '';
    segments.forEach(segment => {
      currentPath += `/${segment}`;
      const name = segment.charAt(0).toUpperCase() + segment.slice(1).replace('-', ' ');
      breadcrumbs.push({ name, url: currentPath });
    });
    
    return breadcrumbs;
  };
  
  return {
    generatePageSEO,
    generateBreadcrumbs,
    currentPath: pathname
  };
}
