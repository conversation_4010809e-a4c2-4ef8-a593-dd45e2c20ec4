/**
 * Request Tracing & Performance Monitoring
 * 
 * Provides comprehensive request tracing with:
 * - Distributed tracing
 * - Performance metrics
 * - Error tracking
 * - CDN optimization
 */

import { NextRequest, NextResponse } from 'next/server';
// import { v4 as uuidv4 } from 'uuid';

// Simple UUID v4 generator fallback
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export interface TraceContext {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  userId?: string;
  organizationId?: string;
  userAgent?: string;
  ip?: string;
  country?: string;
  region?: string;
}

export interface TraceSpan {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  operationName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  tags: Record<string, any>;
  logs: Array<{
    timestamp: number;
    level: 'info' | 'warn' | 'error';
    message: string;
    data?: any;
  }>;
  status: 'ok' | 'error' | 'timeout';
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

export interface PerformanceMetrics {
  requestCount: number;
  errorCount: number;
  avgResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  throughput: number;
  errorRate: number;
  cacheHitRate: number;
}

export class RequestTracer {
  private spans: Map<string, TraceSpan> = new Map();
  private metrics: Map<string, number[]> = new Map();
  private static instance: RequestTracer;

  static getInstance(): RequestTracer {
    if (!RequestTracer.instance) {
      RequestTracer.instance = new RequestTracer();
    }
    return RequestTracer.instance;
  }

  /**
   * Start a new trace
   */
  startTrace(operationName: string, context?: Partial<TraceContext>): TraceSpan {
    const traceId = context?.traceId || generateUUID();
    const spanId = generateUUID();
    
    const span: TraceSpan = {
      traceId,
      spanId,
      parentSpanId: context?.parentSpanId,
      operationName,
      startTime: Date.now(),
      tags: {
        userId: context?.userId,
        organizationId: context?.organizationId,
        userAgent: context?.userAgent,
        ip: context?.ip,
        country: context?.country,
        region: context?.region
      },
      logs: [],
      status: 'ok'
    };

    this.spans.set(spanId, span);
    return span;
  }

  /**
   * Start a child span
   */
  startChildSpan(parentSpan: TraceSpan, operationName: string): TraceSpan {
    return this.startTrace(operationName, {
      traceId: parentSpan.traceId,
      parentSpanId: parentSpan.spanId
    });
  }

  /**
   * Finish a span
   */
  finishSpan(spanId: string, status: 'ok' | 'error' | 'timeout' = 'ok', error?: Error): void {
    const span = this.spans.get(spanId);
    if (!span) return;

    span.endTime = Date.now();
    span.duration = span.endTime - span.startTime;
    span.status = status;

    if (error) {
      span.error = {
        name: error.name,
        message: error.message,
        stack: error.stack
      };
    }

    // Record metrics
    this.recordMetrics(span);

    // Send to monitoring service
    this.sendToMonitoring(span);

    // Clean up
    this.spans.delete(spanId);
  }

  /**
   * Add log to span
   */
  addLog(
    spanId: string, 
    level: 'info' | 'warn' | 'error', 
    message: string, 
    data?: any
  ): void {
    const span = this.spans.get(spanId);
    if (!span) return;

    span.logs.push({
      timestamp: Date.now(),
      level,
      message,
      data
    });
  }

  /**
   * Add tag to span
   */
  addTag(spanId: string, key: string, value: any): void {
    const span = this.spans.get(spanId);
    if (!span) return;

    span.tags[key] = value;
  }

  /**
   * Get span by ID
   */
  getSpan(spanId: string): TraceSpan | undefined {
    return this.spans.get(spanId);
  }

  /**
   * Get performance metrics
   */
  getMetrics(operationName?: string): PerformanceMetrics {
    const key = operationName || 'all';
    const responseTimes = this.metrics.get(`${key}:response_time`) || [];
    const errorCounts = this.metrics.get(`${key}:errors`) || [];
    const cacheHits = this.metrics.get(`${key}:cache_hits`) || [];
    const cacheTotal = this.metrics.get(`${key}:cache_total`) || [];

    if (responseTimes.length === 0) {
      return {
        requestCount: 0,
        errorCount: 0,
        avgResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        throughput: 0,
        errorRate: 0,
        cacheHitRate: 0
      };
    }

    const sortedTimes = [...responseTimes].sort((a, b) => a - b);
    const totalRequests = responseTimes.length;
    const totalErrors = errorCounts.reduce((sum, count) => sum + count, 0);
    const totalCacheHits = cacheHits.reduce((sum, hits) => sum + hits, 0);
    const totalCacheRequests = cacheTotal.reduce((sum, total) => sum + total, 0);

    return {
      requestCount: totalRequests,
      errorCount: totalErrors,
      avgResponseTime: responseTimes.reduce((sum, time) => sum + time, 0) / totalRequests,
      p95ResponseTime: sortedTimes[Math.floor(totalRequests * 0.95)] || 0,
      p99ResponseTime: sortedTimes[Math.floor(totalRequests * 0.99)] || 0,
      throughput: totalRequests / 60, // requests per minute
      errorRate: totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0,
      cacheHitRate: totalCacheRequests > 0 ? (totalCacheHits / totalCacheRequests) * 100 : 0
    };
  }

  // Private methods

  private recordMetrics(span: TraceSpan): void {
    const operation = span.operationName;
    
    // Record response time
    this.addMetric(`${operation}:response_time`, span.duration || 0);
    this.addMetric('all:response_time', span.duration || 0);

    // Record errors
    if (span.status === 'error') {
      this.addMetric(`${operation}:errors`, 1);
      this.addMetric('all:errors', 1);
    }

    // Record cache metrics
    if (span.tags.cacheHit !== undefined) {
      this.addMetric(`${operation}:cache_hits`, span.tags.cacheHit ? 1 : 0);
      this.addMetric(`${operation}:cache_total`, 1);
      this.addMetric('all:cache_hits', span.tags.cacheHit ? 1 : 0);
      this.addMetric('all:cache_total', 1);
    }
  }

  private addMetric(key: string, value: number): void {
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    
    const values = this.metrics.get(key)!;
    values.push(value);
    
    // Keep only last 1000 values
    if (values.length > 1000) {
      values.shift();
    }
  }

  private async sendToMonitoring(span: TraceSpan): Promise<void> {
    try {
      // Send to PostHog
      if (process.env.NEXT_PUBLIC_POSTHOG_KEY) {
        await this.sendToPostHog(span);
      }

      // Send to Sentry
      if (process.env.SENTRY_DSN && span.status === 'error') {
        await this.sendToSentry(span);
      }

      // Send to custom monitoring endpoint
      if (process.env.MONITORING_ENDPOINT) {
        await this.sendToCustomEndpoint(span);
      }

    } catch (error) {
      console.error('Failed to send trace to monitoring:', error);
    }
  }

  private async sendToPostHog(span: TraceSpan): Promise<void> {
    const event = {
      event: 'request_trace',
      properties: {
        trace_id: span.traceId,
        span_id: span.spanId,
        parent_span_id: span.parentSpanId,
        operation_name: span.operationName,
        duration: span.duration,
        status: span.status,
        ...span.tags
      },
      timestamp: new Date(span.startTime).toISOString()
    };

    // Send to PostHog API
    await fetch('https://app.posthog.com/capture/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        api_key: process.env.NEXT_PUBLIC_POSTHOG_KEY,
        event: event.event,
        properties: event.properties,
        timestamp: event.timestamp
      })
    });
  }

  private async sendToSentry(span: TraceSpan): Promise<void> {
    // Send error to Sentry
    const sentryEvent = {
      message: `${span.operationName} failed`,
      level: 'error',
      tags: span.tags,
      extra: {
        traceId: span.traceId,
        spanId: span.spanId,
        duration: span.duration,
        logs: span.logs
      },
      exception: span.error ? {
        values: [{
          type: span.error.name,
          value: span.error.message,
          stacktrace: span.error.stack ? {
            frames: this.parseStackTrace(span.error.stack)
          } : undefined
        }]
      } : undefined
    };

    // Send to Sentry API (simplified)
    console.error('Sentry event:', sentryEvent);
  }

  private async sendToCustomEndpoint(span: TraceSpan): Promise<void> {
    await fetch(process.env.MONITORING_ENDPOINT!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.MONITORING_API_KEY}`
      },
      body: JSON.stringify(span)
    });
  }

  private parseStackTrace(stack: string): any[] {
    return stack.split('\n').map(line => ({
      filename: line.match(/\((.+):\d+:\d+\)/)?.[1] || 'unknown',
      function: line.match(/at (.+) \(/)?.[1] || 'anonymous',
      lineno: parseInt(line.match(/:(\d+):\d+\)/)?.[1] || '0'),
      colno: parseInt(line.match(/:(\d+)\)/)?.[1] || '0')
    }));
  }
}

/**
 * Middleware for automatic request tracing
 */
export function withTracing(handler: Function) {
  return async (request: NextRequest, ...args: any[]) => {
    const tracer = RequestTracer.getInstance();
    
    // Extract trace context from headers
    const traceId = request.headers.get('x-trace-id') || generateUUID();
    const parentSpanId = request.headers.get('x-parent-span-id') || undefined;

    // Get user context
    const userAgent = request.headers.get('user-agent') || undefined;
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'unknown';
    const country = request.headers.get('cf-ipcountry') ||
                   request.headers.get('x-vercel-ip-country') ||
                   'unknown';

    // Start trace
    const span = tracer.startTrace(`${request.method} ${request.nextUrl.pathname}`, {
      traceId,
      parentSpanId,
      userAgent,
      ip,
      country
    });

    // Add request tags
    tracer.addTag(span.spanId, 'http.method', request.method);
    tracer.addTag(span.spanId, 'http.url', request.nextUrl.toString());
    tracer.addTag(span.spanId, 'http.user_agent', userAgent);
    tracer.addTag(span.spanId, 'geo.country', country);

    try {
      // Execute handler
      const response = await handler(request, ...args);
      
      // Add response tags
      if (response instanceof NextResponse) {
        tracer.addTag(span.spanId, 'http.status_code', response.status);
        tracer.addTag(span.spanId, 'http.response_size', 
          response.headers.get('content-length') || 0);
        
        // Add trace headers to response
        response.headers.set('x-trace-id', traceId);
        response.headers.set('x-span-id', span.spanId);
      }

      // Finish span
      tracer.finishSpan(span.spanId, 'ok');
      
      return response;

    } catch (error) {
      // Log error
      const errorMessage = error instanceof Error ? error.message : String(error);
      tracer.addLog(span.spanId, 'error', 'Request failed', { error: errorMessage });

      // Finish span with error
      tracer.finishSpan(span.spanId, 'error', error as Error);
      
      throw error;
    }
  };
}

/**
 * Hook for manual tracing in components
 */
export function useTracing() {
  const tracer = RequestTracer.getInstance();

  const startSpan = (operationName: string, parentSpan?: TraceSpan) => {
    if (parentSpan) {
      return tracer.startChildSpan(parentSpan, operationName);
    }
    return tracer.startTrace(operationName);
  };

  const finishSpan = (spanId: string, status?: 'ok' | 'error' | 'timeout', error?: Error) => {
    tracer.finishSpan(spanId, status, error);
  };

  const addLog = (spanId: string, level: 'info' | 'warn' | 'error', message: string, data?: any) => {
    tracer.addLog(spanId, level, message, data);
  };

  const addTag = (spanId: string, key: string, value: any) => {
    tracer.addTag(spanId, key, value);
  };

  return {
    startSpan,
    finishSpan,
    addLog,
    addTag,
    getMetrics: tracer.getMetrics.bind(tracer)
  };
}

// Export singleton instance
export const requestTracer = RequestTracer.getInstance();
