import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { pushSummaryToCRM, getUserCRMConnections, shouldAutoPushToCRM } from '@/lib/crm-integrations';
// Removed Supabase import - using dev-only mode
import { getCurrentUser } from '@/lib/user-management';
// Removed: import { SentryTracker } from '@/lib/sentry.client';

/**
 * POST /api/crm/push
 * Push a summary to CRM systems
 */
export async function POST(request: NextRequest) {
  try {
    const { 
      summary_id, 
      crm_types, 
      organization_id 
    } = await request.json();

    if (!summary_id) {
      return NextResponse.json(
        { error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    if (!crm_types || !Array.isArray(crm_types) || crm_types.length === 0) {
      return NextResponse.json(
        { error: 'At least one CRM type is required' },
        { status: 400 }
      );
    }

    // Get current user (dev mode)
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user's organization if not provided
    let orgId: string = organization_id || '';
    if (!orgId) {
      // DEV MODE: Use user's default org
      orgId = (user as any).orgId || 'org-dev-001';
    }

    // DEV MODE: Mock summary verification
    const summary = {
      id: summary_id,
      user_id: user.id
    };
  devLog.log('📄 Dev mode: Using mock summary verification');

    // Push to each requested CRM
    const results = [];
    for (const crmType of crm_types) {
      if (!['hubspot', 'salesforce', 'notion'].includes(crmType)) {
        results.push({
          crm_type: crmType,
          success: false,
          error: 'Unsupported CRM type'
        });
        continue;
      }

      try {
        const pushResult = await pushSummaryToCRM(
          summary_id,
          user.id,
          orgId,
          crmType
        );

        results.push({
          crm_type: crmType,
          success: pushResult.success,
          crm_record_id: pushResult.crm_record_id,
          error: pushResult.error
        });
      } catch (error) {
        results.push({
          crm_type: crmType,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    return NextResponse.json({
      success: successCount > 0,
      message: `Pushed to ${successCount}/${totalCount} CRM systems`,
      results,
      summary_id
    });

  } catch (error) {
    console.error('CRM push API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/crm/push
 * Get CRM push history and status
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id');
    const summaryId = searchParams.get('summary_id');
    const limit = parseInt(searchParams.get('limit') || '20');

    // Get current user (dev mode)
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user's organization if not provided (dev mode)
    let orgId: string = organizationId || '';
    if (!orgId) {
      // DEV MODE: Use user's default org
      orgId = (user as any).orgId || 'org-dev-001';
    }

    // Get CRM connections
    const connections = await getUserCRMConnections(user.id, orgId);

    // Get auto-push setting
    const autoPushEnabled = await shouldAutoPushToCRM(user.id, orgId);

    // DEV MODE: Mock push history and statistics
    const pushHistory = [
      {
        id: 'push-1',
        user_id: user.id,
        summary_id: summaryId || 'summary-1',
        integration_id: 'integration-1',
        external_id: 'crm-record-1',
        status: 'success',
        created_at: new Date().toISOString(),
        summaries: { title: 'Demo Summary', created_at: new Date().toISOString() },
        crm_integrations: { crm_type: 'hubspot' }
      }
    ];

    const stats = [
      { status: 'success', crm_type: 'hubspot' },
      { status: 'success', crm_type: 'salesforce' }
    ];
  devLog.log('📄 Dev mode: Using mock CRM push history');

    const statistics = {
      total_pushes: stats?.length || 0,
      successful_pushes: stats?.filter(s => s.status === 'success').length || 0,
      failed_pushes: stats?.filter(s => s.status === 'failed').length || 0,
      pending_pushes: stats?.filter(s => s.status === 'pending').length || 0,
      by_crm_type: stats?.reduce((acc: any, stat: any) => {
        acc[stat.crm_type] = (acc[stat.crm_type] || 0) + 1;
        return acc;
      }, {}) || {}
    };

    return NextResponse.json({
      success: true,
      data: {
        connections: connections.map(conn => ({
          crm_type: conn.crm_type,
          is_active: conn.is_active,
          last_sync_at: conn.last_sync_at,
          created_at: conn.created_at
        })),
        settings: {
          auto_push_enabled: autoPushEnabled
        },
        statistics,
        recent_pushes: pushHistory || []
      }
    });

  } catch (error) {
    console.error('Get CRM push status API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/crm/push
 * Update CRM auto-push settings
 */
export async function PUT(request: NextRequest) {
  try {
    const { 
      auto_push_enabled,
      organization_id 
    } = await request.json();

    // Get current user (dev mode)
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user's organization if not provided (dev mode)
    let orgId: string = organization_id || '';
    if (!orgId) {
      // DEV MODE: Use user's default org
      orgId = (user as any).orgId || 'org-dev-001';
    }

    // Update user settings
    const updateData: any = {
      user_id: user.id,
      organization_id: orgId,
      updated_at: new Date().toISOString()
    };

    if (typeof auto_push_enabled === 'boolean') {
      updateData.auto_push_to_crm = auto_push_enabled;
    }

    // DEV MODE: Mock settings update
  devLog.log('📄 Dev mode: Skipping user settings update');
    const data = {
      ...updateData,
      auto_push_to_crm: auto_push_enabled
    };

    return NextResponse.json({
      success: true,
      message: 'CRM auto-push settings updated successfully',
      data: {
        auto_push_enabled: data.auto_push_to_crm
      }
    });

  } catch (error) {
    console.error('Update CRM settings API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
