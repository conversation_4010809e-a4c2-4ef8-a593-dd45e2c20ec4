import { devLog } from '@/lib/console-cleaner';
'use client';

import { useEffect, useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Zap, 
  Clock, 
  Wifi, 
  HardDrive, 
  Cpu,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
  
  // Custom metrics
  loadTime?: number;
  renderTime?: number;
  bundleSize?: number;
  memoryUsage?: number;
  networkSpeed?: string;
  
  // Performance scores
  performanceScore?: number;
  accessibilityScore?: number;
  bestPracticesScore?: number;
  seoScore?: number;
}

interface PerformanceMonitorProps {
  showDetailedMetrics?: boolean;
  enableRealTimeMonitoring?: boolean;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

export default function PerformanceMonitor({
  showDetailedMetrics = false,
  enableRealTimeMonitoring = true,
  onMetricsUpdate
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Collect Core Web Vitals
  const collectWebVitals = useCallback(() => {
    if (typeof window === 'undefined') return;

    // Performance Observer for Core Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1] as any;
        if (lastEntry) {
          setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }));
        }
      });
      
      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (e) {
        console.warn('LCP observation not supported');
      }

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          setMetrics(prev => ({ ...prev, fid: entry.processingStart - entry.startTime }));
        });
      });
      
      try {
        fidObserver.observe({ entryTypes: ['first-input'] });
      } catch (e) {
        console.warn('FID observation not supported');
      }

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        setMetrics(prev => ({ ...prev, cls: clsValue }));
      });
      
      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (e) {
        console.warn('CLS observation not supported');
      }
    }

    // Navigation Timing API
    if ('performance' in window && window.performance.timing) {
      const timing = window.performance.timing;
      const loadTime = timing.loadEventEnd - timing.navigationStart;
      const ttfb = timing.responseStart - timing.navigationStart;
      const fcp = timing.domContentLoadedEventEnd - timing.navigationStart;
      
      setMetrics(prev => ({
        ...prev,
        loadTime,
        ttfb,
        fcp
      }));
    }

    // Memory usage (if available)
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      setMetrics(prev => ({
        ...prev,
        memoryUsage: memory.usedJSHeapSize / memory.totalJSHeapSize * 100
      }));
    }

    // Network information
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      setMetrics(prev => ({
        ...prev,
        networkSpeed: connection.effectiveType || 'unknown'
      }));
    }

    setLastUpdate(new Date());
  }, []);

  // Calculate performance scores
  const calculateScores = useCallback((metrics: PerformanceMetrics) => {
    let performanceScore = 100;
    
    // LCP scoring (Good: <2.5s, Needs Improvement: 2.5-4s, Poor: >4s)
    if (metrics.lcp) {
      if (metrics.lcp > 4000) performanceScore -= 30;
      else if (metrics.lcp > 2500) performanceScore -= 15;
    }
    
    // FID scoring (Good: <100ms, Needs Improvement: 100-300ms, Poor: >300ms)
    if (metrics.fid) {
      if (metrics.fid > 300) performanceScore -= 25;
      else if (metrics.fid > 100) performanceScore -= 10;
    }
    
    // CLS scoring (Good: <0.1, Needs Improvement: 0.1-0.25, Poor: >0.25)
    if (metrics.cls) {
      if (metrics.cls > 0.25) performanceScore -= 20;
      else if (metrics.cls > 0.1) performanceScore -= 10;
    }
    
    // Load time scoring
    if (metrics.loadTime) {
      if (metrics.loadTime > 5000) performanceScore -= 15;
      else if (metrics.loadTime > 3000) performanceScore -= 8;
    }
    
    return Math.max(0, performanceScore);
  }, []);

  // Start monitoring
  useEffect(() => {
    if (enableRealTimeMonitoring) {
      setIsMonitoring(true);
      collectWebVitals();
      
      const interval = setInterval(collectWebVitals, 5000); // Update every 5 seconds
      
      return () => {
        clearInterval(interval);
        setIsMonitoring(false);
      };
    }
  }, [enableRealTimeMonitoring, collectWebVitals]);

  // Update parent component
  useEffect(() => {
    if (onMetricsUpdate && Object.keys(metrics).length > 0) {
      const updatedMetrics = {
        ...metrics,
        performanceScore: calculateScores(metrics)
      };
      onMetricsUpdate(updatedMetrics);
    }
  }, [metrics, onMetricsUpdate, calculateScores]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number): "default" | "secondary" | "destructive" | "outline" => {
    if (score >= 90) return 'default';
    if (score >= 70) return 'secondary';
    return 'destructive';
  };

  const formatMetric = (value: number | undefined, unit: string = 'ms') => {
    if (value === undefined) return 'N/A';
    if (unit === 'ms') return `${Math.round(value)}ms`;
    if (unit === '%') return `${Math.round(value)}%`;
    return `${Math.round(value)}${unit}`;
  };

  const performanceScore = calculateScores(metrics);

  if (!showDetailedMetrics) {
    return (
      <div className="flex items-center space-x-2">
        <Activity className="h-4 w-4" />
        <Badge variant={getScoreBadgeVariant(performanceScore)}>
          Performance: {performanceScore}
        </Badge>
        {isMonitoring && (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            <RefreshCw className="h-3 w-3 text-blue-500" />
          </motion.div>
        )}
      </div>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center space-x-2">
          <BarChart3 className="h-4 w-4" />
          <span>Performance Monitor</span>
        </CardTitle>
        <div className="flex items-center space-x-2">
          {isMonitoring ? (
            <Badge variant="default" className="flex items-center space-x-1">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <RefreshCw className="h-3 w-3" />
              </motion.div>
              <span>Live</span>
            </Badge>
          ) : (
            <Badge variant="secondary">Paused</Badge>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={collectWebVitals}
            className="h-8"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Overall Performance Score */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Overall Score</span>
          <div className="flex items-center space-x-2">
            <Progress value={performanceScore} className="w-20" />
            <Badge variant={getScoreBadgeVariant(performanceScore)}>
              {performanceScore}
            </Badge>
          </div>
        </div>

        {/* Core Web Vitals */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">LCP</span>
              <span className="text-xs font-mono">{formatMetric(metrics.lcp)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">FID</span>
              <span className="text-xs font-mono">{formatMetric(metrics.fid)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">CLS</span>
              <span className="text-xs font-mono">{formatMetric(metrics.cls, '')}</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">TTFB</span>
              <span className="text-xs font-mono">{formatMetric(metrics.ttfb)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Load</span>
              <span className="text-xs font-mono">{formatMetric(metrics.loadTime)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Memory</span>
              <span className="text-xs font-mono">{formatMetric(metrics.memoryUsage, '%')}</span>
            </div>
          </div>
        </div>

        {/* Network & System Info */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-1">
            <Wifi className="h-3 w-3" />
            <span>{metrics.networkSpeed || 'Unknown'}</span>
          </div>
          {lastUpdate && (
            <span>Updated: {lastUpdate.toLocaleTimeString()}</span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Hook for performance monitoring
export function usePerformanceMonitoring() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    setIsSupported(
      typeof window !== 'undefined' && 
      'PerformanceObserver' in window &&
      'performance' in window
    );
  }, []);

  const startMonitoring = useCallback(() => {
    if (!isSupported) return;
    
    // Implementation would go here
  devLog.log('Performance monitoring started');
  }, [isSupported]);

  const stopMonitoring = useCallback(() => {
  devLog.log('Performance monitoring stopped');
  }, []);

  return {
    metrics,
    isSupported,
    startMonitoring,
    stopMonitoring
  };
}
