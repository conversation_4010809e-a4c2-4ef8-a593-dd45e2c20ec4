#!/usr/bin/env node

/**
 * 📊 MONITORING VALIDATION SCRIPT
 * 
 * Validates that Sentry and PostHog monitoring are properly configured
 * and working in production environment
 */

const https = require('https');

console.log('📊 Validating Production Monitoring Setup...\n');

// Configuration
const PRODUCTION_URL = process.env.PRODUCTION_URL || 'https://your-app.vercel.app';

// Test results tracking
let validationResults = {
  total: 0,
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

// Helper functions
const logTest = (name, status, message, details = '') => {
  validationResults.total++;
  validationResults.tests.push({ name, status, message, details });
  
  const icon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  console.log(`${icon} ${name}: ${message}`);
  
  if (details) {
    console.log(`   ${details}`);
  }
  
  if (status === 'PASS') validationResults.passed++;
  else if (status === 'FAIL') validationResults.failed++;
  else validationResults.warnings++;
};

const makeRequest = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });
    
    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
};

// Validation functions
async function validateSentryIntegration() {
  console.log('🚨 Validating Sentry Error Tracking...');
  
  try {
    // Check if Sentry is loaded in the page
    const response = await makeRequest(PRODUCTION_URL);
    const html = response.data;
    
    if (html.includes('sentry') || html.includes('Sentry')) {
      logTest('Sentry Integration', 'PASS', 'Sentry references found in HTML');
    } else {
      logTest('Sentry Integration', 'WARN', 'No Sentry references found in HTML');
    }
    
    // Check for Sentry DSN in environment
    if (process.env.NEXT_PUBLIC_SENTRY_DSN) {
      logTest('Sentry DSN', 'PASS', 'Sentry DSN configured');
    } else {
      logTest('Sentry DSN', 'FAIL', 'Sentry DSN not configured');
    }
    
    // Test error endpoint (if exists)
    try {
      const errorResponse = await makeRequest(`${PRODUCTION_URL}/api/test-error`);
      if (errorResponse.statusCode === 500) {
        logTest('Sentry Error Capture', 'PASS', 'Error endpoint responds correctly');
      } else {
        logTest('Sentry Error Capture', 'WARN', 'Error endpoint not found or not working');
      }
    } catch (error) {
      logTest('Sentry Error Capture', 'WARN', 'Could not test error capture');
    }
    
  } catch (error) {
    logTest('Sentry Validation', 'FAIL', 'Could not validate Sentry', error.message);
  }
}

async function validatePostHogIntegration() {
  console.log('\n📈 Validating PostHog Analytics...');
  
  try {
    // Check if PostHog is loaded in the page
    const response = await makeRequest(PRODUCTION_URL);
    const html = response.data;
    
    if (html.includes('posthog') || html.includes('PostHog')) {
      logTest('PostHog Integration', 'PASS', 'PostHog references found in HTML');
    } else {
      logTest('PostHog Integration', 'WARN', 'No PostHog references found in HTML');
    }
    
    // Check for PostHog key in environment
    if (process.env.NEXT_PUBLIC_POSTHOG_KEY) {
      logTest('PostHog API Key', 'PASS', 'PostHog API key configured');
    } else {
      logTest('PostHog API Key', 'FAIL', 'PostHog API key not configured');
    }
    
    // Check PostHog host configuration
    if (process.env.NEXT_PUBLIC_POSTHOG_HOST) {
      logTest('PostHog Host', 'PASS', `PostHog host: ${process.env.NEXT_PUBLIC_POSTHOG_HOST}`);
    } else {
      logTest('PostHog Host', 'WARN', 'PostHog host not explicitly configured');
    }
    
  } catch (error) {
    logTest('PostHog Validation', 'FAIL', 'Could not validate PostHog', error.message);
  }
}

async function validateEnvironmentVariables() {
  console.log('\n⚙️ Validating Environment Variables...');
  
  const requiredVars = [
    'NEXT_PUBLIC_SENTRY_DSN',
    'NEXT_PUBLIC_POSTHOG_KEY',
    'SENTRY_AUTH_TOKEN',
    'SENTRY_ORG',
    'SENTRY_PROJECT'
  ];
  
  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      logTest(`Environment: ${varName}`, 'PASS', 'Variable configured');
    } else {
      logTest(`Environment: ${varName}`, 'FAIL', 'Variable missing');
    }
  });
  
  // Check optional variables
  const optionalVars = [
    'NEXT_PUBLIC_POSTHOG_HOST'
  ];
  
  optionalVars.forEach(varName => {
    if (process.env[varName]) {
      logTest(`Optional: ${varName}`, 'PASS', 'Variable configured');
    } else {
      logTest(`Optional: ${varName}`, 'WARN', 'Variable not configured (using defaults)');
    }
  });
}

async function validateMonitoringEndpoints() {
  console.log('\n🔍 Validating Monitoring Endpoints...');
  
  const endpoints = [
    { path: '/api/health', name: 'Health Check' },
    { path: '/api/monitoring/sentry', name: 'Sentry Health' },
    { path: '/api/monitoring/posthog', name: 'PostHog Health' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      const url = `${PRODUCTION_URL}${endpoint.path}`;
      const response = await makeRequest(url);
      
      if (response.statusCode === 200) {
        logTest(`Endpoint: ${endpoint.name}`, 'PASS', `HTTP ${response.statusCode}`);
      } else if (response.statusCode === 404) {
        logTest(`Endpoint: ${endpoint.name}`, 'WARN', 'Endpoint not implemented');
      } else {
        logTest(`Endpoint: ${endpoint.name}`, 'FAIL', `HTTP ${response.statusCode}`);
      }
    } catch (error) {
      logTest(`Endpoint: ${endpoint.name}`, 'WARN', 'Could not reach endpoint');
    }
  }
}

async function validatePerformanceMonitoring() {
  console.log('\n⚡ Validating Performance Monitoring...');
  
  const startTime = Date.now();
  
  try {
    const response = await makeRequest(PRODUCTION_URL);
    const loadTime = Date.now() - startTime;
    
    if (loadTime < 3000) {
      logTest('Page Load Performance', 'PASS', `${loadTime}ms (< 3s target)`);
    } else if (loadTime < 5000) {
      logTest('Page Load Performance', 'WARN', `${loadTime}ms (slower than target)`);
    } else {
      logTest('Page Load Performance', 'FAIL', `${loadTime}ms (too slow)`);
    }
    
    // Check for performance monitoring scripts
    const html = response.data;
    if (html.includes('performance') || html.includes('timing')) {
      logTest('Performance Scripts', 'PASS', 'Performance monitoring scripts detected');
    } else {
      logTest('Performance Scripts', 'WARN', 'No performance monitoring scripts detected');
    }
    
  } catch (error) {
    logTest('Performance Monitoring', 'FAIL', 'Could not validate performance', error.message);
  }
}

// Main validation runner
async function runValidation() {
  console.log(`🎯 Validating Monitoring for: ${PRODUCTION_URL}\n`);
  
  await validateSentryIntegration();
  await validatePostHogIntegration();
  await validateEnvironmentVariables();
  await validateMonitoringEndpoints();
  await validatePerformanceMonitoring();
  
  // Generate validation report
  console.log('\n' + '='.repeat(60));
  console.log('📊 MONITORING VALIDATION RESULTS');
  console.log('='.repeat(60));
  
  console.log(`\n📈 Summary:`);
  console.log(`   Total Tests: ${validationResults.total}`);
  console.log(`   ✅ Passed: ${validationResults.passed}`);
  console.log(`   ❌ Failed: ${validationResults.failed}`);
  console.log(`   ⚠️  Warnings: ${validationResults.warnings}`);
  
  const successRate = Math.round((validationResults.passed / validationResults.total) * 100);
  console.log(`   🎯 Success Rate: ${successRate}%`);
  
  if (validationResults.failed === 0) {
    console.log('\n🎉 MONITORING VALIDATION PASSED!');
    console.log('✅ Error tracking and analytics are properly configured!');
  } else {
    console.log('\n🚨 MONITORING VALIDATION FAILED');
    console.log('❌ Please fix failed validations before going live');
  }
  
  console.log('\n📋 Next Steps:');
  if (validationResults.failed === 0) {
    console.log('1. ✅ Set up Sentry alert rules');
    console.log('2. ✅ Configure PostHog dashboards');
    console.log('3. ✅ Test error notifications');
    console.log('4. ✅ Verify analytics data flow');
    console.log('5. ✅ Set up monitoring alerts');
  } else {
    console.log('1. 🔧 Fix failed monitoring configurations');
    console.log('2. 🔄 Re-run monitoring validation');
    console.log('3. 📊 Test error tracking manually');
    console.log('4. 🧪 Verify analytics events');
  }
  
  console.log('\n🔗 Monitoring Dashboards:');
  console.log('• Sentry: https://sentry.io/organizations/your-org/');
  console.log('• PostHog: https://app.posthog.com/');
  console.log('• Vercel: https://vercel.com/dashboard');
  
  console.log('\n📖 Setup Guide: ./MONITORING_SETUP.md');
  
  // Exit with appropriate code
  process.exit(validationResults.failed > 0 ? 1 : 0);
}

// Run validation
runValidation().catch(error => {
  console.error('\n❌ Monitoring validation failed:', error.message);
  process.exit(1);
});
