/**
 * Security Headers Configuration
 * 
 * Comprehensive security headers for production deployment
 * including CSP, HSTS, and other security measures.
 */

import { NextResponse } from 'next/server';

// Security configuration
export const SECURITY_CONFIG = {
  // Content Security Policy
  CSP: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'", // Required for Next.js
      "'unsafe-eval'", // Required for development
      'https://js.stripe.com',
      'https://checkout.stripe.com',
      'https://app.posthog.com',
      'https://browser.sentry-cdn.com',
      'https://js.sentry-cdn.com',
      'https://clerk.com',
      'https://*.clerk.accounts.dev',
      'https://*.clerk.com',
      'https://vercel.live',
      'https://vitals.vercel-insights.com'
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'", // Required for styled-components and CSS-in-JS
      'https://fonts.googleapis.com',
      'https://clerk.com',
      'https://*.clerk.accounts.dev'
    ],
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'https:',
      'https://img.clerk.com',
      'https://images.clerk.dev',
      'https://www.gravatar.com',
      'https://avatars.githubusercontent.com'
    ],
    'font-src': [
      "'self'",
      'https://fonts.gstatic.com',
      'data:'
    ],
    'connect-src': [
      "'self'",
      'https://api.stripe.com',
      'https://checkout.stripe.com',
      'https://app.posthog.com',
      'https://us.i.posthog.com',
      'https://o4509565369122816.ingest.us.sentry.io',
      'https://clerk.com',
      'https://*.clerk.accounts.dev',
      'https://*.clerk.com',
      'https://api.clerk.com',
      'https://holuppwejzcqwrbdbgkf.supabase.co',
      'https://openrouter.ai',
      'https://api.openrouter.ai',
      'https://api.resend.com',
      'https://vitals.vercel-insights.com',
      'https://vercel.live',
      'wss://realtime.supabase.co'
    ],
    'frame-src': [
      "'self'",
      'https://js.stripe.com',
      'https://checkout.stripe.com',
      'https://hooks.stripe.com',
      'https://clerk.com',
      'https://*.clerk.accounts.dev'
    ],
    'worker-src': [
      "'self'",
      'blob:'
    ],
    'child-src': [
      "'self'",
      'blob:'
    ],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': [
      "'self'",
      'https://checkout.stripe.com',
      'https://clerk.com',
      'https://*.clerk.accounts.dev'
    ],
    'frame-ancestors': ["'none'"],
    'upgrade-insecure-requests': []
  },
  
  // Permissions Policy (formerly Feature Policy)
  PERMISSIONS_POLICY: {
    'camera': [],
    'microphone': [],
    'geolocation': [],
    'payment': ['self', 'https://checkout.stripe.com'],
    'usb': [],
    'bluetooth': [],
    'magnetometer': [],
    'gyroscope': [],
    'accelerometer': [],
    'ambient-light-sensor': [],
    'autoplay': ['self'],
    'encrypted-media': ['self'],
    'fullscreen': ['self'],
    'picture-in-picture': ['self']
  },
  
  // Security headers
  SECURITY_HEADERS: {
    // Prevent MIME type sniffing
    'X-Content-Type-Options': 'nosniff',
    
    // Prevent clickjacking
    'X-Frame-Options': 'DENY',
    
    // XSS protection
    'X-XSS-Protection': '1; mode=block',
    
    // Referrer policy
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    
    // Prevent DNS prefetching
    'X-DNS-Prefetch-Control': 'off',
    
    // Prevent download of untrusted content
    'X-Download-Options': 'noopen',
    
    // Prevent MIME type confusion attacks
    'X-Permitted-Cross-Domain-Policies': 'none',
    
    // Cross-Origin policies
    'Cross-Origin-Embedder-Policy': 'unsafe-none', // Required for some third-party integrations
    'Cross-Origin-Opener-Policy': 'same-origin-allow-popups', // Required for OAuth flows
    'Cross-Origin-Resource-Policy': 'cross-origin'
  },
  
  // HSTS (HTTP Strict Transport Security)
  HSTS: {
    'max-age': 31536000, // 1 year
    'includeSubDomains': true,
    'preload': true
  }
};

/**
 * Generate Content Security Policy header value
 */
export function generateCSPHeader(): string {
  const cspDirectives = Object.entries(SECURITY_CONFIG.CSP)
    .map(([directive, sources]) => {
      if (sources.length === 0) {
        return directive;
      }
      return `${directive} ${sources.join(' ')}`;
    })
    .join('; ');
  
  return cspDirectives;
}

/**
 * Generate Permissions Policy header value
 */
export function generatePermissionsPolicyHeader(): string {
  const policies = Object.entries(SECURITY_CONFIG.PERMISSIONS_POLICY)
    .map(([feature, allowlist]) => {
      if (allowlist.length === 0) {
        return `${feature}=()`;
      }
      const origins = allowlist.map(origin => origin === 'self' ? 'self' : `"${origin}"`).join(' ');
      return `${feature}=(${origins})`;
    })
    .join(', ');
  
  return policies;
}

/**
 * Generate HSTS header value
 */
export function generateHSTSHeader(): string {
  const { 'max-age': maxAge, includeSubDomains, preload } = SECURITY_CONFIG.HSTS;
  let hsts = `max-age=${maxAge}`;
  
  if (includeSubDomains) {
    hsts += '; includeSubDomains';
  }
  
  if (preload) {
    hsts += '; preload';
  }
  
  return hsts;
}

/**
 * Apply security headers to response
 */
export function applySecurityHeaders(response: NextResponse, options: {
  skipCSP?: boolean;
  skipHSTS?: boolean;
  additionalHeaders?: Record<string, string>;
} = {}): NextResponse {
  // Apply basic security headers
  Object.entries(SECURITY_CONFIG.SECURITY_HEADERS).forEach(([header, value]) => {
    response.headers.set(header, value);
  });
  
  // Apply CSP header
  if (!options.skipCSP) {
    response.headers.set('Content-Security-Policy', generateCSPHeader());
  }
  
  // Apply Permissions Policy
  response.headers.set('Permissions-Policy', generatePermissionsPolicyHeader());
  
  // Apply HSTS header (only in production and HTTPS)
  if (!options.skipHSTS && process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', generateHSTSHeader());
  }
  
  // Apply additional headers
  if (options.additionalHeaders) {
    Object.entries(options.additionalHeaders).forEach(([header, value]) => {
      response.headers.set(header, value);
    });
  }
  
  return response;
}

/**
 * Create security headers for API responses
 */
export function getAPISecurityHeaders(): Record<string, string> {
  return {
    ...SECURITY_CONFIG.SECURITY_HEADERS,
    'Content-Security-Policy': generateCSPHeader(),
    'Permissions-Policy': generatePermissionsPolicyHeader(),
    ...(process.env.NODE_ENV === 'production' && {
      'Strict-Transport-Security': generateHSTSHeader()
    })
  };
}

/**
 * Validate request origin for CSRF protection
 */
export function validateRequestOrigin(
  request: Request,
  allowedOrigins: string[] = []
): { valid: boolean; reason?: string } {
  const origin = request.headers.get('origin');
  const referer = request.headers.get('referer');
  
  // Allow same-origin requests
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  const allowedOriginsList = [
    siteUrl,
    process.env.NEXT_PUBLIC_APP_URL,
    'http://localhost:3000',
    'http://localhost:3001',
    ...allowedOrigins
  ].filter(Boolean);
  
  // Check origin header
  if (origin) {
    const isAllowed = allowedOriginsList.some(allowed => 
      origin === allowed || origin.startsWith(allowed)
    );
    
    if (!isAllowed) {
      return {
        valid: false,
        reason: `Origin ${origin} not allowed`
      };
    }
  }
  
  // Check referer header as fallback
  if (!origin && referer) {
    const refererUrl = new URL(referer);
    const isAllowed = allowedOriginsList.some(allowed => {
      try {
        const allowedUrl = new URL(allowed);
        return refererUrl.origin === allowedUrl.origin;
      } catch {
        return false;
      }
    });
    
    if (!isAllowed) {
      return {
        valid: false,
        reason: `Referer ${referer} not allowed`
      };
    }
  }
  
  return { valid: true };
}

/**
 * Check if request is from a bot or crawler
 */
export function isBotRequest(userAgent: string): boolean {
  const botPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /java/i,
    /go-http-client/i,
    /okhttp/i,
    /axios/i,
    /node-fetch/i,
    /postman/i,
    /insomnia/i
  ];
  
  return botPatterns.some(pattern => pattern.test(userAgent));
}

/**
 * Generate nonce for inline scripts
 */
export function generateNonce(): string {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Security middleware configuration
 */
export const SECURITY_MIDDLEWARE_CONFIG = {
  // Rate limiting
  rateLimiting: {
    enabled: true,
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  },
  
  // CSRF protection
  csrfProtection: {
    enabled: true,
    ignoredMethods: ['GET', 'HEAD', 'OPTIONS'],
    ignoredPaths: ['/api/webhooks', '/api/health']
  },
  
  // Bot protection
  botProtection: {
    enabled: true,
    blockBots: false, // Set to true to block all bots
    allowedBots: ['googlebot', 'bingbot', 'slurp'] // SEO bots
  },
  
  // IP filtering
  ipFiltering: {
    enabled: false,
    blockedIPs: [],
    allowedIPs: []
  }
};
