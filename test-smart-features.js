/**
 * Test Smart Tagging & Feedback Features
 */

const { default: fetch } = require('node-fetch');

const testSmartFeatures = async () => {
  try {
    console.log('🧪 Testing Smart Tagging & Feedback Features...');
    
    // Test Smart Tagging API
    console.log('\n🏷️ Testing Smart Tagging API...');
    try {
      const taggingResponse = await fetch('http://localhost:3000/api/summaries/demo-summary-123/tags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          summaryText: 'This is a test meeting summary about React development, project management, and team collaboration. We discussed implementing new features, addressing performance issues, and planning the next sprint. The team showed enthusiasm and confidence about the upcoming deliverables.'
        })
      });
      
      if (taggingResponse.ok) {
        const taggingResult = await taggingResponse.json();
        console.log('✅ Smart Tagging Test PASSED');
        console.log('🏷️ Generated Tags:', {
          success: taggingResult.success,
          skills: taggingResult.data?.tags?.skills?.slice(0, 3),
          technologies: taggingResult.data?.tags?.technologies?.slice(0, 3),
          sentiments: taggingResult.data?.tags?.sentiments,
          confidence: taggingResult.data?.tags?.confidence_score
        });
      } else {
        const error = await taggingResponse.json();
        console.log('❌ Smart Tagging Test FAILED');
        console.log('Error:', error);
      }
    } catch (error) {
      console.log('❌ Smart Tagging Test ERROR:', error.message);
    }
    
    // Test getting existing tags
    console.log('\n🏷️ Testing Get Tags API...');
    try {
      const getTagsResponse = await fetch('http://localhost:3000/api/summaries/demo-summary-123/tags');
      
      if (getTagsResponse.ok) {
        const tagsResult = await getTagsResponse.json();
        console.log('✅ Get Tags Test PASSED');
        console.log('🏷️ Retrieved Tags:', {
          success: tagsResult.success,
          hasData: !!tagsResult.data,
          tagsCount: tagsResult.data?.tags ? Object.keys(tagsResult.data.tags).length : 0
        });
      } else {
        const error = await getTagsResponse.json();
        console.log('❌ Get Tags Test FAILED');
        console.log('Error:', error);
      }
    } catch (error) {
      console.log('❌ Get Tags Test ERROR:', error.message);
    }
    
    // Test Feedback System
    console.log('\n💬 Testing Feedback System...');
    try {
      const feedbackResponse = await fetch('http://localhost:3000/api/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'satisfaction',
          rating: 5,
          feedback: 'The AI summarization is excellent! Very helpful for our team meetings.',
          email: '<EMAIL>',
          allowContact: true,
          trigger: 'post_summary',
          context: {
            summaryId: 'demo-summary-123',
            feature: 'ai_summarization'
          }
        })
      });
      
      if (feedbackResponse.ok) {
        const feedbackResult = await feedbackResponse.json();
        console.log('✅ Feedback Test PASSED');
        console.log('💬 Feedback Response:', {
          success: feedbackResult.success,
          message: feedbackResult.message,
          feedbackId: feedbackResult.feedbackId
        });
      } else {
        const error = await feedbackResponse.json();
        console.log('❌ Feedback Test FAILED');
        console.log('Error:', error);
      }
    } catch (error) {
      console.log('❌ Feedback Test ERROR:', error.message);
    }
    
    // Test Feature Feedback
    console.log('\n💬 Testing Feature Feedback...');
    try {
      const featureFeedbackResponse = await fetch('http://localhost:3000/api/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'feature_request',
          feedback: 'Would love to see integration with Microsoft Teams for meeting summaries.',
          email: '<EMAIL>',
          allowContact: true,
          trigger: 'manual',
          context: {
            page: 'dashboard',
            feature: 'integrations'
          }
        })
      });
      
      if (featureFeedbackResponse.ok) {
        const featureResult = await featureFeedbackResponse.json();
        console.log('✅ Feature Feedback Test PASSED');
        console.log('💬 Feature Response:', {
          success: featureResult.success,
          message: featureResult.message,
          feedbackId: featureResult.feedbackId
        });
      } else {
        const error = await featureFeedbackResponse.json();
        console.log('❌ Feature Feedback Test FAILED');
        console.log('Error:', error);
      }
    } catch (error) {
      console.log('❌ Feature Feedback Test ERROR:', error.message);
    }
    
    // Test Bug Report
    console.log('\n🐛 Testing Bug Report...');
    try {
      const bugReportResponse = await fetch('http://localhost:3000/api/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'bug_report',
          feedback: 'File upload sometimes fails with large PDF files over 15MB.',
          email: '<EMAIL>',
          allowContact: true,
          trigger: 'error_occurred',
          context: {
            page: 'upload',
            error: 'file_too_large',
            fileSize: '18MB',
            browser: 'Chrome'
          }
        })
      });
      
      if (bugReportResponse.ok) {
        const bugResult = await bugReportResponse.json();
        console.log('✅ Bug Report Test PASSED');
        console.log('🐛 Bug Response:', {
          success: bugResult.success,
          message: bugResult.message,
          feedbackId: bugResult.feedbackId
        });
      } else {
        const error = await bugReportResponse.json();
        console.log('❌ Bug Report Test FAILED');
        console.log('Error:', error);
      }
    } catch (error) {
      console.log('❌ Bug Report Test ERROR:', error.message);
    }
    
  } catch (error) {
    console.log('❌ Smart Features Test ERROR');
    console.error('Error:', error);
  }
};

// Run the test
testSmartFeatures();
