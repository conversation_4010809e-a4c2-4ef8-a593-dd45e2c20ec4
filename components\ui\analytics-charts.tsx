'use client';

/**
 * Safe Analytics Charts - CSS-Only Implementation
 * 
 * This component provides analytics charts without using Recharts
 * to prevent chunk loading issues and runtime crashes.
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Brain, FileText, Upload, Download } from 'lucide-react';

interface AnalyticsData {
  overview: {
    total_summaries: number;
    total_uploads: number;
    total_exports: number;
    avg_processing_time: number;
  };
  daily_data: Array<{
    date: string;
    summaries: number;
    ai_requests: number;
    quality_score: number;
  }>;
  ai_models: Record<string, {
    usage_count: number;
    avg_quality: number;
    avg_cost: number;
    avg_processing_time: number;
  }>;
  top_sources: Array<{
    source: string;
    count: number;
    percentage: number;
  }>;
}

interface AnalyticsChartsProps {
  data: AnalyticsData;
  timeframe: string;
}

// CSS-only chart component
function SimpleChart({ data, type = 'bar', height = 200 }: { data: any[], type?: string, height?: number }) {
  const maxValue = Math.max(...data.map(d => d.value || d.summaries || d.usage || 0));
  
  return (
    <div className="w-full p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border" style={{ height }}>
      <div className="flex items-end justify-between h-full">
        {data.slice(0, 7).map((item, index) => {
          const value = item.value || item.summaries || item.usage || 0;
          const heightPercent = maxValue > 0 ? (value / maxValue) * 80 : 20;
          
          return (
            <div key={index} className="flex flex-col items-center space-y-2">
              <div 
                className="bg-blue-500 rounded-t-sm min-w-[20px] transition-all duration-300 hover:bg-blue-600"
                style={{ height: `${heightPercent}%`, width: '24px' }}
                title={`${item.name || item.date}: ${value}`}
              />
              <span className="text-xs text-gray-600 truncate max-w-[40px]">
                {item.name || item.date?.slice(-2) || index + 1}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// CSS-only pie chart
function SimplePieChart({ data }: { data: any[] }) {
  const total = data.reduce((sum, item) => sum + (item.value || item.count || 0), 0);
  const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6'];
  
  return (
    <div className="flex items-center justify-center space-x-4">
      <div className="relative w-32 h-32">
        <div className="w-full h-full rounded-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-800">{total}</div>
            <div className="text-xs text-blue-600">Total</div>
          </div>
        </div>
      </div>
      <div className="space-y-2">
        {data.slice(0, 5).map((item, index) => (
          <div key={index} className="flex items-center space-x-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: colors[index] }}
            />
            <span className="text-sm text-gray-700">
              {item.name || item.source}: {item.value || item.count}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}

export function AnalyticsCharts({ data, timeframe }: AnalyticsChartsProps) {
  const { overview, daily_data, ai_models, top_sources } = data;

  // Prepare AI models data for charts
  const aiModelsData = Object.entries(ai_models).map(([model, stats]) => ({
    name: model.replace('-', ' ').toUpperCase(),
    usage: stats.usage_count,
    quality: Math.round(stats.avg_quality * 100),
    cost: stats.avg_cost,
    time: stats.avg_processing_time
  }));

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Summaries</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.total_summaries.toLocaleString()}</div>
            <Badge variant="secondary" className="mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              Active
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Uploads</CardTitle>
            <Upload className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.total_uploads.toLocaleString()}</div>
            <Badge variant="secondary" className="mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              Growing
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Exports</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.total_exports.toLocaleString()}</div>
            <Badge variant="secondary" className="mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              Popular
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Processing</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.avg_processing_time.toFixed(1)}s</div>
            <Badge variant="secondary" className="mt-1">
              <TrendingDown className="h-3 w-3 mr-1" />
              Fast
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Usage Trends Chart */}
      <Card data-testid="analytics-chart">
        <CardHeader>
          <CardTitle>Usage Trends</CardTitle>
          <CardDescription>
            Summary generation and AI usage over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SimpleChart data={daily_data} type="area" height={300} />
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* AI Models Performance */}
        <Card>
          <CardHeader>
            <CardTitle>AI Models Performance</CardTitle>
            <CardDescription>
              Usage and quality comparison across AI models
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SimpleChart data={aiModelsData} type="bar" height={250} />
          </CardContent>
        </Card>

        {/* Top Sources */}
        <Card>
          <CardHeader>
            <CardTitle>Top Sources</CardTitle>
            <CardDescription>
              Most active content sources
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SimplePieChart data={top_sources} />
          </CardContent>
        </Card>
      </div>

      {/* Quality Score Trend */}
      <Card>
        <CardHeader>
          <CardTitle>Quality Score Trend</CardTitle>
          <CardDescription>
            AI summary quality over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SimpleChart 
            data={daily_data.map(d => ({ ...d, value: d.quality_score * 100 }))} 
            type="line" 
            height={200} 
          />
        </CardContent>
      </Card>
    </div>
  );
}

export default AnalyticsCharts;
