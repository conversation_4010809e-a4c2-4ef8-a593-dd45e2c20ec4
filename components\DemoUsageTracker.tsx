'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Clock, 
  Zap, 
  FileText, 
  Download, 
  Upload, 
  Slack,
  Crown,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface DemoUsage {
  summaries: { used: number; limit: number; remaining: number };
  exports: { used: number; limit: number; remaining: number };
  aiRequests: { used: number; limit: number; remaining: number };
  fileUploads: { used: number; limit: number; remaining: number };
  slackConnections: { used: number; limit: number; remaining: number };
}

interface DemoUsageTrackerProps {
  usage: DemoUsage;
  trialDaysRemaining: number;
  trialExpired: boolean;
  className?: string;
  showUpgradePrompt?: boolean;
  upgradePromptMessage?: string;
}

export default function DemoUsageTracker({
  usage,
  trialDaysRemaining,
  trialExpired,
  className = '',
  showUpgradePrompt = false,
  upgradePromptMessage = ''
}: DemoUsageTrackerProps) {
  const router = useRouter();
  const [showDetails, setShowDetails] = useState(false);

  const usageItems = [
    {
      key: 'summaries',
      label: 'AI Summaries',
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      data: usage.summaries
    },
    {
      key: 'exports',
      label: 'Exports',
      icon: Download,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      data: usage.exports
    },
    {
      key: 'aiRequests',
      label: 'AI Requests',
      icon: Zap,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      data: usage.aiRequests
    },
    {
      key: 'fileUploads',
      label: 'File Uploads',
      icon: Upload,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      data: usage.fileUploads
    },
    {
      key: 'slackConnections',
      label: 'Slack Workspaces',
      icon: Slack,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      data: usage.slackConnections
    }
  ];

  const getProgressColor = (used: number, limit: number) => {
    const percentage = (used / limit) * 100;
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const handleUpgrade = () => {
    router.push('/billing');
  };

  const getTrialStatusColor = () => {
    if (trialExpired) return 'text-red-600';
    if (trialDaysRemaining <= 2) return 'text-orange-600';
    return 'text-green-600';
  };

  const getTrialStatusBadge = () => {
    if (trialExpired) return <Badge variant="destructive">Trial Expired</Badge>;
    if (trialDaysRemaining <= 2) return <Badge variant="secondary">Trial Ending Soon</Badge>;
    return <Badge variant="default">Trial Active</Badge>;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Trial Status Card */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg">Trial Status</CardTitle>
            </div>
            {getTrialStatusBadge()}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-2xl font-bold ${getTrialStatusColor()}`}>
                {trialExpired ? 'Expired' : `${trialDaysRemaining} days left`}
              </p>
              <p className="text-sm text-gray-600">
                {trialExpired 
                  ? 'Your trial has ended. Upgrade to continue.' 
                  : 'Upgrade anytime to unlock unlimited access.'
                }
              </p>
            </div>
            <Button 
              onClick={handleUpgrade}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Crown className="h-4 w-4 mr-2" />
              Upgrade Now
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Usage Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Usage Overview</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? 'Hide Details' : 'Show Details'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {usageItems.map((item) => {
              const Icon = item.icon;
              const percentage = (item.data.used / item.data.limit) * 100;
              const isLimitReached = item.data.remaining === 0;
              
              return (
                <div key={item.key} className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className={`p-2 rounded-lg ${item.bgColor}`}>
                      <Icon className={`h-4 w-4 ${item.color}`} />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{item.label}</p>
                      <p className="text-xs text-gray-600">
                        {item.data.used} / {item.data.limit} used
                      </p>
                    </div>
                    {isLimitReached && (
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  
                  <div className="space-y-1">
                    <Progress 
                      value={percentage} 
                      className="h-2"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>{item.data.remaining} remaining</span>
                      <span>{Math.round(percentage)}%</span>
                    </div>
                  </div>
                  
                  {isLimitReached && (
                    <p className="text-xs text-red-600 font-medium">
                      Limit reached - Upgrade to continue
                    </p>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Upgrade Prompt Alert */}
      {showUpgradePrompt && upgradePromptMessage && (
        <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
          <TrendingUp className="h-4 w-4 text-orange-600" />
          <AlertDescription className="flex items-center justify-between">
            <div className="flex-1 pr-4">
              <div className="font-medium text-orange-800 dark:text-orange-200">
                Ready to unlock more?
              </div>
              <div className="text-sm text-orange-700 dark:text-orange-300 mt-1">
                {upgradePromptMessage}
              </div>
            </div>
            <Button 
              onClick={handleUpgrade}
              size="sm"
              className="bg-orange-600 hover:bg-orange-700 text-white"
            >
              Upgrade
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Detailed Usage (Collapsible) */}
      {showDetails && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Detailed Usage Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {usageItems.map((item) => {
                const Icon = item.icon;
                const percentage = (item.data.used / item.data.limit) * 100;
                
                return (
                  <div key={item.key} className="flex items-center space-x-4 p-3 rounded-lg border">
                    <div className={`p-3 rounded-lg ${item.bgColor}`}>
                      <Icon className={`h-5 w-5 ${item.color}`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{item.label}</h4>
                        <span className="text-sm text-gray-600">
                          {item.data.used} / {item.data.limit}
                        </span>
                      </div>
                      <Progress 
                        value={percentage} 
                        className="h-2 mb-2"
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>{item.data.remaining} remaining</span>
                        <span>{Math.round(percentage)}% used</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
