'use client';

/**
 * SSO Configuration Component
 * Enterprise Single Sign-On management
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, Plus, Settings, TestTube, Trash2, CheckCircle, 
  XCircle, AlertTriangle, Loader2, Crown, Key, Globe 
} from 'lucide-react';
import { getCurrentUserClient } from '@/lib/user-management';
import { toast } from 'sonner';
import type { SSOConfiguration, SSOProvider } from '@/lib/sso-config';

interface SSOConfigurationProps {
  className?: string;
}

export default function SSOConfigurationComponent({ className }: SSOConfigurationProps) {
  const [user, setUser] = useState<any>(null);
  const [config, setConfig] = useState<SSOConfiguration | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState<string | null>(null);
  const [showAddProvider, setShowAddProvider] = useState(false);
  const [newProvider, setNewProvider] = useState({
    name: '',
    type: 'saml' as 'saml' | 'oidc' | 'oauth',
    enabled: true,
    configuration: {},
    domains: [] as string[],
    auto_provision: false,
    default_role: 'member' as 'admin' | 'member',
  });

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { getCurrentUserClient } = await import('@/lib/user-management-client');
        const currentUser = await getCurrentUserClient();
        setUser(currentUser);
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    };

    fetchUser();
  }, []);

  useEffect(() => {
    if (user) {
      fetchSSOConfig();
    }
  }, [user]);

  const fetchSSOConfig = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/sso/config');
      const data = await response.json();

      if (data.success) {
        setConfig(data.config);
      } else {
        if (data.upgrade_required) {
          toast.error('SSO configuration requires Enterprise subscription');
        } else {
          toast.error(data.error || 'Failed to load SSO configuration');
        }
      }
    } catch (error) {
      console.error('Error fetching SSO config:', error);
      toast.error('Failed to load SSO configuration');
    } finally {
      setLoading(false);
    }
  };

  const updateSSOConfig = async (updates: Partial<SSOConfiguration>) => {
    try {
      setSaving(true);
      
      const response = await fetch('/api/sso/config', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });

      const data = await response.json();

      if (data.success) {
        setConfig(data.config);
        toast.success('SSO configuration updated');
      } else {
        toast.error(data.error || 'Failed to update SSO configuration');
      }
    } catch (error) {
      console.error('Error updating SSO config:', error);
      toast.error('Failed to update SSO configuration');
    } finally {
      setSaving(false);
    }
  };

  const addSSOProvider = async () => {
    try {
      setSaving(true);
      
      const response = await fetch('/api/sso/providers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newProvider),
      });

      const data = await response.json();

      if (data.success) {
        await fetchSSOConfig(); // Refresh config
        setShowAddProvider(false);
        setNewProvider({
          name: '',
          type: 'saml',
          enabled: true,
          configuration: {},
          domains: [],
          auto_provision: false,
          default_role: 'member',
        });
        toast.success('SSO provider added');
      } else {
        toast.error(data.error || 'Failed to add SSO provider');
      }
    } catch (error) {
      console.error('Error adding SSO provider:', error);
      toast.error('Failed to add SSO provider');
    } finally {
      setSaving(false);
    }
  };

  const testSSOProvider = async (providerId: string) => {
    try {
      setTesting(providerId);
      
      const response = await fetch('/api/sso/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider_id: providerId }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('SSO provider test successful');
      } else {
        toast.error(data.error || 'SSO provider test failed');
      }
    } catch (error) {
      console.error('Error testing SSO provider:', error);
      toast.error('Failed to test SSO provider');
    } finally {
      setTesting(null);
    }
  };

  const removeSSOProvider = async (providerId: string) => {
    try {
      const response = await fetch(`/api/sso/providers?provider_id=${providerId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        await fetchSSOConfig(); // Refresh config
        toast.success('SSO provider removed');
      } else {
        toast.error(data.error || 'Failed to remove SSO provider');
      }
    } catch (error) {
      console.error('Error removing SSO provider:', error);
      toast.error('Failed to remove SSO provider');
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading SSO configuration...</span>
        </CardContent>
      </Card>
    );
  }

  if (!config) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              SSO configuration requires Enterprise subscription.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6" />
            SSO Configuration
            <Crown className="h-5 w-5 text-yellow-500" />
          </h2>
          <p className="text-gray-600">
            Enterprise Single Sign-On configuration and management
          </p>
        </div>
        
        <Button onClick={() => setShowAddProvider(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Provider
        </Button>
      </div>

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            General Settings
          </CardTitle>
          <CardDescription>
            Configure SSO behavior and security settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="enforce-sso">Enforce SSO</Label>
              <p className="text-sm text-gray-600">
                Require SSO for all users in this organization
              </p>
            </div>
            <Switch
              checked={config.enforce_sso}
              onCheckedChange={(checked) => updateSSOConfig({ enforce_sso: checked })}
              disabled={saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="allow-email-password">Allow Email/Password</Label>
              <p className="text-sm text-gray-600">
                Allow traditional email/password login alongside SSO
              </p>
            </div>
            <Switch
              checked={config.allow_email_password}
              onCheckedChange={(checked) => updateSSOConfig({ allow_email_password: checked })}
              disabled={saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="require-mfa">Require MFA</Label>
              <p className="text-sm text-gray-600">
                Require multi-factor authentication for all users
              </p>
            </div>
            <Switch
              checked={config.require_mfa}
              onCheckedChange={(checked) => updateSSOConfig({ require_mfa: checked })}
              disabled={saving}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
              <Input
                id="session-timeout"
                type="number"
                value={config.session_timeout}
                onChange={(e) => updateSSOConfig({ session_timeout: parseInt(e.target.value) })}
                disabled={saving}
              />
            </div>

            <div>
              <Label htmlFor="allowed-domains">Allowed Domains</Label>
              <Input
                id="allowed-domains"
                placeholder="example.com, company.org"
                value={config.allowed_domains.join(', ')}
                onChange={(e) => updateSSOConfig({ 
                  allowed_domains: e.target.value.split(',').map(d => d.trim()).filter(Boolean)
                })}
                disabled={saving}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* SSO Providers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            SSO Providers ({config.providers.length})
          </CardTitle>
          <CardDescription>
            Configure identity providers for single sign-on
          </CardDescription>
        </CardHeader>
        <CardContent>
          {config.providers.length === 0 ? (
            <Alert>
              <Globe className="h-4 w-4" />
              <AlertDescription>
                No SSO providers configured. Add a provider to enable single sign-on.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {config.providers.map((provider) => (
                <div key={provider.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${provider.enabled ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <Shield className={`h-4 w-4 ${provider.enabled ? 'text-green-600' : 'text-gray-600'}`} />
                    </div>
                    <div>
                      <div className="font-medium">{provider.name}</div>
                      <div className="text-sm text-gray-600">
                        {provider.type.toUpperCase()} • {provider.domains.join(', ')}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Badge variant={provider.enabled ? 'default' : 'secondary'}>
                      {provider.enabled ? 'Enabled' : 'Disabled'}
                    </Badge>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => testSSOProvider(provider.id)}
                      disabled={testing === provider.id}
                    >
                      {testing === provider.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <TestTube className="h-4 w-4" />
                      )}
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeSSOProvider(provider.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Provider Modal */}
      {showAddProvider && (
        <Card>
          <CardHeader>
            <CardTitle>Add SSO Provider</CardTitle>
            <CardDescription>
              Configure a new identity provider for single sign-on
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="provider-name">Provider Name</Label>
                <Input
                  id="provider-name"
                  placeholder="Company SSO"
                  value={newProvider.name}
                  onChange={(e) => setNewProvider(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="provider-type">Provider Type</Label>
                <Select 
                  value={newProvider.type} 
                  onValueChange={(value: 'saml' | 'oidc' | 'oauth') => 
                    setNewProvider(prev => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="saml">SAML 2.0</SelectItem>
                    <SelectItem value="oidc">OpenID Connect</SelectItem>
                    <SelectItem value="oauth">OAuth 2.0</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="provider-domains">Domains</Label>
              <Input
                id="provider-domains"
                placeholder="example.com, company.org"
                value={newProvider.domains.join(', ')}
                onChange={(e) => setNewProvider(prev => ({ 
                  ...prev, 
                  domains: e.target.value.split(',').map(d => d.trim()).filter(Boolean)
                }))}
              />
            </div>

            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={() => setShowAddProvider(false)}
              >
                Cancel
              </Button>
              
              <Button
                onClick={addSSOProvider}
                disabled={saving || !newProvider.name || !newProvider.type}
              >
                {saving ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Adding...
                  </>
                ) : (
                  'Add Provider'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enterprise Features */}
      <Alert>
        <Crown className="h-4 w-4" />
        <AlertDescription>
          <strong>Enterprise SSO:</strong> SAML 2.0, OpenID Connect, and OAuth 2.0 
          integrations with automatic user provisioning and role mapping.
        </AlertDescription>
      </Alert>
    </div>
  );
}
