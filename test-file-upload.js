/**
 * Test File Upload System
 */

const fs = require('fs');
const FormData = require('form-data');
const { default: fetch } = require('node-fetch');

const testFileUpload = async () => {
  try {
    console.log('🧪 Testing File Upload System...');
    
    // Create a test text file
    const testContent = `Meeting Transcript - Q4 Planning Session

<PERSON>: Good morning everyone. Let's start our Q4 planning session. We have several key objectives to discuss today.

<PERSON>: Thanks <PERSON>. I've prepared the marketing strategy overview. Our target demographics show strong engagement in the 25-45 age group.

<PERSON>: From the technical side, we need to ensure our infrastructure can handle the expected 300% traffic increase during the holiday season.

<PERSON>: Excellent points. <PERSON>, can you have the detailed marketing plan ready by Friday?

<PERSON>: Absolutely. I'll include the budget breakdown and timeline.

<PERSON>: I'll run load tests this week and provide a technical readiness report.

<PERSON>: Perfect. Let's schedule a follow-up meeting for Monday to review everything.

Key Action Items:
- Marketing plan due Friday (<PERSON>)
- Load testing this week (Mike)
- Follow-up meeting Monday (All)

Skills Mentioned:
- Marketing strategy
- Infrastructure planning
- Load testing
- Project management

Red Flags:
- 300% traffic increase could overwhelm current systems
- Tight timeline for deliverables`;

    // Write test file
    fs.writeFileSync('test-transcript.txt', testContent);
    
    // Create form data
    const form = new FormData();
    form.append('file', fs.createReadStream('test-transcript.txt'));
    form.append('fileId', 'test-upload-' + Date.now());
    
    console.log('📁 Uploading test file...');
    
    const response = await fetch('http://localhost:3000/api/upload', {
      method: 'POST',
      body: form
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ File Upload Test PASSED');
      console.log('📊 Upload Result:', {
        success: result.success,
        fileId: result.data?.fileId,
        fileName: result.data?.fileName,
        fileSize: result.data?.fileSize,
        uploadStatus: result.data?.uploadStatus,
        processingStatus: result.data?.processingStatus,
        estimatedTime: result.data?.estimatedProcessingTime
      });
      
      // Test status polling
      if (result.data?.fileId) {
        console.log('📊 Testing status polling...');
        await testStatusPolling(result.data.fileId);
      }
    } else {
      console.log('❌ File Upload Test FAILED');
      console.log('Error:', result);
    }
    
    // Clean up
    try {
      fs.unlinkSync('test-transcript.txt');
    } catch (e) {
      // Ignore cleanup errors
    }
    
  } catch (error) {
    console.log('❌ File Upload Test ERROR');
    console.error('Error:', error);
  }
};

const testStatusPolling = async (fileId) => {
  let attempts = 0;
  const maxAttempts = 10;
  
  const poll = async () => {
    try {
      const response = await fetch(`http://localhost:3000/api/upload/status?fileId=${fileId}`);
      const result = await response.json();
      
      if (response.ok) {
        console.log(`📊 Status Poll ${attempts + 1}:`, {
          status: result.data?.status,
          progress: result.data?.progress,
          currentStep: result.data?.processingSteps?.find(s => s.status === 'processing')?.step
        });
        
        if (result.data?.status === 'completed' || result.data?.status === 'error') {
          console.log('✅ Status polling complete');
          return;
        }
      }
      
      attempts++;
      if (attempts < maxAttempts) {
        setTimeout(poll, 2000); // Poll every 2 seconds
      } else {
        console.log('⏰ Status polling timeout');
      }
    } catch (error) {
      console.error('Status polling error:', error);
    }
  };
  
  setTimeout(poll, 1000); // Start polling after 1 second
};

// Run the test
testFileUpload();
