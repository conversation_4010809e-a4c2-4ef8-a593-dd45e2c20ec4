-- Demo Usage Tracking Table
-- Tracks usage limits and trial status for demo users

-- Create demo_usage table
CREATE TABLE IF NOT EXISTS demo_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT NOT NULL UNIQUE,
  summaries_used INTEGER DEFAULT 0 NOT NULL,
  exports_used INTEGER DEFAULT 0 NOT NULL,
  ai_requests_used INTEGER DEFAULT 0 NOT NULL,
  file_uploads_used INTEGER DEFAULT 0 NOT NULL,
  slack_connections_used INTEGER DEFAULT 0 NOT NULL,
  trial_started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  trial_expires_at TIMESTAMPTZ NOT NULL,
  upgrade_prompts_shown INTEGER DEFAULT 0 NOT NULL,
  last_prompt_shown_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_demo_usage_user_id ON demo_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_demo_usage_trial_expires ON demo_usage(trial_expires_at);
CREATE INDEX IF NOT EXISTS idx_demo_usage_created_at ON demo_usage(created_at);

-- Enable Row Level Security
ALTER TABLE demo_usage ENABLE ROW LEVEL SECURITY;

-- RLS Policies for demo_usage table
-- Users can only access their own demo usage data
CREATE POLICY "Users can view own demo usage" ON demo_usage
  FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert own demo usage" ON demo_usage
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update own demo usage" ON demo_usage
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Service role can access all demo usage data for admin purposes
CREATE POLICY "Service role can access all demo usage" ON demo_usage
  FOR ALL USING (auth.role() = 'service_role');

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_demo_usage_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_demo_usage_updated_at_trigger
  BEFORE UPDATE ON demo_usage
  FOR EACH ROW
  EXECUTE FUNCTION update_demo_usage_updated_at();

-- Function to increment usage counters safely
CREATE OR REPLACE FUNCTION increment_demo_usage(
  p_user_id TEXT,
  p_column TEXT,
  p_increment INTEGER DEFAULT 1
)
RETURNS BOOLEAN AS $$
DECLARE
  rows_affected INTEGER;
BEGIN
  -- Validate column name to prevent SQL injection
  IF p_column NOT IN ('summaries_used', 'exports_used', 'ai_requests_used', 'file_uploads_used', 'slack_connections_used', 'upgrade_prompts_shown') THEN
    RAISE EXCEPTION 'Invalid column name: %', p_column;
  END IF;
  
  -- Use dynamic SQL to update the specified column
  EXECUTE format('
    UPDATE demo_usage 
    SET %I = %I + $1, updated_at = NOW() 
    WHERE user_id = $2
  ', p_column, p_column)
  USING p_increment, p_user_id;
  
  GET DIAGNOSTICS rows_affected = ROW_COUNT;
  
  RETURN rows_affected > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has reached usage limit
CREATE OR REPLACE FUNCTION check_demo_usage_limit(
  p_user_id TEXT,
  p_usage_type TEXT,
  p_limit INTEGER
)
RETURNS BOOLEAN AS $$
DECLARE
  current_usage INTEGER;
BEGIN
  -- Validate usage type
  IF p_usage_type NOT IN ('summaries_used', 'exports_used', 'ai_requests_used', 'file_uploads_used', 'slack_connections_used') THEN
    RAISE EXCEPTION 'Invalid usage type: %', p_usage_type;
  END IF;
  
  -- Get current usage
  EXECUTE format('SELECT %I FROM demo_usage WHERE user_id = $1', p_usage_type)
  INTO current_usage
  USING p_user_id;
  
  -- Return true if under limit, false if at or over limit
  RETURN COALESCE(current_usage, 0) < p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get demo usage summary
CREATE OR REPLACE FUNCTION get_demo_usage_summary(p_user_id TEXT)
RETURNS TABLE (
  summaries_used INTEGER,
  exports_used INTEGER,
  ai_requests_used INTEGER,
  file_uploads_used INTEGER,
  slack_connections_used INTEGER,
  trial_days_remaining INTEGER,
  trial_expired BOOLEAN,
  upgrade_prompts_shown INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    du.summaries_used,
    du.exports_used,
    du.ai_requests_used,
    du.file_uploads_used,
    du.slack_connections_used,
    GREATEST(0, EXTRACT(DAY FROM (du.trial_expires_at - NOW()))::INTEGER) as trial_days_remaining,
    (NOW() > du.trial_expires_at) as trial_expired,
    du.upgrade_prompts_shown
  FROM demo_usage du
  WHERE du.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up expired demo usage records (for maintenance)
CREATE OR REPLACE FUNCTION cleanup_expired_demo_usage()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete demo usage records that are more than 30 days past expiration
  DELETE FROM demo_usage 
  WHERE trial_expires_at < NOW() - INTERVAL '30 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON demo_usage TO authenticated;
GRANT EXECUTE ON FUNCTION increment_demo_usage(TEXT, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION check_demo_usage_limit(TEXT, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_demo_usage_summary(TEXT) TO authenticated;

-- Grant service role full access for admin operations
GRANT ALL ON demo_usage TO service_role;
GRANT EXECUTE ON FUNCTION cleanup_expired_demo_usage() TO service_role;

-- Add comments for documentation
COMMENT ON TABLE demo_usage IS 'Tracks usage limits and trial status for demo users';
COMMENT ON COLUMN demo_usage.user_id IS 'Clerk user ID';
COMMENT ON COLUMN demo_usage.summaries_used IS 'Number of AI summaries created during trial';
COMMENT ON COLUMN demo_usage.exports_used IS 'Number of exports performed during trial';
COMMENT ON COLUMN demo_usage.ai_requests_used IS 'Number of AI requests made during trial';
COMMENT ON COLUMN demo_usage.file_uploads_used IS 'Number of files uploaded during trial';
COMMENT ON COLUMN demo_usage.slack_connections_used IS 'Number of Slack workspaces connected during trial';
COMMENT ON COLUMN demo_usage.trial_started_at IS 'When the trial period started';
COMMENT ON COLUMN demo_usage.trial_expires_at IS 'When the trial period expires';
COMMENT ON COLUMN demo_usage.upgrade_prompts_shown IS 'Number of upgrade prompts shown to user';
COMMENT ON COLUMN demo_usage.last_prompt_shown_at IS 'When the last upgrade prompt was shown';

COMMENT ON FUNCTION increment_demo_usage(TEXT, TEXT, INTEGER) IS 'Safely increment usage counters for demo users';
COMMENT ON FUNCTION check_demo_usage_limit(TEXT, TEXT, INTEGER) IS 'Check if user has reached usage limit for specific feature';
COMMENT ON FUNCTION get_demo_usage_summary(TEXT) IS 'Get comprehensive usage summary for demo user';
COMMENT ON FUNCTION cleanup_expired_demo_usage() IS 'Clean up expired demo usage records for maintenance';
