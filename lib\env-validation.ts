import { devLog } from '@/lib/console-cleaner';
/**
 * Environment Variable Validation Utility
 * Validates all required environment variables at runtime
 */

interface EnvConfig {
  // Supabase
  NEXT_PUBLIC_SUPABASE_URL: string;
  NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
  SUPABASE_SERVICE_ROLE_KEY?: string;

  // App URLs
  NEXT_PUBLIC_SITE_URL: string;
  NEXT_PUBLIC_APP_URL: string;
  NEXTAUTH_URL?: string;

  // Clerk Authentication (Live SaaS)
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: string;
  CLERK_SECRET_KEY: string;
  CLERK_WEBHOOK_SECRET?: string;

  // Optional services
  OPENAI_API_KEY?: string;
  SLACK_CLIENT_ID?: string;
  SLACK_CLIENT_SECRET?: string;
  RESEND_API_KEY?: string;
  SENTRY_DSN?: string;
}

const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'NEXT_PUBLIC_SITE_URL',
  'NEXT_PUBLIC_APP_URL',
  'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
  'CLERK_SECRET_KEY'
] as const;

export function validateEnvironmentVariables(): {
  isValid: boolean;
  missing: string[];
  invalid: string[];
  config: Partial<EnvConfig>;
} {
  const missing: string[] = [];
  const invalid: string[] = [];
  const config: Partial<EnvConfig> = {};

  // Check required variables
  for (const envVar of requiredEnvVars) {
    const value = process.env[envVar];
    if (!value) {
      missing.push(envVar);
    } else {
      config[envVar] = value;
      
      // Validate URL format for URL variables
      if (envVar.includes('URL') && !isValidUrl(value)) {
        invalid.push(`${envVar} (invalid URL format: ${value})`);
      }
    }
  }

  // Clerk authentication removed - Public Demo Mode

  // Check optional but commonly used variables
  const optionalVars = [
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXTAUTH_URL',
    'OPENAI_API_KEY',
    'SLACK_CLIENT_ID',
    'SLACK_CLIENT_SECRET',
    'RESEND_API_KEY',
    'SENTRY_DSN'
  ];

  for (const envVar of optionalVars) {
    const value = process.env[envVar];
    if (value) {
      config[envVar as keyof EnvConfig] = value;
    }
  }

  return {
    isValid: missing.length === 0 && invalid.length === 0,
    missing,
    invalid,
    config
  };
}

function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

/**
 * Clerk validation removed - Public Demo Mode
 */
function validateClerkKeys(): { isValid: boolean; errors: string[] } {
  // Always return valid for demo mode
  return {
    isValid: true,
    errors: []
  };
}

export function logEnvironmentStatus(): void {
  const validation = validateEnvironmentVariables();
  devLog.log('🔧 Environment Variable Status:');
  devLog.log('================================');

  if (validation.isValid) {
  devLog.log('✅ All required environment variables are present and valid');
  } else {
  devLog.log('❌ Environment validation failed');
  }

  // Public Demo Mode - No authentication required
  devLog.log('\n🎭 Authentication Status:');
  devLog.log('✅ Public Demo Mode - No authentication required');

  if (validation.missing.length > 0) {
  devLog.log('\n🚨 Missing required variables:');
    validation.missing.forEach(envVar => {
  devLog.log(`   - ${envVar}`);
    });
  }

  if (validation.invalid.length > 0) {
  devLog.log('\n⚠️  Invalid variables:');
    validation.invalid.forEach(envVar => {
  devLog.log(`   - ${envVar}`);
    });
  }
  devLog.log('\n📋 Available variables:');
  Object.entries(validation.config).forEach(([key, value]) => {
    if (key.includes('KEY') || key.includes('SECRET')) {
  devLog.log(`   ✓ ${key}: [REDACTED]`);
    } else {
  devLog.log(`   ✓ ${key}: ${value}`);
    }
  });
  devLog.log('================================');
}

export function getEnvConfig(): EnvConfig {
  const validation = validateEnvironmentVariables();
  
  if (!validation.isValid) {
    throw new Error(
      `Environment validation failed:\n` +
      `Missing: ${validation.missing.join(', ')}\n` +
      `Invalid: ${validation.invalid.join(', ')}`
    );
  }
  
  return validation.config as EnvConfig;
}
