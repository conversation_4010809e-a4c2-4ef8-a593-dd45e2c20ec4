import { toast as sonnerToast } from 'sonner';

export interface ToastOptions {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success';
  duration?: number;
}

export function useToast() {
  const toast = (options: ToastOptions | string) => {
    if (typeof options === 'string') {
      sonnerToast(options);
      return;
    }

    const { title, description, variant = 'default', duration } = options;
    
    const message = title || description || '';
    const config = {
      duration,
      description: title && description ? description : undefined,
    };

    switch (variant) {
      case 'destructive':
        sonnerToast.error(message, config);
        break;
      case 'success':
        sonnerToast.success(message, config);
        break;
      default:
        sonnerToast(message, config);
        break;
    }
  };

  return {
    toast,
    dismiss: sonnerToast.dismiss,
  };
}

// Export a simple toast function for direct use
export const showToast = (options: ToastOptions | string) => {
  // Use sonner directly to avoid hook rules violation
  if (typeof options === 'string') {
    sonnerToast(options);
  } else {
    sonnerToast(options.title || '', options);
  }
};
