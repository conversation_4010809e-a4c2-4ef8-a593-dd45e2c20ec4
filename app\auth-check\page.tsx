'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertCircle, Shield, User, Key, Database } from 'lucide-react';

interface SecurityCheck {
  name: string;
  status: 'pass' | 'fail' | 'warning' | 'loading';
  message: string;
  details?: string;
}

export default function AuthCheckPage() {
  const { isLoaded, isSignedIn, userId } = useAuth();
  const { user } = useUser();
  const [securityChecks, setSecurityChecks] = useState<SecurityCheck[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isLoaded) {
      runSecurityChecks();
    }
  }, [isLoaded, isSignedIn, userId]);

  const runSecurityChecks = async () => {
    setLoading(true);
    const checks: SecurityCheck[] = [];

    // Check 1: Authentication Status
    checks.push({
      name: 'Authentication Status',
      status: isSignedIn ? 'pass' : 'fail',
      message: isSignedIn ? 'User is authenticated' : 'User is not authenticated',
      details: userId ? `User ID: ${userId}` : 'No user ID available'
    });

    // Check 2: User Profile Data
    checks.push({
      name: 'User Profile',
      status: user ? 'pass' : 'fail',
      message: user ? 'User profile loaded' : 'User profile not available',
      details: user ? `Email: ${user.emailAddresses[0]?.emailAddress || 'N/A'}` : undefined
    });

    // Check 3: Protected API Access
    try {
      const response = await fetch('/api/dashboard');
      checks.push({
        name: 'Protected API Access',
        status: response.ok ? 'pass' : 'fail',
        message: response.ok ? 'Can access protected APIs' : 'Cannot access protected APIs',
        details: `Status: ${response.status} ${response.statusText}`
      });
    } catch (error) {
      checks.push({
        name: 'Protected API Access',
        status: 'fail',
        message: 'API request failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Check 4: Session Persistence
    const sessionData = localStorage.getItem('clerk-session');
    checks.push({
      name: 'Session Persistence',
      status: sessionData ? 'pass' : 'warning',
      message: sessionData ? 'Session data found' : 'No session data in localStorage',
      details: sessionData ? 'Session is persisted locally' : 'Session may not persist across browser restarts'
    });

    // Check 5: Environment Configuration
    const hasClerkKeys = !!(process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY);
    checks.push({
      name: 'Environment Configuration',
      status: hasClerkKeys ? 'pass' : 'fail',
      message: hasClerkKeys ? 'Clerk keys configured' : 'Clerk keys missing',
      details: hasClerkKeys ? 'Authentication environment is properly configured' : 'Check environment variables'
    });

    // Check 6: Role-Based Access
    const userRole = user?.publicMetadata?.role || 'user';
    checks.push({
      name: 'Role-Based Access',
      status: 'pass',
      message: `User role: ${userRole}`,
      details: `Role-based permissions are configured`
    });

    // Check 7: Rate Limiting Headers
    try {
      const response = await fetch('/api/dashboard');
      const rateLimitHeaders = {
        limit: response.headers.get('X-RateLimit-Limit'),
        remaining: response.headers.get('X-RateLimit-Remaining'),
        reset: response.headers.get('X-RateLimit-Reset')
      };
      
      const hasRateLimiting = rateLimitHeaders.limit || rateLimitHeaders.remaining;
      checks.push({
        name: 'Rate Limiting',
        status: hasRateLimiting ? 'pass' : 'warning',
        message: hasRateLimiting ? 'Rate limiting active' : 'Rate limiting not detected',
        details: hasRateLimiting ? 
          `Limit: ${rateLimitHeaders.limit}, Remaining: ${rateLimitHeaders.remaining}` : 
          'No rate limit headers found'
      });
    } catch (error) {
      checks.push({
        name: 'Rate Limiting',
        status: 'warning',
        message: 'Could not check rate limiting',
        details: 'API request failed'
      });
    }

    setSecurityChecks(checks);
    setLoading(false);
  };

  const getStatusIcon = (status: SecurityCheck['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'fail':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'loading':
        return <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />;
    }
  };

  const getStatusBadge = (status: SecurityCheck['status']) => {
    switch (status) {
      case 'pass':
        return <Badge variant="default" className="bg-green-100 text-green-800">Pass</Badge>;
      case 'fail':
        return <Badge variant="destructive">Fail</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warning</Badge>;
      case 'loading':
        return <Badge variant="outline">Loading</Badge>;
    }
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading authentication status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Authentication & Security Check
          </h1>
          <p className="mt-2 text-gray-600">
            Comprehensive security validation for Slack Summary Scribe
          </p>
        </div>

        {/* User Info Card */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              User Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Authentication Status</p>
                <p className="text-lg font-semibold">
                  {isSignedIn ? (
                    <span className="text-green-600">Authenticated</span>
                  ) : (
                    <span className="text-red-600">Not Authenticated</span>
                  )}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">User ID</p>
                <p className="text-lg font-mono">{userId || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Email</p>
                <p className="text-lg">{user?.emailAddresses[0]?.emailAddress || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Role</p>
                <p className="text-lg">{(user?.publicMetadata?.role as string) || 'user'}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security Checks */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Security Checks
            </CardTitle>
            <CardDescription>
              Automated security validation results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Running security checks...</p>
                </div>
              ) : (
                securityChecks.map((check, index) => (
                  <div key={index} className="flex items-start gap-3 p-4 border rounded-lg">
                    {getStatusIcon(check.status)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-gray-900">{check.name}</h3>
                        {getStatusBadge(check.status)}
                      </div>
                      <p className="text-gray-600 mt-1">{check.message}</p>
                      {check.details && (
                        <p className="text-sm text-gray-500 mt-1">{check.details}</p>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>

            <div className="mt-6 pt-6 border-t">
              <Button onClick={runSecurityChecks} disabled={loading}>
                {loading ? 'Running Checks...' : 'Re-run Security Checks'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button variant="outline" asChild>
            <a href="/dashboard">Go to Dashboard</a>
          </Button>
          <Button variant="outline" asChild>
            <a href="/api/dashboard" target="_blank">Test API Endpoint</a>
          </Button>
          <Button variant="outline" asChild>
            <a href="/sign-in">Sign In Page</a>
          </Button>
        </div>
      </div>
    </div>
  );
}
