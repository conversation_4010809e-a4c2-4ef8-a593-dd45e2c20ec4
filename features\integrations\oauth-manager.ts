/**
 * Unified OAuth Management System
 * 
 * Centralized OAuth token management for all integrations
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';
import { 
  OAuthToken, 
  TokenRefreshResult, 
  AuthConfig, 
  AuthResult,
  IntegrationProvider,
  AuthenticationError,
  RateLimitError
} from './types';

// OAuth provider configurations
const OAUTH_PROVIDERS: Record<IntegrationProvider, OAuthProviderConfig> = {
  'notion': {
    authUrl: 'https://api.notion.com/v1/oauth/authorize',
    tokenUrl: 'https://api.notion.com/v1/oauth/token',
    scopes: ['read_content', 'insert_content'],
    clientId: process.env.NOTION_CLIENT_ID!,
    clientSecret: process.env.NOTION_CLIENT_SECRET!,
  },
  'google-drive': {
    authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
    tokenUrl: 'https://oauth2.googleapis.com/token',
    scopes: ['https://www.googleapis.com/auth/drive.file'],
    clientId: process.env.GOOGLE_CLIENT_ID!,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
  },
  'dropbox': {
    authUrl: 'https://www.dropbox.com/oauth2/authorize',
    tokenUrl: 'https://api.dropboxapi.com/oauth2/token',
    scopes: ['files.content.write'],
    clientId: process.env.DROPBOX_APP_KEY!,
    clientSecret: process.env.DROPBOX_APP_SECRET!,
  },
  'slack': {
    authUrl: 'https://slack.com/oauth/v2/authorize',
    tokenUrl: 'https://slack.com/api/oauth.v2.access',
    scopes: ['chat:write', 'files:write'],
    clientId: process.env.SLACK_CLIENT_ID!,
    clientSecret: process.env.SLACK_CLIENT_SECRET!,
  },
  'hubspot': {
    authUrl: 'https://app.hubspot.com/oauth/authorize',
    tokenUrl: 'https://api.hubapi.com/oauth/v1/token',
    scopes: ['contacts', 'content'],
    clientId: process.env.HUBSPOT_CLIENT_ID!,
    clientSecret: process.env.HUBSPOT_CLIENT_SECRET!,
  },
  'salesforce': {
    authUrl: 'https://login.salesforce.com/services/oauth2/authorize',
    tokenUrl: 'https://login.salesforce.com/services/oauth2/token',
    scopes: ['api', 'refresh_token'],
    clientId: process.env.SALESFORCE_CLIENT_ID!,
    clientSecret: process.env.SALESFORCE_CLIENT_SECRET!,
  },
  // Add other providers as needed
  'airtable': {
    authUrl: 'https://airtable.com/oauth2/v1/authorize',
    tokenUrl: 'https://airtable.com/oauth2/v1/token',
    scopes: ['data.records:write'],
    clientId: process.env.AIRTABLE_CLIENT_ID!,
    clientSecret: process.env.AIRTABLE_CLIENT_SECRET!,
  },
  'trello': {
    authUrl: 'https://trello.com/1/authorize',
    tokenUrl: 'https://trello.com/1/OAuthGetAccessToken',
    scopes: ['read', 'write'],
    clientId: process.env.TRELLO_API_KEY!,
    clientSecret: process.env.TRELLO_API_SECRET!,
  },
  'asana': {
    authUrl: 'https://app.asana.com/-/oauth_authorize',
    tokenUrl: 'https://app.asana.com/-/oauth_token',
    scopes: ['default'],
    clientId: process.env.ASANA_CLIENT_ID!,
    clientSecret: process.env.ASANA_CLIENT_SECRET!,
  },
  'monday': {
    authUrl: 'https://auth.monday.com/oauth2/authorize',
    tokenUrl: 'https://auth.monday.com/oauth2/token',
    scopes: ['boards:read', 'boards:write'],
    clientId: process.env.MONDAY_CLIENT_ID!,
    clientSecret: process.env.MONDAY_CLIENT_SECRET!,
  },
  'clickup': {
    authUrl: 'https://app.clickup.com/api/oauth/authorize',
    tokenUrl: 'https://api.clickup.com/api/v2/oauth/token',
    scopes: ['task:write'],
    clientId: process.env.CLICKUP_CLIENT_ID!,
    clientSecret: process.env.CLICKUP_CLIENT_SECRET!,
  },
};

interface OAuthProviderConfig {
  authUrl: string;
  tokenUrl: string;
  scopes: string[];
  clientId: string;
  clientSecret: string;
  customParams?: Record<string, string>;
}

/**
 * Generate OAuth authorization URL
 */
export async function generateAuthUrl(
  provider: IntegrationProvider,
  userId: string,
  organizationId: string,
  redirectUri: string,
  state?: string
): Promise<{ authUrl: string; state: string }> {
  const config = OAUTH_PROVIDERS[provider];
  if (!config) {
    throw new Error(`Unsupported OAuth provider: ${provider}`);
  }

  // Generate state parameter for security
  const stateParam = state || generateSecureState();
  
  // Store state in database for validation
  await storeOAuthState(stateParam, provider, userId, organizationId);

  const params = new URLSearchParams({
    client_id: config.clientId,
    redirect_uri: redirectUri,
    scope: config.scopes.join(' '),
    state: stateParam,
    response_type: 'code',
    access_type: 'offline', // For refresh tokens
    ...config.customParams
  });

  const authUrl = `${config.authUrl}?${params.toString()}`;
  
  return { authUrl, state: stateParam };
}

/**
 * Exchange authorization code for access token
 */
export async function exchangeCodeForToken(
  provider: IntegrationProvider,
  code: string,
  state: string,
  redirectUri: string
): Promise<AuthResult> {
  try {
    const config = OAUTH_PROVIDERS[provider];
    if (!config) {
      throw new AuthenticationError(provider, `Unsupported OAuth provider: ${provider}`);
    }

    // Validate state parameter
    const stateData = await validateOAuthState(state);
    if (!stateData) {
      throw new AuthenticationError(provider, 'Invalid or expired state parameter');
    }

    // Exchange code for token
    const tokenResponse = await fetch(config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: config.clientId,
        client_secret: config.clientSecret,
        code,
        redirect_uri: redirectUri,
      }),
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.json().catch(() => ({}));
      throw new AuthenticationError(
        provider, 
        `Token exchange failed: ${errorData.error_description || tokenResponse.statusText}`
      );
    }

    const tokenData = await tokenResponse.json();

    // Calculate expiration time
    const expiresAt = tokenData.expires_in 
      ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
      : undefined;

    // Store token in database
    const token = await storeOAuthToken({
      userId: stateData.userId,
      organizationId: stateData.organizationId,
      provider,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token,
      tokenType: tokenData.token_type || 'Bearer',
      expiresAt,
      scopes: config.scopes,
      metadata: {
        tokenData: {
          ...tokenData,
          access_token: undefined, // Don't store in metadata
          refresh_token: undefined,
        }
      }
    });

    // Clean up state
    await cleanupOAuthState(state);

    return {
      success: true,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token,
      expiresAt,
      tokenType: tokenData.token_type,
      scopes: config.scopes,
      metadata: { tokenId: token.id }
    };

  } catch (error) {
    if (error instanceof AuthenticationError) {
      throw error;
    }
    
    console.error('OAuth token exchange failed:', error);
    throw new AuthenticationError(
      provider, 
      `Token exchange failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Refresh OAuth token
 */
export async function refreshOAuthToken(
  tokenId: string
): Promise<TokenRefreshResult> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get current token
    const { data: token, error } = await supabase
      .from('oauth_tokens')
      .select('*')
      .eq('id', tokenId)
      .single();

    if (error || !token) {
      return { success: false, error: 'Token not found' };
    }

    if (!token.refresh_token) {
      return { success: false, error: 'No refresh token available' };
    }

    const config = OAUTH_PROVIDERS[token.provider as IntegrationProvider];
    if (!config) {
      return { success: false, error: `Unsupported provider: ${token.provider}` };
    }

    // Refresh token
    const refreshResponse = await fetch(config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        client_id: config.clientId,
        client_secret: config.clientSecret,
        refresh_token: token.refresh_token,
      }),
    });

    if (!refreshResponse.ok) {
      const errorData = await refreshResponse.json().catch(() => ({}));
      
      // If refresh token is invalid, mark token as expired
      if (refreshResponse.status === 400 || refreshResponse.status === 401) {
        await supabase
          .from('oauth_tokens')
          .update({ 
            status: 'expired',
            updated_at: new Date().toISOString()
          })
          .eq('id', tokenId);
      }
      
      return { 
        success: false, 
        error: `Token refresh failed: ${errorData.error_description || refreshResponse.statusText}` 
      };
    }

    const refreshData = await refreshResponse.json();

    // Calculate new expiration time
    const expiresAt = refreshData.expires_in 
      ? new Date(Date.now() + refreshData.expires_in * 1000).toISOString()
      : undefined;

    // Update token in database
    const updateData: any = {
      access_token: refreshData.access_token,
      token_type: refreshData.token_type || token.token_type,
      expires_at: expiresAt,
      updated_at: new Date().toISOString()
    };

    // Update refresh token if provided
    if (refreshData.refresh_token) {
      updateData.refresh_token = refreshData.refresh_token;
    }

    await supabase
      .from('oauth_tokens')
      .update(updateData)
      .eq('id', tokenId);

    return {
      success: true,
      newAccessToken: refreshData.access_token,
      newRefreshToken: refreshData.refresh_token,
      expiresAt
    };

  } catch (error) {
    console.error('Token refresh failed:', error);
    return { 
      success: false, 
      error: `Token refresh failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}

/**
 * Get valid access token (refresh if needed)
 */
export async function getValidAccessToken(
  userId: string,
  organizationId: string,
  provider: IntegrationProvider
): Promise<{ token: string; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get current token
    const { data: tokenRecord, error } = await supabase
      .from('oauth_tokens')
      .select('*')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .eq('provider', provider)
      .eq('status', 'active')
      .single();

    if (error || !tokenRecord) {
      return { token: '', error: 'No valid token found' };
    }

    // Check if token is expired
    const now = new Date();
    const expiresAt = tokenRecord.expires_at ? new Date(tokenRecord.expires_at) : null;
    
    if (expiresAt && expiresAt <= now) {
      // Token is expired, try to refresh
      const refreshResult = await refreshOAuthToken(tokenRecord.id);
      
      if (!refreshResult.success) {
        return { token: '', error: refreshResult.error };
      }
      
      return { token: refreshResult.newAccessToken! };
    }

    return { token: tokenRecord.access_token };

  } catch (error) {
    console.error('Failed to get valid access token:', error);
    return { 
      token: '', 
      error: `Failed to get access token: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}

/**
 * Revoke OAuth token
 */
export async function revokeOAuthToken(
  userId: string,
  organizationId: string,
  provider: IntegrationProvider
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get token
    const { data: token, error } = await supabase
      .from('oauth_tokens')
      .select('*')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .eq('provider', provider)
      .single();

    if (error || !token) {
      return { success: false, error: 'Token not found' };
    }

    // Try to revoke token with provider (if supported)
    const config = OAUTH_PROVIDERS[provider];
    if (config && token.access_token) {
      try {
        // Most providers have a revoke endpoint
        const revokeUrl = getRevokeUrl(provider);
        if (revokeUrl) {
          await fetch(revokeUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
              token: token.access_token,
              client_id: config.clientId,
              client_secret: config.clientSecret,
            }),
          });
        }
      } catch (error) {
        console.warn('Failed to revoke token with provider:', error);
        // Continue with local revocation even if provider revocation fails
      }
    }

    // Mark token as revoked in database
    await supabase
      .from('oauth_tokens')
      .update({ 
        status: 'revoked',
        updated_at: new Date().toISOString()
      })
      .eq('id', token.id);

    return { success: true };

  } catch (error) {
    console.error('Failed to revoke OAuth token:', error);
    return { 
      success: false, 
      error: `Failed to revoke token: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}

/**
 * List OAuth tokens for user/organization
 */
export async function listOAuthTokens(
  userId: string,
  organizationId: string
): Promise<{ tokens: OAuthToken[]; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { data: tokens, error } = await supabase
      .from('oauth_tokens')
      .select('*')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });

    if (error) {
      return { tokens: [], error: error.message };
    }

    const formattedTokens: OAuthToken[] = tokens.map(token => ({
      id: token.id,
      userId: token.user_id,
      organizationId: token.organization_id,
      provider: token.provider,
      accessToken: '***', // Don't expose actual token
      refreshToken: token.refresh_token ? '***' : undefined,
      tokenType: token.token_type,
      expiresAt: token.expires_at,
      scopes: token.scopes,
      metadata: token.metadata,
      createdAt: token.created_at,
      updatedAt: token.updated_at,
    }));

    return { tokens: formattedTokens };

  } catch (error) {
    console.error('Failed to list OAuth tokens:', error);
    return { 
      tokens: [], 
      error: `Failed to list tokens: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}

// Helper functions

function generateSecureState(): string {
  return crypto.randomUUID();
}

async function storeOAuthState(
  state: string,
  provider: string,
  userId: string,
  organizationId: string
): Promise<void> {
  const supabase = await createSupabaseServerClient();
  
  await supabase
    .from('oauth_states')
    .insert({
      state,
      provider,
      user_id: userId,
      organization_id: organizationId,
      expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes
      created_at: new Date().toISOString()
    });
}

async function validateOAuthState(state: string): Promise<{
  userId: string;
  organizationId: string;
  provider: string;
} | null> {
  const supabase = await createSupabaseServerClient();
  
  const { data, error } = await supabase
    .from('oauth_states')
    .select('*')
    .eq('state', state)
    .gt('expires_at', new Date().toISOString())
    .single();

  if (error || !data) {
    return null;
  }

  return {
    userId: data.user_id,
    organizationId: data.organization_id,
    provider: data.provider
  };
}

async function cleanupOAuthState(state: string): Promise<void> {
  const supabase = await createSupabaseServerClient();
  
  await supabase
    .from('oauth_states')
    .delete()
    .eq('state', state);
}

async function storeOAuthToken(tokenData: {
  userId: string;
  organizationId: string;
  provider: string;
  accessToken: string;
  refreshToken?: string;
  tokenType: string;
  expiresAt?: string;
  scopes: string[];
  metadata: Record<string, any>;
}): Promise<OAuthToken> {
  const supabase = await createSupabaseServerClient();
  
  // First, mark any existing tokens for this provider as inactive
  await supabase
    .from('oauth_tokens')
    .update({ status: 'replaced' })
    .eq('user_id', tokenData.userId)
    .eq('organization_id', tokenData.organizationId)
    .eq('provider', tokenData.provider)
    .eq('status', 'active');

  // Insert new token
  const { data, error } = await supabase
    .from('oauth_tokens')
    .insert({
      user_id: tokenData.userId,
      organization_id: tokenData.organizationId,
      provider: tokenData.provider,
      access_token: tokenData.accessToken,
      refresh_token: tokenData.refreshToken,
      token_type: tokenData.tokenType,
      expires_at: tokenData.expiresAt,
      scopes: tokenData.scopes,
      metadata: tokenData.metadata,
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to store OAuth token: ${error.message}`);
  }

  return {
    id: data.id,
    userId: data.user_id,
    organizationId: data.organization_id,
    provider: data.provider,
    accessToken: data.access_token,
    refreshToken: data.refresh_token,
    tokenType: data.token_type,
    expiresAt: data.expires_at,
    scopes: data.scopes,
    metadata: data.metadata,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
}

function getRevokeUrl(provider: IntegrationProvider): string | null {
  const revokeUrls: Partial<Record<IntegrationProvider, string>> = {
    'google-drive': 'https://oauth2.googleapis.com/revoke',
    'slack': 'https://slack.com/api/auth.revoke',
    'hubspot': 'https://api.hubapi.com/oauth/v1/refresh-tokens',
    // Add other providers as needed
  };
  
  return revokeUrls[provider] || null;
}
