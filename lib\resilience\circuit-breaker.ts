import { devLog } from '@/lib/console-cleaner';
/**
 * Circuit Breaker Pattern Implementation
 * 
 * Provides fault tolerance for external service calls with:
 * - Automatic failure detection
 * - Circuit opening/closing logic
 * - Exponential backoff
 * - Health monitoring
 * - Fallback mechanisms
 */

export interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  expectedErrors: string[];
  volumeThreshold: number;
  halfOpenMaxCalls: number;
}

export interface CircuitBreakerStats {
  state: CircuitState;
  failureCount: number;
  successCount: number;
  totalCalls: number;
  lastFailureTime?: number;
  lastSuccessTime?: number;
  failureRate: number;
  averageResponseTime: number;
}

export enum CircuitState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN'
}

export class CircuitBreakerError extends Error {
  constructor(message: string, public readonly state: CircuitState) {
    super(message);
    this.name = 'CircuitBreakerError';
  }
}

export class CircuitBreaker {
  private config: CircuitBreakerConfig;
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private successCount: number = 0;
  private totalCalls: number = 0;
  private lastFailureTime?: number;
  private lastSuccessTime?: number;
  private halfOpenCalls: number = 0;
  private responseTimes: number[] = [];
  private stateChangeListeners: Array<(state: CircuitState) => void> = [];

  constructor(
    private name: string,
    config: Partial<CircuitBreakerConfig> = {}
  ) {
    this.config = {
      failureThreshold: 5,
      recoveryTimeout: 60000, // 1 minute
      monitoringPeriod: 300000, // 5 minutes
      expectedErrors: ['ECONNREFUSED', 'ETIMEDOUT', 'ENOTFOUND'],
      volumeThreshold: 10,
      halfOpenMaxCalls: 3,
      ...config
    };

    // Start monitoring
    this.startMonitoring();
  }

  /**
   * Execute function with circuit breaker protection
   */
  async execute<T>(
    fn: () => Promise<T>,
    fallback?: () => Promise<T>
  ): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.setState(CircuitState.HALF_OPEN);
      } else {
        if (fallback) {
          return await fallback();
        }
        throw new CircuitBreakerError(
          `Circuit breaker is OPEN for ${this.name}`,
          this.state
        );
      }
    }

    if (this.state === CircuitState.HALF_OPEN) {
      if (this.halfOpenCalls >= this.config.halfOpenMaxCalls) {
        if (fallback) {
          return await fallback();
        }
        throw new CircuitBreakerError(
          `Circuit breaker is HALF_OPEN and max calls exceeded for ${this.name}`,
          this.state
        );
      }
      this.halfOpenCalls++;
    }

    const startTime = Date.now();
    this.totalCalls++;

    try {
      const result = await fn();
      this.onSuccess(Date.now() - startTime);
      return result;
    } catch (error) {
      this.onFailure(error, Date.now() - startTime);
      
      if (fallback) {
        try {
          return await fallback();
        } catch (fallbackError) {
          throw error; // Throw original error if fallback fails
        }
      }
      
      throw error;
    }
  }

  /**
   * Get current circuit breaker statistics
   */
  getStats(): CircuitBreakerStats {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      totalCalls: this.totalCalls,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
      failureRate: this.calculateFailureRate(),
      averageResponseTime: this.calculateAverageResponseTime()
    };
  }

  /**
   * Manually open the circuit
   */
  open(): void {
    this.setState(CircuitState.OPEN);
    this.lastFailureTime = Date.now();
  }

  /**
   * Manually close the circuit
   */
  close(): void {
    this.setState(CircuitState.CLOSED);
    this.reset();
  }

  /**
   * Add state change listener
   */
  onStateChange(listener: (state: CircuitState) => void): void {
    this.stateChangeListeners.push(listener);
  }

  /**
   * Remove state change listener
   */
  removeStateChangeListener(listener: (state: CircuitState) => void): void {
    const index = this.stateChangeListeners.indexOf(listener);
    if (index > -1) {
      this.stateChangeListeners.splice(index, 1);
    }
  }

  // Private methods

  private onSuccess(responseTime: number): void {
    this.successCount++;
    this.lastSuccessTime = Date.now();
    this.recordResponseTime(responseTime);

    if (this.state === CircuitState.HALF_OPEN) {
      // If we're in half-open and got a success, close the circuit
      this.setState(CircuitState.CLOSED);
      this.reset();
    }
  }

  private onFailure(error: any, responseTime: number): void {
    this.recordResponseTime(responseTime);

    // Check if this is an expected error that should trigger circuit breaker
    if (this.isExpectedError(error)) {
      this.failureCount++;
      this.lastFailureTime = Date.now();

      if (this.state === CircuitState.HALF_OPEN) {
        // If we're in half-open and got a failure, open the circuit
        this.setState(CircuitState.OPEN);
      } else if (this.shouldOpenCircuit()) {
        this.setState(CircuitState.OPEN);
      }
    }
  }

  private isExpectedError(error: any): boolean {
    if (!error) return false;

    const errorCode = error.code || error.errno || '';
    const errorMessage = error.message || '';

    return this.config.expectedErrors.some(expectedError => 
      errorCode.includes(expectedError) || errorMessage.includes(expectedError)
    );
  }

  private shouldOpenCircuit(): boolean {
    // Need minimum volume of calls
    if (this.totalCalls < this.config.volumeThreshold) {
      return false;
    }

    // Check failure rate
    const failureRate = this.calculateFailureRate();
    return failureRate >= (this.config.failureThreshold / 100);
  }

  private shouldAttemptReset(): boolean {
    if (!this.lastFailureTime) return false;
    
    const timeSinceLastFailure = Date.now() - this.lastFailureTime;
    return timeSinceLastFailure >= this.config.recoveryTimeout;
  }

  private setState(newState: CircuitState): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;
  devLog.log(`Circuit breaker ${this.name} state changed: ${oldState} -> ${newState}`);
      
      // Reset half-open call counter when entering half-open state
      if (newState === CircuitState.HALF_OPEN) {
        this.halfOpenCalls = 0;
      }

      // Notify listeners
      this.stateChangeListeners.forEach(listener => {
        try {
          listener(newState);
        } catch (error) {
          console.error('Error in circuit breaker state change listener:', error);
        }
      });
    }
  }

  private reset(): void {
    this.failureCount = 0;
    this.successCount = 0;
    this.halfOpenCalls = 0;
    this.responseTimes = [];
  }

  private calculateFailureRate(): number {
    if (this.totalCalls === 0) return 0;
    return (this.failureCount / this.totalCalls) * 100;
  }

  private calculateAverageResponseTime(): number {
    if (this.responseTimes.length === 0) return 0;
    
    const sum = this.responseTimes.reduce((acc, time) => acc + time, 0);
    return sum / this.responseTimes.length;
  }

  private recordResponseTime(responseTime: number): void {
    this.responseTimes.push(responseTime);
    
    // Keep only recent response times (last 100)
    if (this.responseTimes.length > 100) {
      this.responseTimes = this.responseTimes.slice(-100);
    }
  }

  private startMonitoring(): void {
    setInterval(() => {
      this.cleanupOldData();
    }, this.config.monitoringPeriod);
  }

  private cleanupOldData(): void {
    const now = Date.now();
    const cutoff = now - this.config.monitoringPeriod;

    // Reset counters if data is too old
    if (this.lastFailureTime && this.lastFailureTime < cutoff) {
      this.failureCount = Math.max(0, this.failureCount - 1);
    }

    if (this.lastSuccessTime && this.lastSuccessTime < cutoff) {
      this.successCount = Math.max(0, this.successCount - 1);
    }

    // Reset total calls periodically to prevent overflow
    if (this.totalCalls > 10000) {
      this.totalCalls = Math.floor(this.totalCalls / 2);
      this.failureCount = Math.floor(this.failureCount / 2);
      this.successCount = Math.floor(this.successCount / 2);
    }
  }
}

/**
 * Circuit Breaker Registry for managing multiple circuit breakers
 */
export class CircuitBreakerRegistry {
  private breakers: Map<string, CircuitBreaker> = new Map();

  /**
   * Get or create circuit breaker
   */
  getCircuitBreaker(
    name: string,
    config?: Partial<CircuitBreakerConfig>
  ): CircuitBreaker {
    if (!this.breakers.has(name)) {
      const breaker = new CircuitBreaker(name, config);
      this.breakers.set(name, breaker);
    }
    
    return this.breakers.get(name)!;
  }

  /**
   * Execute function with named circuit breaker
   */
  async execute<T>(
    breakerName: string,
    fn: () => Promise<T>,
    fallback?: () => Promise<T>,
    config?: Partial<CircuitBreakerConfig>
  ): Promise<T> {
    const breaker = this.getCircuitBreaker(breakerName, config);
    return await breaker.execute(fn, fallback);
  }

  /**
   * Get all circuit breaker statistics
   */
  getAllStats(): Record<string, CircuitBreakerStats> {
    const stats: Record<string, CircuitBreakerStats> = {};
    
    for (const [name, breaker] of this.breakers) {
      stats[name] = breaker.getStats();
    }
    
    return stats;
  }

  /**
   * Get health status of all circuit breakers
   */
  getHealthStatus(): {
    healthy: boolean;
    openCircuits: string[];
    degradedCircuits: string[];
  } {
    const openCircuits: string[] = [];
    const degradedCircuits: string[] = [];

    for (const [name, breaker] of this.breakers) {
      const stats = breaker.getStats();
      
      if (stats.state === CircuitState.OPEN) {
        openCircuits.push(name);
      } else if (stats.state === CircuitState.HALF_OPEN || stats.failureRate > 10) {
        degradedCircuits.push(name);
      }
    }

    return {
      healthy: openCircuits.length === 0 && degradedCircuits.length === 0,
      openCircuits,
      degradedCircuits
    };
  }

  /**
   * Reset all circuit breakers
   */
  resetAll(): void {
    for (const breaker of this.breakers.values()) {
      breaker.close();
    }
  }
}

// Global circuit breaker registry
export const circuitBreakerRegistry = new CircuitBreakerRegistry();

// Convenience function for common use cases
export async function withCircuitBreaker<T>(
  name: string,
  fn: () => Promise<T>,
  fallback?: () => Promise<T>,
  config?: Partial<CircuitBreakerConfig>
): Promise<T> {
  return await circuitBreakerRegistry.execute(name, fn, fallback, config);
}

// Predefined circuit breaker configurations for common services
export const CIRCUIT_BREAKER_CONFIGS = {
  database: {
    failureThreshold: 10,
    recoveryTimeout: 30000,
    volumeThreshold: 5,
    expectedErrors: ['ECONNREFUSED', 'ETIMEDOUT', 'connection']
  },
  
  ai_service: {
    failureThreshold: 15,
    recoveryTimeout: 60000,
    volumeThreshold: 3,
    expectedErrors: ['ECONNREFUSED', 'ETIMEDOUT', '429', '503', '504']
  },
  
  external_api: {
    failureThreshold: 20,
    recoveryTimeout: 120000,
    volumeThreshold: 10,
    expectedErrors: ['ECONNREFUSED', 'ETIMEDOUT', '429', '500', '502', '503', '504']
  },
  
  email_service: {
    failureThreshold: 25,
    recoveryTimeout: 300000,
    volumeThreshold: 5,
    expectedErrors: ['ECONNREFUSED', 'ETIMEDOUT', '429', '503']
  }
};
