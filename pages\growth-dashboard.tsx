'use client';

/**
 * Growth Analytics Dashboard
 * 
 * Comprehensive growth metrics powered by PostHog + Stripe:
 * - MRR, conversion, churn, and refund summaries
 * - Aggregate funnel data from PostHog
 * - Cohort analysis and retention tracking
 * - Revenue attribution and forecasting
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  UserPlus, 
  UserMinus,
  Download,
  RefreshCw,
  Calendar,
  Target,
  Zap,
  BarChart3
} from 'lucide-react';

interface GrowthMetrics {
  mrr: {
    current: number;
    previous: number;
    growth: number;
    trend: 'up' | 'down' | 'stable';
  };
  arr: {
    current: number;
    projected: number;
  };
  customers: {
    total: number;
    new: number;
    churned: number;
    netGrowth: number;
  };
  conversion: {
    trialToCustomer: number;
    visitorToTrial: number;
    overall: number;
  };
  churn: {
    rate: number;
    revenue: number;
    customers: number;
  };
  ltv: {
    average: number;
    byPlan: Record<string, number>;
  };
  cac: {
    average: number;
    byChannel: Record<string, number>;
  };
}

interface RevenueData {
  date: string;
  mrr: number;
  newMrr: number;
  churnedMrr: number;
  expansionMrr: number;
  contractionMrr: number;
}

interface FunnelData {
  stage: string;
  users: number;
  conversionRate: number;
  dropOff: number;
}

interface CohortData {
  cohort: string;
  month0: number;
  month1: number;
  month2: number;
  month3: number;
  month6: number;
  month12: number;
}

export default function GrowthDashboard() {
  const [metrics, setMetrics] = useState<GrowthMetrics | null>(null);
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [funnelData, setFunnelData] = useState<FunnelData[]>([]);
  const [cohortData, setCohortData] = useState<CohortData[]>([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30d');
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    fetchGrowthData();
  }, [dateRange]);

  const fetchGrowthData = async () => {
    try {
      setLoading(true);
      
      const [metricsRes, revenueRes, funnelRes, cohortRes] = await Promise.all([
        fetch(`/api/growth/metrics?range=${dateRange}`),
        fetch(`/api/growth/revenue?range=${dateRange}`),
        fetch(`/api/growth/funnel?range=${dateRange}`),
        fetch(`/api/growth/cohorts?range=${dateRange}`)
      ]);

      if (metricsRes.ok) {
        const metricsData = await metricsRes.json();
        setMetrics(metricsData);
      }

      if (revenueRes.ok) {
        const revenueData = await revenueRes.json();
        setRevenueData(revenueData.data || []);
      }

      if (funnelRes.ok) {
        const funnelData = await funnelRes.json();
        setFunnelData(funnelData.data || []);
      }

      if (cohortRes.ok) {
        const cohortData = await cohortRes.json();
        setCohortData(cohortData.data || []);
      }

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch growth data:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportGrowthReport = async () => {
    try {
      const response = await fetch('/api/export/growth-report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ dateRange, format: 'pdf' })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `growth-report-${dateRange}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Failed to export growth report:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <BarChart3 className="w-4 h-4 text-gray-600" />;
    }
  };

  if (loading && !metrics) {
    return (
      <div className="space-y-6 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Growth Dashboard</h1>
          <p className="text-gray-600">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <select 
            value={dateRange} 
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          
          <Button variant="outline" onClick={fetchGrowthData} disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button onClick={exportGrowthReport}>
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Monthly Recurring Revenue</p>
                  <p className="text-2xl font-bold">{formatCurrency(metrics.mrr.current)}</p>
                  <div className="flex items-center gap-1 mt-1">
                    {getTrendIcon(metrics.mrr.trend)}
                    <span className={`text-sm ${
                      metrics.mrr.growth > 0 ? 'text-green-600' : 
                      metrics.mrr.growth < 0 ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {metrics.mrr.growth > 0 ? '+' : ''}{formatPercentage(metrics.mrr.growth)}
                    </span>
                  </div>
                </div>
                <DollarSign className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Customers</p>
                  <p className="text-2xl font-bold">{metrics.customers.total.toLocaleString()}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm text-green-600">+{metrics.customers.new}</span>
                    <span className="text-sm text-red-600">-{metrics.customers.churned}</span>
                  </div>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                  <p className="text-2xl font-bold">{formatPercentage(metrics.conversion.overall)}</p>
                  <p className="text-sm text-gray-600">
                    Trial: {formatPercentage(metrics.conversion.trialToCustomer)}
                  </p>
                </div>
                <Target className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Churn Rate</p>
                  <p className="text-2xl font-bold">{formatPercentage(metrics.churn.rate)}</p>
                  <p className="text-sm text-gray-600">
                    {formatCurrency(metrics.churn.revenue)} lost
                  </p>
                </div>
                <UserMinus className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="revenue" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="funnel">Funnel</TabsTrigger>
          <TabsTrigger value="cohorts">Cohorts</TabsTrigger>
          <TabsTrigger value="forecasting">Forecasting</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* MRR Trend */}
            <Card>
              <CardHeader>
                <CardTitle>MRR Trend</CardTitle>
                <CardDescription>Monthly recurring revenue breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis tickFormatter={(value) => formatCurrency(value)} />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Area 
                      type="monotone" 
                      dataKey="mrr" 
                      stackId="1"
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      fillOpacity={0.6}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="newMrr" 
                      stackId="2"
                      stroke="#82ca9d" 
                      fill="#82ca9d" 
                      fillOpacity={0.6}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Revenue Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Revenue Components</CardTitle>
                <CardDescription>MRR movement analysis</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={revenueData.slice(-12)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis tickFormatter={(value) => formatCurrency(value)} />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Bar dataKey="newMrr" fill="#82ca9d" name="New MRR" />
                    <Bar dataKey="expansionMrr" fill="#8884d8" name="Expansion" />
                    <Bar dataKey="churnedMrr" fill="#ff7c7c" name="Churned" />
                    <Bar dataKey="contractionMrr" fill="#ffc658" name="Contraction" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Additional Revenue Metrics */}
          {metrics && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Customer Lifetime Value</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-4">{formatCurrency(metrics.ltv.average)}</div>
                  <div className="space-y-2">
                    {Object.entries(metrics.ltv.byPlan).map(([plan, value]) => (
                      <div key={plan} className="flex justify-between">
                        <span className="text-sm text-gray-600 capitalize">{plan}</span>
                        <span className="text-sm font-medium">{formatCurrency(value)}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Customer Acquisition Cost</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-4">{formatCurrency(metrics.cac.average)}</div>
                  <div className="space-y-2">
                    {Object.entries(metrics.cac.byChannel).map(([channel, value]) => (
                      <div key={channel} className="flex justify-between">
                        <span className="text-sm text-gray-600 capitalize">{channel}</span>
                        <span className="text-sm font-medium">{formatCurrency(value)}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>LTV:CAC Ratio</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-4">
                    {(metrics.ltv.average / metrics.cac.average).toFixed(1)}:1
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Target Ratio</span>
                      <span className="text-sm font-medium">3:1</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Payback Period</span>
                      <span className="text-sm font-medium">
                        {Math.round(metrics.cac.average / (metrics.mrr.current / metrics.customers.total))} months
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="funnel" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Conversion Funnel</CardTitle>
              <CardDescription>User journey from visitor to customer</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {funnelData.map((stage, index) => (
                  <div key={stage.stage} className="relative">
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="font-semibold">{stage.stage}</h4>
                          <p className="text-sm text-gray-600">{stage.users.toLocaleString()} users</p>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatPercentage(stage.conversionRate)}</div>
                        {index > 0 && (
                          <div className="text-sm text-red-600">
                            -{stage.dropOff.toLocaleString()} dropped off
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {index < funnelData.length - 1 && (
                      <div className="flex justify-center my-2">
                        <div className="w-0.5 h-4 bg-gray-300"></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cohorts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Cohort Retention Analysis</CardTitle>
              <CardDescription>Customer retention by signup month</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Cohort</th>
                      <th className="text-center p-2">Month 0</th>
                      <th className="text-center p-2">Month 1</th>
                      <th className="text-center p-2">Month 2</th>
                      <th className="text-center p-2">Month 3</th>
                      <th className="text-center p-2">Month 6</th>
                      <th className="text-center p-2">Month 12</th>
                    </tr>
                  </thead>
                  <tbody>
                    {cohortData.map((cohort) => (
                      <tr key={cohort.cohort} className="border-b">
                        <td className="p-2 font-medium">{cohort.cohort}</td>
                        <td className="text-center p-2">
                          <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            100%
                          </div>
                        </td>
                        <td className="text-center p-2">
                          <div className={`px-2 py-1 rounded ${
                            cohort.month1 > 0.8 ? 'bg-green-100 text-green-800' :
                            cohort.month1 > 0.6 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {formatPercentage(cohort.month1)}
                          </div>
                        </td>
                        <td className="text-center p-2">
                          <div className={`px-2 py-1 rounded ${
                            cohort.month2 > 0.7 ? 'bg-green-100 text-green-800' :
                            cohort.month2 > 0.5 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {formatPercentage(cohort.month2)}
                          </div>
                        </td>
                        <td className="text-center p-2">
                          <div className={`px-2 py-1 rounded ${
                            cohort.month3 > 0.6 ? 'bg-green-100 text-green-800' :
                            cohort.month3 > 0.4 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {formatPercentage(cohort.month3)}
                          </div>
                        </td>
                        <td className="text-center p-2">
                          {cohort.month6 > 0 ? (
                            <div className={`px-2 py-1 rounded ${
                              cohort.month6 > 0.5 ? 'bg-green-100 text-green-800' :
                              cohort.month6 > 0.3 ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {formatPercentage(cohort.month6)}
                            </div>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </td>
                        <td className="text-center p-2">
                          {cohort.month12 > 0 ? (
                            <div className={`px-2 py-1 rounded ${
                              cohort.month12 > 0.4 ? 'bg-green-100 text-green-800' :
                              cohort.month12 > 0.2 ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {formatPercentage(cohort.month12)}
                            </div>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="forecasting" className="space-y-6">
          {metrics && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Forecast</CardTitle>
                  <CardDescription>Projected ARR based on current trends</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Current ARR</span>
                      <span className="text-lg font-bold">{formatCurrency(metrics.arr.current)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Projected ARR (12 months)</span>
                      <span className="text-lg font-bold text-green-600">{formatCurrency(metrics.arr.projected)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Growth Rate</span>
                      <span className="text-lg font-bold">
                        {formatPercentage((metrics.arr.projected - metrics.arr.current) / metrics.arr.current)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Key Assumptions</CardTitle>
                  <CardDescription>Forecast model parameters</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Monthly Churn Rate</span>
                      <span className="text-sm font-medium">{formatPercentage(metrics.churn.rate)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">New Customer Growth</span>
                      <span className="text-sm font-medium">
                        {formatPercentage(metrics.customers.netGrowth / metrics.customers.total)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Average Revenue per User</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(metrics.mrr.current / metrics.customers.total)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Expansion Revenue</span>
                      <span className="text-sm font-medium">15% annually</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
