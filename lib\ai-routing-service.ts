import { devLog } from '@/lib/console-cleaner';
/**
 * AI Routing Service
 * Intelligent AI model routing based on subscription tiers and usage patterns
 */

import { getUserSubscription, incrementUsage } from '@/lib/subscription-service';
import { 
  generatePremiumAISummary, 
  getModelForSubscriptionTier, 
  canUseModelForTier,
  getAvailableModelsForTier,
  AI_MODELS,
  AIResponse 
} from '@/lib/ai-models';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { SentryTracker } from '@/lib/sentry.client';

export interface AIRoutingRequest {
  userId: string;
  text: string;
  preferredModel?: string;
  context?: string;
  organizationId?: string;
}

export interface AIRoutingResponse {
  success: boolean;
  response?: AIResponse;
  model_used: string;
  subscription_tier: string;
  usage_info: {
    summaries_used: number;
    summaries_limit: number;
    can_create_more: boolean;
  };
  cost_info?: {
    tokens_used: number;
    estimated_cost: number;
  };
  upgrade_suggestion?: {
    message: string;
    recommended_tier: string;
    benefits: string[];
  };
  error?: string;
}

/**
 * Route AI request based on subscription tier and usage
 */
export async function routeAIRequest(request: AIRoutingRequest): Promise<AIRoutingResponse> {
  try {
  devLog.log(`🤖 Routing AI request for user ${request.userId}`);

    // Get user subscription
    const subscription = await getUserSubscription(request.userId);
    if (!subscription) {
      return {
        success: false,
        model_used: 'none',
        subscription_tier: 'UNKNOWN',
        usage_info: {
          summaries_used: 0,
          summaries_limit: 0,
          can_create_more: false,
        },
        error: 'Unable to determine subscription status',
      };
    }

    const userTier = subscription.subscription_tier;

    // Check usage limits
    const canCreateSummary = subscription.monthly_summary_limit === -1 || 
      subscription.monthly_summaries_used < subscription.monthly_summary_limit;

    if (!canCreateSummary) {
      return {
        success: false,
        model_used: 'none',
        subscription_tier: userTier,
        usage_info: {
          summaries_used: subscription.monthly_summaries_used,
          summaries_limit: subscription.monthly_summary_limit,
          can_create_more: false,
        },
        error: 'Monthly summary limit reached',
        upgrade_suggestion: getUpgradeSuggestion(userTier, 'usage_limit'),
      };
    }

    // Determine the best AI model for the user
    let selectedModel: string;
    let upgradeNeeded = false;

    if (request.preferredModel && canUseModelForTier(userTier, request.preferredModel)) {
      selectedModel = request.preferredModel;
    } else {
      selectedModel = getModelForSubscriptionTier(userTier, request.preferredModel);
      
      if (request.preferredModel && !canUseModelForTier(userTier, request.preferredModel)) {
        upgradeNeeded = true;
      }
    }
  devLog.log(`🎯 Selected AI model: ${selectedModel} for tier: ${userTier}`);

    // Generate AI response
    const aiResponse = await generatePremiumAISummary(
      request.text,
      userTier,
      selectedModel,
      request.context
    );

    // Increment usage count
    await incrementUsage(request.userId, 'summaries');

    // Log usage analytics
    await logUsageAnalytics(request.userId, {
      model_used: selectedModel,
      subscription_tier: userTier,
      tokens_used: aiResponse.tokens?.input + aiResponse.tokens?.output || 0,
      cost: aiResponse.cost || 0,
      organization_id: request.organizationId,
    });

    const response: AIRoutingResponse = {
      success: true,
      response: aiResponse,
      model_used: selectedModel,
      subscription_tier: userTier,
      usage_info: {
        summaries_used: subscription.monthly_summaries_used + 1,
        summaries_limit: subscription.monthly_summary_limit,
        can_create_more: subscription.monthly_summary_limit === -1 || 
          subscription.monthly_summaries_used + 1 < subscription.monthly_summary_limit,
      },
      cost_info: aiResponse.cost ? {
        tokens_used: (aiResponse.tokens?.input || 0) + (aiResponse.tokens?.output || 0),
        estimated_cost: aiResponse.cost,
      } : undefined,
    };

    // Add upgrade suggestion if needed
    if (upgradeNeeded) {
      response.upgrade_suggestion = getUpgradeSuggestion(userTier, 'model_access', request.preferredModel);
    }

    return response;

  } catch (error) {
    console.error('AI routing error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      model_used: 'none',
      subscription_tier: 'UNKNOWN',
      usage_info: {
        summaries_used: 0,
        summaries_limit: 0,
        can_create_more: false,
      },
      error: error instanceof Error ? error.message : 'AI routing failed',
    };
  }
}

/**
 * Get available AI models for a user
 */
export async function getAvailableAIModels(userId: string): Promise<{
  success: boolean;
  models?: typeof AI_MODELS;
  subscription_tier?: string;
  error?: string;
}> {
  try {
    const subscription = await getUserSubscription(userId);
    if (!subscription) {
      return {
        success: false,
        error: 'Unable to determine subscription status',
      };
    }

    const availableModels = getAvailableModelsForTier(subscription.subscription_tier);
    const modelsMap = availableModels.reduce((acc, model) => {
      acc[model.id] = model;
      return acc;
    }, {} as typeof AI_MODELS);

    return {
      success: true,
      models: modelsMap,
      subscription_tier: subscription.subscription_tier,
    };

  } catch (error) {
    console.error('Error getting available AI models:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get available models',
    };
  }
}

/**
 * Get upgrade suggestion based on context
 */
function getUpgradeSuggestion(
  currentTier: string,
  reason: 'usage_limit' | 'model_access',
  requestedModel?: string
): {
  message: string;
  recommended_tier: string;
  benefits: string[];
} {
  if (reason === 'usage_limit') {
    if (currentTier === 'FREE') {
      return {
        message: 'Upgrade to Pro for 500 monthly summaries and premium AI models',
        recommended_tier: 'PRO',
        benefits: [
          '500 monthly summaries (vs 10)',
          'Access to GPT-4o and GPT-4o-mini',
          'Smart tagging and auto-posting',
          'CRM integrations',
          'Advanced analytics',
        ],
      };
    } else {
      return {
        message: 'Upgrade to Enterprise for unlimited summaries and team features',
        recommended_tier: 'ENTERPRISE',
        benefits: [
          'Unlimited monthly summaries',
          'Team management and collaboration',
          'SSO integration',
          'Audit logs and compliance',
          'Priority support',
        ],
      };
    }
  }

  if (reason === 'model_access' && requestedModel) {
    const model = AI_MODELS[requestedModel];
    if (model) {
      return {
        message: `Upgrade to ${model.requiredPlan} to use ${model.name}`,
        recommended_tier: model.requiredPlan,
        benefits: model.features,
      };
    }
  }

  return {
    message: 'Upgrade your plan for more features',
    recommended_tier: currentTier === 'FREE' ? 'PRO' : 'ENTERPRISE',
    benefits: ['More features and capabilities'],
  };
}

/**
 * Log usage analytics
 */
async function logUsageAnalytics(
  userId: string,
  analytics: {
    model_used: string;
    subscription_tier: string;
    tokens_used: number;
    cost: number;
    organization_id?: string;
  }
): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();

    await supabase
      .from('usage_analytics')
      .insert({
        user_id: userId,
        organization_id: analytics.organization_id || `org-${userId}`,
        event_type: 'ai_summary_generated',
        event_data: {
          model_used: analytics.model_used,
          subscription_tier: analytics.subscription_tier,
          tokens_used: analytics.tokens_used,
          cost: analytics.cost,
        },
        metadata: {
          timestamp: new Date().toISOString(),
          source: 'ai_routing_service',
        },
      });

  } catch (error) {
    console.error('Error logging usage analytics:', error);
    // Don't throw error as this is non-critical
  }
}

/**
 * Get usage statistics for a user
 */
export async function getUserUsageStats(
  userId: string,
  organizationId?: string,
  timeframe: 'day' | 'week' | 'month' = 'month'
): Promise<{
  success: boolean;
  stats?: {
    total_summaries: number;
    models_used: Record<string, number>;
    total_tokens: number;
    total_cost: number;
    subscription_tier: string;
  };
  error?: string;
}> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Calculate date range
    const now = new Date();
    let startDate = new Date();
    
    switch (timeframe) {
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
    }

    const { data: analytics } = await supabase
      .from('usage_analytics')
      .select('*')
      .eq('user_id', userId)
      .eq('organization_id', organizationId || `org-${userId}`)
      .eq('event_type', 'ai_summary_generated')
      .gte('created_at', startDate.toISOString());

    if (!analytics) {
      return {
        success: true,
        stats: {
          total_summaries: 0,
          models_used: {},
          total_tokens: 0,
          total_cost: 0,
          subscription_tier: 'FREE',
        },
      };
    }

    // Aggregate statistics
    const stats = analytics.reduce((acc, record) => {
      const data = record.event_data;
      acc.total_summaries += 1;
      acc.models_used[data.model_used] = (acc.models_used[data.model_used] || 0) + 1;
      acc.total_tokens += data.tokens_used || 0;
      acc.total_cost += data.cost || 0;
      acc.subscription_tier = data.subscription_tier;
      return acc;
    }, {
      total_summaries: 0,
      models_used: {} as Record<string, number>,
      total_tokens: 0,
      total_cost: 0,
      subscription_tier: 'FREE',
    });

    return {
      success: true,
      stats,
    };

  } catch (error) {
    console.error('Error getting usage stats:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get usage stats',
    };
  }
}
