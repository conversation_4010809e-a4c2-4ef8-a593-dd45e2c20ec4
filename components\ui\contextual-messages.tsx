'use client';

import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  WifiOff, 
  Clock, 
  AlertTriangle, 
  RefreshCw, 
  Shield, 
  Database,
  Zap,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ContextualMessageProps {
  type: 'error' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'destructive';
  };
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

/**
 * Contextual Message Component
 * 
 * Displays contextual messages with appropriate icons, colors, and actions
 */
export function ContextualMessage({
  type,
  title,
  message,
  action,
  dismissible = false,
  onDismiss,
  className
}: ContextualMessageProps) {
  const getIcon = () => {
    switch (type) {
      case 'error':
        return <XCircle className="h-4 w-4" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />;
      case 'info':
        return <Info className="h-4 w-4" />;
      case 'success':
        return <CheckCircle className="h-4 w-4" />;
    }
  };

  const getVariant = () => {
    switch (type) {
      case 'error':
        return 'destructive';
      case 'warning':
        return 'default';
      case 'info':
        return 'default';
      case 'success':
        return 'default';
    }
  };

  return (
    <Alert variant={getVariant()} className={cn("relative", className)}>
      {getIcon()}
      <AlertTitle className="flex items-center justify-between">
        {title}
        {dismissible && onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="h-auto p-1 hover:bg-transparent"
          >
            <XCircle className="h-3 w-3" />
          </Button>
        )}
      </AlertTitle>
      <AlertDescription className="mt-2">
        {message}
        {action && (
          <div className="mt-3">
            <Button
              variant={action.variant || 'default'}
              size="sm"
              onClick={action.onClick}
            >
              {action.label}
            </Button>
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
}

/**
 * Network Error Message
 */
export function NetworkErrorMessage({ onRetry }: { onRetry: () => void }) {
  return (
    <ContextualMessage
      type="error"
      title="Connection Problem"
      message="Unable to connect to our servers. Please check your internet connection and try again."
      action={{
        label: "Try Again",
        onClick: onRetry,
        variant: "default"
      }}
    />
  );
}

/**
 * Timeout Error Message
 */
export function TimeoutErrorMessage({ onRetry }: { onRetry: () => void }) {
  return (
    <ContextualMessage
      type="warning"
      title="Request Timed Out"
      message="The request is taking longer than expected. This might be due to high server load or a slow connection."
      action={{
        label: "Retry",
        onClick: onRetry,
        variant: "outline"
      }}
    />
  );
}

/**
 * Authentication Error Message
 */
export function AuthErrorMessage({ onSignIn }: { onSignIn: () => void }) {
  return (
    <ContextualMessage
      type="error"
      title="Authentication Required"
      message="Your session has expired. Please sign in again to continue."
      action={{
        label: "Sign In",
        onClick: onSignIn,
        variant: "default"
      }}
    />
  );
}

/**
 * Database Error Message
 */
export function DatabaseErrorMessage({ onRetry }: { onRetry: () => void }) {
  return (
    <ContextualMessage
      type="error"
      title="Database Unavailable"
      message="We're experiencing database connectivity issues. Our team has been notified and is working on a fix."
      action={{
        label: "Refresh",
        onClick: onRetry,
        variant: "outline"
      }}
    />
  );
}

/**
 * Maintenance Mode Message
 */
export function MaintenanceMessage() {
  return (
    <ContextualMessage
      type="info"
      title="Scheduled Maintenance"
      message="We're performing scheduled maintenance to improve your experience. Service will be restored shortly."
    />
  );
}

/**
 * Success Message
 */
export function SuccessMessage({ 
  title, 
  message, 
  onDismiss 
}: { 
  title: string; 
  message: string; 
  onDismiss?: () => void;
}) {
  return (
    <ContextualMessage
      type="success"
      title={title}
      message={message}
      dismissible={!!onDismiss}
      onDismiss={onDismiss}
    />
  );
}

/**
 * Smart Error Message - Automatically determines the appropriate message based on error
 */
export function SmartErrorMessage({ 
  error, 
  onRetry, 
  onSignIn 
}: { 
  error: string | Error;
  onRetry: () => void;
  onSignIn?: () => void;
}) {
  const errorMessage = typeof error === 'string' ? error : error.message;
  const errorLower = errorMessage.toLowerCase();

  // Network errors
  if (errorLower.includes('network') || errorLower.includes('fetch') || errorLower.includes('connection')) {
    return <NetworkErrorMessage onRetry={onRetry} />;
  }

  // Timeout errors
  if (errorLower.includes('timeout') || errorLower.includes('aborted') || errorLower.includes('interrupted')) {
    return <TimeoutErrorMessage onRetry={onRetry} />;
  }

  // Authentication errors
  if (errorLower.includes('unauthorized') || errorLower.includes('session') || errorLower.includes('auth')) {
    return <AuthErrorMessage onSignIn={onSignIn || (() => window.location.href = '/login')} />;
  }

  // Database errors
  if (errorLower.includes('database') || errorLower.includes('supabase') || errorLower.includes('sql')) {
    return <DatabaseErrorMessage onRetry={onRetry} />;
  }

  // Generic error
  return (
    <ContextualMessage
      type="error"
      title="Something went wrong"
      message={errorMessage}
      action={{
        label: "Try Again",
        onClick: onRetry,
        variant: "default"
      }}
    />
  );
}

/**
 * Error Message Card - Full-width card for major errors
 */
export function ErrorMessageCard({ 
  error, 
  onRetry, 
  onSignIn,
  className 
}: { 
  error: string | Error;
  onRetry: () => void;
  onSignIn?: () => void;
  className?: string;
}) {
  const errorMessage = typeof error === 'string' ? error : error.message;
  const errorLower = errorMessage.toLowerCase();

  const getErrorDetails = () => {
    if (errorLower.includes('network') || errorLower.includes('fetch')) {
      return {
        icon: <WifiOff className="h-8 w-8 text-orange-500" />,
        title: "Connection Problem",
        description: "Unable to connect to our servers. Please check your internet connection.",
        color: "border-orange-200 bg-orange-50"
      };
    }

    if (errorLower.includes('timeout') || errorLower.includes('aborted')) {
      return {
        icon: <Clock className="h-8 w-8 text-yellow-500" />,
        title: "Request Timed Out",
        description: "The request is taking longer than expected.",
        color: "border-yellow-200 bg-yellow-50"
      };
    }

    if (errorLower.includes('unauthorized') || errorLower.includes('session')) {
      return {
        icon: <Shield className="h-8 w-8 text-red-500" />,
        title: "Authentication Required",
        description: "Your session has expired. Please sign in again.",
        color: "border-red-200 bg-red-50"
      };
    }

    if (errorLower.includes('database') || errorLower.includes('supabase')) {
      return {
        icon: <Database className="h-8 w-8 text-purple-500" />,
        title: "Database Unavailable",
        description: "We're experiencing database connectivity issues.",
        color: "border-purple-200 bg-purple-50"
      };
    }

    return {
      icon: <AlertTriangle className="h-8 w-8 text-red-500" />,
      title: "Something went wrong",
      description: errorMessage,
      color: "border-red-200 bg-red-50"
    };
  };

  const details = getErrorDetails();

  return (
    <Card className={cn("w-full max-w-md mx-auto", details.color, className)}>
      <CardHeader className="text-center pb-4">
        <div className="mx-auto mb-4 p-3 rounded-full bg-white/50">
          {details.icon}
        </div>
        <CardTitle className="text-lg">{details.title}</CardTitle>
        <CardDescription>{details.description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <Button onClick={onRetry} className="w-full">
          <RefreshCw className="mr-2 h-4 w-4" />
          Try Again
        </Button>
        
        {onSignIn && errorLower.includes('auth') && (
          <Button onClick={onSignIn} variant="outline" className="w-full">
            <Shield className="mr-2 h-4 w-4" />
            Sign In
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
