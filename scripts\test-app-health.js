/**
 * Test script to check application health and identify any runtime errors
 */

const http = require('http');

async function testEndpoint(url, description) {
  return new Promise((resolve) => {
    console.log(`🔍 Testing: ${description}`);
    console.log(`📍 URL: ${url}`);
    
    const request = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const success = res.statusCode === 200;
        console.log(`${success ? '✅' : '❌'} Status: ${res.statusCode}`);
        
        if (!success) {
          console.log(`📄 Response body (first 500 chars):`);
          console.log(data.substring(0, 500));
        } else {
          console.log(`📊 Response size: ${data.length} bytes`);
          
          // Check for error indicators in the response
          if (data.includes('Internal Server Error')) {
            console.log('❌ Found "Internal Server Error" in response');
          } else if (data.includes('Error:')) {
            console.log('⚠️  Found "Error:" in response');
          } else {
            console.log('✅ No error indicators found');
          }
        }
        
        console.log('─'.repeat(50));
        resolve({ success, statusCode: res.statusCode, data });
      });
    });
    
    request.on('error', (err) => {
      console.log(`❌ Request failed: ${err.message}`);
      console.log('─'.repeat(50));
      resolve({ success: false, error: err.message });
    });
    
    request.setTimeout(10000, () => {
      console.log('❌ Request timeout');
      request.destroy();
      resolve({ success: false, error: 'timeout' });
    });
  });
}

async function runHealthCheck() {
  console.log('🏥 Application Health Check');
  console.log('═'.repeat(50));
  
  const tests = [
    {
      url: 'http://localhost:3001',
      description: 'Home page'
    },
    {
      url: 'http://localhost:3001/api/health',
      description: 'Health API endpoint'
    },
    {
      url: 'http://localhost:3001/login',
      description: 'Login page'
    }
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await testEndpoint(test.url, test.description);
    results.push({ ...test, ...result });
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('📋 Summary:');
  console.log('═'.repeat(50));
  
  let allPassed = true;
  for (const result of results) {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.description} (${result.statusCode || 'ERROR'})`);
    if (!result.success) allPassed = false;
  }
  
  console.log('═'.repeat(50));
  console.log(`🎯 Overall Status: ${allPassed ? '✅ HEALTHY' : '❌ ISSUES DETECTED'}`);
  
  if (!allPassed) {
    console.log('\n💡 Troubleshooting Tips:');
    console.log('1. Check the Next.js server logs for error details');
    console.log('2. Verify environment variables are properly set');
    console.log('3. Check browser developer console for client-side errors');
    console.log('4. Ensure Supabase connection is working');
  }
}

// Run the health check
runHealthCheck().catch(console.error);
