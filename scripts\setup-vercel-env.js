#!/usr/bin/env node

/**
 * Vercel Environment Variables Setup Script
 * Automatically sets up all required environment variables in Vercel
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function log(message, type = 'info') {
  const prefix = {
    info: 'ℹ️',
    success: '✅',
    error: '❌',
    warning: '⚠️'
  }[type];
  console.log(`${prefix} ${message}`);
}

function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {};
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};

  content.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#') && line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=');
      env[key.trim()] = value.trim();
    }
  });

  return env;
}

async function setupVercelEnv() {
  log('🔧 Setting up Vercel environment variables...');

  // Read environment variables from .env.local
  const envVars = parseEnvFile('.env.local');
  
  if (Object.keys(envVars).length === 0) {
    log('No environment variables found in .env.local', 'warning');
    return;
  }

  log(`Found ${Object.keys(envVars).length} environment variables`);

  // Critical environment variables that must be set
  const criticalVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
    'CLERK_SECRET_KEY',
    'OPENROUTER_API_KEY',
    'NEXT_PUBLIC_SITE_URL',
    'NEXT_PUBLIC_APP_URL'
  ];

  // Set each environment variable in Vercel
  for (const [key, value] of Object.entries(envVars)) {
    if (value && value !== 'your_value_here' && value !== 'placeholder') {
      try {
        const isCritical = criticalVars.includes(key);
        const isSecret = key.includes('SECRET') || key.includes('PRIVATE') || 
                        key.includes('KEY') && !key.startsWith('NEXT_PUBLIC_');

        const command = `vercel env add ${key} production`;
        
        log(`Setting ${key}${isCritical ? ' (critical)' : ''}...`);
        
        // Note: This would require manual input for each variable
        // In practice, you'd use the Vercel dashboard or API
        console.log(`Run: ${command}`);
        console.log(`Value: ${isSecret ? '[HIDDEN]' : value}`);
        console.log('---');
        
      } catch (error) {
        log(`Failed to set ${key}: ${error.message}`, 'error');
      }
    }
  }

  log('Environment variable setup commands generated', 'success');
  log('Please run these commands manually or use the Vercel dashboard', 'info');
}

// Additional Vercel-specific environment variables
const vercelSpecificVars = {
  'NODE_ENV': 'production',
  'NEXT_PUBLIC_MODE': 'production',
  'NEXT_PUBLIC_DEV_MODE': 'false',
  'NEXT_PUBLIC_ENVIRONMENT': 'production',
  'NEXT_TELEMETRY_DISABLED': '1',
  'SKIP_ENV_VALIDATION': 'true',
  'VERCEL': '1',
  'NODE_OPTIONS': '--max_old_space_size=8192',
  'NEXT_PUBLIC_VERCEL_ENV': 'production'
};

log('Additional Vercel-specific variables to set:');
for (const [key, value] of Object.entries(vercelSpecificVars)) {
  console.log(`vercel env add ${key} production`);
  console.log(`Value: ${value}`);
  console.log('---');
}

setupVercelEnv().catch(error => {
  log(`Setup failed: ${error.message}`, 'error');
  process.exit(1);
});
