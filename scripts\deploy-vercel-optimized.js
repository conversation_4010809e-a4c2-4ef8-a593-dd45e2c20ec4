#!/usr/bin/env node

/**
 * Optimized Vercel Deployment Script
 * Handles memory issues and build failures
 */

const { execSync } = require('child_process');
const fs = require('fs');

function logInfo(message) {
  console.log(`ℹ️  ${message}`);
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logError(message) {
  console.log(`❌ ${message}`);
}

function logWarning(message) {
  console.log(`⚠️  ${message}`);
}

async function deployToVercel() {
  console.log('🚀 Starting optimized Vercel deployment...\n');

  // Step 1: Pre-deployment checks
  logInfo('Checking deployment prerequisites...');
  
  try {
    // Check Vercel CLI
    execSync('vercel --version', { stdio: 'pipe' });
    logSuccess('Vercel CLI available');
  } catch (error) {
    logError('Vercel CLI not found. Installing...');
    try {
      execSync('npm install -g vercel', { stdio: 'inherit' });
      logSuccess('Vercel CLI installed');
    } catch (installError) {
      logError('Failed to install Vercel CLI');
      process.exit(1);
    }
  }

  // Step 2: Environment setup
  logInfo('Setting up environment...');
  
  // Copy production env if exists
  if (fs.existsSync('.env.production')) {
    logSuccess('Production environment file found');
  } else {
    logWarning('No .env.production file found');
  }

  // Step 3: Clean build
  logInfo('Cleaning previous builds...');
  try {
    if (fs.existsSync('.next')) {
      execSync('rm -rf .next', { stdio: 'pipe' });
    }
    if (fs.existsSync('.vercel')) {
      execSync('rm -rf .vercel', { stdio: 'pipe' });
    }
    logSuccess('Build artifacts cleaned');
  } catch (error) {
    logWarning('Could not clean all artifacts');
  }

  // Step 4: Deploy with optimizations
  logInfo('Deploying to Vercel...');
  
  const deployCommands = [
    // Try standard deployment first
    'vercel --prod --yes',
    // Fallback with force flag
    'vercel --prod --yes --force',
    // Fallback with different config
    'vercel --prod --yes --local-config vercel-fallback.json'
  ];

  for (let i = 0; i < deployCommands.length; i++) {
    const command = deployCommands[i];
    logInfo(`Attempt ${i + 1}: ${command}`);
    
    try {
      const output = execSync(command, { 
        stdio: 'inherit',
        env: {
          ...process.env,
          NODE_OPTIONS: '--max_old_space_size=4096',
          NEXT_TELEMETRY_DISABLED: '1'
        }
      });
      
      logSuccess('Deployment successful!');
      return true;
      
    } catch (error) {
      logError(`Attempt ${i + 1} failed`);
      
      if (i === deployCommands.length - 1) {
        logError('All deployment attempts failed');
        console.log('\n📋 Troubleshooting steps:');
        console.log('1. Check environment variables in Vercel dashboard');
        console.log('2. Verify all required secrets are set');
        console.log('3. Try deploying via GitHub integration');
        console.log('4. Check Vercel function logs for specific errors');
        return false;
      }
    }
  }
}

// Run deployment
deployToVercel().then(success => {
  if (success) {
    console.log('\n🎉 Deployment completed successfully!');
    console.log('🌐 Your app should be live on Vercel');
  } else {
    console.log('\n💥 Deployment failed');
    process.exit(1);
  }
}).catch(error => {
  console.error('Deployment script error:', error);
  process.exit(1);
});
