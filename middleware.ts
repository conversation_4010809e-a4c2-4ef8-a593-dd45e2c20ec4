import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Use dynamic imports for non-critical middleware dependencies to prevent chunk loading issues
let securityHeaders: any;
let rateLimiter: any;
let authProtection: any;

// Lazy load middleware dependencies with enhanced error handling and retries
async function loadMiddlewareDependencies(retryCount = 0) {
  try {
    if (!securityHeaders) {
      const securityModule = await import('./lib/security-headers');
      securityHeaders = {
        applySecurityHeaders: securityModule.applySecurityHeaders,
        validateRequestOrigin: securityModule.validateRequestOrigin,
        isBotRequest: securityModule.isBotRequest,
        SECURITY_MIDDLEWARE_CONFIG: securityModule.SECURITY_MIDDLEWARE_CONFIG,
        generateNonce: securityModule.generateNonce,
      };
    }

    if (!rateLimiter) {
      const rateLimiterModule = await import('./lib/rate-limiter');
      rateLimiter = { checkRateLimit: rateLimiterModule.checkRateLimit };
    }

    if (!authProtection) {
      const authModule = await import('./lib/auth-protection');
      authProtection = { logSecurityEvent: authModule.logSecurityEvent };
    }
  } catch (error) {
    console.warn('Failed to load middleware dependencies:', error);
    // Provide fallback implementations
    securityHeaders = {
      applySecurityHeaders: () => ({}),
      validateRequestOrigin: () => true,
      isBotRequest: () => false,
      SECURITY_MIDDLEWARE_CONFIG: {},
    };
    rateLimiter = { checkRateLimit: () => Promise.resolve(true) };
    authProtection = { logSecurityEvent: () => {} };
  }
}

// Define protected routes that require authentication
const isProtectedRoute = createRouteMatcher([
  // Core app routes
  '/dashboard(.*)',
  '/upload(.*)',
  '/upload-enhanced(.*)',
  '/summaries(.*)',
  '/settings(.*)',
  '/onboarding(.*)',
  '/billing(.*)',

  // Admin routes
  '/admin(.*)',
  '/analytics(.*)',
  '/audit(.*)',

  // User management
  '/invite(.*)',
  '/team(.*)',
]);

// Define protected API routes that require authentication
const isProtectedApiRoute = createRouteMatcher([
  '/api/dashboard(.*)',
  '/api/upload(.*)',
  '/api/summaries(.*)',
  '/api/export(.*)',
  '/api/notifications(.*)',
  '/api/onboarding(.*)',
  '/api/analytics(.*)',
  '/api/audit(.*)',
  '/api/team(.*)',
  '/api/billing(.*)',
  '/api/subscription(.*)',
  '/api/payments(.*)',
  '/api/integrations(.*)',
  '/api/ai(.*)',
  '/api/usage(.*)',
  '/api/send-email(.*)',
  '/api/crm(.*)',
  '/api/notion(.*)',
  '/api/slack/auth(.*)',
  '/api/slack/channels(.*)',
  '/api/slack/summarize(.*)',
  '/api/slack/scheduler(.*)',
  '/api/slack/templates(.*)',
  '/api/sso(.*)',
]);

// Define public API routes that don't require authentication
const isPublicApiRoute = createRouteMatcher([
  '/api/health(.*)',
  '/api/healthcheck(.*)',
  '/api/sitemap(.*)',
  '/api/route',
  '/api/webhooks(.*)',
  '/api/slack/webhook(.*)',
  '/api/slack/oauth/callback(.*)',
  '/api/auth/callback(.*)',
  '/api/cron(.*)',
  '/api/monitoring/health(.*)',
]);

export default clerkMiddleware(async (auth, req) => {
  try {
    // Load middleware dependencies
    await loadMiddlewareDependencies();

    const { pathname } = req.nextUrl;
    const { userId, sessionId } = auth();

    // Apply security measures first
    const securityResult = await applySecurityMeasures(req, userId);
    if (securityResult) {
      return securityResult;
    }

  // Handle static asset fallbacks
  if (pathname.startsWith('/icons/') || pathname === '/favicon.ico' || pathname.startsWith('/apple-touch-icon')) {
    try {
      // Favicon fallback
      if (pathname === '/favicon.ico') {
        return NextResponse.rewrite(new URL('/favicon.svg', req.url));
      }

      // Apple touch icon fallback
      if (pathname.includes('apple-touch-icon')) {
        return NextResponse.rewrite(new URL('/apple-touch-icon.png', req.url));
      }

      // Generic icon fallback
      if (pathname.startsWith('/icons/') && pathname.endsWith('.png')) {
        return NextResponse.rewrite(new URL('/placeholder.svg', req.url));
      }
    } catch (error) {
      console.warn('Static asset fallback error:', error);
      return new NextResponse(
        '<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><rect width="32" height="32" fill="#ddd"/></svg>',
        {
          status: 200,
          headers: {
            'Content-Type': 'image/svg+xml',
            'Cache-Control': 'public, max-age=3600'
          }
        }
      );
    }
  }

  // Protect routes that require authentication
  if (isProtectedRoute(req)) {
    if (!userId) {
      return NextResponse.redirect(new URL('/sign-in', req.url));
    }
  }

  // Handle API routes with authentication
  if (pathname.startsWith('/api/')) {
    // Check if this is a protected API route
    if (isProtectedApiRoute(req)) {
      if (!userId) {
        return NextResponse.json(
          {
            error: 'Authentication required',
            message: 'This endpoint requires authentication. Please sign in.',
            code: 'AUTH_REQUIRED'
          },
          { status: 401 }
        );
      }

      // Add user context to headers for protected routes
      const response = NextResponse.next();
      response.headers.set('X-User-ID', userId);
      if (sessionId) {
        response.headers.set('X-Session-ID', sessionId);
      }

      // Add security headers for protected API routes
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

      return response;
    }

    // Handle public API routes
    if (isPublicApiRoute(req)) {
      const response = NextResponse.next();

      // Add basic security headers for public routes
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');

      // Add CORS headers for public API routes
      response.headers.set('Access-Control-Allow-Origin', process.env.NEXT_PUBLIC_SITE_URL || '*');
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      response.headers.set('Access-Control-Allow-Credentials', 'true');

      return response;
    }

    // For other API routes, require authentication by default
    if (!userId) {
      return NextResponse.json(
        {
          error: 'Authentication required',
          message: 'This API endpoint requires authentication.',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      );
    }
  }

    // Add security headers to all responses
    const response = NextResponse.next();
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

    return response;
  } catch (error) {
    // Log middleware errors
    console.error('🚨 Middleware error:', error);

    // Log to security audit
    await authProtection?.logSecurityEvent?.({
      type: 'MIDDLEWARE_ERROR',
      severity: 'HIGH',
      details: {
        error: error instanceof Error ? error.message : 'Unknown middleware error',
        pathname: req.nextUrl.pathname,
        method: req.method,
        userAgent: req.headers.get('user-agent'),
        ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      },
      userId: null,
      metadata: {
        timestamp: new Date().toISOString(),
        url: req.url
      }
    });

    // Return error response
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'An error occurred while processing your request.',
        code: 'MIDDLEWARE_ERROR'
      },
      { status: 500 }
    );
  }
});

/**
 * Apply comprehensive security measures
 */
async function applySecurityMeasures(req: NextRequest, userId?: string | null): Promise<NextResponse | null> {
  const { pathname } = req.nextUrl;
  const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
  const userAgent = req.headers.get('user-agent') || '';

  // Skip security checks for static assets and health checks
  if (pathname.startsWith('/_next/') || pathname.startsWith('/api/health') || pathname === '/favicon.ico') {
    return null;
  }

  // 1. Bot Detection and Protection (with fallback)
  const config = securityHeaders?.SECURITY_MIDDLEWARE_CONFIG || { botProtection: { enabled: false } };
  const isBotReq = securityHeaders?.isBotRequest ? securityHeaders.isBotRequest(userAgent) : false;

  if (config.botProtection?.enabled && isBotReq) {
    const isAllowedBot = config.botProtection.allowedBots?.some(
      (bot: string) => userAgent.toLowerCase().includes(bot)
    );

    if (config.botProtection.blockBots && !isAllowedBot) {
      authProtection?.logSecurityEvent?.({
        userId: userId || undefined,
        action: 'BOT_BLOCKED',
        resource: pathname,
        details: { userAgent, ip },
        ip,
        userAgent
      });

      return new NextResponse('Bot access denied', { status: 403 });
    }
  }

  // 2. CSRF Protection for state-changing requests
  const csrfConfig = securityHeaders?.SECURITY_MIDDLEWARE_CONFIG?.csrfProtection || { enabled: false };
  if (csrfConfig.enabled) {
    const method = req.method;
    const isStateChanging = !csrfConfig.ignoredMethods?.includes(method);
    const isIgnoredPath = csrfConfig.ignoredPaths?.some(
      path => pathname.startsWith(path)
    );

    if (isStateChanging && !isIgnoredPath) {
      const originValidation = securityHeaders?.validateRequestOrigin ? securityHeaders.validateRequestOrigin(req) : { valid: true };
      if (!originValidation.valid) {
        authProtection?.logSecurityEvent?.({
          userId: userId || undefined,
          action: 'CSRF_VIOLATION',
          resource: pathname,
          details: {
            reason: originValidation.reason,
            method,
            origin: req.headers.get('origin'),
            referer: req.headers.get('referer')
          },
          ip,
          userAgent
        });

        return new NextResponse('CSRF validation failed', { status: 403 });
      }
    }
  }

  // 3. Rate Limiting
  const rateLimitConfig = securityHeaders?.SECURITY_MIDDLEWARE_CONFIG?.rateLimiting || { enabled: false };
  if (rateLimitConfig.enabled) {
    const rateLimitResult = rateLimiter?.checkRateLimit ? await rateLimiter.checkRateLimit(userId || undefined, ip, pathname, req) : { allowed: true };

    if (!rateLimitResult.allowed) {
      authProtection?.logSecurityEvent?.({
        userId: userId || undefined,
        action: 'RATE_LIMIT_EXCEEDED',
        resource: pathname,
        details: {
          ip,
          retryAfter: rateLimitResult.retryAfter
        },
        ip,
        userAgent
      });

      const response = new NextResponse('Rate limit exceeded', { status: 429 });

      // Add rate limit headers
      Object.entries(rateLimitResult.headers).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      return response;
    }
  }

  // 4. IP Filtering (if enabled)
  const ipFilterConfig = securityHeaders?.SECURITY_MIDDLEWARE_CONFIG?.ipFiltering || { enabled: false };
  if (ipFilterConfig.enabled) {
    const { blockedIPs, allowedIPs } = ipFilterConfig;

    if (blockedIPs.length > 0 && blockedIPs.includes(ip)) {
      authProtection?.logSecurityEvent?.({
        userId: userId || undefined,
        action: 'IP_BLOCKED',
        resource: pathname,
        details: { ip, reason: 'IP in blocklist' },
        ip,
        userAgent
      });

      return new NextResponse('Access denied', { status: 403 });
    }

    if (allowedIPs.length > 0 && !allowedIPs.includes(ip)) {
      authProtection?.logSecurityEvent?.({
        userId: userId || undefined,
        action: 'IP_NOT_ALLOWED',
        resource: pathname,
        details: { ip, reason: 'IP not in allowlist' },
        ip,
        userAgent
      });

      return new NextResponse('Access denied', { status: 403 });
    }
  }

  return null;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
