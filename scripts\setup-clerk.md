# 🔧 Clerk Setup Instructions

## ❌ Current Issue
Your Clerk keys are placeholder/demo keys that don't work with the actual Clerk service.

## ✅ How to Fix

### Step 1: Get Real Clerk Keys
1. Go to [https://dashboard.clerk.com/](https://dashboard.clerk.com/)
2. Sign up for a free account or sign in
3. Click "Create Application" or select an existing one
4. Choose your preferred sign-in methods (Email, Google, etc.)
5. Go to the "API Keys" section in the left sidebar

### Step 2: Copy Your Keys
You'll see two keys:
- **Publishable Key** (starts with `pk_test_`)
- **Secret Key** (starts with `sk_test_`)

### Step 3: Update Your .env.local
Replace the current placeholder keys in your `.env.local` file:

```bash
# Replace these lines (around line 28-32):
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YOUR_REAL_PUBLISHABLE_KEY_HERE
CLERK_SECRET_KEY=sk_test_YOUR_REAL_SECRET_KEY_HERE
```

### Step 4: Configure Clerk Application Settings
In your Clerk dashboard:

1. **Allowed Origins** (under "Domains"):
   - Add `http://localhost:3000`
   - Add `http://localhost:3001` (backup port)

2. **Redirect URLs** (under "Paths"):
   - Sign-in: `/sign-in`
   - Sign-up: `/sign-up` 
   - After sign-in: `/dashboard`
   - After sign-up: `/dashboard`

### Step 5: Test the Fix
1. Save your `.env.local` file
2. Restart your dev server: `npm run dev`
3. Visit `http://localhost:3000`
4. You should no longer see the "Publishable key not valid" error

## 🔍 Verification
After updating the keys, you should see:
- ✅ No "Publishable key not valid" error
- ✅ Clerk authentication components load properly
- ✅ Sign-in/Sign-up buttons work
- ✅ Console shows "Clerk keys are valid" in the environment status

## 🆘 Still Having Issues?
If you're still seeing errors:

1. **Double-check key format**:
   - Publishable key should start with `pk_test_`
   - Secret key should start with `sk_test_`
   - Keys should be 50+ characters long

2. **Check for extra spaces**:
   - No spaces before or after the `=` sign
   - No quotes around the keys

3. **Restart everything**:
   ```bash
   # Stop the dev server (Ctrl+C)
   npm run dev
   ```

4. **Check the browser console** for any remaining errors

## 📝 Example .env.local Format
```bash
# Clerk Authentication (REPLACE WITH YOUR REAL KEYS)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_abcd1234567890abcdefghijklmnopqrstuvwxyz1234567890
CLERK_SECRET_KEY=sk_test_abcd1234567890abcdefghijklmnopqrstuvwxyz1234567890
```

## 🎯 What This Fixes
- ❌ "Publishable key not valid" runtime error
- ❌ Clerk components not loading
- ❌ Authentication not working
- ✅ Full Clerk authentication functionality
- ✅ Sign-in/Sign-up flows working
- ✅ Protected routes working properly
