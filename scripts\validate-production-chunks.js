#!/usr/bin/env node

/**
 * Production Chunk Validation Script
 * Validates that all chunks are properly generated and named
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function logHeader(text) {
  console.log(`\n${colors.cyan}${colors.bright}${text}${colors.reset}`);
  console.log('='.repeat(text.length));
}

function logSuccess(text) {
  console.log(`${colors.green}✅ ${text}${colors.reset}`);
}

function logError(text) {
  console.log(`${colors.red}❌ ${text}${colors.reset}`);
}

function logWarning(text) {
  console.log(`${colors.yellow}⚠️  ${text}${colors.reset}`);
}

function logInfo(text) {
  console.log(`${colors.blue}ℹ️  ${text}${colors.reset}`);
}

function validateChunks() {
  logHeader('🔍 PRODUCTION CHUNK VALIDATION');
  
  const chunksDir = path.join(process.cwd(), '.next/static/chunks');
  
  if (!fs.existsSync(chunksDir)) {
    logError('Chunks directory not found - build may have failed');
    return false;
  }
  
  const chunks = fs.readdirSync(chunksDir, { recursive: true })
    .filter(file => typeof file === 'string' && file.endsWith('.js'));
  
  logInfo(`Found ${chunks.length} JavaScript chunks`);
  
  // Validate chunk categories
  const categories = {
    framework: chunks.filter(f => f.includes('framework')),
    runtime: chunks.filter(f => f.includes('runtime')),
    main: chunks.filter(f => f.includes('main')),
    common: chunks.filter(f => f.includes('common')),
    npm: chunks.filter(f => f.startsWith('npm.')),
    app: chunks.filter(f => f.startsWith('app/')),
    pages: chunks.filter(f => f.startsWith('pages/')),
  };
  
  logHeader('📊 CHUNK ANALYSIS');
  
  // Framework chunks
  if (categories.framework.length > 0) {
    logSuccess(`Framework chunks: ${categories.framework.length}`);
    categories.framework.forEach(chunk => {
      console.log(`   - ${chunk}`);
    });
  } else {
    logWarning('No framework chunks found');
  }
  
  // Runtime chunks
  if (categories.runtime.length > 0) {
    logSuccess(`Runtime chunks: ${categories.runtime.length}`);
    categories.runtime.forEach(chunk => {
      console.log(`   - ${chunk}`);
    });
  } else {
    logWarning('No runtime chunks found');
  }
  
  // NPM library chunks
  if (categories.npm.length > 0) {
    logSuccess(`NPM library chunks: ${categories.npm.length}`);
    console.log(`   Examples: ${categories.npm.slice(0, 5).join(', ')}${categories.npm.length > 5 ? '...' : ''}`);
  } else {
    logError('No NPM library chunks found');
  }
  
  // App route chunks
  if (categories.app.length > 0) {
    logSuccess(`App route chunks: ${categories.app.length}`);
    console.log(`   Examples: ${categories.app.slice(0, 3).join(', ')}${categories.app.length > 3 ? '...' : ''}`);
  } else {
    logWarning('No app route chunks found');
  }
  
  // Validate content hashes
  logHeader('🔐 CONTENT HASH VALIDATION');
  
  const hashedChunks = chunks.filter(chunk => {
    // Check for content hash pattern: 8 character hex
    return /[a-f0-9]{8}/.test(chunk);
  });
  
  const hashPercentage = Math.round((hashedChunks.length / chunks.length) * 100);
  
  if (hashPercentage > 80) {
    logSuccess(`${hashedChunks.length}/${chunks.length} chunks have content hashes (${hashPercentage}%)`);
  } else if (hashPercentage > 50) {
    logWarning(`${hashedChunks.length}/${chunks.length} chunks have content hashes (${hashPercentage}%)`);
  } else {
    logError(`Only ${hashedChunks.length}/${chunks.length} chunks have content hashes (${hashPercentage}%)`);
  }
  
  // Validate chunk sizes
  logHeader('📏 CHUNK SIZE ANALYSIS');
  
  const chunkSizes = chunks.map(chunk => {
    const fullPath = path.join(chunksDir, chunk);
    if (fs.existsSync(fullPath)) {
      const stats = fs.statSync(fullPath);
      return {
        name: chunk,
        size: stats.size,
        sizeKB: Math.round(stats.size / 1024)
      };
    }
    return null;
  }).filter(Boolean);
  
  const totalSize = chunkSizes.reduce((sum, chunk) => sum + chunk.size, 0);
  const totalSizeMB = Math.round(totalSize / (1024 * 1024) * 100) / 100;
  
  logInfo(`Total chunk size: ${totalSizeMB} MB`);
  
  // Find largest chunks
  const largestChunks = chunkSizes
    .sort((a, b) => b.size - a.size)
    .slice(0, 5);
  
  console.log('\n   Largest chunks:');
  largestChunks.forEach(chunk => {
    const status = chunk.sizeKB > 500 ? '🔴' : chunk.sizeKB > 250 ? '🟡' : '🟢';
    console.log(`   ${status} ${chunk.name}: ${chunk.sizeKB} KB`);
  });
  
  // Validate specific critical chunks
  logHeader('🎯 CRITICAL CHUNK VALIDATION');
  
  const criticalChunks = [
    { pattern: /runtime/, name: 'Runtime chunk', required: true },
    { pattern: /framework/, name: 'Framework chunk', required: true },
    { pattern: /main/, name: 'Main chunk', required: true },
    { pattern: /common/, name: 'Common chunk', required: false },
    { pattern: /npm\.clerk/, name: 'Clerk authentication', required: true },
    { pattern: /npm\.supabase/, name: 'Supabase database', required: true },
    { pattern: /npm\.posthog/, name: 'PostHog analytics', required: false },
    { pattern: /npm\.sentry/, name: 'Sentry monitoring', required: false },
  ];
  
  let criticalIssues = 0;
  
  criticalChunks.forEach(({ pattern, name, required }) => {
    const found = chunks.some(chunk => pattern.test(chunk));
    if (found) {
      logSuccess(`${name}: Found`);
    } else if (required) {
      logError(`${name}: Missing (REQUIRED)`);
      criticalIssues++;
    } else {
      logWarning(`${name}: Missing (optional)`);
    }
  });
  
  // Final assessment
  logHeader('📋 FINAL ASSESSMENT');
  
  const issues = [];
  
  if (chunks.length < 10) {
    issues.push('Very few chunks generated - may indicate build issues');
  }
  
  if (hashPercentage < 50) {
    issues.push('Low percentage of chunks with content hashes');
  }
  
  if (criticalIssues > 0) {
    issues.push(`${criticalIssues} critical chunks missing`);
  }
  
  if (categories.npm.length < 5) {
    issues.push('Few NPM library chunks - dependencies may not be properly split');
  }
  
  if (issues.length === 0) {
    logSuccess('🎉 All chunk validations passed!');
    logSuccess('✅ Production build is ready for deployment');
    logSuccess('✅ Dynamic imports are working correctly');
    logSuccess('✅ Chunk loading should be reliable');
    
    console.log('\n📊 Summary:');
    console.log(`   • Total chunks: ${chunks.length}`);
    console.log(`   • Content hashed: ${hashedChunks.length} (${hashPercentage}%)`);
    console.log(`   • Total size: ${totalSizeMB} MB`);
    console.log(`   • NPM libraries: ${categories.npm.length}`);
    console.log(`   • App routes: ${categories.app.length}`);
    
    return true;
  } else {
    logError('❌ Issues found:');
    issues.forEach(issue => {
      console.log(`   • ${issue}`);
    });
    
    return false;
  }
}

if (require.main === module) {
  const success = validateChunks();
  process.exit(success ? 0 : 1);
}

module.exports = { validateChunks };
