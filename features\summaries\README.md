# 📝 Summaries Module

## Overview
Core business logic for AI-powered Slack summary generation, management, and delivery.

## Status
🟢 **Production Ready** - Core functionality implemented and tested

## Features
- ✅ AI summary generation (DeepSeek/GPT integration)
- ✅ Slack thread processing and analysis
- ✅ PDF document summarization
- ✅ Scheduled summary automation
- ✅ Template-based summary formatting
- ✅ Export functionality (PDF, DOCX, Markdown)
- 🟡 Advanced AI model routing (in progress)
- 🔴 Multi-language support (planned)

## Module Structure
```
/features/summaries/
├── README.md                    # This file
├── types.ts                    # Summary-specific types
├── schemas.ts                  # Zod validation schemas
├── services/
│   ├── generation.service.ts   # AI summary generation logic
│   ├── processing.service.ts   # Content processing utilities
│   ├── scheduling.service.ts   # Automated summary scheduling
│   └── export.service.ts       # Export functionality
├── repositories/
│   ├── summaries.repository.ts # Database operations
│   └── templates.repository.ts # Template management
├── api/
│   └── handlers/
│       ├── create.handler.ts   # POST /api/summaries
│       ├── list.handler.ts     # GET /api/summaries
│       ├── update.handler.ts   # PUT /api/summaries/:id
│       └── export.handler.ts   # POST /api/summaries/:id/export
├── components/
│   ├── SummaryCard.tsx         # Summary display component
│   ├── SummaryEditor.tsx       # Summary editing interface
│   ├── ExportDialog.tsx        # Export configuration
│   └── TemplateSelector.tsx    # Template selection
├── hooks/
│   ├── useSummaries.ts         # Summary data management
│   ├── useGeneration.ts        # Summary generation state
│   └── useExport.ts            # Export functionality
├── utils/
│   ├── content-parser.ts       # Content parsing utilities
│   ├── ai-prompts.ts           # AI prompt templates
│   └── format-helpers.ts       # Formatting utilities
└── tests/
    ├── unit/
    │   ├── generation.test.ts
    │   └── processing.test.ts
    ├── integration/
    │   └── api.test.ts
    └── fixtures/
        ├── slack-threads.json
        └── sample-summaries.json
```

## API Endpoints
- `POST /api/summaries` - Create new summary
- `GET /api/summaries` - List user summaries
- `GET /api/summaries/:id` - Get specific summary
- `PUT /api/summaries/:id` - Update summary
- `DELETE /api/summaries/:id` - Delete summary
- `POST /api/summaries/:id/export` - Export summary
- `POST /api/summaries/generate` - Generate from Slack thread
- `GET /api/summaries/templates` - List available templates

## Database Schema
```sql
-- Summaries table
CREATE TABLE summaries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  summary_type TEXT DEFAULT 'manual' CHECK (summary_type IN ('manual', 'scheduled', 'automated')),
  source_type TEXT DEFAULT 'slack' CHECK (source_type IN ('slack', 'pdf', 'text')),
  source_data JSONB DEFAULT '{}',
  metadata JSONB DEFAULT '{}',
  tags TEXT[],
  template_id UUID REFERENCES summary_templates(id),
  word_count INTEGER,
  ai_model TEXT,
  processing_time INTEGER, -- milliseconds
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Summary templates
CREATE TABLE summary_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  prompt_template TEXT NOT NULL,
  format_settings JSONB DEFAULT '{}',
  is_public BOOLEAN DEFAULT false,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Scheduled summaries
CREATE TABLE scheduled_summaries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  schedule_cron TEXT NOT NULL,
  source_config JSONB NOT NULL,
  template_id UUID REFERENCES summary_templates(id),
  enabled BOOLEAN DEFAULT true,
  last_run_at TIMESTAMP WITH TIME ZONE,
  next_run_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Usage Examples

### Generate Summary
```typescript
import { generateSummary } from '@/features/summaries/services/generation.service';

const summary = await generateSummary({
  content: slackThreadContent,
  type: 'slack',
  templateId: 'meeting-notes',
  userId,
  organizationId
});
```

### Schedule Automated Summary
```typescript
import { createScheduledSummary } from '@/features/summaries/services/scheduling.service';

await createScheduledSummary({
  name: 'Daily Team Updates',
  schedule: '0 9 * * 1-5', // Weekdays at 9 AM
  sourceConfig: {
    slackChannels: ['#general', '#dev'],
    keywords: ['update', 'progress']
  },
  templateId: 'daily-digest',
  userId,
  organizationId
});
```

### Export Summary
```typescript
import { exportSummary } from '@/features/summaries/services/export.service';

const exportResult = await exportSummary(summaryId, {
  format: 'pdf',
  template: 'professional',
  includeMetadata: true
});
```

## AI Model Configuration
```typescript
// utils/ai-prompts.ts
export const SUMMARY_PROMPTS = {
  meeting: `
    Analyze this Slack conversation and create a structured meeting summary.
    Include: Key decisions, action items, participants, and next steps.
    Format as markdown with clear sections.
  `,
  
  daily: `
    Create a daily digest from these Slack messages.
    Focus on: Important updates, blockers, achievements, and team mentions.
    Keep it concise and actionable.
  `,
  
  technical: `
    Summarize this technical discussion.
    Include: Technical decisions, code changes, architecture discussions, and issues.
    Use technical language appropriate for developers.
  `
};
```

## Performance Considerations
- **Caching**: Summary content cached for 1 hour
- **Rate Limiting**: 10 summaries per minute per user
- **Async Processing**: Large content processed in background
- **Model Selection**: Automatic model routing based on content size

## Security & Privacy
- **RLS Policies**: Row-level security on all tables
- **Content Sanitization**: All input sanitized before AI processing
- **Data Retention**: Summaries auto-deleted after plan limits
- **Audit Logging**: All summary operations logged

## Testing Strategy
- **Unit Tests**: Service layer and utility functions
- **Integration Tests**: API endpoints and database operations
- **E2E Tests**: Complete summary generation workflow
- **Performance Tests**: Load testing for AI generation

## Monitoring & Alerts
- **Generation Success Rate**: Target >95%
- **Processing Time**: Alert if >30 seconds
- **AI Model Errors**: Immediate Slack alerts
- **Usage Metrics**: Track summaries per plan tier

## TODO Items
- [ ] Implement multi-language AI model support
- [ ] Add summary collaboration features
- [ ] Build advanced template editor
- [ ] Implement summary versioning
- [ ] Add bulk export functionality
- [ ] Create summary analytics dashboard

## Dependencies
- **Internal**: `shared/types`, `shared/utils`, `lib/ai`
- **External**: OpenAI/DeepSeek API, Supabase, Zod
- **UI**: `components/ui`, React Hook Form

## Migration Notes
- **v1.0 → v1.1**: Added template system
- **v1.1 → v1.2**: Implemented scheduled summaries
- **v1.2 → v1.3**: Added export functionality
