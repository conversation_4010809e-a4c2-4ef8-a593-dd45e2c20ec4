/**
 * Role-Based Access Control (RBAC) System
 * 
 * Comprehensive RBAC implementation with hierarchical roles,
 * permissions, and resource-based access control.
 */

import { createClient } from '@supabase/supabase-js';
import { logAuditEvent } from './audit-logger';

// Define roles hierarchy
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  MANAGER = 'manager',
  USER = 'user',
  VIEWER = 'viewer'
}

// Define permissions
export enum Permission {
  // User management
  USER_CREATE = 'user:create',
  USER_READ = 'user:read',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  USER_IMPERSONATE = 'user:impersonate',
  
  // Role management
  ROLE_ASSIGN = 'role:assign',
  ROLE_REVOKE = 'role:revoke',
  ROLE_VIEW = 'role:view',
  
  // Organization management
  ORG_CREATE = 'org:create',
  ORG_UPDATE = 'org:update',
  ORG_DELETE = 'org:delete',
  ORG_VIEW = 'org:view',
  ORG_INVITE = 'org:invite',
  
  // Subscription management
  SUBSCRIPTION_CREATE = 'subscription:create',
  SUBSCRIPTION_UPDATE = 'subscription:update',
  SUBSCRIPTION_CANCEL = 'subscription:cancel',
  SUBSCRIPTION_VIEW = 'subscription:view',
  
  // Content management
  CONTENT_CREATE = 'content:create',
  CONTENT_READ = 'content:read',
  CONTENT_UPDATE = 'content:update',
  CONTENT_DELETE = 'content:delete',
  CONTENT_EXPORT = 'content:export',
  
  // Analytics and reporting
  ANALYTICS_VIEW = 'analytics:view',
  ANALYTICS_EXPORT = 'analytics:export',
  REPORTS_VIEW = 'reports:view',
  REPORTS_CREATE = 'reports:create',
  
  // System administration
  SYSTEM_CONFIG = 'system:config',
  SYSTEM_LOGS = 'system:logs',
  SYSTEM_BACKUP = 'system:backup',
  SYSTEM_MAINTENANCE = 'system:maintenance',
  
  // Integration management
  INTEGRATION_SLACK = 'integration:slack',
  INTEGRATION_CRM = 'integration:crm',
  INTEGRATION_NOTION = 'integration:notion',
  
  // Audit and compliance
  AUDIT_VIEW = 'audit:view',
  AUDIT_EXPORT = 'audit:export',
  COMPLIANCE_VIEW = 'compliance:view',
  
  // Billing and payments
  BILLING_VIEW = 'billing:view',
  BILLING_MANAGE = 'billing:manage',
  PAYMENT_PROCESS = 'payment:process'
}

// Role-Permission mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.SUPER_ADMIN]: [
    // All permissions
    ...Object.values(Permission)
  ],
  
  [UserRole.ADMIN]: [
    // User management
    Permission.USER_CREATE,
    Permission.USER_READ,
    Permission.USER_UPDATE,
    Permission.USER_DELETE,
    
    // Role management (limited)
    Permission.ROLE_ASSIGN,
    Permission.ROLE_REVOKE,
    Permission.ROLE_VIEW,
    
    // Organization management
    Permission.ORG_UPDATE,
    Permission.ORG_VIEW,
    Permission.ORG_INVITE,
    
    // Subscription management
    Permission.SUBSCRIPTION_CREATE,
    Permission.SUBSCRIPTION_UPDATE,
    Permission.SUBSCRIPTION_CANCEL,
    Permission.SUBSCRIPTION_VIEW,
    
    // Content management
    Permission.CONTENT_CREATE,
    Permission.CONTENT_READ,
    Permission.CONTENT_UPDATE,
    Permission.CONTENT_DELETE,
    Permission.CONTENT_EXPORT,
    
    // Analytics and reporting
    Permission.ANALYTICS_VIEW,
    Permission.ANALYTICS_EXPORT,
    Permission.REPORTS_VIEW,
    Permission.REPORTS_CREATE,
    
    // Integration management
    Permission.INTEGRATION_SLACK,
    Permission.INTEGRATION_CRM,
    Permission.INTEGRATION_NOTION,
    
    // Audit and compliance
    Permission.AUDIT_VIEW,
    Permission.AUDIT_EXPORT,
    Permission.COMPLIANCE_VIEW,
    
    // Billing and payments
    Permission.BILLING_VIEW,
    Permission.BILLING_MANAGE,
    Permission.PAYMENT_PROCESS
  ],
  
  [UserRole.MANAGER]: [
    // User management (limited)
    Permission.USER_READ,
    Permission.USER_UPDATE,
    
    // Organization management (limited)
    Permission.ORG_VIEW,
    Permission.ORG_INVITE,
    
    // Subscription management (view only)
    Permission.SUBSCRIPTION_VIEW,
    
    // Content management
    Permission.CONTENT_CREATE,
    Permission.CONTENT_READ,
    Permission.CONTENT_UPDATE,
    Permission.CONTENT_DELETE,
    Permission.CONTENT_EXPORT,
    
    // Analytics and reporting
    Permission.ANALYTICS_VIEW,
    Permission.REPORTS_VIEW,
    Permission.REPORTS_CREATE,
    
    // Integration management
    Permission.INTEGRATION_SLACK,
    Permission.INTEGRATION_CRM,
    Permission.INTEGRATION_NOTION,
    
    // Billing (view only)
    Permission.BILLING_VIEW
  ],
  
  [UserRole.USER]: [
    // Basic user permissions
    Permission.USER_READ, // Own profile only
    
    // Organization (view only)
    Permission.ORG_VIEW,
    
    // Content management
    Permission.CONTENT_CREATE,
    Permission.CONTENT_READ,
    Permission.CONTENT_UPDATE, // Own content only
    Permission.CONTENT_EXPORT,
    
    // Basic analytics
    Permission.ANALYTICS_VIEW, // Own data only
    
    // Integration management
    Permission.INTEGRATION_SLACK,
    Permission.INTEGRATION_CRM,
    Permission.INTEGRATION_NOTION,
    
    // Billing (view only)
    Permission.BILLING_VIEW
  ],
  
  [UserRole.VIEWER]: [
    // Read-only permissions
    Permission.USER_READ, // Own profile only
    Permission.ORG_VIEW,
    Permission.CONTENT_READ,
    Permission.ANALYTICS_VIEW, // Own data only
    Permission.BILLING_VIEW
  ]
};

// Resource ownership types
export enum ResourceType {
  USER = 'user',
  ORGANIZATION = 'organization',
  CONTENT = 'content',
  INTEGRATION = 'integration',
  SUBSCRIPTION = 'subscription',
  REPORT = 'report'
}

export interface UserPermissions {
  userId: string;
  role: UserRole;
  permissions: Permission[];
  organizationId?: string;
  isOwner?: boolean;
  customPermissions?: Permission[];
  restrictions?: Permission[];
}

/**
 * RBAC Manager class
 */
export class RBACManager {
  private static instance: RBACManager;
  private supabase: any;
  private permissionCache = new Map<string, UserPermissions>();
  private cacheExpiry = new Map<string, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  
  private constructor() {
    if (process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      this.supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
      );
    }
  }
  
  static getInstance(): RBACManager {
    if (!RBACManager.instance) {
      RBACManager.instance = new RBACManager();
    }
    return RBACManager.instance;
  }
  
  /**
   * Get user permissions with caching
   */
  async getUserPermissions(userId: string, organizationId?: string): Promise<UserPermissions | null> {
    const cacheKey = `${userId}:${organizationId || 'global'}`;
    
    // Check cache
    if (this.permissionCache.has(cacheKey)) {
      const expiry = this.cacheExpiry.get(cacheKey);
      if (expiry && Date.now() < expiry) {
        return this.permissionCache.get(cacheKey)!;
      }
    }
    
    try {
      // Get user role from database
      let query = this.supabase
        .from('user_roles')
        .select('*')
        .eq('user_id', userId);
      
      if (organizationId) {
        query = query.eq('organization_id', organizationId);
      } else {
        query = query.is('organization_id', null);
      }
      
      const { data: roleData, error } = await query.single();
      
      if (error && error.code !== 'PGRST116') { // Not found is OK
        console.error('Failed to get user role:', error);
        return null;
      }
      
      const role = roleData?.role || UserRole.USER;
      const isOwner = roleData?.is_owner || false;
      const customPermissions = roleData?.custom_permissions || [];
      const restrictions = roleData?.restrictions || [];
      
      // Get base permissions for role
      const basePermissions = ROLE_PERMISSIONS[role] || [];
      
      // Combine permissions
      const allPermissions = [
        ...basePermissions,
        ...customPermissions
      ].filter(permission => !restrictions.includes(permission));
      
      const userPermissions: UserPermissions = {
        userId,
        role,
        permissions: allPermissions,
        organizationId,
        isOwner,
        customPermissions,
        restrictions
      };
      
      // Cache the result
      this.permissionCache.set(cacheKey, userPermissions);
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_TTL);
      
      return userPermissions;
      
    } catch (error) {
      console.error('Failed to get user permissions:', error);
      return null;
    }
  }
  
  /**
   * Check if user has permission
   */
  async hasPermission(
    userId: string,
    permission: Permission,
    resourceType?: ResourceType,
    resourceId?: string,
    organizationId?: string
  ): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId, organizationId);
      
      if (!userPermissions) {
        return false;
      }
      
      // Check if user has the permission
      if (!userPermissions.permissions.includes(permission)) {
        return false;
      }
      
      // Additional resource-based checks
      if (resourceType && resourceId) {
        return await this.checkResourceAccess(
          userPermissions,
          resourceType,
          resourceId,
          permission
        );
      }
      
      return true;
      
    } catch (error) {
      console.error('Permission check failed:', error);
      return false;
    }
  }
  
  /**
   * Check resource-specific access
   */
  private async checkResourceAccess(
    userPermissions: UserPermissions,
    resourceType: ResourceType,
    resourceId: string,
    permission: Permission
  ): Promise<boolean> {
    // Super admins have access to everything
    if (userPermissions.role === UserRole.SUPER_ADMIN) {
      return true;
    }
    
    // Check resource ownership
    if (resourceType === ResourceType.USER && resourceId === userPermissions.userId) {
      // Users can always access their own data
      return true;
    }
    
    // Check organization-level access
    if (userPermissions.organizationId) {
      const hasOrgAccess = await this.checkOrganizationAccess(
        userPermissions,
        resourceType,
        resourceId
      );
      
      if (!hasOrgAccess) {
        return false;
      }
    }
    
    // Additional resource-specific logic can be added here
    return true;
  }
  
  /**
   * Check organization-level access
   */
  private async checkOrganizationAccess(
    userPermissions: UserPermissions,
    resourceType: ResourceType,
    resourceId: string
  ): Promise<boolean> {
    if (!this.supabase || !userPermissions.organizationId) {
      return false;
    }
    
    try {
      // Check if resource belongs to user's organization
      const tableName = this.getTableNameForResource(resourceType);
      if (!tableName) {
        return true; // If we can't determine the table, allow access
      }
      
      const { data, error } = await this.supabase
        .from(tableName)
        .select('organization_id, user_id')
        .eq('id', resourceId)
        .single();
      
      if (error) {
        console.error('Failed to check resource organization:', error);
        return false;
      }
      
      // Check if resource belongs to the same organization
      if (data.organization_id === userPermissions.organizationId) {
        return true;
      }
      
      // Check if user owns the resource
      if (data.user_id === userPermissions.userId) {
        return true;
      }
      
      return false;
      
    } catch (error) {
      console.error('Organization access check failed:', error);
      return false;
    }
  }
  
  /**
   * Get table name for resource type
   */
  private getTableNameForResource(resourceType: ResourceType): string | null {
    const tableMap: Record<ResourceType, string> = {
      [ResourceType.USER]: 'users',
      [ResourceType.ORGANIZATION]: 'organizations',
      [ResourceType.CONTENT]: 'summaries',
      [ResourceType.INTEGRATION]: 'slack_integrations',
      [ResourceType.SUBSCRIPTION]: 'subscriptions',
      [ResourceType.REPORT]: 'reports'
    };
    
    return tableMap[resourceType] || null;
  }
  
  /**
   * Assign role to user
   */
  async assignRole(
    userId: string,
    role: UserRole,
    organizationId?: string,
    assignedBy?: string
  ): Promise<boolean> {
    if (!this.supabase) {
      return false;
    }
    
    try {
      const { error } = await this.supabase
        .from('user_roles')
        .upsert({
          user_id: userId,
          role,
          organization_id: organizationId,
          assigned_by: assignedBy,
          assigned_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: organizationId ? 'user_id,organization_id' : 'user_id'
        });
      
      if (error) {
        console.error('Failed to assign role:', error);
        return false;
      }
      
      // Clear cache
      const cacheKey = `${userId}:${organizationId || 'global'}`;
      this.permissionCache.delete(cacheKey);
      this.cacheExpiry.delete(cacheKey);
      
      // Log audit event
      await logAuditEvent({
        event_type: 'ROLE_ASSIGNED',
        user_id: assignedBy,
        action: 'Role assigned to user',
        resource_type: 'user_role',
        resource_id: userId,
        metadata: {
          target_user_id: userId,
          role,
          organization_id: organizationId
        }
      });
      
      return true;
      
    } catch (error) {
      console.error('Failed to assign role:', error);
      return false;
    }
  }
  
  /**
   * Revoke role from user
   */
  async revokeRole(
    userId: string,
    organizationId?: string,
    revokedBy?: string
  ): Promise<boolean> {
    if (!this.supabase) {
      return false;
    }
    
    try {
      let query = this.supabase
        .from('user_roles')
        .delete()
        .eq('user_id', userId);
      
      if (organizationId) {
        query = query.eq('organization_id', organizationId);
      } else {
        query = query.is('organization_id', null);
      }
      
      const { error } = await query;
      
      if (error) {
        console.error('Failed to revoke role:', error);
        return false;
      }
      
      // Clear cache
      const cacheKey = `${userId}:${organizationId || 'global'}`;
      this.permissionCache.delete(cacheKey);
      this.cacheExpiry.delete(cacheKey);
      
      // Log audit event
      await logAuditEvent({
        event_type: 'ROLE_REVOKED',
        user_id: revokedBy,
        action: 'Role revoked from user',
        resource_type: 'user_role',
        resource_id: userId,
        metadata: {
          target_user_id: userId,
          organization_id: organizationId
        }
      });
      
      return true;
      
    } catch (error) {
      console.error('Failed to revoke role:', error);
      return false;
    }
  }
  
  /**
   * Clear permission cache for user
   */
  clearUserCache(userId: string, organizationId?: string): void {
    const cacheKey = `${userId}:${organizationId || 'global'}`;
    this.permissionCache.delete(cacheKey);
    this.cacheExpiry.delete(cacheKey);
  }
  
  /**
   * Clear all permission cache
   */
  clearAllCache(): void {
    this.permissionCache.clear();
    this.cacheExpiry.clear();
  }
}

/**
 * Convenience functions for RBAC
 */
export const rbac = {
  hasPermission: (userId: string, permission: Permission, resourceType?: ResourceType, resourceId?: string, organizationId?: string) =>
    RBACManager.getInstance().hasPermission(userId, permission, resourceType, resourceId, organizationId),
  
  getUserPermissions: (userId: string, organizationId?: string) =>
    RBACManager.getInstance().getUserPermissions(userId, organizationId),
  
  assignRole: (userId: string, role: UserRole, organizationId?: string, assignedBy?: string) =>
    RBACManager.getInstance().assignRole(userId, role, organizationId, assignedBy),
  
  revokeRole: (userId: string, organizationId?: string, revokedBy?: string) =>
    RBACManager.getInstance().revokeRole(userId, organizationId, revokedBy),
  
  clearCache: (userId: string, organizationId?: string) =>
    RBACManager.getInstance().clearUserCache(userId, organizationId)
};
