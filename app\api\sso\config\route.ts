/**
 * SSO Configuration API Route
 * Enterprise Single Sign-On management
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import {
  getSSOConfiguration,
  updateSSOConfiguration,
} from '@/lib/sso-config';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/sso/config
 * Get SSO configuration
 */
export async function GET(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // SSO requires Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'SSO configuration requires Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const { searchParams } = new URL(req.url);
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const organizationId = searchParams.get('organization_id') || user.id;

      const result = await getSSOConfiguration(organizationId);

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        config: result.config,
      });

    } catch (error) {
      console.error('SSO config API error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to fetch SSO configuration' },
        { status: 500 }
      );
    }
  });
}

/**
 * PUT /api/sso/config
 * Update SSO configuration
 */
export async function PUT(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // SSO requires Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'SSO configuration requires Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const body = await req.json();
      const { 
        enforce_sso, 
        allow_email_password, 
        session_timeout, 
        require_mfa, 
        allowed_domains,
        organization_id 
      } = body;

      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const organizationId = organization_id || user.id;

      const result = await updateSSOConfiguration(
        organizationId,
        context.userId,
        user.email,
        {
          enforce_sso,
          allow_email_password,
          session_timeout,
          require_mfa,
          allowed_domains,
        }
      );

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        config: result.config,
        message: 'SSO configuration updated successfully',
      });

    } catch (error) {
      console.error('SSO config update error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to update SSO configuration' },
        { status: 500 }
      );
    }
  });
}
