/**
 * Analytics Service
 * Comprehensive analytics for user and team usage tracking
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';
import { SentryTracker } from '@/lib/sentry.client';

export interface UserAnalytics {
  user_id: string;
  period: 'daily' | 'weekly' | 'monthly';
  summaries_created: number;
  ai_model_usage: Record<string, number>;
  total_tokens_used: number;
  total_cost: number;
  avg_summary_length: number;
  most_active_day: string;
  most_used_model: string;
  crm_syncs: number;
  slack_posts: number;
  export_count: number;
  date_range: {
    start: string;
    end: string;
  };
}

export interface TeamAnalytics {
  organization_id: string;
  period: 'daily' | 'weekly' | 'monthly';
  total_members: number;
  active_members: number;
  total_summaries: number;
  total_tokens: number;
  total_cost: number;
  model_distribution: Record<string, number>;
  top_users: Array<{
    user_id: string;
    user_name: string;
    summaries_count: number;
    tokens_used: number;
  }>;
  activity_timeline: Array<{
    date: string;
    summaries: number;
    tokens: number;
    active_users: number;
  }>;
  feature_usage: {
    crm_syncs: number;
    slack_posts: number;
    exports: number;
    uploads: number;
  };
  date_range: {
    start: string;
    end: string;
  };
}

export interface AnalyticsFilters {
  period: 'daily' | 'weekly' | 'monthly';
  start_date?: string;
  end_date?: string;
  user_id?: string;
  organization_id?: string;
  model_filter?: string[];
}

/**
 * Get user analytics
 */
export async function getUserAnalytics(
  userId: string,
  filters: AnalyticsFilters
): Promise<{ success: boolean; analytics?: UserAnalytics; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Calculate date range
    const endDate = filters.end_date ? new Date(filters.end_date) : new Date();
    const startDate = filters.start_date 
      ? new Date(filters.start_date)
      : getStartDate(endDate, filters.period);

    // Get usage analytics
    const { data: usageData, error: usageError } = await supabase
      .from('usage_analytics')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at', { ascending: true });

    if (usageError) {
      throw usageError;
    }

    // Get summaries data
    const { data: summariesData, error: summariesError } = await supabase
      .from('summaries')
      .select('id, ai_model, content, created_at')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (summariesError) {
      throw summariesError;
    }

    // Calculate analytics
    const analytics = calculateUserAnalytics(
      userId,
      filters.period,
      usageData || [],
      summariesData || [],
      { start: startDate.toISOString(), end: endDate.toISOString() }
    );

    return { success: true, analytics };

  } catch (error) {
    console.error('Error getting user analytics:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get analytics',
    };
  }
}

/**
 * Get team analytics (Enterprise only)
 */
export async function getTeamAnalytics(
  organizationId: string,
  filters: AnalyticsFilters
): Promise<{ success: boolean; analytics?: TeamAnalytics; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Calculate date range
    const endDate = filters.end_date ? new Date(filters.end_date) : new Date();
    const startDate = filters.start_date 
      ? new Date(filters.start_date)
      : getStartDate(endDate, filters.period);

    // Get team members
    const { data: membersData, error: membersError } = await supabase
      .from('team_memberships')
      .select('user_id, role, created_at')
      .eq('organization_id', organizationId)
      .eq('status', 'active');

    if (membersError) {
      throw membersError;
    }

    // Get team usage analytics
    const memberIds = membersData?.map(m => m.user_id) || [];
    
    const { data: usageData, error: usageError } = await supabase
      .from('usage_analytics')
      .select('*')
      .in('user_id', memberIds)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at', { ascending: true });

    if (usageError) {
      throw usageError;
    }

    // Get team summaries
    const { data: summariesData, error: summariesError } = await supabase
      .from('summaries')
      .select('id, user_id, ai_model, content, created_at')
      .in('user_id', memberIds)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (summariesError) {
      throw summariesError;
    }

    // Calculate team analytics
    const analytics = calculateTeamAnalytics(
      organizationId,
      filters.period,
      membersData || [],
      usageData || [],
      summariesData || [],
      { start: startDate.toISOString(), end: endDate.toISOString() }
    );

    return { success: true, analytics };

  } catch (error) {
    console.error('Error getting team analytics:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get team analytics',
    };
  }
}

/**
 * Track usage event
 */
export async function trackUsageEvent(
  userId: string,
  organizationId: string,
  event: {
    event_type: 'summary_created' | 'ai_request' | 'export' | 'crm_sync' | 'slack_post';
    ai_model?: string;
    tokens_used?: number;
    cost?: number;
    metadata?: Record<string, any>;
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    const { error } = await supabase
      .from('usage_analytics')
      .insert({
        user_id: userId,
        organization_id: organizationId,
        event_type: event.event_type,
        ai_model: event.ai_model,
        tokens_used: event.tokens_used || 0,
        cost: event.cost || 0,
        metadata: event.metadata || {},
        created_at: new Date().toISOString(),
      });

    if (error) {
      throw error;
    }

    return { success: true };

  } catch (error) {
    console.error('Error tracking usage event:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to track usage',
    };
  }
}

/**
 * Export analytics data
 */
export async function exportAnalytics(
  userId: string,
  organizationId: string,
  filters: AnalyticsFilters,
  format: 'csv' | 'json' | 'pdf'
): Promise<{ success: boolean; data?: string; filename?: string; error?: string }> {
  try {
    // Get analytics data
    const userResult = await getUserAnalytics(userId, filters);
    if (!userResult.success || !userResult.analytics) {
      throw new Error('Failed to get analytics data');
    }

    const analytics = userResult.analytics;

    if (format === 'json') {
      return {
        success: true,
        data: JSON.stringify(analytics, null, 2),
        filename: `analytics-${userId}-${filters.period}-${Date.now()}.json`,
      };
    }

    if (format === 'csv') {
      const csvData = convertAnalyticsToCSV(analytics);
      return {
        success: true,
        data: csvData,
        filename: `analytics-${userId}-${filters.period}-${Date.now()}.csv`,
      };
    }

    // PDF format would require additional PDF generation library
    throw new Error('PDF export not yet implemented');

  } catch (error) {
    console.error('Error exporting analytics:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Export failed',
    };
  }
}

// Helper functions

function getStartDate(endDate: Date, period: 'daily' | 'weekly' | 'monthly'): Date {
  const start = new Date(endDate);
  
  switch (period) {
    case 'daily':
      start.setDate(start.getDate() - 7); // Last 7 days
      break;
    case 'weekly':
      start.setDate(start.getDate() - 28); // Last 4 weeks
      break;
    case 'monthly':
      start.setMonth(start.getMonth() - 6); // Last 6 months
      break;
  }
  
  return start;
}

function calculateUserAnalytics(
  userId: string,
  period: 'daily' | 'weekly' | 'monthly',
  usageData: any[],
  summariesData: any[],
  dateRange: { start: string; end: string }
): UserAnalytics {
  const aiModelUsage: Record<string, number> = {};
  let totalTokens = 0;
  let totalCost = 0;

  // Process usage data
  usageData.forEach(usage => {
    if (usage.ai_model) {
      aiModelUsage[usage.ai_model] = (aiModelUsage[usage.ai_model] || 0) + 1;
    }
    totalTokens += usage.tokens_used || 0;
    totalCost += usage.cost || 0;
  });

  // Calculate summary stats
  const avgSummaryLength = summariesData.length > 0
    ? summariesData.reduce((sum, s) => sum + (s.content?.length || 0), 0) / summariesData.length
    : 0;

  const mostUsedModel = Object.entries(aiModelUsage)
    .sort(([,a], [,b]) => b - a)[0]?.[0] || 'none';

  // Find most active day
  const dayActivity: Record<string, number> = {};
  summariesData.forEach(summary => {
    const day = new Date(summary.created_at).toLocaleDateString();
    dayActivity[day] = (dayActivity[day] || 0) + 1;
  });
  
  const mostActiveDay = Object.entries(dayActivity)
    .sort(([,a], [,b]) => b - a)[0]?.[0] || 'none';

  return {
    user_id: userId,
    period,
    summaries_created: summariesData.length,
    ai_model_usage: aiModelUsage,
    total_tokens_used: totalTokens,
    total_cost: totalCost,
    avg_summary_length: Math.round(avgSummaryLength),
    most_active_day: mostActiveDay,
    most_used_model: mostUsedModel,
    crm_syncs: usageData.filter(u => u.event_type === 'crm_sync').length,
    slack_posts: usageData.filter(u => u.event_type === 'slack_post').length,
    export_count: usageData.filter(u => u.event_type === 'export').length,
    date_range: dateRange,
  };
}

function calculateTeamAnalytics(
  organizationId: string,
  period: 'daily' | 'weekly' | 'monthly',
  membersData: any[],
  usageData: any[],
  summariesData: any[],
  dateRange: { start: string; end: string }
): TeamAnalytics {
  const modelDistribution: Record<string, number> = {};
  let totalTokens = 0;
  let totalCost = 0;

  // Process usage data
  usageData.forEach(usage => {
    if (usage.ai_model) {
      modelDistribution[usage.ai_model] = (modelDistribution[usage.ai_model] || 0) + 1;
    }
    totalTokens += usage.tokens_used || 0;
    totalCost += usage.cost || 0;
  });

  // Calculate top users
  const userStats: Record<string, { summaries: number; tokens: number }> = {};
  summariesData.forEach(summary => {
    if (!userStats[summary.user_id]) {
      userStats[summary.user_id] = { summaries: 0, tokens: 0 };
    }
    userStats[summary.user_id].summaries++;
  });

  usageData.forEach(usage => {
    if (!userStats[usage.user_id]) {
      userStats[usage.user_id] = { summaries: 0, tokens: 0 };
    }
    userStats[usage.user_id].tokens += usage.tokens_used || 0;
  });

  const topUsers = Object.entries(userStats)
    .map(([userId, stats]) => ({
      user_id: userId,
      user_name: `User ${userId.slice(0, 8)}`, // Would need to fetch actual names
      summaries_count: stats.summaries,
      tokens_used: stats.tokens,
    }))
    .sort((a, b) => b.summaries_count - a.summaries_count)
    .slice(0, 10);

  // Calculate activity timeline
  const activityByDate: Record<string, { summaries: number; tokens: number; users: Set<string> }> = {};
  
  summariesData.forEach(summary => {
    const date = new Date(summary.created_at).toISOString().split('T')[0];
    if (!activityByDate[date]) {
      activityByDate[date] = { summaries: 0, tokens: 0, users: new Set() };
    }
    activityByDate[date].summaries++;
    activityByDate[date].users.add(summary.user_id);
  });

  usageData.forEach(usage => {
    const date = new Date(usage.created_at).toISOString().split('T')[0];
    if (!activityByDate[date]) {
      activityByDate[date] = { summaries: 0, tokens: 0, users: new Set() };
    }
    activityByDate[date].tokens += usage.tokens_used || 0;
    activityByDate[date].users.add(usage.user_id);
  });

  const activityTimeline = Object.entries(activityByDate)
    .map(([date, activity]) => ({
      date,
      summaries: activity.summaries,
      tokens: activity.tokens,
      active_users: activity.users.size,
    }))
    .sort((a, b) => a.date.localeCompare(b.date));

  // Calculate feature usage
  const featureUsage = {
    crm_syncs: usageData.filter(u => u.event_type === 'crm_sync').length,
    slack_posts: usageData.filter(u => u.event_type === 'slack_post').length,
    exports: usageData.filter(u => u.event_type === 'export').length,
    uploads: usageData.filter(u => u.event_type === 'upload').length,
  };

  // Calculate active members (users with activity in the period)
  const activeUserIds = new Set(usageData.map(u => u.user_id));

  return {
    organization_id: organizationId,
    period,
    total_members: membersData.length,
    active_members: activeUserIds.size,
    total_summaries: summariesData.length,
    total_tokens: totalTokens,
    total_cost: totalCost,
    model_distribution: modelDistribution,
    top_users: topUsers,
    activity_timeline: activityTimeline,
    feature_usage: featureUsage,
    date_range: dateRange,
  };
}

function convertAnalyticsToCSV(analytics: UserAnalytics): string {
  const headers = [
    'Metric',
    'Value',
    'Period',
    'Date Range'
  ];

  const rows = [
    ['Summaries Created', analytics.summaries_created.toString(), analytics.period, `${analytics.date_range.start} to ${analytics.date_range.end}`],
    ['Total Tokens Used', analytics.total_tokens_used.toString(), analytics.period, ''],
    ['Total Cost', `$${analytics.total_cost.toFixed(2)}`, analytics.period, ''],
    ['Average Summary Length', analytics.avg_summary_length.toString(), analytics.period, ''],
    ['Most Active Day', analytics.most_active_day, analytics.period, ''],
    ['Most Used Model', analytics.most_used_model, analytics.period, ''],
    ['CRM Syncs', analytics.crm_syncs.toString(), analytics.period, ''],
    ['Slack Posts', analytics.slack_posts.toString(), analytics.period, ''],
    ['Exports', analytics.export_count.toString(), analytics.period, ''],
  ];

  return [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n');
}
