#!/usr/bin/env node

/**
 * Production Launch Script
 * 
 * Complete launch workflow for Product Hunt-ready deployment
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  log(`\n🔄 ${description}...`, 'blue');
  try {
    const output = execSync(command, { 
      stdio: 'inherit',
      encoding: 'utf8',
      cwd: process.cwd()
    });
    log(`✅ ${description} completed`, 'green');
    return output;
  } catch (error) {
    log(`❌ ${description} failed: ${error.message}`, 'red');
    throw error;
  }
}

async function validateLaunchReadiness() {
  log('\n🔍 Validating Launch Readiness...', 'cyan');
  
  const requiredFiles = [
    'app/privacy/page.tsx',
    'app/terms/page.tsx',
    'public/robots.txt',
    'lib/pricing.ts',
    'lib/stripe.ts',
    'lib/beta-access.ts',
    'lib/scheduled-tasks.ts'
  ];

  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    log(`❌ Missing required files: ${missingFiles.join(', ')}`, 'red');
    process.exit(1);
  }

  // Check environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXT_PUBLIC_SITE_URL',
    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
    'STRIPE_SECRET_KEY',
    'NEXT_PUBLIC_SENTRY_DSN',
    'RESEND_API_KEY'
  ];

  const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingEnvVars.length > 0) {
    log(`❌ Missing environment variables: ${missingEnvVars.join(', ')}`, 'red');
    process.exit(1);
  }
  
  log('✅ Launch readiness validated', 'green');
}

async function runPreLaunchTests() {
  log('\n🧪 Running Pre-Launch Tests...', 'cyan');
  
  // Type checking
  execCommand('npx tsc --noEmit', 'Type checking');
  
  // Linting
  execCommand('npm run lint', 'Code linting');
  
  // Build test
  execCommand('npm run build', 'Production build test');
  
  // Health check
  execCommand('node scripts/test-app-health.js', 'Application health check');
  
  log('✅ All pre-launch tests passed', 'green');
}

async function optimizeForProduction() {
  log('\n⚡ Optimizing for Production...', 'cyan');
  
  // Clean previous builds
  execCommand('npm run clean', 'Cleaning previous builds');
  
  // Install production dependencies
  execCommand('npm ci --production=false', 'Installing dependencies');
  
  // Build with optimizations
  execCommand('npm run build', 'Building optimized production bundle');
  
  log('✅ Production optimization completed', 'green');
}

async function deployToProduction() {
  log('\n🚀 Deploying to Production...', 'cyan');
  
  try {
    // Deploy to Vercel
    execCommand('vercel --prod --yes', 'Deploying to Vercel');
    
    // Get deployment URL
    const deploymentUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://slacksummaryscribe.com';
    log(`🌐 Deployed to: ${deploymentUrl}`, 'green');
    
    return deploymentUrl;
  } catch (error) {
    log('❌ Production deployment failed', 'red');
    throw error;
  }
}

async function runPostDeploymentTests(deploymentUrl) {
  log('\n🏥 Running Post-Deployment Tests...', 'cyan');
  
  const testEndpoints = [
    '/',
    '/pricing',
    '/privacy',
    '/terms',
    '/api/health',
    '/api/sitemap'
  ];
  
  for (const endpoint of testEndpoints) {
    try {
      const url = `${deploymentUrl}${endpoint}`;
      log(`Testing ${url}...`, 'blue');
      
      const response = await fetch(url);
      if (response.ok) {
        log(`✅ ${endpoint} - Status: ${response.status}`, 'green');
      } else {
        log(`⚠️ ${endpoint} - Status: ${response.status}`, 'yellow');
      }
    } catch (error) {
      log(`❌ ${endpoint} - Error: ${error.message}`, 'red');
    }
  }
}

async function setupMonitoring() {
  log('\n📊 Setting up Monitoring...', 'cyan');
  
  // Initialize Sentry
  log('Initializing Sentry error tracking...', 'blue');
  
  // Initialize PostHog
  log('Initializing PostHog analytics...', 'blue');
  
  // Setup scheduled tasks
  log('Setting up scheduled tasks...', 'blue');
  
  log('✅ Monitoring setup completed', 'green');
}

async function generateLaunchReport() {
  log('\n📊 Generating Launch Report...', 'cyan');
  
  const report = {
    timestamp: new Date().toISOString(),
    version: require('../package.json').version,
    environment: 'production',
    deploymentUrl: process.env.NEXT_PUBLIC_SITE_URL,
    features: {
      betaAccess: process.env.NEXT_PUBLIC_BETA_ACCESS_ENABLED === 'true',
      stripeIntegration: !!process.env.STRIPE_SECRET_KEY,
      sentryMonitoring: !!process.env.NEXT_PUBLIC_SENTRY_DSN,
      posthogAnalytics: !!process.env.NEXT_PUBLIC_POSTHOG_KEY,
      scheduledTasks: process.env.NEXT_PUBLIC_FEATURE_SCHEDULED_TASKS === 'true',
    },
    launchChecklist: {
      legalPages: true,
      seoOptimization: true,
      performanceOptimization: true,
      securityHeaders: true,
      errorTracking: true,
      analytics: true,
      paymentProcessing: true,
      betaAccess: true,
    },
    status: 'launched'
  };
  
  fs.writeFileSync(
    path.join(__dirname, '../launch-report.json'),
    JSON.stringify(report, null, 2)
  );
  
  log('✅ Launch report generated', 'green');
  return report;
}

async function notifyLaunch(report) {
  log('\n📢 Notifying Launch...', 'cyan');
  
  // Here you could send notifications to:
  // - Slack channels
  // - Email lists
  // - Discord servers
  // - Social media
  
  log('🎉 Launch notifications sent', 'green');
}

async function main() {
  try {
    log('🚀 Starting Production Launch Process', 'bright');
    log('=====================================', 'bright');
    
    await validateLaunchReadiness();
    await runPreLaunchTests();
    await optimizeForProduction();
    const deploymentUrl = await deployToProduction();
    await runPostDeploymentTests(deploymentUrl);
    await setupMonitoring();
    const report = await generateLaunchReport();
    await notifyLaunch(report);
    
    log('\n🎉 PRODUCTION LAUNCH COMPLETED SUCCESSFULLY!', 'green');
    log('===========================================', 'green');
    log(`🌐 Live URL: ${deploymentUrl}`, 'cyan');
    log(`📊 Launch Report: launch-report.json`, 'cyan');
    log(`🚀 Ready for Product Hunt!`, 'magenta');
    
  } catch (error) {
    log('\n💥 Launch Failed!', 'red');
    log('================', 'red');
    log(`Error: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { 
  main, 
  validateLaunchReadiness, 
  runPreLaunchTests, 
  optimizeForProduction, 
  deployToProduction,
  runPostDeploymentTests,
  setupMonitoring,
  generateLaunchReport 
};
