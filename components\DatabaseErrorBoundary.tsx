import { devLog } from '@/lib/console-cleaner';
'use client';

import React, { Component, ReactNode } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  AlertTriangle, 
  RefreshCw, 
  Database, 
  Wifi, 
  Shield,
  ExternalLink,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface DatabaseErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  retryCount: number;
  showDetails: boolean;
  isRetrying: boolean;
}

interface DatabaseErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  maxRetries?: number;
  showRetryButton?: boolean;
  showHealthCheck?: boolean;
}

export default class DatabaseErrorBoundary extends Component<
  DatabaseErrorBoundaryProps,
  DatabaseErrorBoundaryState
> {
  private retryTimeout: NodeJS.Timeout | null = null;

  constructor(props: DatabaseErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      showDetails: false,
      isRetrying: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<DatabaseErrorBoundaryState> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('DatabaseErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to external error tracking service
    this.logError(error, errorInfo);
  }

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  }

  private logError = async (error: Error, errorInfo: React.ErrorInfo) => {
    try {
      // Log to PostHog or Sentry if available
      if (typeof window !== 'undefined' && (window as any).posthog) {
        (window as any).posthog.capture('database_error_boundary_triggered', {
          error_message: error.message,
          error_stack: error.stack,
          component_stack: errorInfo.componentStack,
          retry_count: this.state.retryCount
        });
      }

      // Log to console for development
      console.group('🚨 Database Error Boundary');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Component Stack:', errorInfo.componentStack);
      console.groupEnd();
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }
  };

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    
    if (this.state.retryCount >= maxRetries) {
      console.warn('Max retries reached, not retrying');
      return;
    }

    this.setState({ 
      isRetrying: true 
    });

    // Add delay before retry to prevent rapid retries
    this.retryTimeout = setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: this.state.retryCount + 1,
        isRetrying: false
      });
    }, 1000 + (this.state.retryCount * 1000)); // Exponential backoff
  };

  private handleHealthCheck = async () => {
    try {
      const response = await fetch('/api/health/database?detailed=true');
      const healthData = await response.json();
      
      if (healthData.success) {
  devLog.log('Database health check:', healthData.health_data);
        
        // Show health status in a new window/tab
        const healthWindow = window.open('', '_blank', 'width=800,height=600');
        if (healthWindow) {
          healthWindow.document.write(`
            <html>
              <head><title>Database Health Check</title></head>
              <body style="font-family: monospace; padding: 20px;">
                <h2>Database Health Check Results</h2>
                <pre>${JSON.stringify(healthData.health_data, null, 2)}</pre>
              </body>
            </html>
          `);
        }
      }
    } catch (error) {
      console.error('Health check failed:', error);
    }
  };

  private getErrorType = (error: Error): string => {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'Network Error';
    }
    
    if (message.includes('database') || message.includes('supabase')) {
      return 'Database Error';
    }
    
    if (message.includes('auth') || message.includes('unauthorized')) {
      return 'Authentication Error';
    }
    
    if (message.includes('permission') || message.includes('forbidden')) {
      return 'Permission Error';
    }
    
    return 'Application Error';
  };

  private getErrorIcon = (errorType: string) => {
    switch (errorType) {
      case 'Network Error':
        return <Wifi className="h-6 w-6 text-orange-500" />;
      case 'Database Error':
        return <Database className="h-6 w-6 text-red-500" />;
      case 'Authentication Error':
      case 'Permission Error':
        return <Shield className="h-6 w-6 text-yellow-500" />;
      default:
        return <AlertTriangle className="h-6 w-6 text-red-500" />;
    }
  };

  private getErrorSuggestions = (errorType: string): string[] => {
    switch (errorType) {
      case 'Network Error':
        return [
          'Check your internet connection',
          'Try refreshing the page',
          'Check if the service is experiencing downtime'
        ];
      case 'Database Error':
        return [
          'The database may be temporarily unavailable',
          'Try again in a few moments',
          'Contact support if the issue persists'
        ];
      case 'Authentication Error':
        return [
          'Try signing out and signing back in',
          'Clear your browser cache and cookies',
          'Check if your session has expired'
        ];
      case 'Permission Error':
        return [
          'You may not have permission to access this resource',
          'Contact your administrator',
          'Try signing out and back in'
        ];
      default:
        return [
          'Try refreshing the page',
          'Clear your browser cache',
          'Contact support if the issue continues'
        ];
    }
  };

  render() {
    if (this.state.hasError) {
      const { 
        fallback, 
        maxRetries = 3, 
        showRetryButton = true, 
        showHealthCheck = true 
      } = this.props;
      
      const { error, retryCount, showDetails, isRetrying } = this.state;
      
      if (fallback) {
        return fallback;
      }

      const errorType = error ? this.getErrorType(error) : 'Unknown Error';
      const errorIcon = this.getErrorIcon(errorType);
      const suggestions = this.getErrorSuggestions(errorType);
      const canRetry = retryCount < maxRetries && showRetryButton;

      return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <div className="flex items-center space-x-3">
                {errorIcon}
                <div>
                  <CardTitle className="text-xl">
                    {errorType}
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    Something went wrong while accessing the database
                  </p>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Error Message */}
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="font-medium mb-2">
                    {error?.message || 'An unexpected error occurred'}
                  </div>
                  {retryCount > 0 && (
                    <div className="text-sm text-gray-600">
                      Retry attempt: {retryCount}/{maxRetries}
                    </div>
                  )}
                </AlertDescription>
              </Alert>

              {/* Suggestions */}
              <div>
                <h4 className="font-medium mb-2">What you can try:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                  {suggestions.map((suggestion, index) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2">
                {canRetry && (
                  <Button 
                    onClick={this.handleRetry}
                    disabled={isRetrying}
                    className="flex items-center space-x-2"
                  >
                    <RefreshCw className={`h-4 w-4 ${isRetrying ? 'animate-spin' : ''}`} />
                    <span>{isRetrying ? 'Retrying...' : 'Try Again'}</span>
                  </Button>
                )}
                
                <Button 
                  variant="outline" 
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </Button>
                
                {showHealthCheck && (
                  <Button 
                    variant="outline" 
                    onClick={this.handleHealthCheck}
                  >
                    <Database className="h-4 w-4 mr-2" />
                    Check Status
                  </Button>
                )}
                
                <Button 
                  variant="ghost" 
                  onClick={() => this.setState({ showDetails: !showDetails })}
                >
                  {showDetails ? (
                    <>
                      <ChevronUp className="h-4 w-4 mr-2" />
                      Hide Details
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-4 w-4 mr-2" />
                      Show Details
                    </>
                  )}
                </Button>
              </div>

              {/* Error Details (Collapsible) */}
              {showDetails && error && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-2">Technical Details:</h4>
                  <div className="text-sm font-mono text-gray-700 space-y-2">
                    <div>
                      <strong>Error:</strong> {error.name}
                    </div>
                    <div>
                      <strong>Message:</strong> {error.message}
                    </div>
                    {error.stack && (
                      <div>
                        <strong>Stack Trace:</strong>
                        <pre className="mt-1 text-xs overflow-x-auto whitespace-pre-wrap">
                          {error.stack}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Support Link */}
              <div className="text-center pt-4 border-t">
                <p className="text-sm text-gray-600">
                  Need help? {' '}
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-blue-600 hover:text-blue-800 inline-flex items-center"
                  >
                    Contact Support
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}
