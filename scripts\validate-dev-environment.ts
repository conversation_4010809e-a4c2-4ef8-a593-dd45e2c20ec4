#!/usr/bin/env tsx

/**
 * Development Environment Validation Script
 * Ensures local development environment matches production capabilities
 */

import fs from 'fs';
import path from 'path';

// Load environment variables from .env.local
function loadEnvironmentVariables() {
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
        const [key, ...valueParts] = trimmed.split('=');
        const value = valueParts.join('=');
        if (key && value && !process.env[key]) {
          process.env[key] = value;
        }
      }
    }
  }
}

// Load environment variables before validation
loadEnvironmentVariables();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

interface ValidationResult {
  passed: number;
  warnings: number;
  errors: number;
  details: string[];
}

/**
 * Validate environment variables
 */
function validateEnvironmentVariables(): ValidationResult {
  const result: ValidationResult = { passed: 0, warnings: 0, errors: 0, details: [] };
  
  const requiredVars = [
    { name: 'NEXT_PUBLIC_SUPABASE_URL', critical: true },
    { name: 'NEXT_PUBLIC_SUPABASE_ANON_KEY', critical: true },
    { name: 'SUPABASE_SERVICE_ROLE_KEY', critical: true },
    { name: 'OPENROUTER_API_KEY', critical: true },
    { name: 'SLACK_CLIENT_ID', critical: true },
    { name: 'SLACK_CLIENT_SECRET', critical: true },
    { name: 'RESEND_API_KEY', critical: false },
    { name: 'NEXT_PUBLIC_SENTRY_DSN', critical: false },
    { name: 'NEXT_PUBLIC_POSTHOG_KEY', critical: false },
  ];
  
  for (const { name, critical } of requiredVars) {
    const value = process.env[name];
    
    if (!value) {
      if (critical) {
        result.errors++;
        result.details.push(`❌ ${name} is missing`);
      } else {
        result.warnings++;
        result.details.push(`⚠️  ${name} is missing (optional)`);
      }
    } else if (value.includes('your_') || value.includes('placeholder')) {
      if (critical) {
        result.errors++;
        result.details.push(`❌ ${name} contains placeholder value`);
      } else {
        result.warnings++;
        result.details.push(`⚠️  ${name} contains placeholder value`);
      }
    } else {
      result.passed++;
      result.details.push(`✅ ${name} configured`);
    }
  }
  
  return result;
}

/**
 * Validate critical files exist
 */
function validateCriticalFiles(): ValidationResult {
  const result: ValidationResult = { passed: 0, warnings: 0, errors: 0, details: [] };
  
  const criticalFiles = [
    'app/layout.tsx',
    'app/dashboard/page.tsx',
    'app/upload/page.tsx',
    'app/api/health/route.ts',
    'app/api/dashboard/route.ts',
    'app/api/summarize/route.ts',
    'app/api/upload/route.ts',
    'lib/supabase-browser.ts',
    'lib/supabase-server.ts',
    'middleware.ts',
    'next.config.mjs',
  ];
  
  for (const file of criticalFiles) {
    if (fs.existsSync(path.join(process.cwd(), file))) {
      result.passed++;
      result.details.push(`✅ ${file} exists`);
    } else {
      result.errors++;
      result.details.push(`❌ ${file} missing`);
    }
  }
  
  return result;
}

/**
 * Test API connectivity
 */
async function testAPIConnectivity(): Promise<ValidationResult> {
  const result: ValidationResult = { passed: 0, warnings: 0, errors: 0, details: [] };
  
  const tests = [
    {
      name: 'Supabase Database',
      url: `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`,
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''}`,
      },
      critical: true,
    },
    {
      name: 'OpenRouter AI API',
      url: 'https://openrouter.ai/api/v1/models',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY || ''}`,
      },
      critical: true,
    },
    {
      name: 'Resend Email API',
      url: 'https://api.resend.com/domains',
      headers: {
        'Authorization': `Bearer ${process.env.RESEND_API_KEY || ''}`,
      },
      critical: false,
    },
  ];
  
  for (const { name, url, headers, critical } of tests) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(url, {
        headers: headers as Record<string, string>,
        signal: controller.signal,
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        result.passed++;
        result.details.push(`✅ ${name} connection successful`);
      } else {
        if (critical) {
          result.errors++;
          result.details.push(`❌ ${name} connection failed (${response.status})`);
        } else {
          result.warnings++;
          result.details.push(`⚠️  ${name} connection failed (${response.status})`);
        }
      }
    } catch (error) {
      if (critical) {
        result.errors++;
        result.details.push(`❌ ${name} connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } else {
        result.warnings++;
        result.details.push(`⚠️  ${name} connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  }
  
  return result;
}

/**
 * Validate package dependencies
 */
function validateDependencies(): ValidationResult {
  const result: ValidationResult = { passed: 0, warnings: 0, errors: 0, details: [] };
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredDeps = [
      'next',
      'react',
      'typescript',
      '@supabase/supabase-js',
      '@supabase/ssr',
      'tailwindcss',
      'lucide-react',
      'sonner',
      'recharts',
      'framer-motion',
    ];
    
    const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    for (const dep of requiredDeps) {
      if (allDeps[dep]) {
        result.passed++;
        result.details.push(`✅ ${dep} installed (${allDeps[dep]})`);
      } else {
        result.errors++;
        result.details.push(`❌ ${dep} missing`);
      }
    }
  } catch (error) {
    result.errors++;
    result.details.push(`❌ Could not read package.json: ${error}`);
  }
  
  return result;
}

/**
 * Main validation function
 */
async function main() {
  log('\n' + '='.repeat(60), 'cyan');
  log('🔍 DEVELOPMENT ENVIRONMENT VALIDATION', 'cyan');
  log('='.repeat(60), 'cyan');
  log('Ensuring local environment matches production capabilities\n', 'bright');
  
  const results: ValidationResult[] = [];
  
  // 1. Environment Variables
  log('📋 Validating Environment Variables...', 'blue');
  const envResult = validateEnvironmentVariables();
  results.push(envResult);
  envResult.details.forEach(detail => log(`  ${detail}`));
  
  // 2. Critical Files
  log('\n📁 Validating Critical Files...', 'blue');
  const filesResult = validateCriticalFiles();
  results.push(filesResult);
  filesResult.details.forEach(detail => log(`  ${detail}`));
  
  // 3. Dependencies
  log('\n📦 Validating Dependencies...', 'blue');
  const depsResult = validateDependencies();
  results.push(depsResult);
  depsResult.details.forEach(detail => log(`  ${detail}`));
  
  // 4. API Connectivity
  log('\n🌐 Testing API Connectivity...', 'blue');
  const apiResult = await testAPIConnectivity();
  results.push(apiResult);
  apiResult.details.forEach(detail => log(`  ${detail}`));
  
  // Summary
  const totalPassed = results.reduce((sum, r) => sum + r.passed, 0);
  const totalWarnings = results.reduce((sum, r) => sum + r.warnings, 0);
  const totalErrors = results.reduce((sum, r) => sum + r.errors, 0);
  
  log('\n' + '='.repeat(60), 'cyan');
  log('📊 VALIDATION SUMMARY', 'cyan');
  log('='.repeat(60), 'cyan');
  
  log(`✅ Passed: ${totalPassed}`, 'green');
  log(`⚠️  Warnings: ${totalWarnings}`, 'yellow');
  log(`❌ Errors: ${totalErrors}`, 'red');
  
  if (totalErrors === 0) {
    log('\n🎉 DEVELOPMENT ENVIRONMENT READY!', 'green');
    log('✅ All critical checks passed', 'green');
    if (totalWarnings > 0) {
      log(`⚠️  ${totalWarnings} warnings to address for optimal development`, 'yellow');
    }
    log('\n🚀 Ready to start development server with npm run dev', 'cyan');
    process.exit(0);
  } else {
    log('\n🚨 DEVELOPMENT ENVIRONMENT ISSUES DETECTED', 'red');
    log(`❌ ${totalErrors} critical errors must be fixed`, 'red');
    log('\n🔧 Fix the errors above and run this script again', 'yellow');
    process.exit(1);
  }
}

// Execute validation
main().catch((error) => {
  console.error('Validation failed:', error);
  process.exit(1);
});
