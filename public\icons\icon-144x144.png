<svg width="144" height="144" viewBox="0 0 144 144" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="72" cy="72" r="68" fill="#4F46E5" stroke="#3730A3" stroke-width="8"/>
  
  <!-- Slack-style icon with document -->
  <g transform="translate(24, 24)">
    <!-- Document icon -->
    <rect x="8" y="8" width="48" height="64" rx="8" fill="white" opacity="0.95"/>
    <rect x="16" y="20" width="32" height="4" fill="#4F46E5" opacity="0.8"/>
    <rect x="16" y="28" width="24" height="4" fill="#4F46E5" opacity="0.8"/>
    <rect x="16" y="36" width="32" height="4" fill="#4F46E5" opacity="0.8"/>
    <rect x="16" y="44" width="20" height="4" fill="#4F46E5" opacity="0.8"/>
    <rect x="16" y="52" width="28" height="4" fill="#4F46E5" opacity="0.8"/>
    <rect x="16" y="60" width="16" height="4" fill="#4F46E5" opacity="0.8"/>
    
    <!-- AI/Summary indicator -->
    <circle cx="64" cy="16" r="12" fill="#10B981" stroke="white" stroke-width="4"/>
    <path d="M60 16 L64 20 L70 12" stroke="white" stroke-width="6" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    
    <!-- Slack-style elements -->
    <circle cx="20" cy="80" r="6" fill="#E11D48"/>
    <circle cx="36" cy="80" r="6" fill="#F59E0B"/>
    <circle cx="52" cy="80" r="6" fill="#10B981"/>
  </g>
  
  <!-- App name text -->
  <text x="72" y="120" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">SLACK SCRIBE</text>
</svg>
