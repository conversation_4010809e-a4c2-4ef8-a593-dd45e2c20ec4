/**
 * Weekly Growth Export API
 * 
 * Generates comprehensive growth reports with:
 * - CSV of all organizations, usage, plan, last active
 * - PDF report with charts and insights
 * - Notion page creation
 * - Google Sheets export
 * - Email delivery to admin
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { Parser } from 'json2csv';
import PDFDocument from 'pdfkit';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

interface OrganizationData {
  id: string;
  name: string;
  plan: string;
  memberCount: number;
  summariesCount: number;
  lastActiveAt: string;
  createdAt: string;
  mrr: number;
  usage: {
    summariesThisMonth: number;
    exportsThisMonth: number;
    integrationCount: number;
  };
  health: {
    engagementScore: number;
    churnRisk: 'low' | 'medium' | 'high';
    lastLogin: string;
  };
}

interface GrowthReport {
  period: string;
  summary: {
    totalOrganizations: number;
    newOrganizations: number;
    churnedOrganizations: number;
    totalMRR: number;
    mrrGrowth: number;
    averageEngagement: number;
  };
  organizations: OrganizationData[];
  insights: {
    topPerformers: OrganizationData[];
    atRiskCustomers: OrganizationData[];
    growthOpportunities: string[];
    recommendations: string[];
  };
}

export async function POST(request: NextRequest) {
  try {
    const { format = 'csv', period = 'week', email } = await request.json();
    
    // Generate growth report data
    const report = await generateGrowthReport(period);
    
    switch (format) {
      case 'csv':
        return await generateCSVResponse(report);
      case 'pdf':
        return await generatePDFResponse(report);
      case 'notion':
        return await createNotionPage(report);
      case 'sheets':
        return await exportToGoogleSheets(report);
      case 'email':
        return await emailReport(report, email);
      default:
        return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
    }
  } catch (error) {
    console.error('Export error:', error);
    return NextResponse.json(
      { error: 'Failed to generate export' },
      { status: 500 }
    );
  }
}

async function generateGrowthReport(period: string): Promise<GrowthReport> {
  const supabase = await createSupabaseServerClient();
  
  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();
  
  switch (period) {
    case 'week':
      startDate.setDate(endDate.getDate() - 7);
      break;
    case 'month':
      startDate.setMonth(endDate.getMonth() - 1);
      break;
    case 'quarter':
      startDate.setMonth(endDate.getMonth() - 3);
      break;
    default:
      startDate.setDate(endDate.getDate() - 7);
  }

  // Fetch organizations with detailed data
  const { data: organizations, error } = await supabase
    .from('organizations')
    .select(`
      *,
      user_organizations!inner (
        user_id,
        role,
        profiles (
          last_active_at
        )
      ),
      summaries (
        id,
        created_at
      )
    `);

  if (error) {
    throw new Error(`Failed to fetch organizations: ${error.message}`);
  }

  // Process organization data
  const processedOrgs: OrganizationData[] = await Promise.all(
    organizations.map(async (org: any) => {
      const memberCount = org.user_organizations?.length || 0;
      const summariesCount = org.summaries?.length || 0;
      
      // Calculate usage metrics
      const summariesThisMonth = org.summaries?.filter((s: any) => 
        new Date(s.created_at) >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      ).length || 0;

      // Get export count
      const { data: exports } = await supabase
        .from('export_jobs')
        .select('id')
        .eq('organization_id', org.id)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      // Get integration count
      const { data: integrations } = await supabase
        .from('oauth_tokens')
        .select('id')
        .eq('organization_id', org.id)
        .eq('status', 'active');

      // Calculate MRR based on plan
      const planMRR = {
        'FREE': 0,
        'PRO': 29,
        'ENTERPRISE': 99
      };

      // Calculate engagement score
      const lastActiveAt = org.user_organizations?.[0]?.profiles?.last_active_at;
      const daysSinceActive = lastActiveAt ? 
        Math.floor((Date.now() - new Date(lastActiveAt).getTime()) / (1000 * 60 * 60 * 24)) : 999;
      
      const engagementScore = Math.max(0, 100 - (daysSinceActive * 2) - (summariesThisMonth === 0 ? 30 : 0));
      
      const churnRisk = engagementScore < 30 ? 'high' : 
                       engagementScore < 60 ? 'medium' : 'low';

      return {
        id: org.id,
        name: org.name,
        plan: org.plan || 'FREE',
        memberCount,
        summariesCount,
        lastActiveAt: lastActiveAt || org.created_at,
        createdAt: org.created_at,
        mrr: planMRR[org.plan as keyof typeof planMRR] || 0,
        usage: {
          summariesThisMonth,
          exportsThisMonth: exports?.length || 0,
          integrationCount: integrations?.length || 0
        },
        health: {
          engagementScore,
          churnRisk,
          lastLogin: lastActiveAt || 'Never'
        }
      };
    })
  );

  // Calculate summary metrics
  const totalOrganizations = processedOrgs.length;
  const newOrganizations = processedOrgs.filter(org => 
    new Date(org.createdAt) >= startDate
  ).length;
  
  const totalMRR = processedOrgs.reduce((sum, org) => sum + org.mrr, 0);
  const averageEngagement = processedOrgs.reduce((sum, org) => 
    sum + org.health.engagementScore, 0) / totalOrganizations;

  // Identify insights
  const topPerformers = processedOrgs
    .filter(org => org.health.engagementScore > 80)
    .sort((a, b) => b.usage.summariesThisMonth - a.usage.summariesThisMonth)
    .slice(0, 5);

  const atRiskCustomers = processedOrgs
    .filter(org => org.health.churnRisk === 'high' && org.plan !== 'FREE')
    .sort((a, b) => a.health.engagementScore - b.health.engagementScore)
    .slice(0, 10);

  const growthOpportunities = [
    `${processedOrgs.filter(org => org.plan === 'FREE' && org.usage.summariesThisMonth > 10).length} free users ready for upgrade`,
    `${processedOrgs.filter(org => org.usage.integrationCount === 0).length} customers not using integrations`,
    `${processedOrgs.filter(org => org.usage.exportsThisMonth === 0 && org.usage.summariesThisMonth > 0).length} customers not using exports`
  ];

  const recommendations = [
    atRiskCustomers.length > 0 ? `Reach out to ${atRiskCustomers.length} at-risk customers` : 'No immediate churn risks',
    `Focus on integration adoption for ${processedOrgs.filter(org => org.usage.integrationCount === 0).length} customers`,
    `Upsell campaign for ${processedOrgs.filter(org => org.plan === 'FREE' && org.usage.summariesThisMonth > 5).length} active free users`
  ];

  return {
    period: `${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`,
    summary: {
      totalOrganizations,
      newOrganizations,
      churnedOrganizations: 0, // Would need historical data
      totalMRR,
      mrrGrowth: 0, // Would need historical data
      averageEngagement
    },
    organizations: processedOrgs,
    insights: {
      topPerformers,
      atRiskCustomers,
      growthOpportunities,
      recommendations
    }
  };
}

async function generateCSVResponse(report: GrowthReport): Promise<NextResponse> {
  const fields = [
    'id',
    'name',
    'plan',
    'memberCount',
    'summariesCount',
    'mrr',
    'usage.summariesThisMonth',
    'usage.exportsThisMonth',
    'usage.integrationCount',
    'health.engagementScore',
    'health.churnRisk',
    'lastActiveAt',
    'createdAt'
  ];

  const parser = new Parser({ fields });
  const csv = parser.parse(report.organizations);

  return new NextResponse(csv, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="growth-report-${Date.now()}.csv"`
    }
  });
}

async function generatePDFResponse(report: GrowthReport): Promise<NextResponse> {
  const doc = new PDFDocument();
  const chunks: Buffer[] = [];

  doc.on('data', chunk => chunks.push(chunk));
  
  return new Promise((resolve) => {
    doc.on('end', () => {
      const pdfBuffer = Buffer.concat(chunks);
      resolve(new NextResponse(pdfBuffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="growth-report-${Date.now()}.pdf"`
        }
      }));
    });

    // Generate PDF content
    doc.fontSize(20).text('Growth Report', 50, 50);
    doc.fontSize(12).text(`Period: ${report.period}`, 50, 80);

    // Summary section
    doc.fontSize(16).text('Summary', 50, 120);
    doc.fontSize(12)
       .text(`Total Organizations: ${report.summary.totalOrganizations}`, 50, 150)
       .text(`New Organizations: ${report.summary.newOrganizations}`, 50, 170)
       .text(`Total MRR: $${report.summary.totalMRR}`, 50, 190)
       .text(`Average Engagement: ${report.summary.averageEngagement.toFixed(1)}%`, 50, 210);

    // Top performers
    doc.fontSize(16).text('Top Performers', 50, 250);
    let yPos = 280;
    report.insights.topPerformers.forEach((org, index) => {
      doc.fontSize(10).text(
        `${index + 1}. ${org.name} (${org.plan}) - ${org.usage.summariesThisMonth} summaries`,
        50, yPos
      );
      yPos += 20;
    });

    // At-risk customers
    doc.fontSize(16).text('At-Risk Customers', 50, yPos + 20);
    yPos += 50;
    report.insights.atRiskCustomers.forEach((org, index) => {
      doc.fontSize(10).text(
        `${index + 1}. ${org.name} (${org.plan}) - Score: ${org.health.engagementScore}`,
        50, yPos
      );
      yPos += 20;
    });

    doc.end();
  });
}

async function createNotionPage(report: GrowthReport): Promise<NextResponse> {
  try {
    // This would integrate with Notion API
    // For now, return a placeholder response
    
    const notionPageUrl = `https://notion.so/growth-report-${Date.now()}`;
    
    return NextResponse.json({
      success: true,
      notionUrl: notionPageUrl,
      message: 'Growth report created in Notion'
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to create Notion page' },
      { status: 500 }
    );
  }
}

async function exportToGoogleSheets(report: GrowthReport): Promise<NextResponse> {
  try {
    // This would integrate with Google Sheets API
    // For now, return a placeholder response
    
    const sheetsUrl = `https://docs.google.com/spreadsheets/d/growth-report-${Date.now()}`;
    
    return NextResponse.json({
      success: true,
      sheetsUrl: sheetsUrl,
      message: 'Growth report exported to Google Sheets'
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to export to Google Sheets' },
      { status: 500 }
    );
  }
}

async function emailReport(report: GrowthReport, email: string): Promise<NextResponse> {
  try {
    // Generate HTML email content
    const htmlContent = `
      <h1>Weekly Growth Report</h1>
      <p><strong>Period:</strong> ${report.period}</p>
      
      <h2>Summary</h2>
      <ul>
        <li>Total Organizations: ${report.summary.totalOrganizations}</li>
        <li>New Organizations: ${report.summary.newOrganizations}</li>
        <li>Total MRR: $${report.summary.totalMRR}</li>
        <li>Average Engagement: ${report.summary.averageEngagement.toFixed(1)}%</li>
      </ul>
      
      <h2>Key Insights</h2>
      <h3>Growth Opportunities</h3>
      <ul>
        ${report.insights.growthOpportunities.map(opp => `<li>${opp}</li>`).join('')}
      </ul>
      
      <h3>Recommendations</h3>
      <ul>
        ${report.insights.recommendations.map(rec => `<li>${rec}</li>`).join('')}
      </ul>
      
      <h3>At-Risk Customers</h3>
      <ul>
        ${report.insights.atRiskCustomers.slice(0, 5).map(org => 
          `<li>${org.name} (${org.plan}) - Engagement: ${org.health.engagementScore}%</li>`
        ).join('')}
      </ul>
    `;

    await resend.emails.send({
      from: '<EMAIL>',
      to: email,
      subject: `Weekly Growth Report - ${new Date().toLocaleDateString()}`,
      html: htmlContent
    });

    return NextResponse.json({
      success: true,
      message: 'Growth report emailed successfully'
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to email report' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const format = searchParams.get('format') || 'csv';
  const period = searchParams.get('period') || 'week';

  return POST(new NextRequest(request.url, {
    method: 'POST',
    body: JSON.stringify({ format, period })
  }));
}
