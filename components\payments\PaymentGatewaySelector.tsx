'use client';

/**
 * Payment Gateway Selector Component
 * Shows available payment gateways with fallback status
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CreditCard, Shield, Globe, CheckCircle, XCircle, 
  Loader2, AlertTriangle, RefreshCw 
} from 'lucide-react';
import { toast } from 'sonner';

interface PaymentGateway {
  id: 'stripe' | 'cashfree';
  name: string;
  enabled: boolean;
  regions: string[];
  priority: number;
}

interface PaymentGatewayStatus {
  gateways: PaymentGateway[];
  health_check: Record<string, boolean>;
  timestamp: string;
}

interface PaymentGatewaySelectorProps {
  onGatewaySelect?: (gateway: 'stripe' | 'cashfree') => void;
  selectedPlan?: string;
  userRegion?: string;
  className?: string;
}

export default function PaymentGatewaySelector({ 
  onGatewaySelect, 
  selectedPlan = 'pro',
  userRegion = 'US',
  className 
}: PaymentGatewaySelectorProps) {
  const [status, setStatus] = useState<PaymentGatewayStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<string | null>(null);

  useEffect(() => {
    fetchGatewayStatus();
  }, []);

  const fetchGatewayStatus = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/payments/status');
      const data = await response.json();

      if (data.success) {
        setStatus(data);
      } else {
        toast.error('Failed to load payment gateway status');
      }
    } catch (error) {
      console.error('Error fetching gateway status:', error);
      toast.error('Failed to load payment gateway status');
    } finally {
      setLoading(false);
    }
  };

  const handleCheckout = async (gatewayId: 'stripe' | 'cashfree') => {
    try {
      setProcessing(gatewayId);
      
      const response = await fetch('/api/payments/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan_id: selectedPlan,
          region: userRegion,
          success_url: `${window.location.origin}/dashboard?payment=success`,
          cancel_url: `${window.location.origin}/pricing?payment=cancelled`,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Redirect to checkout
        window.location.href = data.checkout_url;
      } else {
        toast.error(data.error || 'Failed to create checkout session');
        
        if (data.fallback_attempted) {
          toast.info(`Fallback to ${data.gateway_used} was attempted`);
        }
      }
    } catch (error) {
      console.error('Checkout error:', error);
      toast.error('Failed to start checkout process');
    } finally {
      setProcessing(null);
    }
  };

  const getGatewayIcon = (gatewayId: string) => {
    switch (gatewayId) {
      case 'stripe':
        return <CreditCard className="h-5 w-5" />;
      case 'cashfree':
        return <Shield className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  const getHealthIcon = (isHealthy: boolean) => {
    return isHealthy ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getPreferredGateway = () => {
    if (!status) return null;
    
    return status.gateways
      .filter(g => g.enabled && g.regions.includes(userRegion))
      .sort((a, b) => a.priority - b.priority)[0] || status.gateways[0];
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading payment options...</span>
        </CardContent>
      </Card>
    );
  }

  if (!status) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to load payment gateway status. Please try again.
            </AlertDescription>
          </Alert>
          <Button 
            variant="outline" 
            onClick={fetchGatewayStatus}
            className="mt-4"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  const preferredGateway = getPreferredGateway();

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Preferred Gateway */}
      {preferredGateway && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getGatewayIcon(preferredGateway.id)}
              {preferredGateway.name}
              <Badge variant="secondary">Recommended</Badge>
            </CardTitle>
            <CardDescription>
              Optimized for your region ({userRegion})
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getHealthIcon(status.health_check[preferredGateway.id])}
                <span className="text-sm">
                  {status.health_check[preferredGateway.id] ? 'Available' : 'Unavailable'}
                </span>
              </div>
              
              <Button 
                onClick={() => handleCheckout(preferredGateway.id)}
                disabled={!status.health_check[preferredGateway.id] || processing === preferredGateway.id}
              >
                {processing === preferredGateway.id ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Processing...
                  </>
                ) : (
                  <>
                    <CreditCard className="h-4 w-4 mr-2" />
                    Pay with {preferredGateway.name}
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alternative Gateways */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-gray-700">Alternative Payment Methods</h4>
        
        {status.gateways
          .filter(g => g.id !== preferredGateway?.id)
          .map((gateway) => (
            <Card key={gateway.id} className="border-gray-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getGatewayIcon(gateway.id)}
                    <div>
                      <div className="font-medium">{gateway.name}</div>
                      <div className="text-xs text-gray-600">
                        Regions: {gateway.regions.join(', ')}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {getHealthIcon(status.health_check[gateway.id])}
                    
                    <Button 
                      variant="outline"
                      size="sm"
                      onClick={() => handleCheckout(gateway.id)}
                      disabled={!gateway.enabled || !status.health_check[gateway.id] || processing === gateway.id}
                    >
                      {processing === gateway.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        'Select'
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
      </div>

      {/* Fallback Information */}
      <Alert>
        <Globe className="h-4 w-4" />
        <AlertDescription>
          <strong>Automatic Fallback:</strong> If your preferred payment method fails, 
          we'll automatically try alternative gateways to ensure your payment goes through.
        </AlertDescription>
      </Alert>

      {/* Gateway Status */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Gateway Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {status.gateways.map((gateway) => (
              <div key={gateway.id} className="flex items-center justify-between text-sm">
                <span>{gateway.name}</span>
                <div className="flex items-center gap-2">
                  {getHealthIcon(status.health_check[gateway.id])}
                  <Badge variant={gateway.enabled ? "default" : "secondary"}>
                    {gateway.enabled ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
          <div className="text-xs text-gray-500 mt-2">
            Last checked: {new Date(status.timestamp).toLocaleTimeString()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
