/**
 * Team Management API Routes
 * Enterprise team management functionality
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import {
  getTeamMembers,
  inviteTeamMember,
  updateTeamMemberRole,
  removeTeamMember,
  checkWorkspaceLimits,
} from '@/lib/team-management';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/team
 * Get team members for organization
 */
export async function GET(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      const { searchParams } = new URL(req.url);
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const organizationId = searchParams.get('organization_id') || user.id;

      // Team management requires Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'Team management requires Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const result = await getTeamMembers(organizationId, context.userId);

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: result.error === 'Access denied' ? 403 : 500 }
        );
      }

      return NextResponse.json({
        success: true,
        members: result.members,
      });

    } catch (error) {
      console.error('Team API error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to fetch team members' },
        { status: 500 }
      );
    }
  });
}

/**
 * POST /api/team
 * Invite team member
 */
export async function POST(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      const body = await req.json();
      const { email, role, organization_id } = body;

      if (!email || !role) {
        return NextResponse.json(
          { error: 'Email and role are required' },
          { status: 400 }
        );
      }

      if (!['admin', 'member'].includes(role)) {
        return NextResponse.json(
          { error: 'Invalid role. Must be admin or member' },
          { status: 400 }
        );
      }

      // Team management requires Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'Team invitations require Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const organizationId = organization_id || user.id;

      const result = await inviteTeamMember(
        organizationId,
        context.userId,
        email,
        role
      );

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }

      return NextResponse.json({
        success: true,
        invitation: result.invitation,
        message: `Invitation sent to ${email}`,
      });

    } catch (error) {
      console.error('Team invite error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to invite team member' },
        { status: 500 }
      );
    }
  });
}

/**
 * PUT /api/team
 * Update team member role
 */
export async function PUT(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      const body = await req.json();
      const { user_id, role, organization_id } = body;

      if (!user_id || !role) {
        return NextResponse.json(
          { error: 'User ID and role are required' },
          { status: 400 }
        );
      }

      if (!['admin', 'member'].includes(role)) {
        return NextResponse.json(
          { error: 'Invalid role. Must be admin or member' },
          { status: 400 }
        );
      }

      // Team management requires Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'Team management requires Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const organizationId = organization_id || user.id;

      const result = await updateTeamMemberRole(
        organizationId,
        user_id,
        role,
        context.userId
      );

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: result.error === 'Insufficient permissions' ? 403 : 400 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Team member role updated successfully',
      });

    } catch (error) {
      console.error('Team role update error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to update team member role' },
        { status: 500 }
      );
    }
  });
}

/**
 * DELETE /api/team
 * Remove team member
 */
export async function DELETE(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      const { searchParams } = new URL(req.url);
      const userId = searchParams.get('user_id');
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const organizationId = searchParams.get('organization_id') || user.id;

      if (!userId) {
        return NextResponse.json(
          { error: 'User ID is required' },
          { status: 400 }
        );
      }

      // Team management requires Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'Team management requires Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const result = await removeTeamMember(
        organizationId,
        userId,
        context.userId
      );

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: result.error === 'Insufficient permissions' ? 403 : 400 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Team member removed successfully',
      });

    } catch (error) {
      console.error('Team member removal error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to remove team member' },
        { status: 500 }
      );
    }
  });
}
