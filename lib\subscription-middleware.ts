/**
 * Subscription Middleware
 * Validates subscription tiers and enforces usage limits
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getUserSubscription } from '@/lib/subscription-service';
import { canUseModelForTier, getModelForSubscriptionTier } from '@/lib/ai-models';

// Live SaaS pricing plans
const PRICING_PLANS = {
  FREE: {
    name: 'Free',
    features: [
      '10 summaries per month',
      'DeepSeek R1 AI model',
      'Basic Slack integration',
      'Email support',
      'Standard templates'
    ],
    limits: {
      monthlySummaries: 10,
      aiModels: ['deepseek-r1'],
    },
  },
  PRO: {
    name: 'Pro',
    features: [
      '100 summaries per month',
      'All AI models (GPT-4o-mini, Claude)',
      'Advanced Slack integration',
      'Priority support',
      'Custom templates',
      'Basic analytics'
    ],
    limits: {
      monthlySummaries: 100,
      aiModels: ['deepseek-r1', 'gpt-4o-mini', 'claude-3-haiku'],
    },
  },
  ENTERPRISE: {
    name: 'Enterprise',
    features: [
      'Unlimited summaries',
      'All AI models + premium',
      'Full Slack automation',
      '24/7 support',
      'Custom integrations',
      'Advanced analytics',
      'Audit logs',
      'SSO integration'
    ],
    limits: {
      monthlySummaries: -1, // Unlimited
      aiModels: ['deepseek-r1', 'gpt-4o-mini', 'claude-3-haiku', 'gpt-4o', 'claude-3-opus'],
    },
  }
};

export interface SubscriptionContext {
  userId: string;
  subscription: {
    tier: 'FREE' | 'PRO' | 'ENTERPRISE';
    status: string;
    monthly_summaries_used: number;
    monthly_summary_limit: number;
    can_create_summary: boolean;
  };
  plan: {
    name: string;
    features: string[];
    limits: {
      monthlySummaries: number;
      aiModels: string[];
    };
  };
}

/**
 * Middleware to check subscription tier and usage limits
 */
export async function withSubscriptionCheck(
  request: NextRequest,
  handler: (req: NextRequest, context: SubscriptionContext) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Get authenticated user from Clerk
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user subscription
    const subscription = await getUserSubscription(userId);
    if (!subscription) {
      return NextResponse.json(
        { error: 'Unable to determine subscription status' },
        { status: 500 }
      );
    }

    const plan = PRICING_PLANS[subscription.subscription_tier];
    const canCreateSummary = subscription.monthly_summary_limit === -1 || 
      subscription.monthly_summaries_used < subscription.monthly_summary_limit;

    const context: SubscriptionContext = {
      userId,
      subscription: {
        tier: subscription.subscription_tier,
        status: subscription.status,
        monthly_summaries_used: subscription.monthly_summaries_used,
        monthly_summary_limit: subscription.monthly_summary_limit,
        can_create_summary: canCreateSummary,
      },
      plan: {
        name: plan.name,
        features: [...plan.features],
        limits: {
          ...plan.limits,
          aiModels: [...plan.limits.aiModels],
        },
      },
    };

    return await handler(request, context);

  } catch (error) {
    console.error('Subscription middleware error:', error);
    return NextResponse.json(
      { error: 'Subscription validation failed' },
      { status: 500 }
    );
  }
}

/**
 * Check if user can use a specific feature
 */
export function canUseFeature(
  context: SubscriptionContext,
  feature: string
): boolean {
  return context.plan.features.includes(feature);
}

/**
 * Check if user can create summaries (usage limit)
 */
export function canCreateSummary(context: SubscriptionContext): boolean {
  return context.subscription.can_create_summary;
}

/**
 * Check if user can use a specific AI model
 */
export function canUseAIModel(
  context: SubscriptionContext,
  modelId: string
): boolean {
  return canUseModelForTier(context.subscription.tier, modelId);
}

/**
 * Get the best AI model for user's subscription tier
 */
export function getBestAIModel(
  context: SubscriptionContext,
  preferredModel?: string
): string {
  return getModelForSubscriptionTier(context.subscription.tier, preferredModel);
}

/**
 * Validate AI model request
 */
export function validateAIModelRequest(
  context: SubscriptionContext,
  requestedModel?: string
): {
  valid: boolean;
  model: string;
  error?: string;
  upgradePrompt?: {
    message: string;
    requiredTier: string;
    features: string[];
  };
} {
  // If no model requested, use the best model for their tier
  if (!requestedModel) {
    return {
      valid: true,
      model: getBestAIModel(context),
    };
  }

  // Check if user can use the requested model
  if (canUseAIModel(context, requestedModel)) {
    return {
      valid: true,
      model: requestedModel,
    };
  }

  // User can't use the requested model, provide upgrade prompt
  const bestModel = getBestAIModel(context);
  const requiredTier = getRequiredTierForModel(requestedModel);
  const requiredPlan = requiredTier ? DEMO_PRICING_PLANS[requiredTier] : null;

  return {
    valid: false,
    model: bestModel,
    error: `${requestedModel} requires ${requiredTier} subscription`,
    upgradePrompt: requiredPlan && requiredTier ? {
      message: `Upgrade to ${requiredPlan.name} to use ${requestedModel}`,
      requiredTier,
      features: [...requiredPlan.features],
    } : undefined,
  };
}

/**
 * Get required tier for a specific AI model
 */
function getRequiredTierForModel(modelId: string): 'FREE' | 'PRO' | 'ENTERPRISE' | null {
  // This would typically come from the AI_MODELS configuration
  const modelTierMap: Record<string, 'FREE' | 'PRO' | 'ENTERPRISE'> = {
    'deepseek-r1': 'FREE',
    'gpt-4o-mini': 'PRO',
    'gpt-4o': 'PRO',
    'claude-3-5-sonnet': 'ENTERPRISE',
  };

  return modelTierMap[modelId] || null;
}

/**
 * Usage limit enforcement middleware
 */
export async function withUsageLimitCheck(
  request: NextRequest,
  handler: (req: NextRequest, context: SubscriptionContext) => Promise<NextResponse>
): Promise<NextResponse> {
  return withSubscriptionCheck(request, async (req, context) => {
    // Check if user can create summaries
    if (!canCreateSummary(context)) {
      return NextResponse.json(
        {
          error: 'Monthly summary limit reached',
          usage: {
            used: context.subscription.monthly_summaries_used,
            limit: context.subscription.monthly_summary_limit,
            tier: context.subscription.tier,
          },
          upgradePrompt: {
            message: 'Upgrade your plan to create more summaries',
            requiredTier: context.subscription.tier === 'FREE' ? 'PRO' : 'ENTERPRISE',
            features: context.subscription.tier === 'FREE'
              ? DEMO_PRICING_PLANS.PRO.features
              : DEMO_PRICING_PLANS.ENTERPRISE.features,
          },
        },
        { status: 403 }
      );
    }

    return await handler(req, context);
  });
}

/**
 * Feature access middleware
 */
export function withFeatureCheck(feature: string) {
  return async (
    request: NextRequest,
    handler: (req: NextRequest, context: SubscriptionContext) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    return withSubscriptionCheck(request, async (req, context) => {
      // Check if user can use the feature
      if (!canUseFeature(context, feature)) {
        const requiredTier = getRequiredTierForFeature(feature);
        const requiredPlan = requiredTier ? DEMO_PRICING_PLANS[requiredTier] : null;

        return NextResponse.json(
          {
            error: `Feature '${feature}' requires ${requiredTier} subscription`,
            upgradePrompt: requiredPlan ? {
              message: `Upgrade to ${requiredPlan.name} to use ${feature}`,
              requiredTier,
              features: requiredPlan.features,
            } : undefined,
          },
          { status: 403 }
        );
      }

      return await handler(req, context);
    });
  };
}

/**
 * Get required tier for a specific feature
 */
function getRequiredTierForFeature(feature: string): 'FREE' | 'PRO' | 'ENTERPRISE' | null {
  const featureTierMap: Record<string, 'FREE' | 'PRO' | 'ENTERPRISE'> = {
    'Smart Tagging': 'PRO',
    'Auto-posting to Slack': 'PRO',
    'CRM Integration': 'PRO',
    'Advanced Analytics': 'PRO',
    'Team Management': 'ENTERPRISE',
    'SSO Integration': 'ENTERPRISE',
    'Audit Logs': 'ENTERPRISE',
    'Priority Support': 'ENTERPRISE',
  };

  return featureTierMap[feature] || null;
}

/**
 * AI model validation middleware
 */
export function withAIModelValidation() {
  return async (
    request: NextRequest,
    handler: (req: NextRequest, context: SubscriptionContext & { aiModel: string }) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    return withSubscriptionCheck(request, async (req, context) => {
      const body = await req.json();
      const requestedModel = body.model || body.ai_model || body.preferredModel;

      const validation = validateAIModelRequest(context, requestedModel);

      if (!validation.valid && validation.error) {
        return NextResponse.json(
          {
            error: validation.error,
            suggested_model: validation.model,
            upgrade_prompt: validation.upgradePrompt,
          },
          { status: 403 }
        );
      }

      // Add the validated model to context
      const enhancedContext = {
        ...context,
        aiModel: validation.model,
      };

      return await handler(req, enhancedContext);
    });
  };
}
