/**
 * Demo Status API
 * Returns demo usage status and trial information
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { 
  getDemoStatus, 
  initializeDemoMode,
  shouldShowUpgradePrompt,
  recordUpgradePromptShown 
} from '@/lib/demo-mode';
import { getDemoStatusForAPI } from '@/lib/demo-middleware';
import { logAuditEvent } from '@/lib/audit-logger';

export async function GET(request: NextRequest) {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get comprehensive demo status
    const demoStatus = await getDemoStatus(userId);
    const apiStatus = await getDemoStatusForAPI(userId);
    const upgradePrompt = await shouldShowUpgradePrompt(userId);

    // Log demo status check
    await logAuditEvent({
      event_type: 'DEMO_STATUS_CHECK',
      user_id: userId,
      action: 'Demo status checked',
      resource_type: 'demo_usage',
      resource_id: userId,
      metadata: {
        is_demo: demoStatus.isInDemo,
        trial_days_remaining: demoStatus.trialDaysRemaining,
        trial_expired: demoStatus.trialExpired
      }
    });

    return NextResponse.json({
      success: true,
      demo_status: {
        is_in_demo: demoStatus.isInDemo,
        trial_days_remaining: demoStatus.trialDaysRemaining,
        trial_expired: demoStatus.trialExpired,
        usage: demoStatus.usage,
        features: {
          basic_summaries: demoStatus.canUseFeature('basicSummaries'),
          file_upload: demoStatus.canUseFeature('fileUpload'),
          slack_integration: demoStatus.canUseFeature('slackIntegration'),
          basic_exports: demoStatus.canUseFeature('basicExports'),
          dashboard: demoStatus.canUseFeature('dashboard'),
          analytics: demoStatus.canUseFeature('analytics'),
          custom_templates: demoStatus.canUseFeature('customTemplates'),
          team_management: demoStatus.canUseFeature('teamManagement'),
          advanced_ai: demoStatus.canUseFeature('advancedAI')
        },
        upgrade_prompt: {
          should_show: upgradePrompt.show,
          message: upgradePrompt.message,
          reason: upgradePrompt.reason,
          urgency: upgradePrompt.urgency
        }
      },
      api_status: apiStatus
    });

  } catch (error) {
    console.error('Error getting demo status:', error);
    
    // Log error
    const { userId } = auth();
    if (userId) {
      await logAuditEvent({
        event_type: 'DEMO_STATUS_ERROR',
        user_id: userId,
        action: 'Demo status check failed',
        resource_type: 'demo_usage',
        resource_id: userId,
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }

    return NextResponse.json(
      { 
        error: 'Failed to get demo status',
        demo_status: {
          is_in_demo: false,
          trial_days_remaining: 0,
          trial_expired: false,
          usage: {
            summaries: { used: 0, limit: -1, remaining: -1 },
            exports: { used: 0, limit: -1, remaining: -1 },
            aiRequests: { used: 0, limit: -1, remaining: -1 },
            fileUploads: { used: 0, limit: -1, remaining: -1 },
            slackConnections: { used: 0, limit: -1, remaining: -1 }
          },
          features: {
            basic_summaries: true,
            file_upload: true,
            slack_integration: true,
            basic_exports: true,
            dashboard: true,
            analytics: true,
            custom_templates: true,
            team_management: true,
            advanced_ai: true
          },
          upgrade_prompt: {
            should_show: false,
            message: '',
            reason: '',
            urgency: 'low'
          }
        }
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'initialize':
        // Initialize demo mode for new user
        const demoUsage = await initializeDemoMode(userId);
        
        if (!demoUsage) {
          return NextResponse.json(
            { error: 'Failed to initialize demo mode' },
            { status: 500 }
          );
        }

        await logAuditEvent({
          event_type: 'DEMO_INITIALIZED',
          user_id: userId,
          action: 'Demo mode initialized',
          resource_type: 'demo_usage',
          resource_id: demoUsage.id,
          metadata: {
            trial_expires_at: demoUsage.trial_expires_at
          }
        });

        return NextResponse.json({
          success: true,
          message: 'Demo mode initialized',
          demo_usage: demoUsage
        });

      case 'record_upgrade_prompt':
        // Record that upgrade prompt was shown
        await recordUpgradePromptShown(userId);
        
        await logAuditEvent({
          event_type: 'UPGRADE_PROMPT_SHOWN',
          user_id: userId,
          action: 'Upgrade prompt shown to user',
          resource_type: 'demo_usage',
          resource_id: userId,
          metadata: {
            timestamp: new Date().toISOString()
          }
        });

        return NextResponse.json({
          success: true,
          message: 'Upgrade prompt recorded'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error handling demo status action:', error);
    
    const { userId } = auth();
    if (userId) {
      await logAuditEvent({
        event_type: 'DEMO_ACTION_ERROR',
        user_id: userId,
        action: 'Demo action failed',
        resource_type: 'demo_usage',
        resource_id: userId,
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }

    return NextResponse.json(
      { error: 'Failed to process demo action' },
      { status: 500 }
    );
  }
}
