{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env.local", ".env.production"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".eslintrc.js", ".eslintrc.json", "eslint.config.js"]}, "type-check": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "tsconfig.json"]}, "test": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "vitest.config.ts", "jest.config.js"], "outputs": ["coverage/**"]}, "test:unit": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "vitest.config.ts"], "outputs": ["coverage/**"]}, "test:integration": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "vitest.config.ts"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "playwright.config.ts"], "outputs": ["playwright-report/**", "test-results/**"]}, "clean": {"cache": false}, "format": {"inputs": ["$TURBO_DEFAULT$", ".prettier<PERSON>", ".prettierrc.json", "prettier.config.js"]}, "format:check": {"inputs": ["$TURBO_DEFAULT$", ".prettier<PERSON>", ".prettierrc.json", "prettier.config.js"]}, "audit": {"inputs": ["$TURBO_DEFAULT$", "scripts/audit.js"]}, "db:migrate": {"cache": false}, "db:seed": {"cache": false}, "deploy": {"dependsOn": ["build", "test", "lint"], "cache": false}}, "globalDependencies": ["package.json", "turbo.json", ".env", ".env.local", ".env.production"], "globalEnv": ["NODE_ENV", "VERCEL", "VERCEL_ENV", "CI", "NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY", "NEXTAUTH_SECRET", "NEXTAUTH_URL", "STRIPE_SECRET_KEY", "STRIPE_WEBHOOK_SECRET", "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY", "OPENAI_API_KEY", "DEEPSEEK_API_KEY", "RESEND_API_KEY", "SENTRY_DSN", "NEXT_PUBLIC_POSTHOG_KEY", "NEXT_PUBLIC_POSTHOG_HOST"]}