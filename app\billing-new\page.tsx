'use client';

import { useEffect, useState } from 'react';
import { useAuth, useUser } from '@clerk/nextjs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CreditCard, 
  Calendar, 
  DollarSign, 
  Settings, 
  Download,
  AlertCircle,
  CheckCircle,
  Clock,
  Shield,
  Zap,
  Users,
  BarChart3,
  ExternalLink,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';

interface Subscription {
  id: string;
  subscription_tier: string;
  status: string;
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  stripe_customer_id?: string;
  stripe_subscription_id?: string;
  monthly_summary_limit: number;
  monthly_summaries_used: number;
  payment_reference?: string;
}

interface PlanFeatures {
  [key: string]: {
    name: string;
    price: number;
    features: string[];
    limits: {
      summaries: number;
      exports: number;
      integrations: number;
    };
  };
}

const PLAN_FEATURES: PlanFeatures = {
  FREE: {
    name: 'Free',
    price: 0,
    features: ['Basic AI summaries', 'Email notifications', 'Standard support'],
    limits: { summaries: 10, exports: 5, integrations: 1 }
  },
  PRO: {
    name: 'Pro',
    price: 29,
    features: ['Advanced AI models', 'Priority support', 'Team collaboration', 'Custom integrations'],
    limits: { summaries: 100, exports: 50, integrations: 5 }
  },
  ENTERPRISE: {
    name: 'Enterprise',
    price: 99,
    features: ['Unlimited AI summaries', '24/7 support', 'Advanced analytics', 'Custom branding', 'SSO'],
    limits: { summaries: 1000, exports: 500, integrations: 20 }
  }
};

export default function BillingPage() {
  const { isLoaded, isSignedIn, userId } = useAuth();
  const { user } = useUser();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    if (isLoaded && isSignedIn) {
      fetchSubscription();
    }
  }, [isLoaded, isSignedIn]);

  const fetchSubscription = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/subscription');
      
      if (response.ok) {
        const data = await response.json();
        setSubscription(data.subscription);
      } else {
        console.error('Failed to fetch subscription');
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
      toast.error('Failed to load subscription details');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (planId: string) => {
    try {
      setActionLoading(true);
      
      const response = await fetch('/api/payments/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan_id: planId,
          success_url: `${window.location.origin}/billing?payment=success`,
          cancel_url: `${window.location.origin}/billing?payment=cancelled`,
        }),
      });

      const data = await response.json();

      if (data.checkout_url) {
        window.location.href = data.checkout_url;
      } else {
        toast.error(data.error || 'Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error creating checkout:', error);
      toast.error('Failed to start upgrade process');
    } finally {
      setActionLoading(false);
    }
  };

  const handleManageBilling = async () => {
    try {
      setActionLoading(true);
      
      const response = await fetch('/api/stripe/portal', {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        window.location.href = data.url;
      } else {
        toast.error('Failed to open billing portal');
      }
    } catch (error) {
      console.error('Error opening billing portal:', error);
      toast.error('Failed to open billing portal');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Active</Badge>;
      case 'cancelled':
        return <Badge variant="destructive"><AlertCircle className="h-3 w-3 mr-1" />Cancelled</Badge>;
      case 'past_due':
        return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />Past Due</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getUsagePercentage = () => {
    if (!subscription) return 0;
    return Math.round((subscription.monthly_summaries_used / subscription.monthly_summary_limit) * 100);
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading billing information...</p>
        </div>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>Please sign in to view your billing information.</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <a href="/sign-in">Sign In</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentPlan = subscription ? PLAN_FEATURES[subscription.subscription_tier] : PLAN_FEATURES.FREE;
  const usagePercentage = getUsagePercentage();

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <CreditCard className="h-8 w-8 text-blue-600" />
            Billing & Subscription
          </h1>
          <p className="mt-2 text-gray-600">
            Manage your subscription, view usage, and update billing information
          </p>
        </div>

        {/* Current Subscription */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Current Plan
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchSubscription}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
                </div>
              ) : subscription ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-semibold">{currentPlan.name} Plan</h3>
                      <p className="text-gray-600">
                        ${currentPlan.price}/month
                      </p>
                    </div>
                    {getStatusBadge(subscription.status)}
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Current Period</p>
                      <p className="text-sm">
                        {new Date(subscription.current_period_start).toLocaleDateString()} - {' '}
                        {new Date(subscription.current_period_end).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Payment Reference</p>
                      <p className="text-sm font-mono">{subscription.payment_reference || 'N/A'}</p>
                    </div>
                  </div>

                  {subscription.cancel_at_period_end && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Your subscription will be cancelled at the end of the current billing period.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600 mb-4">No active subscription found</p>
                  <Button onClick={() => handleUpgrade('pro')}>
                    Start Pro Trial
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Usage Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Usage This Month
              </CardTitle>
            </CardHeader>
            <CardContent>
              {subscription ? (
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>AI Summaries</span>
                      <span>{subscription.monthly_summaries_used} / {subscription.monthly_summary_limit}</span>
                    </div>
                    <Progress value={usagePercentage} className="h-2" />
                    {usagePercentage > 80 && (
                      <p className="text-sm text-amber-600 mt-1">
                        You're approaching your monthly limit
                      </p>
                    )}
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Exports</span>
                      <span>Available</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Integrations</span>
                      <span>{currentPlan.limits.integrations}</span>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-gray-600">No usage data available</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Actions */}
        <div className="flex flex-wrap gap-4 mb-8">
          {subscription?.stripe_customer_id && (
            <Button
              onClick={handleManageBilling}
              disabled={actionLoading}
              variant="outline"
            >
              <Settings className="h-4 w-4 mr-2" />
              Manage Billing
            </Button>
          )}
          
          {(!subscription || subscription.subscription_tier === 'FREE') && (
            <Button
              onClick={() => handleUpgrade('pro')}
              disabled={actionLoading}
            >
              <Zap className="h-4 w-4 mr-2" />
              Upgrade to Pro
            </Button>
          )}
          
          {subscription?.subscription_tier === 'PRO' && (
            <Button
              onClick={() => handleUpgrade('enterprise')}
              disabled={actionLoading}
            >
              <Shield className="h-4 w-4 mr-2" />
              Upgrade to Enterprise
            </Button>
          )}
        </div>

        {/* Plan Comparison */}
        <Card>
          <CardHeader>
            <CardTitle>Available Plans</CardTitle>
            <CardDescription>
              Choose the plan that best fits your needs
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Object.entries(PLAN_FEATURES).map(([planId, plan]) => (
                <div
                  key={planId}
                  className={`border rounded-lg p-6 ${
                    subscription?.subscription_tier === planId
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200'
                  }`}
                >
                  <div className="text-center mb-4">
                    <h3 className="text-lg font-semibold">{plan.name}</h3>
                    <p className="text-2xl font-bold">
                      ${plan.price}
                      <span className="text-sm font-normal text-gray-600">/month</span>
                    </p>
                  </div>
                  
                  <ul className="space-y-2 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <div className="text-center">
                    {subscription?.subscription_tier === planId ? (
                      <Badge className="w-full justify-center">Current Plan</Badge>
                    ) : (
                      <Button
                        className="w-full"
                        variant={planId === 'PRO' ? 'default' : 'outline'}
                        onClick={() => handleUpgrade(planId.toLowerCase())}
                        disabled={actionLoading || planId === 'FREE'}
                      >
                        {planId === 'FREE' ? 'Free Forever' : `Upgrade to ${plan.name}`}
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
