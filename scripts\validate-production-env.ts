#!/usr/bin/env node

/**
 * Production Environment Validation Script
 * Validates all required environment variables for production deployment
 */

const { config } = require('dotenv');

// Load environment variables
config({ path: '.env.local' });

const validateEnvironmentVariables = () => {
  const categories = [
    {
      name: 'Core Application',
      critical: true,
      variables: [
        validateVar('NODE_ENV', true, (val) => ['development', 'production'].includes(val || '')),
        validateVar('NEXT_PUBLIC_SITE_URL', true, (val) => isValidUrl(val)),
        validateVar('NEXT_PUBLIC_APP_URL', true, (val) => isValidUrl(val)),
        validateVar('NEXT_PUBLIC_ENVIRONMENT', true, (val) => ['development', 'production'].includes(val || '')),
      ]
    },
    {
      name: 'Authentication (Public Demo Mode)',
      critical: false,
      variables: [
        // Authentication removed - Public Demo Mode
      ]
    },
    {
      name: 'Database (Supabase)',
      critical: true,
      variables: [
        validateVar('NEXT_PUBLIC_SUPABASE_URL', true, (val) => isValidUrl(val) && (val?.includes('supabase.co') || false)),
        validateVar('NEXT_PUBLIC_SUPABASE_ANON_KEY', true, (val) => isValidJWT(val)),
        validateVar('SUPABASE_SERVICE_ROLE_KEY', true, (val) => isValidJWT(val)),
        validateVar('DATABASE_URL', true, (val) => val?.startsWith('postgresql://') || false),
      ]
    },
    {
      name: 'AI Services',
      critical: true,
      variables: [
        validateVar('OPENROUTER_API_KEY', true, (val) => val?.startsWith('sk-or-v1-') || false),
      ]
    },
    {
      name: 'Slack Integration',
      critical: false,
      variables: [
        validateVar('SLACK_CLIENT_ID', false, (val) => !!(val && val.length > 10)),
        validateVar('SLACK_CLIENT_SECRET', false, (val) => !!(val && val.length > 10)),
        validateVar('SLACK_SIGNING_SECRET', false, (val) => !!(val && val.length > 10)),
      ]
    },
    {
      name: 'Error Tracking & Analytics',
      critical: false,
      variables: [
        validateVar('NEXT_PUBLIC_SENTRY_DSN', false, (val) => isValidUrl(val)),
        validateVar('NEXT_PUBLIC_POSTHOG_KEY', false, (val) => val?.startsWith('phc_') || false),
      ]
    },
    {
      name: 'Email & Notifications',
      critical: false,
      variables: [
        validateVar('RESEND_API_KEY', false, (val) => val?.startsWith('re_') || false),
        validateVar('EMAIL_FROM', false, (val) => isValidEmail(val)),
      ]
    }
  ];

  return categories;
};

const validateVar = (key: string, required: boolean, validator?: (val: string | undefined) => boolean) => {
  const value = process.env[key];
  const issues = [];
  let valid = true;

  if (required && !value) {
    issues.push('Missing required environment variable');
    valid = false;
  }

  if (value && validator && !validator(value)) {
    issues.push('Invalid format or value');
    valid = false;
  }

  // Check for demo/test values
  if (value && isDemoValue(value)) {
    issues.push('Using demo/test value - replace with production value');
    valid = false;
  }

  return { key, value, required, valid, issues };
};

const isValidUrl = (url: string | undefined): boolean => {
  if (!url) return false;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

const isValidEmail = (email: string | undefined): boolean => {
  if (!email) return false;
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};

const isValidJWT = (token: string | undefined): boolean => {
  if (!token) return false;
  return token.split('.').length === 3;
};

const isValidClerkKey = (key: string | undefined, prefix: string): boolean => {
  if (!key) return false;
  return key.startsWith(prefix) && !isDemoValue(key);
};

const isDemoValue = (value: string | undefined): boolean => {
  const demoPatterns = [
    'demo',
    'test',
    'example',
    'your_key_here',
    'Y2xlcmstZGVtby0xMjM0NTY3ODkw', // Base64 encoded "clerk-demo-1234567890"
    'pk_test_Y2xlcmstZGVtby0',
    'sk_test_Y2xlcmstZGVtby0'
  ];
  
  return demoPatterns.some(pattern => value?.toLowerCase().includes(pattern.toLowerCase()) || false);
};

const main = () => {
  console.log('🔍 Validating Production Environment Variables...\n');

  const categories = validateEnvironmentVariables();
  let totalIssues = 0;
  let criticalIssues = 0;

  categories.forEach(category => {
    const categoryIssues = category.variables.filter(v => !v.valid).length;
    const statusIcon = categoryIssues === 0 ? '✅' : (category.critical ? '❌' : '⚠️');
    
    console.log(`${statusIcon} ${category.name} (${categoryIssues} issues)`);
    
    category.variables.forEach(variable => {
      const icon = variable.valid ? '  ✓' : '  ✗';
      const status = variable.required ? 'REQUIRED' : 'OPTIONAL';
      const maskedValue = variable.value ? maskSensitiveValue(variable.value) : 'NOT SET';
      
      console.log(`${icon} ${variable.key} (${status}): ${maskedValue}`);
      
      if (!variable.valid) {
        variable.issues.forEach(issue => {
          console.log(`    → ${issue}`);
        });
        totalIssues++;
        if (variable.required && category.critical) {
          criticalIssues++;
        }
      }
    });
    
    console.log('');
  });

  console.log('📊 Summary:');
  console.log(`   Total Issues: ${totalIssues}`);
  console.log(`   Critical Issues: ${criticalIssues}`);
  
  if (criticalIssues > 0) {
    console.log('\n❌ PRODUCTION DEPLOYMENT BLOCKED');
    console.log('   Critical environment variables must be configured before deployment.');
    process.exit(1);
  } else if (totalIssues > 0) {
    console.log('\n⚠️  PRODUCTION DEPLOYMENT WITH WARNINGS');
    console.log('   Some optional features may not work properly.');
    process.exit(0);
  } else {
    console.log('\n✅ PRODUCTION DEPLOYMENT READY');
    console.log('   All environment variables are properly configured.');
    process.exit(0);
  }
};

const maskSensitiveValue = (value: string | undefined): string => {
  if (!value) return '(undefined)';
  if (value.length <= 8) {
    return '*'.repeat(value.length);
  }
  return value.substring(0, 4) + '*'.repeat(value.length - 8) + value.substring(value.length - 4);
};

if (require.main === module) {
  main();
}
