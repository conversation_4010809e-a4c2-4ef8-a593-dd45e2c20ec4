/**
 * Fetch Utilities with <PERSON><PERSON> and <PERSON>rror Handling
 * 
 * Provides robust API request handling with exponential backoff,
 * timeout management, and comprehensive error handling
 */

import { useState, useCallback, useRef } from 'react';

export interface FetchOptions extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  retryMultiplier?: number;
}

export interface FetchResult<T = any> {
  data: T | null;
  error: string | null;
  isLoading: boolean;
  responseTime: number | null;
  requestId: string | null;
}

/**
 * Enhanced fetch with retry logic and timeout handling
 */
export async function fetchWithRetry<T = any>(
  url: string, 
  options: FetchOptions = {}
): Promise<T> {
  const {
    timeout = 15000,
    retries = 3,
    retryDelay = 1000,
    retryMultiplier = 2,
    ...fetchOptions
  } = options;

  const requestId = Math.random().toString(36).substring(7);
  const startTime = Date.now();

  for (let attempt = 0; attempt <= retries; attempt++) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...fetchOptions,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': requestId,
          ...fetchOptions.headers,
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const responseTime = Date.now() - startTime;

      console.log(`✅ Request ${requestId} completed in ${responseTime}ms`);
      return data;

    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        console.warn(`⏰ Request ${requestId} timed out after ${timeout}ms (attempt ${attempt + 1})`);
      } else {
        console.error(`❌ Request ${requestId} failed (attempt ${attempt + 1}):`, error);
      }

      // Don't retry on the last attempt
      if (attempt === retries) {
        throw error;
      }

      // Wait before retrying
      const delay = retryDelay * Math.pow(retryMultiplier, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw new Error('Max retries exceeded');
}

/**
 * Hook for dashboard data fetching with loading states
 */
export function useDashboardFetch() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSlowLoading, setIsSlowLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRequestId, setLastRequestId] = useState<string | null>(null);
  const [responseTime, setResponseTime] = useState<number | null>(null);

  const fetchDashboard = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setIsSlowLoading(false);

    // Show slow loading indicator after 5 seconds
    const slowLoadingTimer = setTimeout(() => {
      setIsSlowLoading(true);
    }, 5000);

    try {
      const startTime = Date.now();
      const data = await fetchWithRetry('/api/dashboard', {
        timeout: parseInt(process.env.NEXT_PUBLIC_FETCH_TIMEOUT || '15000'),
        retries: 2,
      });

      const responseTime = Date.now() - startTime;
      setResponseTime(responseTime);
      setLastRequestId(data.requestId || null);

      return data;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load dashboard data';
      setError(errorMessage);
      console.error('Dashboard fetch error:', err);
      return null;

    } finally {
      clearTimeout(slowLoadingTimer);
      setIsLoading(false);
      setIsSlowLoading(false);
    }
  }, []);

  const retry = useCallback(() => {
    return fetchDashboard();
  }, [fetchDashboard]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    fetchDashboard,
    retry,
    isLoading,
    isSlowLoading,
    error,
    lastRequestId,
    responseTime,
    clearError,
  };
}

/**
 * Hook for summary creation with progress tracking
 */
export function useSummaryCreation() {
  const [isCreating, setIsCreating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const createSummary = useCallback(async (transcriptText: string, userId: string) => {
    setIsCreating(true);
    setError(null);
    setProgress(0);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 500);

      const data = await fetchWithRetry('/api/summarize', {
        method: 'POST',
        body: JSON.stringify({
          transcriptText: transcriptText.trim(),
          userId,
          context: {
            source: 'manual',
            timestamp: new Date().toISOString(),
          }
        }),
        timeout: 30000, // Longer timeout for AI processing
        retries: 1, // Fewer retries for expensive operations
      });

      clearInterval(progressInterval);
      setProgress(100);

      return data;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create summary';
      setError(errorMessage);
      throw err;

    } finally {
      setIsCreating(false);
      setTimeout(() => setProgress(0), 1000); // Reset progress after delay
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    createSummary,
    isCreating,
    progress,
    error,
    clearError,
  };
}

/**
 * Generic API hook for any endpoint
 */
export function useApiRequest<T = any>() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<T | null>(null);

  const request = useCallback(async (url: string, options: FetchOptions = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await fetchWithRetry<T>(url, options);
      setData(result);
      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Request failed';
      setError(errorMessage);
      throw err;

    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setIsLoading(false);
  }, []);

  return {
    request,
    isLoading,
    error,
    data,
    clearError,
    reset,
  };
}



/**
 * Simple debounce utility
 */
function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * Request deduplication utility
 */
const requestCache = new Map<string, Promise<any>>();

export function deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
  if (requestCache.has(key)) {
    return requestCache.get(key)!;
  }

  const promise = requestFn().finally(() => {
    requestCache.delete(key);
  });

  requestCache.set(key, promise);
  return promise;
}

/**
 * Debounced fetch hook for preventing excessive API calls
 */
export function useDebouncedSubmit<T = any>(
  fetchFn: () => Promise<T>,
  delay: number = 300
) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isDebouncing, setIsDebouncing] = useState(false);

  const debouncedFetch = useCallback(async (): Promise<T | null> => {
    return new Promise((resolve) => {
      setIsDebouncing(true);

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(async () => {
        try {
          const result = await fetchFn();
          setIsDebouncing(false);
          resolve(result);
        } catch (error) {
          setIsDebouncing(false);
          throw error;
        }
      }, delay);
    });
  }, [fetchFn, delay]);

  const cancelDebounce = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
      setIsDebouncing(false);
    }
  }, []);

  return {
    debouncedFetch,
    cancelDebounce,
    isDebouncing
  };
}
