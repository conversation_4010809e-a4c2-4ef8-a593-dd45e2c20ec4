/**
 * Test AI directly with OpenRouter
 */

const testDirectAI = async () => {
  const apiKey = '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';
  
  try {
    console.log('🧪 Testing Direct AI with structured prompt...');
    
    const prompt = `You are an expert AI assistant specializing in content summarization. 
Analyze the following meeting content and provide a comprehensive summary.

Language: English
Content Type: meeting

Please provide your response in the following JSON format:
{
  "summary": "Main summary (2-3 paragraphs)",
  "keyPoints": ["Key point 1", "Key point 2"],
  "actionItems": ["Action 1", "Action 2"],
  "redFlags": ["Red flag 1", "Red flag 2"],
  "skills": ["Skill 1", "Skill 2"],
  "speakers": ["Speaker 1", "Speaker 2"],
  "confidence": 0.95
}

Requirements:
- Summary should be concise but comprehensive
- Extract 3-7 key points
- Identify actionable items and next steps
- Flag potential issues, risks, or concerns
- Identify skills, technologies, or competencies mentioned
- List speakers/participants if identifiable
- Provide confidence score (0-1)

Content to analyze:`;

    const content = `John: Hey team, I wanted to discuss our Q4 goals and the new product launch.
<PERSON>: Great! I've been working on the marketing strategy. We should focus on our target demographics.
Mike: From a technical perspective, we need to ensure our infrastructure can handle the increased load.
John: Excellent points. Sarah, can you prepare a detailed marketing plan by next Friday?
Sarah: Absolutely, I'll have it ready.
<PERSON>: I'll also run some load tests this week to identify any potential bottlenecks.
John: Perfect. Let's reconvene next Monday to review everything.`;
    
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Slack Summary Scribe'
      },
      body: JSON.stringify({
        model: 'tngtech/deepseek-r1t2-chimera:free',
        messages: [
          {
            role: 'system',
            content: prompt
          },
          {
            role: 'user',
            content: content
          }
        ],
        max_tokens: 4000,
        temperature: 0.7
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Direct AI Test PASSED');
      console.log('📝 Raw Response:', result.choices?.[0]?.message?.content || 'No content');
      
      // Try to parse JSON
      const aiText = result.choices?.[0]?.message?.content || '';
      try {
        const jsonMatch = aiText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const parsed = JSON.parse(jsonMatch[0]);
          console.log('✅ JSON Parsing successful');
          console.log('📊 Parsed Summary:', {
            summary: parsed.summary?.substring(0, 100) + '...',
            keyPoints: parsed.keyPoints?.length || 0,
            actionItems: parsed.actionItems?.length || 0,
            redFlags: parsed.redFlags?.length || 0,
            skills: parsed.skills?.length || 0,
            speakers: parsed.speakers?.length || 0,
            confidence: parsed.confidence
          });
        } else {
          console.log('⚠️ No JSON found in response');
        }
      } catch (parseError) {
        console.log('❌ JSON parsing failed:', parseError.message);
      }
    } else {
      const error = await response.text();
      console.log('❌ Direct AI Test FAILED:', response.status, error);
    }
    
  } catch (error) {
    console.log('❌ Direct AI Test ERROR');
    console.error('Error:', error);
  }
};

// Run the test
testDirectAI();
