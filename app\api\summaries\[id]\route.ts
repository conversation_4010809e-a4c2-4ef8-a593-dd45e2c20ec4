import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';

/**
 * GET /api/summaries/[id]
 * Get a specific summary by ID with Clerk authentication
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: summaryId } = await params;
  devLog.log('📄 Get Summary API called:', summaryId);

    if (!summaryId) {
      return NextResponse.json(
        { error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    // Get authenticated user from Clerk
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get Supabase client
    const supabase = createSupabaseServerClient();

    // Query summary from Supabase
    const { data: summary, error } = await supabase
      .from('summaries')
      .select('*')
      .eq('id', summaryId)
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching summary:', error);
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Summary not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to fetch summary' },
        { status: 500 }
      );
    }

    if (!summary) {
      return NextResponse.json(
        { error: 'Summary not found' },
        { status: 404 }
      );
    }
  devLog.log('📄 Returning summary:', summary.title);
    return NextResponse.json({
      success: true,
      summary: {
        id: summary.id,
        title: summary.title,
        content: summary.content,
        summary_data: summary.summary_data,
        source_type: summary.source_type,
        source_data: summary.metadata,
        ai_model: summary.metadata?.ai_model || 'deepseek-r1',
        quality_score: summary.confidence || 0.85,
        tags: summary.tags || [],
        created_at: summary.created_at,
        updated_at: summary.updated_at,
        user_id: summary.user_id
      }
    });


  } catch (error) {
    console.error('Get summary API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/summaries/[id]
 * Update a specific summary with Clerk authentication
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: summaryId } = await params;
  devLog.log('📄 Update Summary API called:', summaryId);

    const body = await request.json();
    const { title, content, tags } = body;

    if (!summaryId) {
      return NextResponse.json(
        { error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    // Get authenticated user from Clerk
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get Supabase client
    const supabase = createSupabaseServerClient();

    // Update summary in database
    const { data: updatedSummary, error } = await supabase
      .from('summaries')
      .update({
        title: title,
        content: content,
        tags: tags,
        updated_at: new Date().toISOString()
      })
      .eq('id', summaryId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating summary:', error);
      return NextResponse.json(
        { error: 'Failed to update summary' },
        { status: 500 }
      );
    }
  devLog.log('📄 Summary updated successfully:', summaryId);

    return NextResponse.json({
      success: true,
      message: 'Summary updated successfully',
      data: updatedSummary
    });

  } catch (error) {
    console.error('Update summary API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/summaries/[id]
 * Delete a specific summary with Clerk authentication
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: summaryId } = await params;
  devLog.log('📄 Delete Summary API called:', summaryId);

    if (!summaryId) {
      return NextResponse.json(
        { error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    // Get authenticated user from Clerk
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get Supabase client
    const supabase = createSupabaseServerClient();

    // Delete summary from database
    const { error } = await supabase
      .from('summaries')
      .delete()
      .eq('id', summaryId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error deleting summary:', error);
      return NextResponse.json(
        { error: 'Failed to delete summary' },
        { status: 500 }
      );
    }
  devLog.log('📄 Summary deleted successfully:', summaryId);

    return NextResponse.json({
      success: true,
      message: 'Summary deleted successfully'
    });

  } catch (error) {
    console.error('Delete summary API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
