'use client';

/**
 * Team Management Component
 * Enterprise team management with invitations and role control
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Users, UserPlus, Mail, Shield, Trash2, Crown, Settings, 
  Loader2, AlertCircle, CheckCircle, Clock 
} from 'lucide-react';
import { getCurrentUserClient } from '@/lib/user-management-client';
import { toast } from 'sonner';
import type { TeamMember, WorkspaceLimits } from '@/lib/team-management';

interface TeamManagementProps {
  className?: string;
}

export function TeamManagement({ className }: TeamManagementProps) {
  const [user, setUser] = useState<any>(null);
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [limits, setLimits] = useState<WorkspaceLimits | null>(null);
  const [loading, setLoading] = useState(true);
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'admin' | 'member'>('member');
  const [inviting, setInviting] = useState(false);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { getCurrentUserClient } = await import('@/lib/user-management-client');
        const currentUser = await getCurrentUserClient();
        setUser(currentUser);
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    };

    fetchUser();
  }, []);

  useEffect(() => {
    if (user) {
      fetchTeamData();
    }
  }, [user]);

  const fetchTeamData = async () => {
    try {
      setLoading(true);
      
      // Fetch team members and limits in parallel
      const [membersResponse, limitsResponse] = await Promise.all([
        fetch('/api/team'),
        fetch('/api/team/limits'),
      ]);

      const membersData = await membersResponse.json();
      const limitsData = await limitsResponse.json();

      if (membersData.success) {
        setMembers(membersData.members || []);
      } else {
        if (membersData.upgrade_required) {
          toast.error('Team management requires Enterprise subscription');
        } else {
          toast.error(membersData.error || 'Failed to load team members');
        }
      }

      if (limitsData.success) {
        setLimits(limitsData.limits);
      }

    } catch (error) {
      console.error('Error fetching team data:', error);
      toast.error('Failed to load team data');
    } finally {
      setLoading(false);
    }
  };

  const inviteTeamMember = async () => {
    if (!inviteEmail.trim()) {
      toast.error('Please enter an email address');
      return;
    }

    try {
      setInviting(true);
      
      const response = await fetch('/api/team', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: inviteEmail.trim(),
          role: inviteRole,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        setInviteDialogOpen(false);
        setInviteEmail('');
        setInviteRole('member');
        fetchTeamData(); // Refresh data
      } else {
        if (data.upgrade_required) {
          toast.error('Team invitations require Enterprise subscription');
        } else {
          toast.error(data.error || 'Failed to invite team member');
        }
      }
    } catch (error) {
      console.error('Error inviting team member:', error);
      toast.error('Failed to invite team member');
    } finally {
      setInviting(false);
    }
  };

  const updateMemberRole = async (memberId: string, newRole: 'admin' | 'member') => {
    try {
      const response = await fetch('/api/team', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: memberId,
          role: newRole,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Member role updated successfully');
        fetchTeamData(); // Refresh data
      } else {
        toast.error(data.error || 'Failed to update member role');
      }
    } catch (error) {
      console.error('Error updating member role:', error);
      toast.error('Failed to update member role');
    }
  };

  const removeMember = async (memberId: string, memberEmail: string) => {
    if (!confirm(`Are you sure you want to remove ${memberEmail} from the team?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/team?user_id=${memberId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Team member removed successfully');
        fetchTeamData(); // Refresh data
      } else {
        toast.error(data.error || 'Failed to remove team member');
      }
    } catch (error) {
      console.error('Error removing team member:', error);
      toast.error('Failed to remove team member');
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'admin':
        return <Shield className="h-4 w-4 text-blue-500" />;
      case 'member':
        return <Users className="h-4 w-4 text-gray-500" />;
      default:
        return <Users className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspended</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading team management...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Users className="h-6 w-6" />
            Team Management
            <Crown className="h-5 w-5 text-yellow-500" />
          </h2>
          <p className="text-gray-600">
            Manage your team members and workspace settings
          </p>
        </div>
        
        <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className="h-4 w-4 mr-2" />
              Invite Member
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Invite Team Member</DialogTitle>
              <DialogDescription>
                Send an invitation to join your team
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Email Address</label>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Role</label>
                <Select value={inviteRole} onValueChange={(value: any) => setInviteRole(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="member">Member</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setInviteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={inviteTeamMember} disabled={inviting}>
                  {inviting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="h-4 w-4 mr-2" />
                      Send Invitation
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Workspace Limits */}
      {limits && (
        <Card>
          <CardHeader>
            <CardTitle>Workspace Usage</CardTitle>
            <CardDescription>
              Current usage and limits for your {limits.subscription_tier} plan
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Team Members</span>
                  <span className="text-sm text-gray-600">
                    {limits.current_members} / {limits.max_members === -1 ? '∞' : limits.max_members}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ 
                      width: limits.max_members === -1 ? '20%' : `${Math.min((limits.current_members / limits.max_members) * 100, 100)}%` 
                    }}
                  ></div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Monthly Summaries</span>
                  <span className="text-sm text-gray-600">
                    {limits.current_monthly_summaries} / {limits.max_monthly_summaries === -1 ? '∞' : limits.max_monthly_summaries}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full" 
                    style={{ 
                      width: limits.max_monthly_summaries === -1 ? '20%' : `${Math.min((limits.current_monthly_summaries / limits.max_monthly_summaries) * 100, 100)}%` 
                    }}
                  ></div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Plan</span>
                  <Badge variant="outline">{limits.subscription_tier}</Badge>
                </div>
                <div className="text-xs text-gray-600">
                  {limits.features_enabled.team_analytics && '✓ Team Analytics '}
                  {limits.features_enabled.crm_integrations && '✓ CRM Integrations '}
                  {limits.features_enabled.sso_integration && '✓ SSO '}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Team Members */}
      <Card>
        <CardHeader>
          <CardTitle>Team Members ({members.length})</CardTitle>
          <CardDescription>
            Manage roles and permissions for your team
          </CardDescription>
        </CardHeader>
        <CardContent>
          {members.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No team members yet. Invite colleagues to collaborate on summaries.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {members.map((member) => (
                <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      {getRoleIcon(member.role)}
                      <div>
                        <div className="font-medium">{member.email}</div>
                        <div className="text-sm text-gray-600 capitalize">
                          {member.role} • {getStatusBadge(member.status)}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {member.role !== 'owner' && member.user_id !== user?.id && (
                      <>
                        <Select
                          value={member.role}
                          onValueChange={(value: any) => updateMemberRole(member.user_id, value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="member">Member</SelectItem>
                            <SelectItem value="admin">Admin</SelectItem>
                          </SelectContent>
                        </Select>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeMember(member.user_id, member.email)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    
                    {member.role === 'owner' && (
                      <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                        Owner
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enterprise Features */}
      <Alert>
        <Crown className="h-4 w-4" />
        <AlertDescription>
          <strong>Enterprise Features:</strong> Team management, role-based access control, 
          workspace limits, and advanced analytics are available with Enterprise subscription.
        </AlertDescription>
      </Alert>
    </div>
  );
}

export default TeamManagement;
