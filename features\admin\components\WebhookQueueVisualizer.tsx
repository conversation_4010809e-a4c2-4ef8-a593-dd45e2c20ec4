'use client';

/**
 * Webhook Queue Visualizer
 * 
 * Real-time visualization of webhook delivery queues with:
 * - Live queue status
 * - Delivery success/failure rates
 * - Retry patterns
 * - Performance metrics
 */

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  Webhook, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw,
  Play,
  Pause,
  BarChart3,
  Activity
} from 'lucide-react';

interface WebhookJob {
  id: string;
  url: string;
  provider: string;
  payload: any;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'retrying';
  attempts: number;
  maxRetries: number;
  nextRetryAt?: string;
  createdAt: string;
  completedAt?: string;
  error?: string;
  responseTime?: number;
  responseStatus?: number;
}

interface QueueMetrics {
  totalJobs: number;
  pendingJobs: number;
  processingJobs: number;
  completedJobs: number;
  failedJobs: number;
  retryingJobs: number;
  avgResponseTime: number;
  successRate: number;
  throughput: number;
}

interface ProviderStats {
  provider: string;
  totalJobs: number;
  successRate: number;
  avgResponseTime: number;
  failureReasons: Record<string, number>;
}

export default function WebhookQueueVisualizer() {
  const [jobs, setJobs] = useState<WebhookJob[]>([]);
  const [metrics, setMetrics] = useState<QueueMetrics | null>(null);
  const [providerStats, setProviderStats] = useState<ProviderStats[]>([]);
  const [timeSeriesData, setTimeSeriesData] = useState<any[]>([]);
  const [isRealTime, setIsRealTime] = useState(true);
  const [selectedProvider, setSelectedProvider] = useState<string>('all');
  const [timeRange, setTimeRange] = useState('1h');
  const intervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    fetchData();
    
    if (isRealTime) {
      intervalRef.current = setInterval(fetchData, 5000); // Update every 5 seconds
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRealTime, selectedProvider, timeRange]);

  const fetchData = async () => {
    try {
      const [jobsRes, metricsRes, statsRes, timeSeriesRes] = await Promise.all([
        fetch(`/api/admin/webhooks/jobs?provider=${selectedProvider}&limit=100`),
        fetch(`/api/admin/webhooks/metrics?timeRange=${timeRange}`),
        fetch(`/api/admin/webhooks/provider-stats?timeRange=${timeRange}`),
        fetch(`/api/admin/webhooks/time-series?timeRange=${timeRange}`)
      ]);

      if (jobsRes.ok) {
        const jobsData = await jobsRes.json();
        setJobs(jobsData.jobs || []);
      }

      if (metricsRes.ok) {
        const metricsData = await metricsRes.json();
        setMetrics(metricsData);
      }

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setProviderStats(statsData.providers || []);
      }

      if (timeSeriesRes.ok) {
        const timeSeriesData = await timeSeriesRes.json();
        setTimeSeriesData(timeSeriesData.data || []);
      }

    } catch (error) {
      console.error('Failed to fetch webhook data:', error);
    }
  };

  const retryJob = async (jobId: string) => {
    try {
      const response = await fetch(`/api/admin/webhooks/jobs/${jobId}/retry`, {
        method: 'POST'
      });

      if (response.ok) {
        fetchData(); // Refresh data
      }
    } catch (error) {
      console.error('Failed to retry job:', error);
    }
  };

  const cancelJob = async (jobId: string) => {
    try {
      const response = await fetch(`/api/admin/webhooks/jobs/${jobId}/cancel`, {
        method: 'POST'
      });

      if (response.ok) {
        fetchData(); // Refresh data
      }
    } catch (error) {
      console.error('Failed to cancel job:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'processing':
        return 'bg-blue-500';
      case 'retrying':
        return 'bg-yellow-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'processing':
        return <Activity className="w-4 h-4 text-blue-600 animate-spin" />;
      case 'retrying':
        return <RefreshCw className="w-4 h-4 text-yellow-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const pieChartData = metrics ? [
    { name: 'Completed', value: metrics.completedJobs, color: '#10b981' },
    { name: 'Failed', value: metrics.failedJobs, color: '#ef4444' },
    { name: 'Processing', value: metrics.processingJobs, color: '#3b82f6' },
    { name: 'Pending', value: metrics.pendingJobs, color: '#6b7280' },
    { name: 'Retrying', value: metrics.retryingJobs, color: '#f59e0b' }
  ].filter(item => item.value > 0) : [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Webhook Queue Monitor</h1>
          <p className="text-gray-600">Real-time webhook delivery monitoring</p>
        </div>
        
        <div className="flex items-center gap-2">
          <select 
            value={selectedProvider} 
            onChange={(e) => setSelectedProvider(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="all">All Providers</option>
            <option value="slack">Slack</option>
            <option value="notion">Notion</option>
            <option value="hubspot">HubSpot</option>
            <option value="salesforce">Salesforce</option>
          </select>
          
          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="1h">Last Hour</option>
            <option value="6h">Last 6 Hours</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
          </select>
          
          <Button
            variant={isRealTime ? "default" : "outline"}
            onClick={() => setIsRealTime(!isRealTime)}
          >
            {isRealTime ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
            {isRealTime ? 'Pause' : 'Resume'}
          </Button>
          
          <Button variant="outline" onClick={fetchData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Metrics Overview */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Jobs</p>
                  <p className="text-2xl font-bold">{metrics.totalJobs.toLocaleString()}</p>
                </div>
                <Webhook className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Success Rate</p>
                  <p className="text-2xl font-bold">{(metrics.successRate * 100).toFixed(1)}%</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
                  <p className="text-2xl font-bold">{Math.round(metrics.avgResponseTime)}ms</p>
                </div>
                <BarChart3 className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Throughput</p>
                  <p className="text-2xl font-bold">{metrics.throughput.toFixed(1)}/min</p>
                </div>
                <Activity className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Failed Jobs</p>
                  <p className="text-2xl font-bold text-red-600">{metrics.failedJobs}</p>
                </div>
                <XCircle className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="queue" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="queue">Live Queue</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="providers">Providers</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="queue" className="space-y-6">
          {/* Job Status Distribution */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Recent Jobs</CardTitle>
                <CardDescription>Latest webhook delivery jobs</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {jobs.map((job) => (
                    <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        {getStatusIcon(job.status)}
                        <div>
                          <h4 className="font-semibold">{job.provider}</h4>
                          <p className="text-sm text-gray-600 truncate max-w-md">{job.url}</p>
                          <p className="text-xs text-gray-500">
                            {new Date(job.createdAt).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge variant={job.status === 'completed' ? 'default' : 
                                     job.status === 'failed' ? 'destructive' : 'secondary'}>
                          {job.status}
                        </Badge>
                        
                        {job.attempts > 1 && (
                          <Badge variant="outline">
                            {job.attempts}/{job.maxRetries} attempts
                          </Badge>
                        )}
                        
                        {job.responseTime && (
                          <Badge variant="outline">
                            {job.responseTime}ms
                          </Badge>
                        )}
                        
                        <div className="flex gap-1">
                          {job.status === 'failed' && (
                            <Button size="sm" variant="outline" onClick={() => retryJob(job.id)}>
                              <RefreshCw className="w-3 h-3" />
                            </Button>
                          )}
                          
                          {(job.status === 'pending' || job.status === 'retrying') && (
                            <Button size="sm" variant="outline" onClick={() => cancelJob(job.id)}>
                              <XCircle className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {pieChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                
                <div className="mt-4 space-y-2">
                  {pieChartData.map((entry, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: entry.color }}
                        ></div>
                        <span className="text-sm">{entry.name}</span>
                      </div>
                      <span className="text-sm font-medium">{entry.value}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Delivery Success Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="successRate" 
                      stroke="#10b981" 
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Response Times</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="avgResponseTime" 
                      stroke="#3b82f6" 
                      fill="#3b82f6" 
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="providers" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {providerStats.map((provider) => (
              <Card key={provider.provider}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="capitalize">{provider.provider}</span>
                    <Badge variant={provider.successRate > 0.95 ? 'default' : 
                                  provider.successRate > 0.8 ? 'secondary' : 'destructive'}>
                      {(provider.successRate * 100).toFixed(1)}% success
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Total Jobs</p>
                      <p className="text-2xl font-bold">{provider.totalJobs}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Avg Response Time</p>
                      <p className="text-2xl font-bold">{Math.round(provider.avgResponseTime)}ms</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Top Failure Reason</p>
                      <p className="text-sm font-medium">
                        {Object.entries(provider.failureReasons)[0]?.[0] || 'None'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Delivery Timeline</CardTitle>
              <CardDescription>Webhook delivery volume over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={timeSeriesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="completed" fill="#10b981" name="Completed" />
                  <Bar dataKey="failed" fill="#ef4444" name="Failed" />
                  <Bar dataKey="pending" fill="#6b7280" name="Pending" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
