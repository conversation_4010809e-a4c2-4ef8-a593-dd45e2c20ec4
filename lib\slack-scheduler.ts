/**
 * Slack Auto-Posting Scheduler Service
 * Handles scheduled posting of summaries to Slack channels
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';
import { SentryTracker } from '@/lib/sentry.client';

export interface ScheduledPost {
  id: string;
  user_id: string;
  organization_id: string;
  slack_channel_id: string;
  slack_channel_name: string;
  template_id: string;
  schedule_type: 'daily' | 'weekly' | 'monthly';
  schedule_time: string; // HH:MM format
  schedule_day?: number; // 0-6 for weekly (0=Sunday), 1-31 for monthly
  timezone: string;
  is_active: boolean;
  last_posted_at?: string;
  next_post_at: string;
  created_at: string;
  updated_at: string;
}

export interface PostTemplate {
  id: string;
  user_id: string;
  organization_id: string;
  name: string;
  description: string;
  template_content: string;
  variables: string[]; // Available template variables
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface SlackChannel {
  id: string;
  name: string;
  is_private: boolean;
  is_member: boolean;
}

/**
 * Get user's Slack channels
 */
export async function getUserSlackChannels(userId: string, organizationId: string): Promise<SlackChannel[]> {
  try {
    // Get Slack bot token from integrations
    const supabase = await createSupabaseServerClient();
    
    const { data: integration } = await supabase
      .from('integrations')
      .select('access_token')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .eq('integration_type', 'slack')
      .eq('is_active', true)
      .single();

    if (!integration?.access_token) {
      throw new Error('Slack integration not found or inactive');
    }

    // Fetch channels from Slack API
    const response = await fetch('https://slack.com/api/conversations.list', {
      headers: {
        'Authorization': `Bearer ${integration.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    if (!data.ok) {
      throw new Error(`Slack API error: ${data.error}`);
    }

    return data.channels.map((channel: any) => ({
      id: channel.id,
      name: channel.name,
      is_private: channel.is_private,
      is_member: channel.is_member,
    }));

  } catch (error) {
    console.error('Error fetching Slack channels:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return [];
  }
}

/**
 * Create a new scheduled post
 */
export async function createScheduledPost(
  userId: string,
  organizationId: string,
  postData: Omit<ScheduledPost, 'id' | 'user_id' | 'organization_id' | 'created_at' | 'updated_at' | 'last_posted_at'>
): Promise<{ success: boolean; scheduledPost?: ScheduledPost; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Calculate next post time
    const nextPostAt = calculateNextPostTime(
      postData.schedule_type,
      postData.schedule_time,
      postData.schedule_day,
      postData.timezone
    );

    const { data, error } = await supabase
      .from('scheduled_posts')
      .insert({
        user_id: userId,
        organization_id: organizationId,
        slack_channel_id: postData.slack_channel_id,
        slack_channel_name: postData.slack_channel_name,
        template_id: postData.template_id,
        schedule_type: postData.schedule_type,
        schedule_time: postData.schedule_time,
        schedule_day: postData.schedule_day,
        timezone: postData.timezone,
        is_active: postData.is_active,
        next_post_at: nextPostAt.toISOString(),
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { success: true, scheduledPost: data };

  } catch (error) {
    console.error('Error creating scheduled post:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to create scheduled post' 
    };
  }
}

/**
 * Get user's scheduled posts
 */
export async function getUserScheduledPosts(
  userId: string, 
  organizationId: string
): Promise<ScheduledPost[]> {
  try {
    const supabase = await createSupabaseServerClient();

    const { data, error } = await supabase
      .from('scheduled_posts')
      .select('*')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return data || [];

  } catch (error) {
    console.error('Error fetching scheduled posts:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return [];
  }
}

/**
 * Update scheduled post
 */
export async function updateScheduledPost(
  postId: string,
  userId: string,
  updates: Partial<ScheduledPost>
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // If schedule details are updated, recalculate next post time
    if (updates.schedule_type || updates.schedule_time || updates.schedule_day || updates.timezone) {
      const { data: currentPost } = await supabase
        .from('scheduled_posts')
        .select('*')
        .eq('id', postId)
        .eq('user_id', userId)
        .single();

      if (currentPost) {
        const nextPostAt = calculateNextPostTime(
          updates.schedule_type || currentPost.schedule_type,
          updates.schedule_time || currentPost.schedule_time,
          updates.schedule_day || currentPost.schedule_day,
          updates.timezone || currentPost.timezone
        );
        updates.next_post_at = nextPostAt.toISOString();
      }
    }

    const { error } = await supabase
      .from('scheduled_posts')
      .update(updates)
      .eq('id', postId)
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    return { success: true };

  } catch (error) {
    console.error('Error updating scheduled post:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update scheduled post' 
    };
  }
}

/**
 * Delete scheduled post
 */
export async function deleteScheduledPost(
  postId: string,
  userId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    const { error } = await supabase
      .from('scheduled_posts')
      .delete()
      .eq('id', postId)
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    return { success: true };

  } catch (error) {
    console.error('Error deleting scheduled post:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to delete scheduled post' 
    };
  }
}

/**
 * Calculate next post time based on schedule
 */
function calculateNextPostTime(
  scheduleType: 'daily' | 'weekly' | 'monthly',
  scheduleTime: string, // HH:MM
  scheduleDay?: number,
  timezone: string = 'UTC'
): Date {
  const now = new Date();
  const [hours, minutes] = scheduleTime.split(':').map(Number);
  
  let nextPost = new Date(now);
  nextPost.setHours(hours, minutes, 0, 0);

  switch (scheduleType) {
    case 'daily':
      // If time has passed today, schedule for tomorrow
      if (nextPost <= now) {
        nextPost.setDate(nextPost.getDate() + 1);
      }
      break;

    case 'weekly':
      // Schedule for specific day of week
      if (scheduleDay !== undefined) {
        const currentDay = nextPost.getDay();
        const daysUntilTarget = (scheduleDay - currentDay + 7) % 7;
        
        if (daysUntilTarget === 0 && nextPost <= now) {
          // Same day but time passed, schedule for next week
          nextPost.setDate(nextPost.getDate() + 7);
        } else if (daysUntilTarget > 0) {
          nextPost.setDate(nextPost.getDate() + daysUntilTarget);
        }
      }
      break;

    case 'monthly':
      // Schedule for specific day of month
      if (scheduleDay !== undefined) {
        nextPost.setDate(scheduleDay);
        
        // If date has passed this month, schedule for next month
        if (nextPost <= now) {
          nextPost.setMonth(nextPost.getMonth() + 1);
          nextPost.setDate(scheduleDay);
        }
      }
      break;
  }

  return nextPost;
}

/**
 * Get posts that are due for posting
 */
export async function getDuePosts(): Promise<ScheduledPost[]> {
  try {
    const supabase = await createSupabaseServerClient();
    const now = new Date().toISOString();

    const { data, error } = await supabase
      .from('scheduled_posts')
      .select('*')
      .eq('is_active', true)
      .lte('next_post_at', now);

    if (error) {
      throw error;
    }

    return data || [];

  } catch (error) {
    console.error('Error fetching due posts:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return [];
  }
}
