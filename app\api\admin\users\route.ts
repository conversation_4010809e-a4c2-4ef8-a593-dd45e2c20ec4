import { NextRequest, NextResponse } from 'next/server';
import { createRBACProtectedRoute, Permission } from '@/lib/auth-protection';
import { createSecureApiRoute } from '@/lib/api-security';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { rbac } from '@/lib/rbac';

/**
 * Admin Users API
 * 
 * Manages user data for admin dashboard
 */
export const GET = createRBACProtectedRoute(
  async (request: NextRequest, authResult) => {
    try {
      const supabase = await createSupabaseServerClient();
      
      // Get users with their roles and subscription info
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select(`
          id,
          email,
          first_name,
          last_name,
          created_at,
          last_sign_in_at,
          image_url
        `)
        .order('created_at', { ascending: false })
        .limit(100);
      
      if (usersError) {
        console.error('Failed to fetch users:', usersError);
        return NextResponse.json(
          { error: 'Failed to load users' },
          { status: 500 }
        );
      }
      
      // Get user roles
      const { data: userRoles, error: rolesError } = await supabase
        .from('user_roles')
        .select('user_id, role, organization_id');
      
      if (rolesError) {
        console.error('Failed to fetch user roles:', rolesError);
      }
      
      // Get subscription info
      const { data: subscriptions, error: subscriptionsError } = await supabase
        .from('subscriptions')
        .select('user_id, status, plan_id')
        .eq('status', 'active');
      
      if (subscriptionsError) {
        console.error('Failed to fetch subscriptions:', subscriptionsError);
      }
      
      // Combine user data
      const enrichedUsers = users?.map(user => {
        const userRole = userRoles?.find(role => role.user_id === user.id);
        const subscription = subscriptions?.find(sub => sub.user_id === user.id);
        
        // Determine user status
        let status: 'active' | 'inactive' | 'suspended' = 'inactive';
        if (user.last_sign_in_at) {
          const lastSignIn = new Date(user.last_sign_in_at);
          const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          status = lastSignIn > sevenDaysAgo ? 'active' : 'inactive';
        }
        
        return {
          id: user.id,
          email: user.email,
          name: user.first_name && user.last_name 
            ? `${user.first_name} ${user.last_name}` 
            : user.first_name || user.email,
          role: userRole?.role || 'user',
          status,
          lastActive: user.last_sign_in_at || user.created_at,
          subscription: subscription?.plan_id || 'free',
          createdAt: user.created_at,
          imageUrl: user.image_url
        };
      }) || [];
      
      return NextResponse.json({ 
        users: enrichedUsers,
        total: enrichedUsers.length
      });
      
    } catch (error) {
      console.error('Failed to get users:', error);
      return NextResponse.json(
        { error: 'Failed to load users' },
        { status: 500 }
      );
    }
  },
  {
    requiredPermission: Permission.USER_READ,
    requireAuth: true,
    rateLimit: 30, // 30 requests per minute
    auditLog: true,
    allowedMethods: ['GET']
  }
);
