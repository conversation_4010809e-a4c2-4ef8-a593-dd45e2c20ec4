/**
 * Background Job Framework
 * 
 * Provides a robust worker pattern for handling:
 * - Cron exports
 * - Slack delivery
 * - Webhook retry queues
 * - Email campaigns
 * - Data processing
 */

import { EventEmitter } from 'events';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { circuitBreakerRegistry } from '@/lib/resilience/circuit-breaker';
// import Redis from 'ioredis'; // Optional dependency

// Mock Redis interface for when Redis is not available
interface MockRedis {
  lpush(key: string, value: string): Promise<number>;
  hset(key: string, field: string, value: string): Promise<number>;
  zadd(key: string, score: number, member: string): Promise<number>;
  zrangebyscore(key: string, min: number, max: number, ...args: any[]): Promise<string[]>;
  hget(key: string, field: string): Promise<string | null>;
  zrem(key: string, member: string): Promise<number>;
  rpop(key: string, count?: number): Promise<string | string[] | null>;
  hdel(key: string, ...fields: string[]): Promise<number>;
}

// In-memory fallback implementation
class MemoryRedis implements MockRedis {
  private data = new Map<string, any>();
  private lists = new Map<string, string[]>();
  private sortedSets = new Map<string, Array<{ score: number; member: string }>>();

  async lpush(key: string, value: string): Promise<number> {
    if (!this.lists.has(key)) this.lists.set(key, []);
    const list = this.lists.get(key)!;
    list.unshift(value);
    return list.length;
  }

  async hset(key: string, field: string, value: string): Promise<number> {
    if (!this.data.has(key)) this.data.set(key, new Map());
    const hash = this.data.get(key);
    const isNew = !hash.has(field);
    hash.set(field, value);
    return isNew ? 1 : 0;
  }

  async zadd(key: string, score: number, member: string): Promise<number> {
    if (!this.sortedSets.has(key)) this.sortedSets.set(key, []);
    const set = this.sortedSets.get(key)!;
    const existing = set.findIndex(item => item.member === member);
    if (existing >= 0) {
      set[existing].score = score;
      return 0;
    } else {
      set.push({ score, member });
      set.sort((a, b) => a.score - b.score);
      return 1;
    }
  }

  async zrangebyscore(key: string, min: number, max: number, ...args: any[]): Promise<string[]> {
    const set = this.sortedSets.get(key) || [];
    let filtered = set.filter(item => item.score >= min && item.score <= max);

    // Handle LIMIT option
    if (args.includes('LIMIT')) {
      const limitIndex = args.indexOf('LIMIT');
      const offset = args[limitIndex + 1] || 0;
      const count = args[limitIndex + 2] || filtered.length;
      filtered = filtered.slice(offset, offset + count);
    }

    return filtered.map(item => item.member);
  }

  async hget(key: string, field: string): Promise<string | null> {
    const hash = this.data.get(key);
    return hash ? hash.get(field) || null : null;
  }

  async zrem(key: string, member: string): Promise<number> {
    const set = this.sortedSets.get(key) || [];
    const index = set.findIndex(item => item.member === member);
    if (index >= 0) {
      set.splice(index, 1);
      return 1;
    }
    return 0;
  }

  async rpop(key: string, count?: number): Promise<string | string[] | null> {
    const list = this.lists.get(key);
    if (!list || list.length === 0) return null;

    if (count === undefined) {
      return list.pop() || null;
    } else {
      const result = [];
      for (let i = 0; i < count && list.length > 0; i++) {
        const item = list.pop();
        if (item) result.push(item);
      }
      return result;
    }
  }

  async hdel(key: string, ...fields: string[]): Promise<number> {
    const hash = this.data.get(key);
    if (!hash) return 0;

    let deleted = 0;
    for (const field of fields) {
      if (hash.delete(field)) deleted++;
    }
    return deleted;
  }
}

type Redis = MockRedis;

export interface JobDefinition {
  id: string;
  type: string;
  payload: Record<string, any>;
  priority: number;
  maxRetries: number;
  retryDelay: number;
  timeout: number;
  scheduledAt?: Date;
  userId?: string;
  organizationId?: string;
  metadata?: Record<string, any>;
}

export interface JobResult {
  success: boolean;
  data?: any;
  error?: string;
  duration: number;
  retryCount: number;
}

export interface JobHandler {
  type: string;
  handler: (job: JobDefinition) => Promise<JobResult>;
  concurrency?: number;
  timeout?: number;
}

export interface WorkerConfig {
  redis?: {
    host: string;
    port: number;
    password?: string;
    db?: number;
  };
  supabase: {
    url: string;
    serviceKey: string;
  };
  concurrency: number;
  pollInterval: number;
  retryDelay: number;
  maxRetries: number;
  enableMetrics: boolean;
}

export interface JobMetrics {
  processed: number;
  failed: number;
  retried: number;
  avgDuration: number;
  queueSize: number;
  activeJobs: number;
}

export class JobManager extends EventEmitter {
  private config: WorkerConfig;
  private redis?: Redis;
  private handlers: Map<string, JobHandler> = new Map();
  private activeJobs: Map<string, JobDefinition> = new Map();
  private metrics: JobMetrics = {
    processed: 0,
    failed: 0,
    retried: 0,
    avgDuration: 0,
    queueSize: 0,
    activeJobs: 0
  };
  private isRunning = false;
  private pollTimer?: NodeJS.Timeout;

  constructor(config: WorkerConfig) {
    super();
    this.config = config;
    
    if (config.redis) {
      // Use memory-based Redis fallback
      this.redis = new MemoryRedis();
    }
  }

  /**
   * Register a job handler
   */
  registerHandler(handler: JobHandler): void {
    this.handlers.set(handler.type, handler);
    this.emit('handler:registered', handler.type);
  }

  /**
   * Queue a job for processing
   */
  async queueJob(job: Omit<JobDefinition, 'id'>): Promise<string> {
    const jobId = this.generateJobId();
    const fullJob: JobDefinition = {
      ...job,
      id: jobId
    };

    if (this.redis) {
      await this.queueJobRedis(fullJob);
    } else {
      await this.queueJobSupabase(fullJob);
    }

    this.emit('job:queued', fullJob);
    return jobId;
  }

  /**
   * Schedule a job for future execution
   */
  async scheduleJob(
    job: Omit<JobDefinition, 'id' | 'scheduledAt'>,
    scheduledAt: Date
  ): Promise<string> {
    const jobId = this.generateJobId();
    const fullJob: JobDefinition = {
      ...job,
      id: jobId,
      scheduledAt
    };

    if (this.redis) {
      await this.scheduleJobRedis(fullJob);
    } else {
      await this.scheduleJobSupabase(fullJob);
    }

    this.emit('job:scheduled', fullJob);
    return jobId;
  }

  /**
   * Start the job processor
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.emit('worker:started');

    // Start polling for jobs
    this.pollTimer = setInterval(
      () => this.processJobs(),
      this.config.pollInterval
    );

    // Process any immediate jobs
    await this.processJobs();
  }

  /**
   * Stop the job processor
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.pollTimer) {
      clearInterval(this.pollTimer);
      this.pollTimer = undefined;
    }

    // Wait for active jobs to complete
    await this.waitForActiveJobs();

    this.emit('worker:stopped');
  }

  /**
   * Get job metrics
   */
  getMetrics(): JobMetrics {
    return {
      ...this.metrics,
      activeJobs: this.activeJobs.size
    };
  }

  /**
   * Get job status
   */
  async getJobStatus(jobId: string): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'failed';
    result?: JobResult;
    job?: JobDefinition;
  }> {
    // Check if job is currently active
    if (this.activeJobs.has(jobId)) {
      return {
        status: 'processing',
        job: this.activeJobs.get(jobId)
      };
    }

    // Check storage for job status
    if (this.redis) {
      return await this.getJobStatusRedis(jobId);
    } else {
      return await this.getJobStatusSupabase(jobId);
    }
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    // Cancel active job
    if (this.activeJobs.has(jobId)) {
      this.activeJobs.delete(jobId);
      this.emit('job:cancelled', jobId);
      return true;
    }

    // Remove from queue
    if (this.redis) {
      return await this.cancelJobRedis(jobId);
    } else {
      return await this.cancelJobSupabase(jobId);
    }
  }

  // Private methods

  private async processJobs(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      const availableSlots = this.config.concurrency - this.activeJobs.size;
      if (availableSlots <= 0) {
        return;
      }

      const jobs = await this.getNextJobs(availableSlots);
      
      for (const job of jobs) {
        this.processJob(job).catch(error => {
          console.error('Job processing error:', error);
          this.emit('job:error', job, error);
        });
      }

    } catch (error) {
      console.error('Job polling error:', error);
      this.emit('worker:error', error);
    }
  }

  private async processJob(job: JobDefinition): Promise<void> {
    const startTime = Date.now();
    this.activeJobs.set(job.id, job);
    this.emit('job:started', job);

    try {
      const handler = this.handlers.get(job.type);
      if (!handler) {
        throw new Error(`No handler registered for job type: ${job.type}`);
      }

      // Execute job with circuit breaker
      const result = await circuitBreakerRegistry.execute(
        `job-${job.type}`,
        () => this.executeJobWithTimeout(handler, job),
        () => this.getJobFallback(job)
      );

      const duration = Date.now() - startTime;
      
      // Update metrics
      this.updateMetrics(true, duration);

      // Store result
      await this.storeJobResult(job.id, result);

      this.emit('job:completed', job, result);

    } catch (error) {
      const duration = Date.now() - startTime;
      const result: JobResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
        retryCount: job.metadata?.retryCount || 0
      };

      // Update metrics
      this.updateMetrics(false, duration);

      // Handle retry logic
      if (result.retryCount < job.maxRetries) {
        await this.retryJob(job, result);
      } else {
        await this.storeJobResult(job.id, result);
        this.emit('job:failed', job, result);
      }

    } finally {
      this.activeJobs.delete(job.id);
    }
  }

  private async executeJobWithTimeout(
    handler: JobHandler,
    job: JobDefinition
  ): Promise<JobResult> {
    const timeout = handler.timeout || job.timeout || 30000;

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Job ${job.id} timed out after ${timeout}ms`));
      }, timeout);

      handler.handler(job)
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  private async getJobFallback(job: JobDefinition): Promise<JobResult> {
    return {
      success: false,
      error: 'Circuit breaker open - job execution skipped',
      duration: 0,
      retryCount: job.metadata?.retryCount || 0
    };
  }

  private async retryJob(job: JobDefinition, lastResult: JobResult): Promise<void> {
    const retryCount = (job.metadata?.retryCount || 0) + 1;
    const retryDelay = job.retryDelay * Math.pow(2, retryCount - 1); // Exponential backoff

    const retryJob: JobDefinition = {
      ...job,
      scheduledAt: new Date(Date.now() + retryDelay),
      metadata: {
        ...job.metadata,
        retryCount,
        lastError: lastResult.error
      }
    };

    if (this.redis) {
      await this.scheduleJobRedis(retryJob);
    } else {
      await this.scheduleJobSupabase(retryJob);
    }

    this.metrics.retried++;
    this.emit('job:retried', retryJob, lastResult);
  }

  private updateMetrics(success: boolean, duration: number): void {
    if (success) {
      this.metrics.processed++;
    } else {
      this.metrics.failed++;
    }

    // Update average duration
    const totalJobs = this.metrics.processed + this.metrics.failed;
    this.metrics.avgDuration = 
      (this.metrics.avgDuration * (totalJobs - 1) + duration) / totalJobs;
  }

  private async getNextJobs(limit: number): Promise<JobDefinition[]> {
    if (this.redis) {
      return await this.getNextJobsRedis(limit);
    } else {
      return await this.getNextJobsSupabase(limit);
    }
  }

  // Redis implementation
  private async queueJobRedis(job: JobDefinition): Promise<void> {
    if (!this.redis) return;

    const jobData = JSON.stringify(job);
    await this.redis.lpush('jobs:queue', jobData);
    await this.redis.hset('jobs:data', job.id, jobData);
  }

  private async scheduleJobRedis(job: JobDefinition): Promise<void> {
    if (!this.redis || !job.scheduledAt) return;

    const score = job.scheduledAt.getTime();
    const jobData = JSON.stringify(job);
    
    await this.redis.zadd('jobs:scheduled', score, job.id);
    await this.redis.hset('jobs:data', job.id, jobData);
  }

  private async getNextJobsRedis(limit: number): Promise<JobDefinition[]> {
    if (!this.redis) return [];

    const jobs: JobDefinition[] = [];

    // Get scheduled jobs that are ready
    const now = Date.now();
    const scheduledJobIds = await this.redis.zrangebyscore(
      'jobs:scheduled',
      0,
      now,
      'LIMIT',
      0,
      limit
    );

    for (const jobId of scheduledJobIds) {
      const jobData = await this.redis.hget('jobs:data', jobId);
      if (jobData) {
        jobs.push(JSON.parse(jobData));
        await this.redis.zrem('jobs:scheduled', jobId);
      }
    }

    // Get immediate jobs
    const remaining = limit - jobs.length;
    if (remaining > 0) {
      const immediateJobs = await this.redis.rpop('jobs:queue', remaining);
      if (immediateJobs) {
        const jobArray = Array.isArray(immediateJobs) ? immediateJobs : [immediateJobs];
        jobs.push(...jobArray.map(data => JSON.parse(data)));
      }
    }

    return jobs;
  }

  private async getJobStatusRedis(jobId: string): Promise<any> {
    if (!this.redis) return { status: 'pending' };

    const resultData = await this.redis.hget('jobs:results', jobId);
    if (resultData) {
      const result = JSON.parse(resultData);
      return {
        status: result.success ? 'completed' : 'failed',
        result
      };
    }

    const jobData = await this.redis.hget('jobs:data', jobId);
    if (jobData) {
      return {
        status: 'pending',
        job: JSON.parse(jobData)
      };
    }

    return { status: 'pending' };
  }

  private async cancelJobRedis(jobId: string): Promise<boolean> {
    if (!this.redis) return false;

    const removed = await this.redis.zrem('jobs:scheduled', jobId);
    await this.redis.hdel('jobs:data', jobId);
    
    return removed > 0;
  }

  // Supabase implementation
  private async queueJobSupabase(job: JobDefinition): Promise<void> {
    const supabase = await createSupabaseServerClient();
    
    await supabase.from('background_jobs').insert({
      id: job.id,
      type: job.type,
      payload: job.payload,
      priority: job.priority,
      max_retries: job.maxRetries,
      retry_delay: job.retryDelay,
      timeout: job.timeout,
      scheduled_at: job.scheduledAt?.toISOString(),
      user_id: job.userId,
      organization_id: job.organizationId,
      metadata: job.metadata,
      status: 'pending',
      created_at: new Date().toISOString()
    });
  }

  private async scheduleJobSupabase(job: JobDefinition): Promise<void> {
    await this.queueJobSupabase(job);
  }

  private async getNextJobsSupabase(limit: number): Promise<JobDefinition[]> {
    const supabase = await createSupabaseServerClient();
    
    const { data, error } = await supabase
      .from('background_jobs')
      .select('*')
      .eq('status', 'pending')
      .or(`scheduled_at.is.null,scheduled_at.lte.${new Date().toISOString()}`)
      .order('priority', { ascending: false })
      .order('created_at', { ascending: true })
      .limit(limit);

    if (error) {
      throw error;
    }

    // Mark jobs as processing
    if (data && data.length > 0) {
      const jobIds = data.map(job => job.id);
      await supabase
        .from('background_jobs')
        .update({ status: 'processing' })
        .in('id', jobIds);
    }

    return (data || []).map(row => ({
      id: row.id,
      type: row.type,
      payload: row.payload,
      priority: row.priority,
      maxRetries: row.max_retries,
      retryDelay: row.retry_delay,
      timeout: row.timeout,
      scheduledAt: row.scheduled_at ? new Date(row.scheduled_at) : undefined,
      userId: row.user_id,
      organizationId: row.organization_id,
      metadata: row.metadata
    }));
  }

  private async getJobStatusSupabase(jobId: string): Promise<any> {
    const supabase = await createSupabaseServerClient();
    
    const { data, error } = await supabase
      .from('background_jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (error || !data) {
      return { status: 'pending' };
    }

    return {
      status: data.status,
      result: data.result,
      job: {
        id: data.id,
        type: data.type,
        payload: data.payload,
        priority: data.priority,
        maxRetries: data.max_retries,
        retryDelay: data.retry_delay,
        timeout: data.timeout,
        scheduledAt: data.scheduled_at ? new Date(data.scheduled_at) : undefined,
        userId: data.user_id,
        organizationId: data.organization_id,
        metadata: data.metadata
      }
    };
  }

  private async cancelJobSupabase(jobId: string): Promise<boolean> {
    const supabase = await createSupabaseServerClient();
    
    const { error } = await supabase
      .from('background_jobs')
      .update({ status: 'cancelled' })
      .eq('id', jobId)
      .eq('status', 'pending');

    return !error;
  }

  private async storeJobResult(jobId: string, result: JobResult): Promise<void> {
    if (this.redis) {
      await this.redis.hset('jobs:results', jobId, JSON.stringify(result));
      await this.redis.hdel('jobs:data', jobId);
    } else {
      const supabase = await createSupabaseServerClient();
      await supabase
        .from('background_jobs')
        .update({
          status: result.success ? 'completed' : 'failed',
          result,
          completed_at: new Date().toISOString()
        })
        .eq('id', jobId);
    }
  }

  private async waitForActiveJobs(): Promise<void> {
    while (this.activeJobs.size > 0) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const jobManager = new JobManager({
  redis: process.env.REDIS_URL ? {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD
  } : undefined,
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY!
  },
  concurrency: parseInt(process.env.JOB_CONCURRENCY || '5'),
  pollInterval: parseInt(process.env.JOB_POLL_INTERVAL || '5000'),
  retryDelay: parseInt(process.env.JOB_RETRY_DELAY || '1000'),
  maxRetries: parseInt(process.env.JOB_MAX_RETRIES || '3'),
  enableMetrics: process.env.NODE_ENV === 'production'
});
