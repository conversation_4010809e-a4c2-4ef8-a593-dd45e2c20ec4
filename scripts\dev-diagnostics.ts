#!/usr/bin/env tsx

/**
 * Development Server Diagnostics Script
 * 
 * FEATURES:
 * ✅ Port availability checking
 * ✅ Network interface validation
 * ✅ Environment variable verification
 * ✅ Clerk authentication status
 * ✅ Clear server startup logging
 * ✅ Automatic port fallback
 * ✅ Browser auto-opening
 */

import { execSync, spawn } from 'child_process';
import { performance } from 'perf_hooks';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

interface DiagnosticResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * Check if a port is available
 */
async function checkPortAvailability(port: number): Promise<boolean> {
  return new Promise((resolve) => {
    const net = require('net');
    const server = net.createServer();
    
    server.listen(port, () => {
      server.once('close', () => resolve(true));
      server.close();
    });
    
    server.on('error', () => resolve(false));
  });
}

/**
 * Find an available port starting from the preferred port
 */
async function findAvailablePort(startPort: number = 3000): Promise<number> {
  for (let port = startPort; port <= startPort + 10; port++) {
    if (await checkPortAvailability(port)) {
      return port;
    }
  }
  throw new Error('No available ports found in range');
}

/**
 * Check network connectivity
 */
function checkNetworkConnectivity(): DiagnosticResult {
  try {
    // Check if localhost resolves
    execSync('ping -n 1 127.0.0.1', { stdio: 'pipe' });
    return { success: true, message: 'Network connectivity OK' };
  } catch (error) {
    return { 
      success: false, 
      message: 'Network connectivity issues detected',
      details: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Validate environment variables
 */
function validateEnvironment(): DiagnosticResult {
  const requiredVars = [
    'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
    'CLERK_SECRET_KEY'
  ];
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    return {
      success: false,
      message: `Missing required environment variables: ${missing.join(', ')}`,
      details: { missing }
    };
  }
  
  return { success: true, message: 'Environment variables validated' };
}

/**
 * Check Clerk configuration
 */
function validateClerkConfig(): DiagnosticResult {
  const publishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;
  
  if (!publishableKey) {
    return { success: false, message: 'Clerk publishable key not found' };
  }
  
  if (!publishableKey.startsWith('pk_test_') && !publishableKey.startsWith('pk_live_')) {
    return { success: false, message: 'Invalid Clerk key format' };
  }
  
  const environment = publishableKey.startsWith('pk_test_') ? 'test' : 'live';
  
  return { 
    success: true, 
    message: `Clerk configuration valid (${environment} environment)`,
    details: { environment, keyPrefix: publishableKey.substring(0, 8) }
  };
}

/**
 * Run comprehensive diagnostics
 */
async function runDiagnostics(): Promise<void> {
  const startTime = performance.now();
  
  console.log('🔍 Running development server diagnostics...\n');
  
  // Check network connectivity
  const networkResult = checkNetworkConnectivity();
  console.log(`🌐 Network: ${networkResult.success ? '✅' : '❌'} ${networkResult.message}`);
  
  // Validate environment
  const envResult = validateEnvironment();
  console.log(`🔧 Environment: ${envResult.success ? '✅' : '❌'} ${envResult.message}`);
  
  // Validate Clerk
  const clerkResult = validateClerkConfig();
  console.log(`🔐 Clerk: ${clerkResult.success ? '✅' : '❌'} ${clerkResult.message}`);
  
  // Check port availability
  const preferredPort = 3000;
  const isPortAvailable = await checkPortAvailability(preferredPort);
  console.log(`🔌 Port ${preferredPort}: ${isPortAvailable ? '✅ Available' : '❌ In use'}`);
  
  let selectedPort = preferredPort;
  if (!isPortAvailable) {
    try {
      selectedPort = await findAvailablePort(preferredPort + 1);
      console.log(`🔄 Using alternative port: ${selectedPort}`);
    } catch (error) {
      console.error('❌ No available ports found');
      process.exit(1);
    }
  }
  
  const duration = performance.now() - startTime;
  console.log(`\n📊 Diagnostics completed in ${duration.toFixed(2)}ms`);
  
  // Check for critical issues
  const criticalIssues = [networkResult, envResult, clerkResult].filter(r => !r.success);
  
  if (criticalIssues.length > 0) {
    console.log('\n🚨 Critical issues detected:');
    criticalIssues.forEach(issue => {
      console.log(`   ❌ ${issue.message}`);
    });
    console.log('\n💡 Run npm run validate:clerk to fix authentication issues');
    process.exit(1);
  }
  
  console.log('\n🎉 All diagnostics passed! Starting development server...\n');
  
  // Start the development server
  startDevelopmentServer(selectedPort);
}

/**
 * Start the Next.js development server with enhanced logging
 */
function startDevelopmentServer(port: number): void {
  const serverProcess = spawn('npx', ['next', 'dev', '--port', port.toString()], {
    stdio: 'inherit',
    shell: true
  });
  
  console.log(`🚀 Starting Next.js development server on port ${port}...`);
  console.log(`📱 Local URL: http://localhost:${port}`);
  console.log(`🌍 Network URL: http://0.0.0.0:${port}`);
  console.log('\n⏳ Waiting for server to be ready...\n');
  
  // Handle server process events
  serverProcess.on('error', (error) => {
    console.error('❌ Failed to start development server:', error);
    process.exit(1);
  });
  
  serverProcess.on('exit', (code) => {
    if (code !== 0) {
      console.error(`❌ Development server exited with code ${code}`);
      process.exit(code || 1);
    }
  });
  
  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down development server...');
    serverProcess.kill('SIGINT');
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down development server...');
    serverProcess.kill('SIGTERM');
    process.exit(0);
  });
}

// Run diagnostics if called directly
if (require.main === module) {
  runDiagnostics().catch((error) => {
    console.error('❌ Diagnostics failed:', error);
    process.exit(1);
  });
}

export { runDiagnostics, checkPortAvailability, findAvailablePort };
