/**
 * Vercel-specific optimizations and memory management
 */

import { NextRequest, NextResponse } from 'next/server';

// Memory management for large file operations
export function withMemoryOptimization<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    try {
      const result = await fn(...args);
      
      // Clean up after operation
      if (global.gc) {
        global.gc();
      }
      
      return result;
    } catch (error) {
      // Clean up on error
      if (global.gc) {
        global.gc();
      }
      throw error;
    }
  };
}

// Vercel-optimized API route wrapper
export function createVercelApiRoute(
  handler: (req: NextRequest) => Promise<NextResponse>
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    // Set memory-friendly headers
    const response = await handler(req);
    
    // Add Vercel-specific headers
    response.headers.set('X-Vercel-Cache', 'MISS');
    response.headers.set('Cache-Control', 'no-store, max-age=0');
    
    return response;
  };
}

// File size limits for Vercel
export const VERCEL_LIMITS = {
  MAX_FILE_SIZE: 20 * 1024 * 1024, // 20MB
  MAX_REQUEST_SIZE: 50 * 1024 * 1024, // 50MB
  MAX_RESPONSE_SIZE: 5 * 1024 * 1024, // 5MB
  FUNCTION_TIMEOUT: 30000, // 30 seconds
} as const;

// Check if file is within Vercel limits
export function validateFileForVercel(file: File): { valid: boolean; error?: string } {
  if (file.size > VERCEL_LIMITS.MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `File size ${(file.size / 1024 / 1024).toFixed(1)}MB exceeds limit of ${VERCEL_LIMITS.MAX_FILE_SIZE / 1024 / 1024}MB`
    };
  }
  
  return { valid: true };
}

// Optimize buffer operations for Vercel
export function optimizeBufferForVercel(buffer: Buffer): Buffer {
  // If buffer is too large, we might need to process in chunks
  if (buffer.length > VERCEL_LIMITS.MAX_REQUEST_SIZE) {
    throw new Error(`Buffer size ${(buffer.length / 1024 / 1024).toFixed(1)}MB exceeds Vercel limit`);
  }
  
  return buffer;
}

// Environment detection
export function isVercelEnvironment(): boolean {
  return process.env.VERCEL === '1' || process.env.VERCEL_ENV !== undefined;
}

// Vercel-specific error handling
export function createVercelErrorResponse(error: unknown, context?: string): NextResponse {
  const isVercel = isVercelEnvironment();
  
  let message = 'Internal server error';
  let details = '';
  
  if (error instanceof Error) {
    message = error.message;
    details = isVercel ? 'Check Vercel function logs for details' : error.stack || '';
  }
  
  console.error(`Vercel API Error${context ? ` (${context})` : ''}:`, error);
  
  return NextResponse.json(
    {
      error: message,
      details: isVercel ? undefined : details,
      timestamp: new Date().toISOString(),
      environment: isVercel ? 'vercel' : 'local'
    },
    { status: 500 }
  );
}
