import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { trackPublicExport } from '@/lib/analytics';

export async function POST(request: NextRequest) {
  try {
  devLog.log('📄 PDF Export API called - Public Mode');

    // Get user (public mode - no authentication required)
    const user = await getCurrentUser();
    if (!user) {
  devLog.log('📄 Using anonymous user for PDF export');
    }
    const userId = user?.id || 'anonymous-' + Date.now();

    const { summaryId } = await request.json();

    if (!summaryId) {
      return NextResponse.json(
        { success: false, error: 'Summary ID is required' },
        { status: 400 }
      );
    }
  devLog.log('📄 PDF Export: Processing summary:', summaryId);

    // PUBLIC MODE: Use mock summary data for demo
    const summary = {
      id: summaryId,
      user_id: userId,
      title: 'Demo Meeting Summary',
      summary_text: 'This is a demo summary for PDF export testing. The meeting covered quarterly results, project updates, and action items for the next sprint.',
      summary: {
        text: 'This is a demo summary for PDF export testing. The meeting covered quarterly results, project updates, and action items for the next sprint.',
        skills: ['Project Management', 'Strategic Planning', 'Team Leadership'],
        redFlags: ['Budget concerns', 'Timeline delays'],
        actions: ['Review budget allocation', 'Update project timeline', 'Schedule follow-up meeting'],
        keyPoints: ['Q4 targets achieved', 'New product launch planned', 'Team expansion needed'],
        participants: ['John Doe', 'Jane Smith', 'Mike Johnson'],
        sentiment: 'positive'
      },
      created_at: new Date().toISOString(),
      source_type: 'demo'
    };
  devLog.log('📄 Dev mode: Using mock summary for PDF export');

    // Generate PDF content
    const pdfContent = `## Meeting Overview
**Date:** ${new Date().toLocaleDateString()}
**Duration:** 45 minutes
**Participants:** 8 team members

## Key Discussion Points

### 1. Revenue Targets
- Q4 target: $2.5M ARR
- Current pipeline: $1.8M
- Gap analysis and action items identified

### 2. Product Roadmap
- New AI features planned for December
- Mobile app beta launch in November
- Integration with Slack and Teams

### 3. Team Expansion
- Hiring 3 new engineers
- Sales team expansion by 2 reps
- Customer success manager onboarding

## Action Items
1. **Sarah** - Finalize Q4 marketing budget by Friday
2. **Mike** - Complete technical architecture review
3. **Lisa** - Schedule customer feedback sessions
4. **Team** - Prepare individual Q4 goals by next week

## Next Steps
- Follow-up meeting scheduled for next Tuesday
- Weekly check-ins to track progress
- Monthly all-hands to review metrics

## Summary
Productive session with clear alignment on Q4 objectives. Team is confident about hitting targets with the outlined strategy.`;

    // Create demo summary data for PDF generation
    const demoSummary = {
      id: summaryId,
      title: summary.title || 'Meeting Summary',
      content: pdfContent,
      summary_data: {
        summary: 'Productive Q4 planning session with clear objectives and action items',
        key_points: [
          'Q4 revenue target set at $2.5M ARR',
          'Product roadmap includes AI features and mobile app',
          'Team expansion planned across engineering and sales',
          'Clear action items assigned with deadlines'
        ],
        action_items: [
          'Finalize Q4 marketing budget',
          'Complete technical architecture review',
          'Schedule customer feedback sessions',
          'Prepare individual Q4 goals'
        ],
        participants: 8,
        duration: '45 minutes',
        sentiment: 'positive'
      },
      tags: ['planning', 'q4', 'revenue', 'roadmap', 'team'],
      created_at: new Date().toISOString(),
      user_id: user.id,
      organization_id: null
    };

    // Create PDF document
    const doc = new PDFDocument({
      margin: 50,
      size: 'A4'
    });

    const chunks: Buffer[] = [];
    
    doc.on('data', (chunk) => {
      chunks.push(chunk);
    });

    // Add header
    doc.fontSize(20)
       .font('Helvetica-Bold')
       .text('Document Summary', { align: 'center' });

    doc.moveDown(1);

    // Add title
    doc.fontSize(16)
       .font('Helvetica-Bold')
       .text(summary.title || 'Untitled Summary');

    doc.moveDown(0.5);

    // Add metadata
    doc.fontSize(10)
       .font('Helvetica')
       .fillColor('gray')
       .text(`Created: ${new Date(summary.created_at).toLocaleString()}`)
       .text(`Source: Demo`)
       .text(`File: demo-export.pdf`);

    // Add demo file information
    doc.text(`File Size: 2.5 MB`)
       .text(`File Type: PDF`);

    doc.moveDown(1);

    // Add separator line
    doc.strokeColor('lightgray')
       .lineWidth(1)
       .moveTo(50, doc.y)
       .lineTo(550, doc.y)
       .stroke();

    doc.moveDown(1);

    // Add content
    doc.fontSize(12)
       .font('Helvetica')
       .fillColor('black')
       .text('Summary Content:', { underline: true });

    doc.moveDown(0.5);

    // Split content into paragraphs and add them
    const content = summary.summary_text || summary.summary?.text || 'No content available';
    const paragraphs = content.split('\n\n');

    paragraphs.forEach((paragraph: string, index: number) => {
      if (paragraph.trim()) {
        doc.text(paragraph.trim(), {
          align: 'justify',
          lineGap: 2
        });
        
        if (index < paragraphs.length - 1) {
          doc.moveDown(0.5);
        }
      }
    });

    // Add metadata section (demo data)
    const demoMetadata = {
      processing_time: '1.25 seconds',
      ai_model: 'deepseek-chat',
      confidence_score: '92%',
      word_count: 450,
      language: 'English'
    };

    doc.addPage();

    doc.fontSize(16)
       .font('Helvetica-Bold')
       .text('Document Metadata');

    doc.moveDown(1);

    Object.entries(demoMetadata).forEach(([key, value]) => {
      doc.fontSize(10)
         .font('Helvetica-Bold')
         .text(`${key.charAt(0).toUpperCase() + key.slice(1)}:`, { continued: true })
         .font('Helvetica')
         .text(` ${String(value)}`);
    });

    // Add footer
    const pageCount = doc.bufferedPageRange().count;
    for (let i = 0; i < pageCount; i++) {
      doc.switchToPage(i);
      
      doc.fontSize(8)
         .font('Helvetica')
         .fillColor('gray')
         .text(
           `Generated by Slack Summary Scribe - Page ${i + 1} of ${pageCount}`,
           50,
           doc.page.height - 50,
           { align: 'center' }
         );
    }

    // Finalize PDF
    doc.end();

    // Wait for PDF generation to complete
    const buffer = await new Promise<Buffer>((resolve) => {
      doc.on('end', () => {
        resolve(Buffer.concat(chunks));
      });
    });

    // Track export activity for analytics
    trackPublicExport('pdf', summaryId);
  devLog.log('📄 PDF Export: Export completed successfully');

    // Log completion
  devLog.log('🔔 PDF Export: Export tracking completed');

    // Return PDF file
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${summary.title || 'summary'}.pdf"`,
        'Content-Length': buffer.length.toString()
      }
    });

  } catch (error) {
    console.error('PDF export error:', error);

    return NextResponse.json(
      { success: false, error: 'Export failed' },
      { status: 500 }
    );
  }
}
