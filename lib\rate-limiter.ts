import { devLog } from '@/lib/console-cleaner';
/**
 * Advanced Rate Limiting System
 * 
 * Implements multiple rate limiting strategies including:
 * - Per-user rate limiting
 * - Per-IP rate limiting
 * - Per-endpoint rate limiting
 * - Sliding window algorithm
 * - Burst protection
 */

import { NextRequest } from 'next/server';
import { logSecurityEvent } from './auth-protection';

// Rate limiting configuration
export const RATE_LIMIT_CONFIG = {
  // Global limits
  GLOBAL_REQUESTS_PER_MINUTE: 1000,
  GLOBAL_REQUESTS_PER_HOUR: 10000,
  
  // Per-user limits
  USER_REQUESTS_PER_MINUTE: 100,
  USER_REQUESTS_PER_HOUR: 1000,
  USER_REQUESTS_PER_DAY: 10000,
  
  // Per-IP limits
  IP_REQUESTS_PER_MINUTE: 60,
  IP_REQUESTS_PER_HOUR: 600,
  
  // Endpoint-specific limits
  ENDPOINTS: {
    '/api/upload': { perMinute: 10, perHour: 50 },
    '/api/summarize': { perMinute: 20, perHour: 200 },
    '/api/ai': { perMinute: 30, perHour: 300 },
    '/api/export': { perMinute: 15, perHour: 100 },
    '/api/payments': { perMinute: 5, perHour: 20 },
    '/api/auth': { perMinute: 10, perHour: 50 },
    '/api/slack': { perMinute: 30, perHour: 300 },
  },
  
  // Burst protection
  BURST_THRESHOLD: 10, // Max requests in 10 seconds
  BURST_WINDOW_SECONDS: 10,
  
  // Cleanup intervals
  CLEANUP_INTERVAL_MS: 5 * 60 * 1000, // 5 minutes
  MEMORY_CLEANUP_THRESHOLD: 10000, // Max entries before cleanup
};

// In-memory storage for rate limiting
const rateLimitStore = new Map<string, {
  requests: number[];
  burstRequests: number[];
  lastCleanup: number;
}>();

// Global counters
let globalRequestCount = { minute: 0, hour: 0, lastReset: { minute: 0, hour: 0 } };

/**
 * Rate limiter class with sliding window algorithm
 */
export class RateLimiter {
  private static instance: RateLimiter;
  
  static getInstance(): RateLimiter {
    if (!RateLimiter.instance) {
      RateLimiter.instance = new RateLimiter();
    }
    return RateLimiter.instance;
  }
  
  private constructor() {
    // Start cleanup interval
    setInterval(() => this.cleanup(), RATE_LIMIT_CONFIG.CLEANUP_INTERVAL_MS);
  }
  
  /**
   * Check if request is allowed
   */
  async checkRateLimit(
    key: string,
    limits: { perMinute?: number; perHour?: number; perDay?: number },
    req?: NextRequest
  ): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
    retryAfter?: number;
    reason?: string;
  }> {
    const now = Date.now();
    
    // Get or create rate limit data
    let data = rateLimitStore.get(key);
    if (!data) {
      data = {
        requests: [],
        burstRequests: [],
        lastCleanup: now
      };
      rateLimitStore.set(key, data);
    }
    
    // Clean old requests
    this.cleanupKey(key, data, now);
    
    // Check burst protection
    const burstCheck = this.checkBurstLimit(data, now);
    if (!burstCheck.allowed) {
      this.logRateLimitViolation(key, 'BURST_LIMIT_EXCEEDED', req);
      return burstCheck;
    }
    
    // Check minute limit
    if (limits.perMinute) {
      const minuteCheck = this.checkTimeWindow(
        data.requests,
        now,
        60 * 1000,
        limits.perMinute,
        'minute'
      );
      if (!minuteCheck.allowed) {
        this.logRateLimitViolation(key, 'MINUTE_LIMIT_EXCEEDED', req);
        return minuteCheck;
      }
    }
    
    // Check hour limit
    if (limits.perHour) {
      const hourCheck = this.checkTimeWindow(
        data.requests,
        now,
        60 * 60 * 1000,
        limits.perHour,
        'hour'
      );
      if (!hourCheck.allowed) {
        this.logRateLimitViolation(key, 'HOUR_LIMIT_EXCEEDED', req);
        return hourCheck;
      }
    }
    
    // Check day limit
    if (limits.perDay) {
      const dayCheck = this.checkTimeWindow(
        data.requests,
        now,
        24 * 60 * 60 * 1000,
        limits.perDay,
        'day'
      );
      if (!dayCheck.allowed) {
        this.logRateLimitViolation(key, 'DAY_LIMIT_EXCEEDED', req);
        return dayCheck;
      }
    }
    
    // Add request to tracking
    data.requests.push(now);
    data.burstRequests.push(now);
    
    // Calculate remaining requests (use most restrictive limit)
    const remaining = this.calculateRemaining(data, now, limits);
    
    return {
      allowed: true,
      remaining,
      resetTime: now + (60 * 1000) // Next minute
    };
  }
  
  /**
   * Check burst protection
   */
  private checkBurstLimit(data: any, now: number) {
    const burstWindow = RATE_LIMIT_CONFIG.BURST_WINDOW_SECONDS * 1000;
    const recentBurstRequests = data.burstRequests.filter(
      (timestamp: number) => now - timestamp < burstWindow
    );
    
    if (recentBurstRequests.length >= RATE_LIMIT_CONFIG.BURST_THRESHOLD) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: now + burstWindow,
        retryAfter: RATE_LIMIT_CONFIG.BURST_WINDOW_SECONDS,
        reason: 'Burst limit exceeded'
      };
    }
    
    return { allowed: true, remaining: 0, resetTime: 0 };
  }
  
  /**
   * Check time window limits
   */
  private checkTimeWindow(
    requests: number[],
    now: number,
    windowMs: number,
    limit: number,
    windowName: string
  ) {
    const windowStart = now - windowMs;
    const requestsInWindow = requests.filter(timestamp => timestamp > windowStart);
    
    if (requestsInWindow.length >= limit) {
      const oldestRequest = Math.min(...requestsInWindow);
      const retryAfter = Math.ceil((oldestRequest + windowMs - now) / 1000);
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: oldestRequest + windowMs,
        retryAfter,
        reason: `${windowName} limit exceeded`
      };
    }
    
    return {
      allowed: true,
      remaining: limit - requestsInWindow.length,
      resetTime: now + windowMs
    };
  }
  
  /**
   * Calculate remaining requests
   */
  private calculateRemaining(data: any, now: number, limits: any): number {
    const remainingCounts = [];
    
    if (limits.perMinute) {
      const minuteRequests = data.requests.filter(
        (timestamp: number) => now - timestamp < 60 * 1000
      );
      remainingCounts.push(limits.perMinute - minuteRequests.length);
    }
    
    if (limits.perHour) {
      const hourRequests = data.requests.filter(
        (timestamp: number) => now - timestamp < 60 * 60 * 1000
      );
      remainingCounts.push(limits.perHour - hourRequests.length);
    }
    
    return Math.min(...remainingCounts.filter(count => count >= 0));
  }
  
  /**
   * Clean up old requests for a specific key
   */
  private cleanupKey(key: string, data: any, now: number) {
    const dayAgo = now - (24 * 60 * 60 * 1000);
    const burstWindow = now - (RATE_LIMIT_CONFIG.BURST_WINDOW_SECONDS * 1000);
    
    // Clean old requests (keep last 24 hours)
    data.requests = data.requests.filter((timestamp: number) => timestamp > dayAgo);
    
    // Clean old burst requests
    data.burstRequests = data.burstRequests.filter((timestamp: number) => timestamp > burstWindow);
    
    data.lastCleanup = now;
  }
  
  /**
   * Global cleanup of rate limit store
   */
  private cleanup() {
    const now = Date.now();
    const dayAgo = now - (24 * 60 * 60 * 1000);
    
    // Remove entries with no recent requests
    for (const [key, data] of rateLimitStore.entries()) {
      if (data.requests.length === 0 || Math.max(...data.requests) < dayAgo) {
        rateLimitStore.delete(key);
      }
    }
    
    // If store is still too large, remove oldest entries
    if (rateLimitStore.size > RATE_LIMIT_CONFIG.MEMORY_CLEANUP_THRESHOLD) {
      const entries = Array.from(rateLimitStore.entries());
      entries.sort((a, b) => {
        const aLatest = a[1].requests.length > 0 ? Math.max(...a[1].requests) : 0;
        const bLatest = b[1].requests.length > 0 ? Math.max(...b[1].requests) : 0;
        return aLatest - bLatest;
      });
      
      // Remove oldest 20%
      const toRemove = Math.floor(entries.length * 0.2);
      for (let i = 0; i < toRemove; i++) {
        rateLimitStore.delete(entries[i][0]);
      }
    }
  devLog.log(`Rate limiter cleanup: ${rateLimitStore.size} entries remaining`);
  }
  
  /**
   * Log rate limit violations
   */
  private logRateLimitViolation(key: string, reason: string, req?: NextRequest) {
    const [type, identifier] = key.split(':');
    
    logSecurityEvent({
      userId: type === 'user' ? identifier : undefined,
      action: 'RATE_LIMIT_VIOLATION',
      resource: 'rate_limiter',
      details: {
        key,
        reason,
        type,
        identifier
      },
      ip: req?.headers.get('x-forwarded-for') || req?.headers.get('x-real-ip') || undefined,
      userAgent: req?.headers.get('user-agent') || undefined
    });
  }
  
  /**
   * Get current rate limit status
   */
  async getRateLimitStatus(key: string): Promise<{
    requests: number;
    remaining: number;
    resetTime: number;
  }> {
    const data = rateLimitStore.get(key);
    if (!data) {
      return { requests: 0, remaining: 100, resetTime: Date.now() + 60000 };
    }
    
    const now = Date.now();
    const minuteAgo = now - (60 * 1000);
    const recentRequests = data.requests.filter(timestamp => timestamp > minuteAgo);
    
    return {
      requests: recentRequests.length,
      remaining: Math.max(0, RATE_LIMIT_CONFIG.USER_REQUESTS_PER_MINUTE - recentRequests.length),
      resetTime: now + (60 * 1000)
    };
  }
}

/**
 * Convenience function for checking rate limits
 */
export async function checkRateLimit(
  userId: string | undefined,
  ip: string | undefined,
  endpoint: string,
  req?: NextRequest
): Promise<{
  allowed: boolean;
  headers: Record<string, string>;
  retryAfter?: number;
}> {
  const rateLimiter = RateLimiter.getInstance();
  
  // Determine rate limit key and limits
  const key = userId ? `user:${userId}` : `ip:${ip || 'unknown'}`;
  const endpointLimits = RATE_LIMIT_CONFIG.ENDPOINTS[endpoint as keyof typeof RATE_LIMIT_CONFIG.ENDPOINTS];
  
  const limits = {
    perMinute: endpointLimits?.perMinute || RATE_LIMIT_CONFIG.USER_REQUESTS_PER_MINUTE,
    perHour: endpointLimits?.perHour || RATE_LIMIT_CONFIG.USER_REQUESTS_PER_HOUR,
    perDay: RATE_LIMIT_CONFIG.USER_REQUESTS_PER_DAY
  };
  
  const result = await rateLimiter.checkRateLimit(key, limits, req);
  
  const headers = {
    'X-RateLimit-Limit': limits.perMinute.toString(),
    'X-RateLimit-Remaining': result.remaining.toString(),
    'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString(),
  };
  
  if (!result.allowed && result.retryAfter) {
    headers['Retry-After'] = result.retryAfter.toString();
  }
  
  return {
    allowed: result.allowed,
    headers,
    retryAfter: result.retryAfter
  };
}
