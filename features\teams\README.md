# 👥 Team Management & Multi-User Features

## Overview
Comprehensive team management system with role-based access control, shared workspaces, and organization-level features.

## Features Implemented

### 1. Role-Based Access Control (RBAC)
- **Owner**: Full access, billing management, team management
- **Admin**: Team management, workspace settings, user management
- **Editor**: Create/edit summaries, manage own content
- **Viewer**: Read-only access to summaries and reports

### 2. Team Management
- **Invite System**: Email-based team invitations with role assignment
- **Member Management**: Add, remove, and modify team member roles
- **Workspace Sharing**: Share Slack workspaces across team members
- **Organization Settings**: Team-wide preferences and configurations

### 3. Multi-Workspace Support
- **Shared Access**: Multiple team members can access same Slack workspaces
- **Permission Inheritance**: Workspace permissions based on team roles
- **Usage Tracking**: Monitor workspace usage across team members

## File Structure
```
/features/teams/
├── README.md                    # This file
├── types.ts                    # TypeScript interfaces and types
├── rbac.ts                     # Role-based access control logic
├── team-management.ts          # Core team management functions
├── invitations.ts              # Team invitation system
├── workspace-sharing.ts        # Shared workspace management
├── permissions.ts              # Permission checking utilities
├── team-settings.ts            # Organization-level settings
└── components/
    ├── TeamDashboard.tsx       # Main team management UI
    ├── InviteMember.tsx        # Invite new team members
    ├── MemberList.tsx          # Team member management
    ├── RoleSelector.tsx        # Role assignment component
    └── WorkspaceSharing.tsx    # Workspace sharing interface
```

## Database Schema
```sql
-- Enhanced organizations table
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}';
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS plan TEXT DEFAULT 'FREE';
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS max_members INTEGER DEFAULT 1;

-- Enhanced user_organizations table with roles
ALTER TABLE user_organizations ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'member';
ALTER TABLE user_organizations ADD COLUMN IF NOT EXISTS permissions JSONB DEFAULT '{}';
ALTER TABLE user_organizations ADD COLUMN IF NOT EXISTS invited_by UUID REFERENCES auth.users(id);
ALTER TABLE user_organizations ADD COLUMN IF NOT EXISTS invited_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE user_organizations ADD COLUMN IF NOT EXISTS accepted_at TIMESTAMP WITH TIME ZONE;

-- Team invitations table
CREATE TABLE team_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'viewer',
  invited_by UUID REFERENCES auth.users(id),
  token TEXT UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  accepted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Shared workspaces table
CREATE TABLE shared_workspaces (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  slack_team_id TEXT NOT NULL,
  slack_team_name TEXT NOT NULL,
  shared_by UUID REFERENCES auth.users(id),
  permissions JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Environment Variables
```bash
# Team Management
TEAM_INVITATION_EXPIRY_HOURS=72
MAX_TEAM_SIZE_FREE=3
MAX_TEAM_SIZE_PRO=10
MAX_TEAM_SIZE_ENTERPRISE=50
TEAM_INVITATION_EMAIL_TEMPLATE=team-invite
```

## Usage Examples

### Check User Permissions
```typescript
import { checkPermission } from '@/features/teams/permissions';

const canEdit = await checkPermission(userId, organizationId, 'summaries:edit');
if (canEdit) {
  // Allow editing
}
```

### Invite Team Member
```typescript
import { inviteTeamMember } from '@/features/teams/invitations';

const invitation = await inviteTeamMember({
  organizationId,
  email: '<EMAIL>',
  role: 'editor',
  invitedBy: currentUserId
});
```

### Share Workspace
```typescript
import { shareWorkspace } from '@/features/teams/workspace-sharing';

await shareWorkspace({
  organizationId,
  slackTeamId: 'T1234567',
  slackTeamName: 'My Team',
  sharedBy: userId,
  permissions: { canSummarize: true, canExport: true }
});
```

## API Endpoints
- `GET /api/teams/members` - List team members
- `POST /api/teams/invite` - Invite new team member
- `PUT /api/teams/members/:id/role` - Update member role
- `DELETE /api/teams/members/:id` - Remove team member
- `GET /api/teams/workspaces` - List shared workspaces
- `POST /api/teams/workspaces/share` - Share workspace with team
- `GET /api/teams/settings` - Get organization settings
- `PUT /api/teams/settings` - Update organization settings

## Role Permissions Matrix

| Permission | Owner | Admin | Editor | Viewer |
|------------|-------|-------|--------|--------|
| Manage billing | ✅ | ❌ | ❌ | ❌ |
| Invite members | ✅ | ✅ | ❌ | ❌ |
| Remove members | ✅ | ✅ | ❌ | ❌ |
| Change roles | ✅ | ✅* | ❌ | ❌ |
| Share workspaces | ✅ | ✅ | ✅ | ❌ |
| Create summaries | ✅ | ✅ | ✅ | ❌ |
| Edit summaries | ✅ | ✅ | ✅** | ❌ |
| View summaries | ✅ | ✅ | ✅ | ✅ |
| Export data | ✅ | ✅ | ✅ | ❌ |
| Org settings | ✅ | ✅ | ❌ | ❌ |

*Admins cannot change owner role or promote to admin
**Editors can only edit their own summaries

## Best Practices
1. **Principle of Least Privilege**: Grant minimum necessary permissions
2. **Role Inheritance**: Higher roles inherit lower role permissions
3. **Audit Trail**: Log all role changes and member actions
4. **Invitation Security**: Use secure tokens with expiration
5. **Workspace Isolation**: Ensure proper workspace access control
