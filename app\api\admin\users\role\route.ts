import { NextRequest, NextResponse } from 'next/server';
import { createRBACProtectedRoute, Permission } from '@/lib/auth-protection';
import { createSecureApiRoute } from '@/lib/api-security';
import { rbac, UserRole } from '@/lib/rbac';

/**
 * Admin User Role Management API
 * 
 * Allows admins to assign and revoke user roles
 */
export const POST = createRBACProtectedRoute(
  async (request: NextRequest, authResult) => {
    try {
      const { userId, role, organizationId } = await request.json();
      
      if (!userId || !role) {
        return NextResponse.json(
          { error: 'Missing required fields: userId, role' },
          { status: 400 }
        );
      }
      
      // Validate role
      if (!Object.values(UserRole).includes(role as UserRole)) {
        return NextResponse.json(
          { error: 'Invalid role specified' },
          { status: 400 }
        );
      }
      
      // Prevent self-role modification for security
      if (userId === authResult.userId) {
        return NextResponse.json(
          { error: 'Cannot modify your own role' },
          { status: 403 }
        );
      }
      
      // Check if admin can assign this role
      const adminPermissions = await rbac.getUserPermissions(authResult.userId, organizationId);
      
      // Super admins can assign any role
      // Regular admins cannot assign super admin roles
      if (adminPermissions?.role !== UserRole.SUPER_ADMIN && role === UserRole.SUPER_ADMIN) {
        return NextResponse.json(
          { error: 'Insufficient permissions to assign super admin role' },
          { status: 403 }
        );
      }
      
      // Assign the role
      const success = await rbac.assignRole(
        userId,
        role as UserRole,
        organizationId,
        authResult.userId
      );
      
      if (!success) {
        return NextResponse.json(
          { error: 'Failed to assign role' },
          { status: 500 }
        );
      }
      
      return NextResponse.json({ 
        success: true,
        message: `Role ${role} assigned to user ${userId}`,
        userId,
        role,
        organizationId
      });
      
    } catch (error) {
      console.error('Failed to assign user role:', error);
      return NextResponse.json(
        { error: 'Failed to assign role' },
        { status: 500 }
      );
    }
  },
  {
    requiredPermission: Permission.ROLE_ASSIGN,
    requireAuth: true,
    rateLimit: 10, // 10 requests per minute
    auditLog: true,
    allowedMethods: ['POST']
  }
);

export const DELETE = createRBACProtectedRoute(
  async (request: NextRequest, authResult) => {
    try {
      const { userId, organizationId } = await request.json();
      
      if (!userId) {
        return NextResponse.json(
          { error: 'Missing required field: userId' },
          { status: 400 }
        );
      }
      
      // Prevent self-role modification for security
      if (userId === authResult.userId) {
        return NextResponse.json(
          { error: 'Cannot revoke your own role' },
          { status: 403 }
        );
      }
      
      // Revoke the role
      const success = await rbac.revokeRole(
        userId,
        organizationId,
        authResult.userId
      );
      
      if (!success) {
        return NextResponse.json(
          { error: 'Failed to revoke role' },
          { status: 500 }
        );
      }
      
      return NextResponse.json({ 
        success: true,
        message: `Role revoked from user ${userId}`,
        userId,
        organizationId
      });
      
    } catch (error) {
      console.error('Failed to revoke user role:', error);
      return NextResponse.json(
        { error: 'Failed to revoke role' },
        { status: 500 }
      );
    }
  },
  {
    requiredPermission: Permission.ROLE_REVOKE,
    requireAuth: true,
    rateLimit: 10, // 10 requests per minute
    auditLog: true,
    allowedMethods: ['DELETE']
  }
);
