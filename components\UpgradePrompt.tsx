'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Crown, 
  Zap, 
  Check, 
  X, 
  Star,
  TrendingUp,
  Users,
  Shield,
  Infinity
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface UpgradePromptProps {
  isOpen: boolean;
  onClose: () => void;
  message: string;
  trialDaysRemaining: number;
  trialExpired: boolean;
  limitReached?: string;
  onUpgradeClick?: () => void;
}

const PRICING_PLANS = [
  {
    id: 'pro',
    name: 'Pro',
    price: 29,
    period: 'month',
    popular: true,
    features: [
      'Unlimited AI summaries',
      'All AI models (GPT-4o, Claude)',
      'Advanced Slack integration',
      'Priority support',
      'Custom templates',
      'Team collaboration (5 members)',
      'Advanced analytics',
      'Export to all formats'
    ],
    limits: {
      summaries: 'Unlimited',
      exports: 'Unlimited',
      aiRequests: 'Unlimited',
      fileUploads: 'Unlimited',
      slackConnections: '5 workspaces'
    }
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 99,
    period: 'month',
    popular: false,
    features: [
      'Everything in Pro',
      'Unlimited team members',
      'Custom AI models',
      '24/7 priority support',
      'Advanced security',
      'SSO integration',
      'Audit logs',
      'Custom integrations',
      'Dedicated account manager'
    ],
    limits: {
      summaries: 'Unlimited',
      exports: 'Unlimited',
      aiRequests: 'Unlimited',
      fileUploads: 'Unlimited',
      slackConnections: 'Unlimited'
    }
  }
];

export default function UpgradePrompt({
  isOpen,
  onClose,
  message,
  trialDaysRemaining,
  trialExpired,
  limitReached,
  onUpgradeClick
}: UpgradePromptProps) {
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState('pro');

  const handleUpgrade = (planId: string) => {
    if (onUpgradeClick) {
      onUpgradeClick();
    }
    router.push(`/billing?plan=${planId}`);
    onClose();
  };

  const getUrgencyMessage = () => {
    if (trialExpired) {
      return {
        text: 'Your trial has expired',
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200'
      };
    }
    
    if (limitReached) {
      return {
        text: `${limitReached} limit reached`,
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200'
      };
    }
    
    if (trialDaysRemaining <= 2) {
      return {
        text: `Only ${trialDaysRemaining} days left in trial`,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200'
      };
    }
    
    return {
      text: 'Unlock unlimited access',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    };
  };

  const urgency = getUrgencyMessage();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center space-x-2">
            <Crown className="h-6 w-6 text-yellow-500" />
            <DialogTitle className="text-2xl">Upgrade Your Plan</DialogTitle>
          </div>
          <DialogDescription className="text-lg">
            {message}
          </DialogDescription>
        </DialogHeader>

        {/* Urgency Banner */}
        <div className={`p-4 rounded-lg border ${urgency.bgColor} ${urgency.borderColor}`}>
          <div className="flex items-center space-x-2">
            <TrendingUp className={`h-5 w-5 ${urgency.color}`} />
            <span className={`font-medium ${urgency.color}`}>
              {urgency.text}
            </span>
          </div>
        </div>

        {/* Pricing Plans */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          {PRICING_PLANS.map((plan) => (
            <Card 
              key={plan.id}
              className={`relative cursor-pointer transition-all duration-200 ${
                selectedPlan === plan.id 
                  ? 'ring-2 ring-blue-500 shadow-lg' 
                  : 'hover:shadow-md'
              } ${plan.popular ? 'border-blue-500' : ''}`}
              onClick={() => setSelectedPlan(plan.id)}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white px-3 py-1">
                    <Star className="h-3 w-3 mr-1" />
                    Most Popular
                  </Badge>
                </div>
              )}
              
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold">{plan.name}</h3>
                  <div className="mt-2">
                    <span className="text-3xl font-bold">${plan.price}</span>
                    <span className="text-gray-600">/{plan.period}</span>
                  </div>
                </div>

                {/* Key Limits */}
                <div className="mb-6 p-3 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-2 flex items-center">
                    <Infinity className="h-4 w-4 mr-1" />
                    Usage Limits
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>AI Summaries:</span>
                      <span className="font-medium">{plan.limits.summaries}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Exports:</span>
                      <span className="font-medium">{plan.limits.exports}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Slack Workspaces:</span>
                      <span className="font-medium">{plan.limits.slackConnections}</span>
                    </div>
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-3 mb-6">
                  {plan.features.slice(0, 6).map((feature, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                  {plan.features.length > 6 && (
                    <div className="text-sm text-gray-600">
                      +{plan.features.length - 6} more features
                    </div>
                  )}
                </div>

                <Button
                  onClick={() => handleUpgrade(plan.id)}
                  className={`w-full ${
                    plan.popular 
                      ? 'bg-blue-600 hover:bg-blue-700' 
                      : 'bg-gray-600 hover:bg-gray-700'
                  }`}
                >
                  {selectedPlan === plan.id && (
                    <Check className="h-4 w-4 mr-2" />
                  )}
                  Choose {plan.name}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Benefits Section */}
        <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Zap className="h-5 w-5 mr-2 text-blue-600" />
            Why Upgrade Now?
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Infinity className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium">Unlimited Usage</h4>
                <p className="text-sm text-gray-600">No more limits on summaries, exports, or AI requests</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <h4 className="font-medium">Team Collaboration</h4>
                <p className="text-sm text-gray-600">Invite team members and collaborate seamlessly</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Shield className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <h4 className="font-medium">Priority Support</h4>
                <p className="text-sm text-gray-600">Get help when you need it with priority support</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-sm text-gray-600">
            💳 Secure payment • 📞 Cancel anytime • 🔒 30-day money-back guarantee
          </div>
          <Button variant="ghost" onClick={onClose}>
            <X className="h-4 w-4 mr-2" />
            Maybe Later
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
