#!/bin/bash

# Slack Summary Scribe - Public SaaS Quick Start Script
# This script helps you get the application running quickly in public mode

set -e

echo "🚀 Slack Summary Scribe - Public SaaS Quick Start"
echo "=============================================="
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    echo "   Please upgrade Node.js to version 18 or higher."
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm $(npm -v) detected"

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "⚠️  .env.local file not found. Creating template..."
    cat > .env.local << 'EOF'
# Slack Summary Scribe - Public SaaS Mode Configuration
# Copy this file to .env.local and fill in your actual API keys

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_SITE_URL=http://localhost:3001
NODE_ENV=development

# =============================================================================
# AI MODELS & SUMMARIZATION (CRITICAL)
# =============================================================================

# OpenAI API (Primary AI Provider)
OPENAI_API_KEY=sk-your_openai_api_key_here

# OpenRouter API (Fallback AI Provider)
OPENROUTER_API_KEY=sk-or-your_openrouter_api_key_here

# =============================================================================
# DATABASE & STORAGE
# =============================================================================

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =============================================================================
# ANALYTICS & MONITORING
# =============================================================================

# PostHog Analytics
NEXT_PUBLIC_POSTHOG_KEY=phc_your_posthog_key_here
POSTHOG_API_KEY=phx_your_posthog_api_key_here
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# =============================================================================
# NOTIFICATIONS & INTEGRATIONS
# =============================================================================

# Slack Integration
SLACK_BOT_TOKEN=xoxb-your_slack_bot_token_here
SLACK_APP_TOKEN=xapp-your_slack_app_token_here
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url

# =============================================================================
# EXPORT & INTEGRATION SERVICES
# =============================================================================

# Notion Integration
NOTION_CLIENT_ID=your_notion_client_id_here
NOTION_CLIENT_SECRET=your_notion_client_secret_here

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

NEXT_PUBLIC_FETCH_TIMEOUT=15000
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_DEMO_MODE=true
EOF
    echo "✅ Created .env.local template"
    echo ""
    echo "⚠️  IMPORTANT: Please edit .env.local and add your actual API keys before continuing."
    echo "   At minimum, you need:"
    echo "   - OPENAI_API_KEY (or OPENROUTER_API_KEY)"
    echo "   - NEXT_PUBLIC_SUPABASE_URL"
    echo "   - NEXT_PUBLIC_SUPABASE_ANON_KEY"
    echo ""
    echo "   Press Enter when you've updated .env.local, or Ctrl+C to exit..."
    read -r
else
    echo "✅ .env.local file found"
fi

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
npm install

# Check if build is needed
if [ ! -d ".next" ]; then
    echo ""
    echo "🔨 Building application..."
    npm run build
else
    echo "✅ Application already built"
fi

# Check environment variables
echo ""
echo "🔍 Checking environment configuration..."

# Check for required variables
REQUIRED_VARS=("OPENAI_API_KEY" "NEXT_PUBLIC_SUPABASE_URL" "NEXT_PUBLIC_SUPABASE_ANON_KEY")
MISSING_VARS=()

for var in "${REQUIRED_VARS[@]}"; do
    if ! grep -q "^${var}=" .env.local || grep -q "^${var}=.*your_.*_here" .env.local; then
        MISSING_VARS+=("$var")
    fi
done

if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    echo "⚠️  Warning: Some required environment variables are missing or have placeholder values:"
    for var in "${MISSING_VARS[@]}"; do
        echo "   - $var"
    done
    echo ""
    echo "   The app will work with limited functionality. For full features, please configure all required variables."
    echo ""
fi

# Check for optional variables
OPTIONAL_VARS=("NEXT_PUBLIC_POSTHOG_KEY" "SLACK_WEBHOOK_URL" "NOTION_CLIENT_ID")
OPTIONAL_MISSING=()

for var in "${OPTIONAL_VARS[@]}"; do
    if ! grep -q "^${var}=" .env.local || grep -q "^${var}=.*your_.*_here" .env.local; then
        OPTIONAL_MISSING+=("$var")
    fi
done

if [ ${#OPTIONAL_MISSING[@]} -gt 0 ]; then
    echo "ℹ️  Optional features not configured:"
    for var in "${OPTIONAL_MISSING[@]}"; do
        case $var in
            "NEXT_PUBLIC_POSTHOG_KEY")
                echo "   - Analytics tracking (PostHog)"
                ;;
            "SLACK_WEBHOOK_URL")
                echo "   - Slack notifications"
                ;;
            "NOTION_CLIENT_ID")
                echo "   - Notion export"
                ;;
        esac
    done
    echo ""
fi

# Start the application
echo ""
echo "🚀 Starting Slack Summary Scribe in public mode..."
echo ""
echo "📱 The application will be available at: http://localhost:3001"
echo "🎯 Features available:"
echo "   - AI-powered summarization"
echo "   - PDF and Excel export"
echo "   - Dashboard with analytics"
echo "   - File upload support"
echo "   - No authentication required"
echo ""
echo "🛑 Press Ctrl+C to stop the server"
echo ""

# Start the development server
npm run dev 