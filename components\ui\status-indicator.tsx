'use client';

import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Wifi, 
  WifiOff, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  RefreshCw,
  Activity,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatusIndicatorProps {
  responseTime?: number | null;
  lastUpdate?: string | null;
  isOnline?: boolean;
  className?: string;
}

/**
 * Real-time Status Indicator Component
 * 
 * Shows connection status, response times, and last update timestamp
 */
export function StatusIndicator({
  responseTime,
  lastUpdate,
  isOnline = true,
  className
}: StatusIndicatorProps) {
  const [currentTime, setCurrentTime] = useState<Date | null>(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    setCurrentTime(new Date());

    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const getStatusColor = () => {
    if (!isOnline) return 'text-red-600 bg-red-50 border-red-200';
    if (!responseTime) return 'text-gray-600 bg-gray-50 border-gray-200';
    if (responseTime < 500) return 'text-green-600 bg-green-50 border-green-200';
    if (responseTime < 2000) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getStatusIcon = () => {
    if (!isOnline) return <WifiOff className="h-3 w-3" />;
    if (!responseTime) return <Activity className="h-3 w-3" />;
    if (responseTime < 500) return <Zap className="h-3 w-3" />;
    if (responseTime < 2000) return <Clock className="h-3 w-3" />;
    return <AlertTriangle className="h-3 w-3" />;
  };

  const getStatusText = () => {
    if (!isOnline) return 'Offline';
    if (!responseTime) return 'Connecting...';
    if (responseTime < 500) return 'Fast';
    if (responseTime < 2000) return 'Normal';
    return 'Slow';
  };

  const getTimeSinceUpdate = () => {
    if (!lastUpdate || !currentTime || !isClient) return null;

    const updateTime = new Date(lastUpdate);
    const diffMs = currentTime.getTime() - updateTime.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);

    if (diffSeconds < 60) return `${diffSeconds}s ago`;
    if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}m ago`;
    return `${Math.floor(diffSeconds / 3600)}h ago`;
  };

  return (
    <div className={cn("flex items-center space-x-2 text-xs", className)}>
      {/* Connection Status */}
      <Badge 
        variant="outline" 
        className={cn("gap-1 px-2 py-1", getStatusColor())}
      >
        {getStatusIcon()}
        <span>{getStatusText()}</span>
        {responseTime && <span>{responseTime}ms</span>}
      </Badge>

      {/* Last Update Time */}
      {lastUpdate && (
        <span className="text-muted-foreground">
          Updated {getTimeSinceUpdate()}
        </span>
      )}
    </div>
  );
}

/**
 * Floating Status Widget for Dashboard
 */
export function FloatingStatusWidget({ 
  responseTime, 
  lastUpdate, 
  isOnline = true,
  onRefresh,
  className 
}: StatusIndicatorProps & { onRefresh?: () => void }) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className={cn(
      "fixed bottom-4 right-4 z-50 transition-all duration-200",
      isExpanded ? "w-64" : "w-auto",
      className
    )}>
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center space-x-2 text-sm hover:text-primary transition-colors"
          >
            <StatusIndicator 
              responseTime={responseTime}
              lastUpdate={lastUpdate}
              isOnline={isOnline}
            />
          </button>

          {onRefresh && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefresh}
              className="h-6 w-6 p-0 ml-2"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          )}
        </div>

        {isExpanded && (
          <div className="mt-3 pt-3 border-t space-y-2 text-xs text-muted-foreground">
            <div className="flex justify-between">
              <span>Status:</span>
              <span className={isOnline ? 'text-green-600' : 'text-red-600'}>
                {isOnline ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            
            {responseTime && (
              <div className="flex justify-between">
                <span>Response:</span>
                <span>{responseTime}ms</span>
              </div>
            )}
            
            {lastUpdate && (
              <div className="flex justify-between">
                <span>Last Update:</span>
                <span>{new Date(lastUpdate).toLocaleTimeString()}</span>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Performance Metrics Display
 */
export function PerformanceMetrics({ 
  metrics 
}: { 
  metrics: {
    responseTime?: number;
    queryTime?: number;
    renderTime?: number;
    totalTime?: number;
  }
}) {
  if (!metrics.responseTime) return null;

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
      {metrics.responseTime && (
        <div className="flex flex-col items-center p-2 bg-gray-50 rounded">
          <span className="text-muted-foreground">API</span>
          <span className="font-mono font-medium">{metrics.responseTime}ms</span>
        </div>
      )}
      
      {metrics.queryTime && (
        <div className="flex flex-col items-center p-2 bg-gray-50 rounded">
          <span className="text-muted-foreground">Query</span>
          <span className="font-mono font-medium">{metrics.queryTime}ms</span>
        </div>
      )}
      
      {metrics.renderTime && (
        <div className="flex flex-col items-center p-2 bg-gray-50 rounded">
          <span className="text-muted-foreground">Render</span>
          <span className="font-mono font-medium">{metrics.renderTime}ms</span>
        </div>
      )}
      
      {metrics.totalTime && (
        <div className="flex flex-col items-center p-2 bg-gray-50 rounded">
          <span className="text-muted-foreground">Total</span>
          <span className="font-mono font-medium">{metrics.totalTime}ms</span>
        </div>
      )}
    </div>
  );
}

/**
 * Connection Quality Indicator
 */
export function ConnectionQuality({ responseTime }: { responseTime?: number }) {
  if (!responseTime) return null;

  const getQuality = () => {
    if (responseTime < 200) return { level: 'excellent', color: 'bg-green-500', bars: 4 };
    if (responseTime < 500) return { level: 'good', color: 'bg-green-400', bars: 3 };
    if (responseTime < 1000) return { level: 'fair', color: 'bg-yellow-400', bars: 2 };
    return { level: 'poor', color: 'bg-red-400', bars: 1 };
  };

  const quality = getQuality();

  return (
    <div className="flex items-center space-x-2">
      <div className="flex space-x-1">
        {Array.from({ length: 4 }).map((_, i) => (
          <div
            key={i}
            className={cn(
              "w-1 rounded-full transition-colors",
              i < quality.bars ? quality.color : 'bg-gray-200',
              i === 0 && 'h-2',
              i === 1 && 'h-3',
              i === 2 && 'h-4',
              i === 3 && 'h-5'
            )}
          />
        ))}
      </div>
      <span className="text-xs text-muted-foreground capitalize">
        {quality.level}
      </span>
    </div>
  );
}

/**
 * Live Activity Indicator
 */
export function LiveActivityIndicator({ isActive }: { isActive: boolean }) {
  return (
    <div className="flex items-center space-x-2">
      <div className={cn(
        "w-2 h-2 rounded-full transition-colors",
        isActive ? "bg-green-500 animate-pulse" : "bg-gray-300"
      )} />
      <span className="text-xs text-muted-foreground">
        {isActive ? 'Live' : 'Idle'}
      </span>
    </div>
  );
}
