# 🚀 Enterprise Onboarding Guide

## Welcome to Slack Summary Scribe Enterprise

This guide will help new engineers get up and running with our enterprise-grade SaaS platform in under 30 minutes.

## 📋 Prerequisites

### Required Software
- **Node.js** 18.0.0 or higher
- **npm** 9.0.0 or higher
- **Git** 2.30.0 or higher
- **Docker** (optional, for local services)
- **VS Code** (recommended) with extensions:
  - TypeScript and JavaScript Language Features
  - ESLint
  - Prettier
  - Tailwind CSS IntelliSense
  - GitLens

### Required Accounts
- **GitHub** access to the repository
- **Vercel** account for deployments
- **Supabase** project access
- **Stripe** dashboard access (for billing features)
- **Sentry** project access (for error monitoring)
- **PostHog** project access (for analytics)

## 🛠️ Quick Setup (5 minutes)

### 1. Clone and Install
```bash
# Clone the repository
git clone https://github.com/your-org/slack-summary-scribe.git
cd slack-summary-scribe

# Install dependencies
npm install

# Copy environment template
cp .env.example .env.local
```

### 2. Environment Configuration
Edit `.env.local` with your credentials:

```bash
# Core Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Stripe (use test keys for development)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...

# AI Services
OPENAI_API_KEY=your_openai_key
DEEPSEEK_API_KEY=your_deepseek_key

# Email
RESEND_API_KEY=your_resend_key

# Monitoring
SENTRY_DSN=your_sentry_dsn
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
```

### 3. Database Setup
```bash
# Run database migrations
npm run db:migrate

# Seed with sample data
npm run db:seed

# Verify setup
npm run health-check
```

### 4. Start Development
```bash
# Start the development server
npm run dev

# Open in browser
open http://localhost:3000
```

## 🏗️ Architecture Overview

### Tech Stack
- **Frontend**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **Backend**: Next.js API routes, Supabase (PostgreSQL + Auth + Storage)
- **Payments**: Stripe with webhook handling
- **AI**: OpenAI GPT-4 + DeepSeek for cost optimization
- **Monitoring**: Sentry (errors) + PostHog (analytics)
- **Email**: Resend for transactional emails
- **Deployment**: Vercel with GitHub Actions CI/CD

### Project Structure
```
/
├── app/                    # Next.js 15 App Router
│   ├── (auth)/            # Authentication pages
│   ├── (dashboard)/       # Protected dashboard pages
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # Reusable UI components
│   ├── ui/               # Base UI components (shadcn/ui)
│   └── features/         # Feature-specific components
├── features/             # Domain-driven feature modules
│   ├── accounts/         # User profiles & preferences
│   ├── organizations/    # Multi-tenant org management
│   ├── summaries/        # Core AI summary logic
│   ├── billing/          # Stripe integration
│   ├── integrations/     # Third-party services
│   ├── analytics/        # Usage metrics
│   ├── admin/           # Admin dashboard
│   └── growth/          # Retention & campaigns
├── lib/                  # Shared utilities
│   ├── supabase-server.ts # Server-side Supabase client
│   ├── supabase-browser.ts # Client-side Supabase client
│   ├── stripe.ts         # Stripe configuration
│   └── utils.ts          # General utilities
├── scripts/              # Automation scripts
│   ├── audit.js          # Enterprise audit
│   └── health-check.js   # System validation
└── packages/             # Reusable packages (monorepo)
    ├── integration-sdk/  # Universal integration library
    ├── ui-kit/          # Shared UI components
    └── analytics-core/   # Analytics utilities
```

## 🧪 Development Workflow

### Daily Development
```bash
# Pull latest changes
git pull origin main

# Install any new dependencies
npm install

# Start development with hot reload
npm run dev

# Run tests in watch mode
npm run test:watch

# Check types
npm run type-check

# Lint and format code
npm run lint:fix
npm run format
```

### Feature Development
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add your feature description"

# Push and create PR
git push origin feature/your-feature-name
```

### Testing Strategy
```bash
# Unit tests (Vitest)
npm run test:unit

# Integration tests
npm run test:integration

# E2E tests (Playwright)
npm run test:e2e

# Coverage report
npm run test:coverage
```

## 🔧 Common Development Tasks

### Adding a New Feature Module
```bash
# Create feature directory
mkdir features/your-feature

# Create basic structure
mkdir features/your-feature/{components,hooks,services,types}

# Add to feature exports
echo "export * from './your-feature';" >> features/index.ts
```

### Adding a New API Route
```typescript
// app/api/your-endpoint/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Your logic here
    
    return NextResponse.json({ success: true, data: {} });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### Adding a New Database Table
```sql
-- Create migration file: supabase/migrations/YYYYMMDD_your_table.sql
CREATE TABLE your_table (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE your_table ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view own records" ON your_table
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own records" ON your_table
  FOR INSERT WITH CHECK (auth.uid() = user_id);
```

### Adding a New Integration
```typescript
// features/integrations/providers/your-service.ts
import { BaseProvider } from './base-provider';

export class YourServiceProvider extends BaseProvider {
  name = 'your-service';
  displayName = 'Your Service';
  
  async authenticate(config: AuthConfig): Promise<AuthResult> {
    // OAuth implementation
  }
  
  async export(payload: ExportPayload): Promise<ExportResult> {
    // Export implementation
  }
}
```

## 🚀 Deployment Process

### Staging Deployment
```bash
# Push to develop branch
git push origin develop

# Automatic deployment to staging via GitHub Actions
# Check deployment status at: https://github.com/your-org/repo/actions
```

### Production Deployment
```bash
# Create release PR to main
git checkout main
git pull origin main
git merge develop

# Push to main (triggers production deployment)
git push origin main

# Monitor deployment
npm run health-check:production
```

### Manual Deployment
```bash
# Build for production
npm run build

# Deploy to Vercel
npm run deploy:production

# Run post-deployment checks
npm run audit
```

## 🔍 Debugging and Troubleshooting

### Common Issues

#### Environment Variables Not Loading
```bash
# Check if .env.local exists
ls -la .env.local

# Validate environment variables
npm run validate-env

# Restart development server
npm run dev
```

#### Database Connection Issues
```bash
# Test Supabase connection
npm run db:test-connection

# Check RLS policies
npm run db:check-policies

# Reset local database
npm run db:reset
```

#### Build Failures
```bash
# Clear Next.js cache
rm -rf .next

# Clear node_modules and reinstall
npm run clean-install

# Check TypeScript errors
npm run type-check
```

### Debugging Tools

#### VS Code Debug Configuration
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node-terminal",
      "request": "launch",
      "command": "npm run dev"
    }
  ]
}
```

#### Browser DevTools
- **React DevTools**: Component inspection
- **Network Tab**: API request debugging
- **Console**: Error messages and logs
- **Application Tab**: Local storage and cookies

## 📚 Learning Resources

### Internal Documentation
- [Architecture Guide](./docs/architecture/README.md)
- [API Documentation](./docs/api/README.md)
- [Database Schema](./docs/database/schema.md)
- [Deployment Guide](./docs/deployment/README.md)

### External Resources
- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Stripe Documentation](https://stripe.com/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

## 🆘 Getting Help

### Team Communication
- **Slack**: #engineering channel for general questions
- **GitHub**: Create issues for bugs or feature requests
- **Code Reviews**: Tag @team-leads for urgent reviews

### Escalation Process
1. **Self-help**: Check documentation and common issues
2. **Team Slack**: Ask in #engineering channel
3. **Senior Engineer**: Direct message for complex issues
4. **Team Lead**: For architectural decisions
5. **On-call**: For production emergencies

## ✅ Onboarding Checklist

### Day 1
- [ ] Repository access granted
- [ ] Local development environment setup
- [ ] Environment variables configured
- [ ] Application running locally
- [ ] First successful login to local app

### Week 1
- [ ] Complete first code review
- [ ] Deploy to staging environment
- [ ] Understand core architecture
- [ ] Complete security training
- [ ] Set up monitoring access

### Month 1
- [ ] Ship first feature to production
- [ ] Understand all major systems
- [ ] Complete performance optimization task
- [ ] Mentor new team member
- [ ] Contribute to documentation

## 🎯 Success Metrics

### Developer Productivity
- **Time to first commit**: < 1 day
- **Time to first deployment**: < 1 week
- **Time to first production feature**: < 1 month

### Code Quality
- **Test coverage**: > 80%
- **TypeScript strict mode**: Enabled
- **ESLint errors**: 0
- **Security vulnerabilities**: 0

### Performance
- **Build time**: < 2 minutes
- **Test suite**: < 5 minutes
- **Local dev startup**: < 30 seconds

Welcome to the team! 🎉
