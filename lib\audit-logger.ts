import { devLog } from '@/lib/console-cleaner';
/**
 * Comprehensive Audit Logging System
 * 
 * Tracks all security events, user actions, and system events
 * for compliance and security monitoring.
 */

import { createClient } from '@supabase/supabase-js';

// Audit event types
export type AuditEventType = 
  // Authentication events
  | 'USER_LOGIN' | 'USER_LOGOUT' | 'USER_SIGNUP' | 'USER_PASSWORD_CHANGE'
  | 'USER_EMAIL_CHANGE' | 'USER_PROFILE_UPDATE' | 'USER_DELETE'
  
  // Authorization events
  | 'ACCESS_GRANTED' | 'ACCESS_DENIED' | 'PERMISSION_CHANGE'
  | 'ROLE_ASSIGNED' | 'ROLE_REMOVED'
  
  // Security events
  | 'RATE_LIMIT_EXCEEDED' | 'CSRF_VIOLATION' | 'BOT_BLOCKED'
  | 'IP_BLOCKED' | 'IP_NOT_ALLOWED' | 'SUSPICIOUS_ACTIVITY'
  | 'SECURITY_SCAN_DETECTED' | 'MALICIOUS_REQUEST'
  
  // Payment events
  | 'PAYMENT_INITIATED' | 'PAYMENT_SUCCESS' | 'PAYMENT_FAILED'
  | 'SUBSCRIPTION_CREATED' | 'SUBSCRIPTION_UPDATED' | 'SUBSCRIPTION_CANCELLED'
  | 'REFUND_INITIATED' | 'REFUND_COMPLETED'
  
  // Data events
  | 'FILE_UPLOADED' | 'FILE_DELETED' | 'FILE_DOWNLOADED'
  | 'SUMMARY_CREATED' | 'SUMMARY_DELETED' | 'SUMMARY_EXPORTED'
  | 'DATA_EXPORT' | 'DATA_IMPORT' | 'DATA_BACKUP'
  
  // System events
  | 'API_ERROR' | 'SYSTEM_ERROR' | 'PERFORMANCE_ISSUE'
  | 'MAINTENANCE_START' | 'MAINTENANCE_END' | 'DEPLOYMENT'
  
  // Integration events
  | 'SLACK_CONNECTED' | 'SLACK_DISCONNECTED' | 'SLACK_ERROR'
  | 'CRM_SYNC' | 'NOTION_EXPORT' | 'EMAIL_SENT'
  
  // Admin events
  | 'ADMIN_LOGIN' | 'ADMIN_ACTION' | 'USER_IMPERSONATION'
  | 'SYSTEM_CONFIG_CHANGE' | 'FEATURE_FLAG_CHANGE';

export interface AuditEvent {
  // Core event data
  event_type: AuditEventType;
  user_id?: string;
  session_id?: string;
  
  // Request context
  ip_address?: string;
  user_agent?: string;
  request_id?: string;
  
  // Resource information
  resource_type?: string;
  resource_id?: string;
  resource_name?: string;
  
  // Event details
  action: string;
  description?: string;
  metadata?: Record<string, any>;
  
  // Security context
  risk_level?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  threat_indicators?: string[];
  
  // Outcome
  success: boolean;
  error_message?: string;
  error_code?: string;
  
  // Timing
  timestamp: string;
  duration_ms?: number;
  
  // Compliance
  compliance_tags?: string[];
  retention_period_days?: number;
}

// Audit configuration
export const AUDIT_CONFIG = {
  // Retention periods by event type (in days)
  RETENTION_PERIODS: {
    // Security events - long retention
    'RATE_LIMIT_EXCEEDED': 365,
    'CSRF_VIOLATION': 365,
    'BOT_BLOCKED': 90,
    'SUSPICIOUS_ACTIVITY': 365,
    'MALICIOUS_REQUEST': 365,
    
    // Authentication events
    'USER_LOGIN': 90,
    'USER_LOGOUT': 30,
    'USER_SIGNUP': 365,
    'USER_PASSWORD_CHANGE': 365,
    
    // Payment events - compliance requirement
    'PAYMENT_INITIATED': 2555, // 7 years
    'PAYMENT_SUCCESS': 2555,
    'PAYMENT_FAILED': 365,
    'SUBSCRIPTION_CREATED': 2555,
    'REFUND_COMPLETED': 2555,
    
    // Data events
    'FILE_UPLOADED': 90,
    'SUMMARY_CREATED': 90,
    'DATA_EXPORT': 365,
    
    // Default retention
    'DEFAULT': 90
  },
  
  // Risk level thresholds
  RISK_LEVELS: {
    CRITICAL: ['MALICIOUS_REQUEST', 'SECURITY_SCAN_DETECTED', 'USER_DELETE'],
    HIGH: ['CSRF_VIOLATION', 'SUSPICIOUS_ACTIVITY', 'PAYMENT_FAILED'],
    MEDIUM: ['RATE_LIMIT_EXCEEDED', 'ACCESS_DENIED', 'API_ERROR'],
    LOW: ['USER_LOGIN', 'FILE_UPLOADED', 'SUMMARY_CREATED']
  },
  
  // Compliance tags
  COMPLIANCE_TAGS: {
    PCI_DSS: ['PAYMENT_INITIATED', 'PAYMENT_SUCCESS', 'PAYMENT_FAILED'],
    GDPR: ['USER_SIGNUP', 'USER_DELETE', 'DATA_EXPORT', 'DATA_BACKUP'],
    SOX: ['ADMIN_ACTION', 'SYSTEM_CONFIG_CHANGE', 'USER_IMPERSONATION'],
    HIPAA: [], // Add if handling health data
    SOC2: ['ACCESS_GRANTED', 'ACCESS_DENIED', 'PERMISSION_CHANGE']
  }
};

/**
 * Audit Logger class
 */
export class AuditLogger {
  private static instance: AuditLogger;
  private supabase: any;
  private buffer: AuditEvent[] = [];
  private flushInterval: NodeJS.Timeout | null = null;
  
  private constructor() {
    // Initialize Supabase client for audit logging
    if (process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      this.supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
      );
    }
    
    // Start buffer flush interval (every 30 seconds)
    this.flushInterval = setInterval(() => {
      this.flushBuffer();
    }, 30000);
  }
  
  static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }
  
  /**
   * Log an audit event
   */
  async logEvent(event: Partial<AuditEvent>): Promise<void> {
    try {
      const auditEvent = this.enrichEvent(event);
      
      // Add to buffer for batch processing
      this.buffer.push(auditEvent);
      
      // Flush immediately for critical events
      if (auditEvent.risk_level === 'CRITICAL') {
        await this.flushBuffer();
      }
      
      // Also log to console in development
      if (process.env.NODE_ENV === 'development') {
  devLog.log('🔍 Audit Event:', {
          type: auditEvent.event_type,
          action: auditEvent.action,
          user: auditEvent.user_id,
          risk: auditEvent.risk_level,
          success: auditEvent.success
        });
      }
      
    } catch (error) {
      console.error('Failed to log audit event:', error);
      // Don't throw - audit logging should not break the application
    }
  }
  
  /**
   * Enrich event with metadata and context
   */
  private enrichEvent(event: Partial<AuditEvent>): AuditEvent {
    const now = new Date().toISOString();
    const eventType = event.event_type || 'SYSTEM_ERROR';
    
    // Determine risk level
    const riskLevel = this.determineRiskLevel(eventType);
    
    // Get retention period
    const retentionPeriod = AUDIT_CONFIG.RETENTION_PERIODS[eventType] || 
                           AUDIT_CONFIG.RETENTION_PERIODS.DEFAULT;
    
    // Get compliance tags
    const complianceTags = this.getComplianceTags(eventType);
    
    return {
      event_type: eventType,
      action: event.action || 'UNKNOWN_ACTION',
      success: event.success ?? true,
      timestamp: now,
      risk_level: riskLevel,
      retention_period_days: retentionPeriod,
      compliance_tags: complianceTags,
      ...event
    };
  }
  
  /**
   * Determine risk level for event type
   */
  private determineRiskLevel(eventType: AuditEventType): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    for (const [level, events] of Object.entries(AUDIT_CONFIG.RISK_LEVELS)) {
      if (events.includes(eventType)) {
        return level as 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
      }
    }
    return 'LOW';
  }
  
  /**
   * Get compliance tags for event type
   */
  private getComplianceTags(eventType: AuditEventType): string[] {
    const tags: string[] = [];
    
    for (const [tag, events] of Object.entries(AUDIT_CONFIG.COMPLIANCE_TAGS)) {
      if (events.includes(eventType)) {
        tags.push(tag);
      }
    }
    
    return tags;
  }
  
  /**
   * Flush buffer to database
   */
  private async flushBuffer(): Promise<void> {
    if (this.buffer.length === 0 || !this.supabase) {
      return;
    }
    
    const events = [...this.buffer];
    this.buffer = [];
    
    try {
      const { error } = await this.supabase
        .from('audit_logs')
        .insert(events);
      
      if (error) {
        console.error('Failed to flush audit events:', error);
        // Re-add events to buffer for retry
        this.buffer.unshift(...events);
      }
      
    } catch (error) {
      console.error('Failed to flush audit events:', error);
      // Re-add events to buffer for retry
      this.buffer.unshift(...events);
    }
  }
  
  /**
   * Get audit events for a user
   */
  async getUserAuditEvents(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      eventTypes?: AuditEventType[];
      startDate?: string;
      endDate?: string;
    } = {}
  ): Promise<AuditEvent[]> {
    if (!this.supabase) {
      return [];
    }
    
    try {
      let query = this.supabase
        .from('audit_logs')
        .select('*')
        .eq('user_id', userId)
        .order('timestamp', { ascending: false });
      
      if (options.eventTypes?.length) {
        query = query.in('event_type', options.eventTypes);
      }
      
      if (options.startDate) {
        query = query.gte('timestamp', options.startDate);
      }
      
      if (options.endDate) {
        query = query.lte('timestamp', options.endDate);
      }
      
      if (options.limit) {
        query = query.limit(options.limit);
      }
      
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1);
      }
      
      const { data, error } = await query;
      
      if (error) {
        console.error('Failed to get user audit events:', error);
        return [];
      }
      
      return data || [];
      
    } catch (error) {
      console.error('Failed to get user audit events:', error);
      return [];
    }
  }
  
  /**
   * Cleanup old audit events based on retention policy
   */
  async cleanupOldEvents(): Promise<void> {
    if (!this.supabase) {
      return;
    }
    
    try {
      // Get unique event types with their retention periods
      const retentionPolicies = Object.entries(AUDIT_CONFIG.RETENTION_PERIODS);
      
      for (const [eventType, retentionDays] of retentionPolicies) {
        if (eventType === 'DEFAULT') continue;
        
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
        
        const { error } = await this.supabase
          .from('audit_logs')
          .delete()
          .eq('event_type', eventType)
          .lt('timestamp', cutoffDate.toISOString());
        
        if (error) {
          console.error(`Failed to cleanup ${eventType} events:`, error);
        }
      }
  devLog.log('Audit log cleanup completed');
      
    } catch (error) {
      console.error('Failed to cleanup audit events:', error);
    }
  }
  
  /**
   * Destroy the audit logger instance
   */
  destroy(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }
    
    // Flush remaining events
    this.flushBuffer();
  }
}

/**
 * Convenience function for logging audit events
 */
export async function logAuditEvent(event: Partial<AuditEvent>): Promise<void> {
  const logger = AuditLogger.getInstance();
  await logger.logEvent(event);
}

/**
 * Convenience functions for common audit events
 */
export const auditEvents = {
  userLogin: (userId: string, ip?: string, userAgent?: string) =>
    logAuditEvent({
      event_type: 'USER_LOGIN',
      user_id: userId,
      action: 'User logged in',
      ip_address: ip,
      user_agent: userAgent
    }),
  
  paymentSuccess: (userId: string, amount: number, currency: string, paymentId: string) =>
    logAuditEvent({
      event_type: 'PAYMENT_SUCCESS',
      user_id: userId,
      action: 'Payment completed successfully',
      resource_type: 'payment',
      resource_id: paymentId,
      metadata: { amount, currency }
    }),
  
  fileUpload: (userId: string, fileName: string, fileSize: number) =>
    logAuditEvent({
      event_type: 'FILE_UPLOADED',
      user_id: userId,
      action: 'File uploaded',
      resource_type: 'file',
      resource_name: fileName,
      metadata: { file_size: fileSize }
    }),
  
  securityViolation: (eventType: AuditEventType, details: Record<string, any>, ip?: string) =>
    logAuditEvent({
      event_type: eventType,
      action: 'Security violation detected',
      success: false,
      ip_address: ip,
      metadata: details,
      threat_indicators: ['suspicious_activity']
    })
};
