'use client';

import React, { useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useDemoStatus } from '@/hooks/useDemoStatus';
import DemoBanner from '@/components/DemoBanner';
import UpgradePrompt from '@/components/UpgradePrompt';

interface DemoAwareLayoutProps {
  children: React.ReactNode;
  showBanner?: boolean;
  showUpgradePrompt?: boolean;
  className?: string;
}

export default function DemoAwareLayout({
  children,
  showBanner = true,
  showUpgradePrompt = true,
  className = ''
}: DemoAwareLayoutProps) {
  const { user, isLoaded } = useUser();
  const { 
    demoStatus, 
    loading, 
    error, 
    recordUpgradePrompt, 
    initializeDemoMode 
  } = useDemoStatus();
  const [upgradePromptOpen, setUpgradePromptOpen] = React.useState(false);

  // Initialize demo mode for new users
  useEffect(() => {
    if (isLoaded && user && !loading && !demoStatus?.isInDemo && !error) {
      // Check if user might need demo mode initialization
      // This could be based on subscription status or other criteria
      const shouldInitializeDemo = true; // You can add logic here
      
      if (shouldInitializeDemo) {
        initializeDemoMode();
      }
    }
  }, [isLoaded, user, loading, demoStatus, error, initializeDemoMode]);

  // Show upgrade prompt based on demo status
  useEffect(() => {
    if (
      showUpgradePrompt &&
      demoStatus?.upgradePrompt.should_show &&
      demoStatus.upgradePrompt.urgency === 'high'
    ) {
      setUpgradePromptOpen(true);
      recordUpgradePrompt();
    }
  }, [demoStatus?.upgradePrompt, showUpgradePrompt, recordUpgradePrompt]);

  // Don't render anything while loading user or demo status
  if (!isLoaded || loading) {
    return (
      <div className={className}>
        {children}
      </div>
    );
  }

  // Don't show demo components if there's an error or user is not authenticated
  if (error || !user) {
    return (
      <div className={className}>
        {children}
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Demo Banner */}
      {showBanner && demoStatus && (
        <DemoBanner
          isInDemo={demoStatus.isInDemo}
          trialDaysRemaining={demoStatus.trialDaysRemaining}
          trialExpired={demoStatus.trialExpired}
          usage={demoStatus.usage}
          showDetails={true}
          className="container mx-auto px-4"
        />
      )}

      {/* Main Content */}
      {children}

      {/* Upgrade Prompt Modal */}
      {showUpgradePrompt && demoStatus && (
        <UpgradePrompt
          isOpen={upgradePromptOpen}
          onClose={() => setUpgradePromptOpen(false)}
          message={demoStatus.upgradePrompt.message || 'Upgrade to unlock unlimited access'}
          trialDaysRemaining={demoStatus.trialDaysRemaining}
          trialExpired={demoStatus.trialExpired}
          limitReached={
            Object.entries(demoStatus.usage)
              .find(([_, usage]) => usage.remaining === 0)?.[0]
          }
          onUpgradeClick={() => {
            recordUpgradePrompt();
            setUpgradePromptOpen(false);
          }}
        />
      )}
    </div>
  );
}
