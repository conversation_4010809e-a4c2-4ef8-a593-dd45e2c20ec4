name: ✨ Feature Request
description: Suggest a new feature or enhancement
title: "[Feature]: "
labels: ["enhancement", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new feature! Please provide as much detail as possible to help us understand your request.

  - type: checkboxes
    id: checklist
    attributes:
      label: Pre-submission Checklist
      description: Please verify the following before submitting
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have read the [Contributing Guidelines](../CONTRIBUTING.md)
          required: true
        - label: This feature aligns with the project's goals and scope
          required: true

  - type: dropdown
    id: package
    attributes:
      label: Package/Module
      description: Which package or module should this feature be added to?
      options:
        - "apps/slack-summary-scribe (Main App)"
        - "packages/integration-sdk"
        - "packages/admin-ui"
        - "packages/drip-campaign-engine"
        - "packages/ui-kit"
        - "New package"
        - "Other/Multiple packages"
    validations:
      required: true

  - type: dropdown
    id: type
    attributes:
      label: Feature Type
      description: What type of feature is this?
      options:
        - "New Component"
        - "API Enhancement"
        - "UI/UX Improvement"
        - "Integration"
        - "Performance Optimization"
        - "Developer Experience"
        - "Documentation"
        - "Other"
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this feature?
      options:
        - "High (Critical for users)"
        - "Medium (Would be very helpful)"
        - "Low (Nice to have)"
    validations:
      required: true

  - type: textarea
    id: problem
    attributes:
      label: Problem Statement
      description: What problem does this feature solve?
      placeholder: |
        Describe the problem or pain point this feature would address.
        Include any relevant user stories or use cases.
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe your proposed solution
      placeholder: |
        Clearly describe what you'd like to see implemented.
        Include any specific requirements or constraints.
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternative Solutions
      description: Have you considered any alternative solutions?
      placeholder: |
        Describe any alternative solutions or features you've considered.
        Why is your proposed solution better?

  - type: textarea
    id: examples
    attributes:
      label: Examples/Mockups
      description: Provide examples, mockups, or references
      placeholder: |
        Include any examples from other tools, mockups, wireframes, or code snippets.
        You can drag and drop images here.

  - type: textarea
    id: acceptance
    attributes:
      label: Acceptance Criteria
      description: What would make this feature complete?
      placeholder: |
        Define clear acceptance criteria:
        - [ ] Criterion 1
        - [ ] Criterion 2
        - [ ] Criterion 3

  - type: textarea
    id: impact
    attributes:
      label: Impact Assessment
      description: What would be the impact of this feature?
      placeholder: |
        Consider:
        - User experience improvements
        - Performance implications
        - Maintenance overhead
        - Breaking changes
        - Documentation needs

  - type: checkboxes
    id: implementation
    attributes:
      label: Implementation Considerations
      description: Check all that apply
      options:
        - label: This feature requires API changes
        - label: This feature requires database changes
        - label: This feature affects existing functionality
        - label: This feature requires new dependencies
        - label: This feature needs documentation updates
        - label: This feature requires testing strategy

  - type: textarea
    id: technical
    attributes:
      label: Technical Details
      description: Any technical considerations or implementation notes
      placeholder: |
        Include any technical details, architecture considerations,
        or implementation suggestions.

  - type: checkboxes
    id: contribution
    attributes:
      label: Contribution
      description: Would you like to contribute to implementing this feature?
      options:
        - label: I would like to implement this feature
        - label: I can help with design/planning
        - label: I can help with testing
        - label: I can help with documentation
