#!/usr/bin/env node

/**
 * Test script to verify chunk loading error fixes
 */

const puppeteer = require('puppeteer');
const path = require('path');

async function testChunkLoading() {
  console.log('🧪 Testing chunk loading error fixes...\n');

  let browser;
  try {
    // Launch browser
    browser = await puppeteer.launch({
      headless: false, // Set to true for CI
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Listen for console errors
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });

    // Listen for page errors
    const pageErrors = [];
    page.on('pageerror', error => {
      pageErrors.push(error.message);
    });

    // Navigate to the app
    console.log('📱 Navigating to http://localhost:3002...');
    await page.goto('http://localhost:3002', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    // Wait for the page to load
    await page.waitForTimeout(3000);

    // Check for chunk loading errors
    const chunkErrors = [...errors, ...pageErrors].filter(error => 
      error.includes('Cannot read properties of undefined (reading \'call\')') ||
      error.includes('ChunkLoadError') ||
      error.includes('Loading chunk') ||
      error.includes('webpack.js') ||
      error.includes('pages-brows._global-error.js')
    );

    if (chunkErrors.length > 0) {
      console.log('❌ Chunk loading errors detected:');
      chunkErrors.forEach(error => console.log(`   - ${error}`));
      return false;
    }

    // Test navigation to different pages
    console.log('🔄 Testing navigation...');
    
    const testRoutes = [
      '/dashboard',
      '/pricing',
      '/features',
      '/contact'
    ];

    for (const route of testRoutes) {
      try {
        console.log(`   Testing ${route}...`);
        await page.goto(`http://localhost:3002${route}`, { 
          waitUntil: 'networkidle2',
          timeout: 10000 
        });
        await page.waitForTimeout(1000);
      } catch (error) {
        console.log(`   ⚠️  ${route} failed to load: ${error.message}`);
      }
    }

    // Check for any new errors after navigation
    const finalErrors = [...errors, ...pageErrors].filter(error => 
      error.includes('Cannot read properties of undefined (reading \'call\')') ||
      error.includes('ChunkLoadError') ||
      error.includes('Loading chunk')
    );

    if (finalErrors.length > 0) {
      console.log('❌ Errors detected during navigation:');
      finalErrors.forEach(error => console.log(`   - ${error}`));
      return false;
    }

    console.log('✅ No chunk loading errors detected!');
    console.log('✅ Navigation test completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
testChunkLoading().then(success => {
  if (success) {
    console.log('\n🎉 All tests passed! Chunk loading errors have been fixed.');
    process.exit(0);
  } else {
    console.log('\n💥 Tests failed. Chunk loading errors still exist.');
    process.exit(1);
  }
}).catch(error => {
  console.error('\n💥 Test runner failed:', error);
  process.exit(1);
});
