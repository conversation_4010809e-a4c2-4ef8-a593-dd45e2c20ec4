'use client'

import React, { Component, ReactNode } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>ert<PERSON>riangle, RefreshCw, Home, Bug, Wifi, WifiOff } from 'lucide-react'
import { toast } from 'sonner'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: any) => void
  showReportButton?: boolean
  context?: string
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: any
  isRetrying: boolean
  retryCount: number
  errorType: 'chunk' | 'network' | 'runtime' | 'unknown'
}

export class ChunkErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      isRetrying: false,
      retryCount: 0,
      errorType: 'unknown'
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorType: ChunkErrorBoundary.determineErrorType(error)
    }
  }

  static determineErrorType(error: Error): State['errorType'] {
    if (!error || typeof error !== 'object') return 'unknown'

    const message = error.message || ''
    const stack = error.stack || ''

    if (message.includes('ChunkLoadError') ||
        message.includes('Loading chunk') ||
        message.includes('Failed to import') ||
        stack.includes('runtime.js')) {
      return 'chunk'
    }

    if (message.includes('fetch') ||
        message.includes('network') ||
        message.includes('NetworkError')) {
      return 'network'
    }

    if (message.includes("Cannot read properties of undefined (reading 'call')") ||
        message.includes('Script error')) {
      return 'runtime'
    }

    return 'unknown'
  }

  componentDidCatch(error: Error, errorInfo: any) {
    try {
      // Safe error logging
      console.error('ChunkErrorBoundary caught an error:', error || 'Unknown error', errorInfo || 'No error info')

      const errorType = ChunkErrorBoundary.determineErrorType(error)

      this.setState({
        error,
        errorInfo,
        errorType
      })

      // Enhanced error reporting with user context
      this.logErrorToServices(error, errorInfo, errorType)

      // Call custom error handler if provided
      if (this.props.onError) {
        this.props.onError(error, errorInfo)
      }

      // Check if it's a chunk loading error (only if error is valid)
      if (error && this.isChunkLoadError(error)) {
        this.handleChunkLoadError()
      }
    } catch (loggingError) {
      console.error('🚨 Error in ChunkErrorBoundary logging:', loggingError);
      console.error('🚨 Original error (fallback):', error);
    }
  }

  private async logErrorToServices(error: Error, errorInfo: any, errorType: State['errorType']) {
    try {
      const errorData = {
        message: error?.message || 'No message available',
        stack: error?.stack || 'No stack trace available',
        componentStack: errorInfo?.componentStack || 'No component stack available',
        errorType,
        context: this.props.context || 'ChunkErrorBoundary',
        timestamp: new Date().toISOString(),
        url: typeof window !== 'undefined' ? window.location.href : 'SSR',
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'Unknown',
        retryCount: this.state.retryCount
      }

      // Log to console for development
      console.error('🔥 Enhanced error logging:', errorData)

      // PostHog error tracking
      if (typeof window !== 'undefined' && (window as any).posthog) {
        try {
          (window as any).posthog.capture('error_boundary_triggered', {
            error_type: errorType,
            error_message: error?.message,
            component_context: this.props.context,
            retry_count: this.state.retryCount,
            is_chunk_error: this.isChunkLoadError(error)
          })
        } catch (posthogError) {
          console.warn('Failed to log to PostHog:', posthogError)
        }
      }

      // Sentry error tracking
      if (typeof window !== 'undefined' && (window as any).Sentry) {
        try {
          (window as any).Sentry.withScope((scope: any) => {
            scope.setTag('errorBoundary', 'ChunkErrorBoundary')
            scope.setTag('errorType', errorType)
            scope.setContext('errorInfo', errorData)
            scope.setLevel('error')
            ;(window as any).Sentry.captureException(error)
          })
        } catch (sentryError) {
          console.warn('Failed to log to Sentry:', sentryError)
        }
      }
    } catch (loggingError) {
      console.error('Failed to log error to services:', loggingError)
    }
  }

  private isChunkLoadError(error: Error): boolean {
    if (!error || typeof error !== 'object') {
      return false
    }

    const chunkErrorPatterns = [
      'ChunkLoadError',
      'Loading chunk',
      'Failed to import',
      'Cannot read properties of undefined (reading \'call\')',
      'runtime.js',
      'Script error'
    ]

    const errorMessage = (error.message && typeof error.message === 'string') ? error.message : ''
    const errorStack = (error.stack && typeof error.stack === 'string') ? error.stack : ''

    return chunkErrorPatterns.some(pattern =>
      errorMessage.includes(pattern) || errorStack.includes(pattern)
    )
  }

  private handleChunkLoadError() {
    // Clear caches and reload
    if (typeof window !== 'undefined') {
      // Clear service worker cache
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(registrations => {
          registrations.forEach(registration => registration.unregister())
        })
      }

      // Clear browser caches
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name))
        })
      }

      // Reload after a short delay
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    }
  }

  private handleRetry = async () => {
    this.setState({ isRetrying: true })

    try {
      // Add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 500))

      // Increment retry count
      const newRetryCount = this.state.retryCount + 1

      // Log retry attempt
      if (typeof window !== 'undefined' && (window as any).posthog) {
        (window as any).posthog.capture('error_boundary_retry', {
          retry_count: newRetryCount,
          error_type: this.state.errorType,
          context: this.props.context
        })
      }

      this.setState({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        isRetrying: false,
        retryCount: newRetryCount
      })

      toast.success('Retrying...', { duration: 2000 })
    } catch (retryError) {
      console.error('Error during retry:', retryError)
      this.setState({ isRetrying: false })
      toast.error('Retry failed. Please reload the page.')
    }
  }

  private handleReload = () => {
    if (typeof window !== 'undefined') {
      // Log reload attempt
      if ((window as any).posthog) {
        (window as any).posthog.capture('error_boundary_reload', {
          error_type: this.state.errorType,
          context: this.props.context
        })
      }

      toast.loading('Reloading page...', { duration: 1000 })
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    }
  }

  private handleGoHome = () => {
    if (typeof window !== 'undefined') {
      // Log navigation attempt
      if ((window as any).posthog) {
        (window as any).posthog.capture('error_boundary_go_home', {
          error_type: this.state.errorType,
          context: this.props.context
        })
      }

      window.location.href = '/'
    }
  }

  private handleReportError = () => {
    const errorReport = {
      message: this.state.error?.message || 'Unknown error',
      stack: this.state.error?.stack || 'No stack trace',
      errorType: this.state.errorType,
      context: this.props.context,
      timestamp: new Date().toISOString(),
      url: window.location.href
    }

    const subject = encodeURIComponent(`Error Report: ${this.state.errorType} in ${this.props.context || 'Application'}`)
    const body = encodeURIComponent(`Error Details:\n\n${JSON.stringify(errorReport, null, 2)}`)
    const mailtoUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`

    window.open(mailtoUrl, '_blank')

    // Log error report
    if ((window as any).posthog) {
      (window as any).posthog.capture('error_boundary_report', {
        error_type: this.state.errorType,
        context: this.props.context
      })
    }
  }

  private getErrorIcon() {
    switch (this.state.errorType) {
      case 'network':
        return <WifiOff className="h-8 w-8 text-orange-500" />
      case 'chunk':
        return <RefreshCw className="h-8 w-8 text-blue-500" />
      case 'runtime':
        return <Bug className="h-8 w-8 text-red-500" />
      default:
        return <AlertTriangle className="h-8 w-8 text-yellow-500" />
    }
  }

  private getErrorTitle() {
    switch (this.state.errorType) {
      case 'network':
        return 'Connection Problem'
      case 'chunk':
        return 'Loading Issue'
      case 'runtime':
        return 'Application Error'
      default:
        return 'Something Went Wrong'
    }
  }

  private getErrorDescription() {
    switch (this.state.errorType) {
      case 'network':
        return 'Unable to connect to our servers. Please check your internet connection and try again.'
      case 'chunk':
        return 'There was a problem loading part of the application. This usually resolves with a refresh.'
      case 'runtime':
        return 'The application encountered an unexpected error. Our team has been notified.'
      default:
        return 'An unexpected error occurred. Please try the suggested actions below.'
    }
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Enhanced error UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
          <Card className="max-w-lg w-full shadow-lg">
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-4">
                {this.getErrorIcon()}
              </div>
              <CardTitle className="text-xl font-semibold text-gray-900">
                {this.getErrorTitle()}
              </CardTitle>
              <CardDescription className="text-gray-600 mt-2">
                {this.getErrorDescription()}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              {this.state.retryCount > 0 && (
                <Alert className="border-yellow-200 bg-yellow-50">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-800">
                    Retry attempt #{this.state.retryCount}. If the problem persists, try reloading the page.
                  </AlertDescription>
                </Alert>
              )}

              <div className="grid grid-cols-1 gap-3">
                <Button
                  onClick={this.handleRetry}
                  disabled={this.state.isRetrying}
                  className="w-full"
                  variant="default"
                >
                  {this.state.isRetrying ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Retrying...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Try Again
                    </>
                  )}
                </Button>

                <div className="grid grid-cols-2 gap-2">
                  <Button onClick={this.handleReload} variant="outline">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reload
                  </Button>
                  <Button onClick={this.handleGoHome} variant="outline">
                    <Home className="h-4 w-4 mr-2" />
                    Home
                  </Button>
                </div>

                {this.props.showReportButton && (
                  <Button
                    onClick={this.handleReportError}
                    variant="ghost"
                    size="sm"
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <Bug className="h-4 w-4 mr-2" />
                    Report Issue
                  </Button>
                )}
              </div>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-4 p-4 bg-gray-100 rounded-lg text-xs">
                  <summary className="cursor-pointer font-medium text-gray-700 hover:text-gray-900">
                    🔍 Error Details (Development)
                  </summary>
                  <div className="mt-3 space-y-2">
                    <div>
                      <strong>Error Type:</strong> {this.state.errorType}
                    </div>
                    <div>
                      <strong>Message:</strong> {this.state.error?.message || 'No message available'}
                    </div>
                    <div>
                      <strong>Context:</strong> {this.props.context || 'Unknown'}
                    </div>
                    <pre className="mt-2 whitespace-pre-wrap text-xs bg-white p-2 rounded border overflow-auto max-h-32">
                      {this.state.error?.stack || 'No stack trace available'}
                    </pre>
                  </div>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

export default ChunkErrorBoundary
