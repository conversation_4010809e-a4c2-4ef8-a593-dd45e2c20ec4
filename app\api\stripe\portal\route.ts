/**
 * Stripe Customer Portal API Route
 * 
 * Creates secure customer portal sessions for subscription management
 */

import { NextRequest } from 'next/server';
import { createSecureApiRoute, createSuccessResponse, createErrorResponse } from '@/lib/api-security';
import { stripe } from '@/lib/stripe-config';
import { logPaymentSecurityEvent } from '@/lib/payment-security';
import { createSupabaseServerClient } from '@/lib/supabase-server';

export const POST = createSecureApiRoute(
  async (request: NextRequest, authResult) => {
    try {
      const { userId } = authResult;

      // Get user's Stripe customer ID from subscription
      const supabase = await createSupabaseServerClient();
      
      const { data: subscription, error } = await supabase
        .from('subscriptions')
        .select('stripe_customer_id, subscription_tier, status')
        .eq('user_id', userId)
        .single();

      if (error || !subscription) {
        return createErrorResponse(
          'No subscription found',
          'You need an active subscription to access the billing portal',
          404,
          'NO_SUBSCRIPTION'
        );
      }

      if (!subscription.stripe_customer_id) {
        return createErrorResponse(
          'No Stripe customer',
          'No Stripe customer ID found for your subscription',
          400,
          'NO_STRIPE_CUSTOMER'
        );
      }

      // Create customer portal session
      const portalSession = await stripe.billingPortal.sessions.create({
        customer: subscription.stripe_customer_id,
        return_url: `${process.env.NEXT_PUBLIC_SITE_URL}/billing`,
        configuration: {
          business_profile: {
            headline: 'Manage your Slack Summary Scribe subscription',
          },
          features: {
            payment_method_update: {
              enabled: true,
            },
            invoice_history: {
              enabled: true,
            },
            customer_update: {
              enabled: true,
              allowed_updates: ['email', 'address', 'tax_id'],
            },
            subscription_cancel: {
              enabled: true,
              mode: 'at_period_end',
              proration_behavior: 'none',
            },
            subscription_pause: {
              enabled: false, // Disable pause for simplicity
            },
            subscription_update: {
              enabled: true,
              default_allowed_updates: ['price'],
              proration_behavior: 'create_prorations',
            },
          },
        },
      });

      // Log portal access
      logPaymentSecurityEvent(userId, 'PORTAL_ACCESSED', {
        customer_id: subscription.stripe_customer_id,
        subscription_tier: subscription.subscription_tier,
        portal_session_id: portalSession.id
      }, request);

      return createSuccessResponse({
        url: portalSession.url,
        portal_session_id: portalSession.id
      }, 'Portal session created successfully');

    } catch (error) {
      console.error('Stripe portal error:', error);
      
      logPaymentSecurityEvent(authResult.userId, 'PORTAL_ERROR', {
        error: error instanceof Error ? error.message : 'Unknown error'
      }, request);
      
      return createErrorResponse(
        'Portal creation failed',
        'Failed to create billing portal session. Please try again.',
        500,
        'PORTAL_ERROR'
      );
    }
  },
  {
    requireAuth: true,
    rateLimit: 20, // 20 portal requests per minute
    auditLog: true,
    allowedMethods: ['POST']
  }
);
