import { devLog } from '@/lib/console-cleaner';
'use client';

import React, { useState, useEffect, Suspense, lazy } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Smartphone, Wifi, WifiOff, Battery, BatteryLow } from 'lucide-react';
import { useLazyLoad, usePerformanceObserver } from '@/hooks/useIntersectionObserver';
import { SkeletonLoader } from '@/components/ui/skeleton-loaders';

// Lazy load heavy components for mobile
const LazyChartComponent = lazy(() => import('@/components/ui/charts').then(module => ({ default: module.ChartComponent })));
const LazyExportPanel = lazy(() => import('@/components/ExportPanel').then(module => ({ default: module.ExportPanel })));
const LazyAnalyticsDashboard = lazy(() => import('@/components/AnalyticsDashboard').then(module => ({ default: module.AnalyticsDashboard })));

interface MobilePerformanceOptimizerProps {
  children: React.ReactNode;
  enableLazyLoading?: boolean;
  enableNetworkOptimization?: boolean;
  enableBatteryOptimization?: boolean;
}

interface NetworkInfo {
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
  saveData?: boolean;
}

interface BatteryInfo {
  charging?: boolean;
  level?: number;
  chargingTime?: number;
  dischargingTime?: number;
}

export function MobilePerformanceOptimizer({
  children,
  enableLazyLoading = true,
  enableNetworkOptimization = true,
  enableBatteryOptimization = true
}: MobilePerformanceOptimizerProps) {
  const [isMobile, setIsMobile] = useState(false);
  const [networkInfo, setNetworkInfo] = useState<NetworkInfo>({});
  const [batteryInfo, setBatteryInfo] = useState<BatteryInfo>({});
  const [isLowPowerMode, setIsLowPowerMode] = useState(false);
  const [shouldOptimize, setShouldOptimize] = useState(false);
  
  const { ref, shouldLoad } = useLazyLoad(enableLazyLoading);
  const performanceMetrics = usePerformanceObserver();

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice = window.innerWidth < 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Monitor network conditions
  useEffect(() => {
    if (!enableNetworkOptimization || typeof window === 'undefined') return;

    const updateNetworkInfo = () => {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      
      if (connection) {
        setNetworkInfo({
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData
        });
      }
    };

    updateNetworkInfo();
    
    const connection = (navigator as any).connection;
    if (connection) {
      connection.addEventListener('change', updateNetworkInfo);
      return () => connection.removeEventListener('change', updateNetworkInfo);
    }
  }, [enableNetworkOptimization]);

  // Monitor battery status
  useEffect(() => {
    if (!enableBatteryOptimization || typeof window === 'undefined') return;

    const updateBatteryInfo = async () => {
      try {
        const battery = await (navigator as any).getBattery?.();
        if (battery) {
          setBatteryInfo({
            charging: battery.charging,
            level: battery.level,
            chargingTime: battery.chargingTime,
            dischargingTime: battery.dischargingTime
          });
        }
      } catch (error) {
  devLog.log('Battery API not supported');
      }
    };

    updateBatteryInfo();
  }, [enableBatteryOptimization]);

  // Determine if we should optimize performance
  useEffect(() => {
    const shouldOptimizePerformance = 
      isMobile && (
        networkInfo.effectiveType === 'slow-2g' ||
        networkInfo.effectiveType === '2g' ||
        networkInfo.saveData ||
        (batteryInfo.level !== undefined && batteryInfo.level < 0.2 && !batteryInfo.charging) ||
        (performanceMetrics.renderTime && performanceMetrics.renderTime > 1000)
      );

    setShouldOptimize(shouldOptimizePerformance);
    setIsLowPowerMode(shouldOptimizePerformance);
  }, [isMobile, networkInfo, batteryInfo, performanceMetrics]);

  // Performance status indicator
  const PerformanceIndicator = () => {
    if (!isMobile) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed top-4 right-4 z-50 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 border"
      >
        <div className="flex items-center space-x-2 text-xs">
          <Smartphone className="h-3 w-3 text-blue-600" />
          
          {/* Network indicator */}
          {networkInfo.effectiveType && (
            <div className="flex items-center space-x-1">
              {networkInfo.effectiveType === 'slow-2g' || networkInfo.effectiveType === '2g' ? (
                <WifiOff className="h-3 w-3 text-red-500" />
              ) : (
                <Wifi className="h-3 w-3 text-green-500" />
              )}
              <span className="text-gray-600 dark:text-gray-400">
                {networkInfo.effectiveType}
              </span>
            </div>
          )}

          {/* Battery indicator */}
          {batteryInfo.level !== undefined && (
            <div className="flex items-center space-x-1">
              {batteryInfo.level < 0.2 && !batteryInfo.charging ? (
                <BatteryLow className="h-3 w-3 text-red-500" />
              ) : (
                <Battery className="h-3 w-3 text-green-500" />
              )}
              <span className="text-gray-600 dark:text-gray-400">
                {Math.round(batteryInfo.level * 100)}%
              </span>
            </div>
          )}

          {/* Performance mode indicator */}
          {shouldOptimize && (
            <div className="px-2 py-1 bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300 rounded text-xs">
              Power Save
            </div>
          )}
        </div>
      </motion.div>
    );
  };

  // Optimized component wrapper
  const OptimizedWrapper = ({ children }: { children: React.ReactNode }) => {
    if (!shouldLoad && enableLazyLoading) {
      return (
        <div ref={ref} className="w-full">
          <SkeletonLoader className="h-64 w-full" />
        </div>
      );
    }

    if (shouldOptimize) {
      return (
        <div className="space-y-4">
          <div className="p-3 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg">
            <div className="flex items-center space-x-2 text-sm text-orange-700 dark:text-orange-300">
              <Battery className="h-4 w-4" />
              <span>Performance optimized for your device</span>
            </div>
          </div>
          <Suspense fallback={<SkeletonLoader className="h-64 w-full" />}>
            {children}
          </Suspense>
        </div>
      );
    }

    return (
      <Suspense fallback={<SkeletonLoader className="h-64 w-full" />}>
        {children}
      </Suspense>
    );
  };

  return (
    <>
      <PerformanceIndicator />
      <OptimizedWrapper>
        {children}
      </OptimizedWrapper>
    </>
  );
}

// Specific optimized components for mobile
export const MobileOptimizedChart = ({ children }: { children: React.ReactNode }) => (
  <MobilePerformanceOptimizer enableLazyLoading={true}>
    {children}
  </MobilePerformanceOptimizer>
);

export const MobileOptimizedExport = ({ children }: { children: React.ReactNode }) => (
  <MobilePerformanceOptimizer enableNetworkOptimization={true}>
    {children}
  </MobilePerformanceOptimizer>
);

export const MobileOptimizedAnalytics = ({ children }: { children: React.ReactNode }) => (
  <MobilePerformanceOptimizer enableLazyLoading={true} enableBatteryOptimization={true}>
    {children}
  </MobilePerformanceOptimizer>
);
