/**
 * Test Dashboard API
 */

const { default: fetch } = require('node-fetch');

const testDashboard = async () => {
  try {
    console.log('🧪 Testing Dashboard API...');
    
    // Test main dashboard endpoint
    console.log('📊 Testing /api/dashboard...');
    const dashboardResponse = await fetch('http://localhost:3000/api/dashboard');
    const dashboardResult = await dashboardResponse.json();
    
    if (dashboardResponse.ok) {
      console.log('✅ Dashboard API Test PASSED');
      console.log('📊 Dashboard Data:', {
        success: dashboardResult.success,
        userId: dashboardResult.data?.user?.id,
        totalSummaries: dashboardResult.data?.stats?.totalSummaries,
        workspacesConnected: dashboardResult.data?.stats?.workspacesConnected,
        timing: dashboardResult.timing
      });
    } else {
      console.log('❌ Dashboard API Test FAILED');
      console.log('Error:', dashboardResult);
    }
    
    // Test analytics endpoint
    console.log('\n📊 Testing /api/analytics...');
    const analyticsResponse = await fetch('http://localhost:3000/api/analytics');
    const analyticsResult = await analyticsResponse.json();
    
    if (analyticsResponse.ok) {
      console.log('✅ Analytics API Test PASSED');
      console.log('📊 Analytics Data:', {
        success: analyticsResult.success,
        totalSummaries: analyticsResult.data?.overview?.total_summaries,
        avgProcessingTime: analyticsResult.data?.overview?.avg_processing_time,
        dailyDataPoints: analyticsResult.data?.daily_data?.length
      });
    } else {
      console.log('❌ Analytics API Test FAILED');
      console.log('Error:', analyticsResult);
    }
    
    // Test metrics endpoint
    console.log('\n📊 Testing /api/analytics/metrics...');
    const metricsResponse = await fetch('http://localhost:3000/api/analytics/metrics');
    const metricsResult = await metricsResponse.json();
    
    if (metricsResponse.ok) {
      console.log('✅ Metrics API Test PASSED');
      console.log('📊 Metrics Data:', {
        success: metricsResult.success,
        totalSummaries: metricsResult.data?.totalSummaries,
        totalUsers: metricsResult.data?.totalUsers,
        topChannels: metricsResult.data?.topChannels?.length
      });
    } else {
      console.log('❌ Metrics API Test FAILED');
      console.log('Error:', metricsResult);
    }
    
  } catch (error) {
    console.log('❌ Dashboard Test ERROR');
    console.error('Error:', error);
  }
};

// Run the test
testDashboard();
