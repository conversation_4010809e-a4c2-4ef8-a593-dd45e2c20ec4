#!/usr/bin/env tsx

/**
 * Complete Flow Test Script
 * 
 * This script tests the entire application flow to ensure everything works correctly
 */

import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  message: string;
  duration?: number;
}

class FlowTester {
  private results: TestResult[] = [];
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  }

  private async test(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        status: 'PASS',
        message: 'Test passed successfully',
        duration
      });
      console.log(`✅ ${name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        status: 'FAIL',
        message: error instanceof Error ? error.message : 'Unknown error',
        duration
      });
      console.log(`❌ ${name} (${duration}ms): ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async skip(name: string, reason: string): Promise<void> {
    this.results.push({
      name,
      status: 'SKIP',
      message: reason
    });
    console.log(`⏭️ ${name}: ${reason}`);
  }

  async testEnvironmentVariables(): Promise<void> {
    await this.test('Environment Variables Check', async () => {
      const requiredVars = [
        'NEXT_PUBLIC_SUPABASE_URL',
        'NEXT_PUBLIC_SUPABASE_ANON_KEY',
        'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
        'CLERK_SECRET_KEY'
      ];

      const missingVars = requiredVars.filter(varName => !process.env[varName]);
      
      if (missingVars.length > 0) {
        throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
      }

      // Validate Clerk key format
      const clerkKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;
      if (clerkKey && !clerkKey.startsWith('pk_test_') && !clerkKey.startsWith('pk_live_')) {
        throw new Error('Invalid Clerk publishable key format');
      }
    });
  }

  async testSupabaseConnection(): Promise<void> {
    await this.test('Supabase Connection', async () => {
      const { createClient } = await import('@supabase/supabase-js');
      
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
      
      const supabase = createClient(supabaseUrl, supabaseKey);
      
      // Test connection with a simple query
      const { error } = await supabase.from('summaries').select('count').limit(1);
      
      if (error && error.code !== 'PGRST116') {
        throw new Error(`Supabase connection failed: ${error.message}`);
      }
    });
  }

  async testAPIRoutes(): Promise<void> {
    await this.test('API Routes Health Check', async () => {
      const routes = [
        '/api/health',
        '/api/dashboard',
      ];

      for (const route of routes) {
        try {
          const response = await fetch(`${this.baseUrl}${route}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          // For protected routes, 401 is expected without auth
          if (route === '/api/dashboard' && response.status === 401) {
            continue; // This is expected for protected routes
          }

          if (!response.ok && response.status !== 401) {
            throw new Error(`Route ${route} returned status ${response.status}`);
          }
        } catch (error) {
          if (error instanceof Error && error.message.includes('ECONNREFUSED')) {
            throw new Error(`Cannot connect to ${this.baseUrl}. Make sure the development server is running.`);
          }
          throw error;
        }
      }
    });
  }

  async testPageRoutes(): Promise<void> {
    await this.test('Page Routes Accessibility', async () => {
      const routes = [
        '/',
        '/sign-in',
        '/sign-up',
        '/upload',
        '/dashboard'
      ];

      for (const route of routes) {
        try {
          const response = await fetch(`${this.baseUrl}${route}`, {
            method: 'GET',
            headers: {
              'Accept': 'text/html',
            },
          });

          if (!response.ok) {
            throw new Error(`Route ${route} returned status ${response.status}`);
          }

          const html = await response.text();
          if (!html.includes('<!DOCTYPE html>') && !html.includes('<html')) {
            throw new Error(`Route ${route} did not return valid HTML`);
          }
        } catch (error) {
          if (error instanceof Error && error.message.includes('ECONNREFUSED')) {
            throw new Error(`Cannot connect to ${this.baseUrl}. Make sure the development server is running.`);
          }
          throw error;
        }
      }
    });
  }

  async testClerkConfiguration(): Promise<void> {
    await this.test('Clerk Configuration', async () => {
      const publishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;
      const secretKey = process.env.CLERK_SECRET_KEY;

      if (!publishableKey || !secretKey) {
        throw new Error('Clerk keys are not configured');
      }

      // Validate key formats
      if (!publishableKey.startsWith('pk_test_') && !publishableKey.startsWith('pk_live_')) {
        throw new Error('Invalid Clerk publishable key format');
      }

      if (!secretKey.startsWith('sk_test_') && !secretKey.startsWith('sk_live_')) {
        throw new Error('Invalid Clerk secret key format');
      }

      // Check if keys match environment (both test or both live)
      const pubKeyEnv = publishableKey.startsWith('pk_test_') ? 'test' : 'live';
      const secretKeyEnv = secretKey.startsWith('sk_test_') ? 'test' : 'live';

      if (pubKeyEnv !== secretKeyEnv) {
        throw new Error('Clerk publishable key and secret key environments do not match');
      }
    });
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Complete Flow Tests...\n');

    await this.testEnvironmentVariables();
    await this.testSupabaseConnection();
    await this.testClerkConfiguration();
    await this.testAPIRoutes();
    await this.testPageRoutes();

    this.printSummary();
  }

  private printSummary(): void {
    console.log('\n📊 Test Summary:');
    console.log('================');

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const skipped = this.results.filter(r => r.status === 'SKIP').length;

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏭️ Skipped: ${skipped}`);
    console.log(`📈 Total: ${this.results.length}`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`  - ${r.name}: ${r.message}`));
    }

    if (failed === 0) {
      console.log('\n🎉 All tests passed! Your application is ready to run.');
      console.log('\n🚀 Next steps:');
      console.log('1. Start the development server: npm run dev');
      console.log('2. Open http://localhost:3000 in your browser');
      console.log('3. Sign up or sign in with Clerk');
      console.log('4. Test the dashboard and upload functionality');
    } else {
      console.log('\n🔧 Please fix the failed tests before running the application.');
    }
  }
}

// Run the tests
if (require.main === module) {
  const tester = new FlowTester();
  tester.runAllTests()
    .then(() => {
      const failedTests = tester['results'].filter(r => r.status === 'FAIL').length;
      process.exit(failedTests > 0 ? 1 : 0);
    })
    .catch((error) => {
      console.error('❌ Test runner failed:', error);
      process.exit(1);
    });
}

export { FlowTester };
