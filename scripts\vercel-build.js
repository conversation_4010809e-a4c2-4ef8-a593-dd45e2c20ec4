#!/usr/bin/env node

/**
 * Vercel Build Script
 * Optimized build process for Vercel deployment
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Vercel-optimized build process...\n');

// 1. Environment validation
console.log('1️⃣ Validating environment...');
try {
  // Check if we're in Vercel environment
  const isVercel = process.env.VERCEL === '1';
  console.log(`Environment: ${isVercel ? 'Vercel' : 'Local'}`);
  
  // Set memory optimization
  if (!process.env.NODE_OPTIONS) {
    process.env.NODE_OPTIONS = '--max_old_space_size=4096';
  }
  
  console.log('✅ Environment validated\n');
} catch (error) {
  console.error('❌ Environment validation failed:', error.message);
  process.exit(1);
}

// 2. Clean previous builds
console.log('2️⃣ Cleaning previous builds...');
try {
  if (fs.existsSync('.next')) {
    execSync('rm -rf .next', { stdio: 'inherit' });
  }
  console.log('✅ Build artifacts cleaned\n');
} catch (error) {
  console.warn('⚠️ Could not clean build artifacts:', error.message);
}

// 3. Install dependencies (if needed)
console.log('3️⃣ Checking dependencies...');
try {
  if (!fs.existsSync('node_modules')) {
    console.log('Installing dependencies...');
    execSync('npm ci', { stdio: 'inherit' });
  }
  console.log('✅ Dependencies ready\n');
} catch (error) {
  console.error('❌ Dependency installation failed:', error.message);
  process.exit(1);
}

// 4. Run Next.js build with Vercel optimizations
console.log('4️⃣ Building Next.js application...');
try {
  const buildEnv = {
    ...process.env,
    NODE_ENV: 'production',
    NEXT_TELEMETRY_DISABLED: '1',
    SKIP_ENV_VALIDATION: 'true',
    NODE_OPTIONS: '--max_old_space_size=8192 --no-warnings',
    VERCEL: '1'
  };

  // First attempt with standard build
  execSync('next build', {
    stdio: 'inherit',
    env: buildEnv
  });
  console.log('✅ Next.js build completed\n');
} catch (error) {
  console.error('❌ Next.js build failed:', error.message);

  // Try with maximum memory optimization
  console.log('🔄 Retrying with maximum memory optimization...');
  try {
    const maxMemoryEnv = {
      ...process.env,
      NODE_ENV: 'production',
      NEXT_TELEMETRY_DISABLED: '1',
      SKIP_ENV_VALIDATION: 'true',
      NODE_OPTIONS: '--max_old_space_size=8192 --no-warnings --expose-gc',
      VERCEL: '1'
    };

    execSync('node --max_old_space_size=8192 --expose-gc ./node_modules/.bin/next build', {
      stdio: 'inherit',
      env: maxMemoryEnv
    });
    console.log('✅ Next.js build completed with maximum memory optimization\n');
  } catch (retryError) {
    console.error('❌ Build failed even with maximum memory optimization:', retryError.message);

    // Final attempt with minimal build
    console.log('🔄 Final attempt with minimal configuration...');
    try {
      const minimalEnv = {
        NODE_ENV: 'production',
        NEXT_TELEMETRY_DISABLED: '1',
        SKIP_ENV_VALIDATION: 'true',
        NODE_OPTIONS: '--max_old_space_size=8192',
        VERCEL: '1'
      };

      execSync('next build', {
        stdio: 'inherit',
        env: minimalEnv
      });
      console.log('✅ Next.js build completed with minimal configuration\n');
    } catch (finalError) {
      console.error('❌ All build attempts failed:', finalError.message);
      process.exit(1);
    }
  }
}

// 5. Verify build output
console.log('5️⃣ Verifying build output...');
try {
  const buildDir = '.next';
  const staticDir = path.join(buildDir, 'static');
  
  if (!fs.existsSync(buildDir)) {
    throw new Error('Build directory not found');
  }
  
  if (!fs.existsSync(staticDir)) {
    throw new Error('Static assets not generated');
  }
  
  console.log('✅ Build output verified\n');
} catch (error) {
  console.error('❌ Build verification failed:', error.message);
  process.exit(1);
}

console.log('🎉 Vercel build completed successfully!');
console.log('📦 Build artifacts are ready for deployment');
