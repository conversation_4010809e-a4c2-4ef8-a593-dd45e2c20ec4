'use client';

/**
 * Client-Side User Management
 * 
 * For use in client components only
 */

import { createBrowserSupabaseClient } from './supabase-browser';

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  last_active_at?: string;
  settings?: UserSettings;
}

export interface UserSettings {
  theme: 'light' | 'dark' | 'system';
  notifications: {
    email: boolean;
    slack: boolean;
    push: boolean;
  };
  timezone: string;
  language: string;
}

/**
 * Get current authenticated user (client-side)
 * Use this in client components only
 */
export async function getCurrentUserClient(): Promise<User | null> {
  try {
    const supabase = createBrowserSupabaseClient();
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Session error:', sessionError);
      return null;
    }
    
    if (!session?.user) {
      return null;
    }
    
    // Get user profile from database
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('id', session.user.id)
      .single();
    
    if (profileError) {
      console.error('Profile error:', profileError);
      // Return basic user info from auth if profile doesn't exist
      return {
        id: session.user.id,
        email: session.user.email || '',
        name: session.user.user_metadata?.name || session.user.user_metadata?.full_name,
        avatar_url: session.user.user_metadata?.avatar_url,
        created_at: session.user.created_at,
        updated_at: session.user.updated_at || session.user.created_at
      };
    }
    
    return profile;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Update user profile (client-side)
 */
export async function updateUserProfile(updates: Partial<User>): Promise<User | null> {
  try {
    const supabase = createBrowserSupabaseClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('No authenticated user');
    }
    
    const { data, error } = await supabase
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)
      .select()
      .single();
    
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error updating user profile:', error);
    return null;
  }
}

/**
 * Sign out user (client-side)
 */
export async function signOut(): Promise<void> {
  try {
    const supabase = createBrowserSupabaseClient();
    await supabase.auth.signOut();
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
}
