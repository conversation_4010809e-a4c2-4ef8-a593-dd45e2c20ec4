import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { analytics } from '@/lib/posthog.client';

export async function POST(request: NextRequest) {
  try {
  devLog.log('📄 DOCX Parse API called');

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      return NextResponse.json(
        { error: 'Invalid file type. Expected DOCX.' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
  devLog.log(`📄 Parsing DOCX: ${file.name} (${buffer.length} bytes)`);

    // Parse DOCX with dynamic import
    const mammoth = await import('mammoth');
    const result = await mammoth.extractRawText({ buffer });
    
    if (!result.value || result.value.trim().length === 0) {
      return NextResponse.json(
        { error: 'No text content found in DOCX' },
        { status: 400 }
      );
    }
  devLog.log(`📄 DOCX parsed successfully: ${result.value.length} characters`);

    // Track analytics
    if (typeof window !== 'undefined') {
      analytics.track('docx_parsed', {
        file_name: file.name,
        file_size: file.size,
        text_length: result.value.length,
        warnings: result.messages.length
      });
    }

    return NextResponse.json({
      success: true,
      content: result.value,
      metadata: {
        textLength: result.value.length,
        warnings: result.messages,
        hasImages: result.value.includes('[image]')
      }
    });

  } catch (error) {
    console.error('DOCX parsing error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to parse DOCX',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
