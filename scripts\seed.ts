#!/usr/bin/env tsx

/**
 * Database Seeding Script
 * 
 * This script seeds the database with sample data for development and testing
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Sample data
const sampleUserProfile = {
  clerk_user_id: 'demo-user-123',
  email: '<EMAIL>',
  full_name: 'Demo User',
  name: 'Demo User',
  subscription_plan: 'Free',
  subscription_status: 'active'
};

const sampleSummaries = [
  {
    title: 'Weekly Team Standup - Product Planning',
    content: 'This week we discussed the upcoming product roadmap and sprint planning. Key decisions were made regarding feature prioritization and resource allocation.',
    key_points: [
      'Finalized Q1 roadmap priorities',
      'Allocated resources for new feature development',
      'Identified potential blockers and mitigation strategies'
    ],
    action_items: [
      'Create detailed user stories for priority features',
      'Schedule design review sessions',
      'Set up development environment for new team members'
    ],
    participants: ['Alice Johnson', 'Bob Smith', 'Carol Davis'],
    source_type: 'manual',
    status: 'completed',
    tags: ['planning', 'roadmap', 'team'],
    user_id: 'demo-user-123'
  },
  {
    title: 'Client Meeting - Project Requirements',
    content: 'Met with the client to discuss project requirements and timeline. They provided detailed feedback on the initial mockups and requested several modifications.',
    key_points: [
      'Client approved overall design direction',
      'Requested changes to user interface layout',
      'Confirmed project timeline and milestones'
    ],
    action_items: [
      'Update mockups based on client feedback',
      'Prepare revised timeline proposal',
      'Schedule follow-up meeting for next week'
    ],
    participants: ['Demo User', 'Client Representative', 'Project Manager'],
    source_type: 'manual',
    status: 'completed',
    tags: ['client', 'requirements', 'design'],
    user_id: 'demo-user-123'
  },
  {
    title: 'Technical Architecture Review',
    content: 'Reviewed the technical architecture for the new microservices implementation. Discussed scalability concerns and security considerations.',
    key_points: [
      'Approved microservices architecture design',
      'Identified security requirements',
      'Discussed deployment and monitoring strategies'
    ],
    action_items: [
      'Create detailed technical specifications',
      'Set up CI/CD pipeline',
      'Implement security audit checklist'
    ],
    participants: ['Tech Lead', 'Senior Developer', 'DevOps Engineer'],
    source_type: 'slack',
    status: 'completed',
    tags: ['architecture', 'technical', 'security'],
    user_id: 'demo-user-123'
  }
];

const sampleWorkspaces = [
  {
    name: 'Product Team Workspace',
    slack_team_id: 'T1234567890',
    connected: true,
    user_id: 'demo-user-123'
  },
  {
    name: 'Engineering Workspace',
    slack_team_id: 'T0987654321',
    connected: true,
    user_id: 'demo-user-123'
  }
];

const sampleNotifications = [
  {
    type: 'summary_created',
    title: 'New Summary Created',
    message: 'Your meeting summary "Weekly Team Standup" has been generated successfully.',
    read: false,
    user_id: 'demo-user-123'
  },
  {
    type: 'workspace_connected',
    title: 'Workspace Connected',
    message: 'Successfully connected to Product Team Workspace.',
    read: true,
    user_id: 'demo-user-123'
  }
];

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // Seed user profile
    console.log('👤 Seeding user profile...');
    const { error: profileError } = await supabase
      .from('user_profiles')
      .upsert(sampleUserProfile, { onConflict: 'clerk_user_id' });

    if (profileError) {
      console.warn('⚠️ Could not seed user profile (table may not exist):', profileError.message);
    } else {
      console.log('✅ User profile seeded successfully');
    }

    // Seed summaries
    console.log('📝 Seeding summaries...');
    const { error: summariesError } = await supabase
      .from('summaries')
      .upsert(sampleSummaries, { onConflict: 'id' });

    if (summariesError) {
      console.warn('⚠️ Could not seed summaries (table may not exist):', summariesError.message);
    } else {
      console.log('✅ Summaries seeded successfully');
    }

    // Seed workspaces
    console.log('🏢 Seeding workspaces...');
    const { error: workspacesError } = await supabase
      .from('workspaces')
      .upsert(sampleWorkspaces, { onConflict: 'id' });

    if (workspacesError) {
      console.warn('⚠️ Could not seed workspaces (table may not exist):', workspacesError.message);
    } else {
      console.log('✅ Workspaces seeded successfully');
    }

    // Seed notifications
    console.log('🔔 Seeding notifications...');
    const { error: notificationsError } = await supabase
      .from('notifications')
      .upsert(sampleNotifications, { onConflict: 'id' });

    if (notificationsError) {
      console.warn('⚠️ Could not seed notifications (table may not exist):', notificationsError.message);
    } else {
      console.log('✅ Notifications seeded successfully');
    }

    console.log('🎉 Database seeding completed!');
    console.log('');
    console.log('📊 Seeded data summary:');
    console.log(`- User profiles: 1`);
    console.log(`- Summaries: ${sampleSummaries.length}`);
    console.log(`- Workspaces: ${sampleWorkspaces.length}`);
    console.log(`- Notifications: ${sampleNotifications.length}`);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seeding script
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding script failed:', error);
      process.exit(1);
    });
}

export { seedDatabase };
