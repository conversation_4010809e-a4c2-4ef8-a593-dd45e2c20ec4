'use client'

import { useState, useEffect } from 'react'

// Safe chart components with fallbacks
interface ChartProps {
  data: any[]
  className?: string
  height?: number
}

// Fallback chart component using CSS
const FallbackChart = ({ data, className = "", height = 300 }: ChartProps) => {
  const maxValue = Math.max(...data.map(d => d.value || d.summaries || d.uploads || 0))
  
  return (
    <div className={`relative ${className}`} style={{ height }}>
      <div className="flex items-end justify-between h-full p-4 bg-gradient-to-t from-blue-50 to-white rounded-lg border">
        {data.slice(0, 7).map((item, index) => {
          const value = item.value || item.summaries || item.uploads || 0
          const heightPercent = maxValue > 0 ? (value / maxValue) * 80 : 20
          
          return (
            <div key={index} className="flex flex-col items-center space-y-2">
              <div 
                className="bg-blue-500 rounded-t-sm min-w-[20px] transition-all duration-300 hover:bg-blue-600"
                style={{ height: `${heightPercent}%`, width: '24px' }}
                title={`${item.name || item.date}: ${value}`}
              />
              <span className="text-xs text-gray-600 rotate-45 origin-left">
                {item.name || item.date || `Item ${index + 1}`}
              </span>
            </div>
          )
        })}
      </div>
    </div>
  )
}

// Safe line chart
export const SafeLineChart = ({ data, className, height = 300 }: ChartProps) => {
  const [RechartsComponents, setRechartsComponents] = useState<any>(null)
  const [error, setError] = useState(false)

  useEffect(() => {
    import('recharts')
      .then((recharts) => {
        setRechartsComponents({
          LineChart: recharts.LineChart,
          Line: recharts.Line,
          XAxis: recharts.XAxis,
          YAxis: recharts.YAxis,
          CartesianGrid: recharts.CartesianGrid,
          Tooltip: recharts.Tooltip,
          ResponsiveContainer: recharts.ResponsiveContainer
        })
      })
      .catch(() => {
        console.warn('Recharts failed to load, using fallback chart')
        setError(true)
      })
  }, [])

  if (error || !RechartsComponents) {
    return <FallbackChart data={data} className={className} height={height} />
  }

  const { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } = RechartsComponents

  return (
    <ResponsiveContainer width="100%" height={height} className={className}>
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Line type="monotone" dataKey="value" stroke="#3b82f6" strokeWidth={2} />
      </LineChart>
    </ResponsiveContainer>
  )
}

// Safe area chart
export const SafeAreaChart = ({ data, className, height = 300 }: ChartProps) => {
  const [RechartsComponents, setRechartsComponents] = useState<any>(null)
  const [error, setError] = useState(false)

  useEffect(() => {
    import('recharts')
      .then((recharts) => {
        setRechartsComponents({
          AreaChart: recharts.AreaChart,
          Area: recharts.Area,
          XAxis: recharts.XAxis,
          YAxis: recharts.YAxis,
          CartesianGrid: recharts.CartesianGrid,
          Tooltip: recharts.Tooltip,
          ResponsiveContainer: recharts.ResponsiveContainer
        })
      })
      .catch(() => {
        console.warn('Recharts failed to load, using fallback chart')
        setError(true)
      })
  }, [])

  if (error || !RechartsComponents) {
    return <FallbackChart data={data} className={className} height={height} />
  }

  const { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } = RechartsComponents

  return (
    <ResponsiveContainer width="100%" height={height} className={className}>
      <AreaChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Area type="monotone" dataKey="value" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.3} />
      </AreaChart>
    </ResponsiveContainer>
  )
}

// Safe bar chart
export const SafeBarChart = ({ data, className, height = 300 }: ChartProps) => {
  const [RechartsComponents, setRechartsComponents] = useState<any>(null)
  const [error, setError] = useState(false)

  useEffect(() => {
    import('recharts')
      .then((recharts) => {
        setRechartsComponents({
          BarChart: recharts.BarChart,
          Bar: recharts.Bar,
          XAxis: recharts.XAxis,
          YAxis: recharts.YAxis,
          CartesianGrid: recharts.CartesianGrid,
          Tooltip: recharts.Tooltip,
          ResponsiveContainer: recharts.ResponsiveContainer
        })
      })
      .catch(() => {
        console.warn('Recharts failed to load, using fallback chart')
        setError(true)
      })
  }, [])

  if (error || !RechartsComponents) {
    return <FallbackChart data={data} className={className} height={height} />
  }

  const { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } = RechartsComponents

  return (
    <ResponsiveContainer width="100%" height={height} className={className}>
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Bar dataKey="value" fill="#3b82f6" />
      </BarChart>
    </ResponsiveContainer>
  )
}

// Safe pie chart
export const SafePieChart = ({ data, className, height = 300 }: ChartProps) => {
  const [RechartsComponents, setRechartsComponents] = useState<any>(null)
  const [error, setError] = useState(false)

  useEffect(() => {
    import('recharts')
      .then((recharts) => {
        setRechartsComponents({
          PieChart: recharts.PieChart,
          Pie: recharts.Pie,
          Cell: recharts.Cell,
          Tooltip: recharts.Tooltip,
          ResponsiveContainer: recharts.ResponsiveContainer
        })
      })
      .catch(() => {
        console.warn('Recharts failed to load, using fallback chart')
        setError(true)
      })
  }, [])

  if (error || !RechartsComponents) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height }}>
        <div className="grid grid-cols-2 gap-4">
          {data.slice(0, 4).map((item, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: `hsl(${index * 90}, 70%, 50%)` }}
              />
              <span className="text-sm">{item.name}: {item.value}</span>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const { PieChart, Pie, Cell, Tooltip, ResponsiveContainer } = RechartsComponents
  const COLORS = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6']

  return (
    <ResponsiveContainer width="100%" height={height} className={className}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip />
      </PieChart>
    </ResponsiveContainer>
  )
}

// Export all components as default
const SafeCharts = {
  SafeLineChart,
  SafeAreaChart,
  SafeBarChart,
  SafePieChart
}

export default SafeCharts
