import { NextRequest, NextResponse } from 'next/server';
import { createRBACProtectedRoute, Permission } from '@/lib/auth-protection';
import { createSecureApiRoute } from '@/lib/api-security';

/**
 * Admin Access Check API
 * 
 * Verifies if the current user has admin access
 */
export const GET = createRBACProtectedRoute(
  async (request: NextRequest, authResult) => {
    // If we reach here, user has admin permissions
    return NextResponse.json({ 
      success: true, 
      message: 'Admin access granted',
      userId: authResult.userId
    });
  },
  {
    requiredPermission: Permission.SYSTEM_CONFIG,
    requireAuth: true,
    rateLimit: 60, // 60 requests per minute
    auditLog: true,
    allowedMethods: ['GET']
  }
);
