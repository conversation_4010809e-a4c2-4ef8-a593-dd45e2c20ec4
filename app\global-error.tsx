'use client';

import { devLog } from '@/lib/console-cleaner';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, RefreshCw, Home, Bug, Zap } from 'lucide-react';
import { toast } from 'sonner';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    const handleError = async () => {
      try {
        // Enhanced error logging with analytics
        if (typeof window !== 'undefined') {
          // PostHog error tracking
          if ((window as any).posthog) {
            try {
              (window as any).posthog.capture('global_error', {
                error_message: error?.message || 'Unknown error',
                error_digest: error?.digest,
                error_stack: error?.stack,
                retry_count: retryCount,
                url: window.location.href,
                timestamp: new Date().toISOString()
              });
            } catch (e) {
              devLog.warn('PostHog logging failed:', e);
            }
          }

          // Sentry error tracking
          if ((window as any).Sentry) {
            try {
              (window as any).Sentry.withScope((scope: any) => {
                scope.setTag('errorBoundary', 'GlobalError');
                scope.setContext('errorInfo', {
                  digest: error?.digest,
                  retryCount,
                  url: window.location.href
                });
                scope.setLevel('error');
                (window as any).Sentry.captureException(error);
              });
            } catch (e) {
              devLog.warn('Sentry logging failed:', e);
            }
          }
        }
      }

      console.error('🕐 Error time:', new Date().toISOString());
      console.error('🌐 Current URL:', typeof window !== 'undefined' ? window.location.href : 'SSR');

      // Enhanced error logging with analytics
      if (typeof window !== 'undefined') {
        // PostHog error tracking
        if ((window as any).posthog) {
          try {
            (window as any).posthog.capture('global_error_caught', {
              error_message: error?.message || 'Unknown error',
              error_digest: error?.digest,
              error_stack: error?.stack,
              url: window.location.href,
              timestamp: new Date().toISOString()
            });
          } catch (posthogError) {
            console.warn('Failed to log to PostHog:', posthogError);
          }
        }

        // Sentry error tracking
        if ((window as any).Sentry) {
          try {
            (window as any).Sentry.withScope((scope: any) => {
              scope.setTag('errorBoundary', 'GlobalError');
              scope.setTag('errorType', 'global');
              scope.setContext('errorInfo', {
                digest: error?.digest,
                url: window.location.href,
                timestamp: new Date().toISOString()
              });
              scope.setLevel('error');
              (window as any).Sentry.captureException(error);
            });
          } catch (sentryError) {
            console.warn('Failed to log to Sentry:', sentryError);
          }
        }

        console.error('🔥 Global error logged for debugging');
      }

      // Check for chunk loading errors and auto-recover
      const errorMessage = (error && typeof error === 'object' && error.message) ? error.message : '';
      const errorStack = (error && typeof error === 'object' && error.stack) ? error.stack : '';

      if (errorMessage.includes('Cannot read properties of undefined (reading \'call\')') ||
          errorMessage.includes('ChunkLoadError') ||
          errorMessage.includes('Loading chunk') ||
          errorMessage.includes('runtime.js') ||
          errorStack.includes('webpack.js') ||
          errorStack.includes('pages-brows._global-error.js')) {
  devLog.log('🔄 Chunk/webpack loading error detected, clearing caches and reloading...');

        // Clear all caches and reload
        if (typeof window !== 'undefined') {
          try {
            Promise.all([
              // Clear service worker caches
              'caches' in window ? caches.keys().then(names =>
                Promise.all(names.map(name => caches.delete(name)))
              ).catch(() => Promise.resolve()) : Promise.resolve(),
              // Clear localStorage safely
              (() => {
                try {
                  localStorage.clear();
                } catch (e) {
                  console.warn('Failed to clear localStorage:', e);
                }
                return Promise.resolve();
              })(),
              // Clear sessionStorage safely
              (() => {
                try {
                  sessionStorage.clear();
                } catch (e) {
                  console.warn('Failed to clear sessionStorage:', e);
                }
                return Promise.resolve();
              })()
            ]).finally(() => {
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            });
          } catch (clearError) {
            console.warn('Error during cache clearing:', clearError);
            // Fallback: just reload
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        }
      }
    } catch (loggingError) {
      // Fallback logging if even the error logging fails
      console.error('🚨 Error in error logging:', loggingError);
      console.error('🚨 Original error (fallback):', error);
    }
  }, [error]);

  const handleRetry = async () => {
    setIsRetrying(true);

    try {
      // Add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 500));

      // Increment retry count
      const newRetryCount = retryCount + 1;
      setRetryCount(newRetryCount);

      // Log retry attempt
      if (typeof window !== 'undefined' && (window as any).posthog) {
        (window as any).posthog.capture('global_error_retry', {
          retry_count: newRetryCount,
          error_message: error?.message
        });
      }

      reset();
      toast.success('Retrying...', { duration: 2000 });
    } catch (retryError) {
      console.error('Error during retry:', retryError);
      toast.error('Retry failed. Please reload the page.');
    } finally {
      setIsRetrying(false);
    }
  };

  const handleReload = () => {
    if (typeof window !== 'undefined') {
      // Log reload attempt
      if ((window as any).posthog) {
        (window as any).posthog.capture('global_error_reload', {
          error_message: error?.message
        });
      }

      toast.loading('Reloading page...', { duration: 1000 });
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  };

  const handleGoHome = () => {
    if (typeof window !== 'undefined') {
      // Log navigation attempt
      if ((window as any).posthog) {
        (window as any).posthog.capture('global_error_go_home', {
          error_message: error?.message
        });
      }

      window.location.href = '/';
    }
  };

  const handleReportError = () => {
    const errorReport = {
      message: error?.message || 'Unknown error',
      stack: error?.stack || 'No stack trace',
      digest: error?.digest || 'No digest',
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    const subject = encodeURIComponent('Critical Error Report - Global Error');
    const body = encodeURIComponent(`Critical Error Report:\n\n${JSON.stringify(errorReport, null, 2)}`);
    const mailtoUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;

    window.open(mailtoUrl, '_blank');

    // Log error report
    if ((window as any).posthog) {
      (window as any).posthog.capture('global_error_report', {
        error_message: error?.message
      });
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100 p-4">
      <Card className="max-w-lg w-full shadow-xl">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <Zap className="h-16 w-16 text-red-500" />
              <div className="absolute -top-2 -right-2 bg-red-600 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold animate-pulse">
                !
              </div>
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
            Critical Application Error
          </CardTitle>
          <CardDescription className="text-gray-600 text-base">
            A serious error occurred that prevented the application from working properly.
            Our team has been automatically notified.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {retryCount > 0 && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                Retry attempt #{retryCount}. If the problem persists, please contact support.
              </AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 gap-3">
            <Button
              onClick={handleRetry}
              disabled={isRetrying}
              className="w-full"
              variant="default"
            >
              {isRetrying ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Retrying...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </>
              )}
            </Button>

            <div className="grid grid-cols-2 gap-2">
              <Button onClick={handleReload} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload
              </Button>
              <Button onClick={handleGoHome} variant="outline">
                <Home className="h-4 w-4 mr-2" />
                Home
              </Button>
            </div>

            <Button
              onClick={handleReportError}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-700"
            >
              <Bug className="h-4 w-4 mr-2" />
              Report Critical Issue
            </Button>
          </div>

          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4 p-4 bg-red-100 rounded-lg text-xs">
              <summary className="cursor-pointer font-medium text-red-700 hover:text-red-900">
                🔍 Error Details (Development)
              </summary>
              <div className="mt-3 space-y-2">
                <div>
                  <strong>Message:</strong> {error?.message || 'No message available'}
                </div>
                <div>
                  <strong>Digest:</strong> {error?.digest || 'No digest available'}
                </div>
                <pre className="mt-2 whitespace-pre-wrap text-xs bg-white p-2 rounded border overflow-auto max-h-32">
                  {error?.stack || 'No stack trace available'}
                </pre>
              </div>
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
