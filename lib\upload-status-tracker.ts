import { devLog } from '@/lib/console-cleaner';
/**
 * Upload Status Tracking System
 * 
 * Manages file upload progress and status for public mode
 * Provides real-time status updates without authentication
 */

export interface UploadStatus {
  fileId: string;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  fileName?: string;
  fileSize?: number;
  summaryId?: string;
  errorMessage?: string;
  startTime: number;
  lastUpdate: number;
  processingSteps?: ProcessingStep[];
}

export interface ProcessingStep {
  step: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  message: string;
  timestamp: number;
}

class UploadStatusTracker {
  private statusMap = new Map<string, UploadStatus>();
  private readonly CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_AGE = 30 * 60 * 1000; // 30 minutes

  constructor() {
    // Clean up old entries periodically
    if (typeof window !== 'undefined') {
      setInterval(() => this.cleanup(), this.CLEANUP_INTERVAL);
    }
  }

  /**
   * Initialize upload tracking
   */
  initializeUpload(fileId: string, fileName: string, fileSize: number): UploadStatus {
    const status: UploadStatus = {
      fileId,
      status: 'uploading',
      progress: 0,
      fileName,
      fileSize,
      startTime: Date.now(),
      lastUpdate: Date.now(),
      processingSteps: [
        {
          step: 'upload',
          status: 'processing',
          message: 'Uploading file...',
          timestamp: Date.now()
        }
      ]
    };

    this.statusMap.set(fileId, status);
  devLog.log(`📁 Upload tracking initialized for ${fileId}: ${fileName}`);
    return status;
  }

  /**
   * Update upload progress
   */
  updateProgress(fileId: string, progress: number, message?: string): void {
    const status = this.statusMap.get(fileId);
    if (!status) return;

    status.progress = Math.min(100, Math.max(0, progress));
    status.lastUpdate = Date.now();

    if (message && status.processingSteps) {
      const currentStep = status.processingSteps[status.processingSteps.length - 1];
      if (currentStep) {
        currentStep.message = message;
        currentStep.timestamp = Date.now();
      }
    }

    this.statusMap.set(fileId, status);
  devLog.log(`📁 Upload progress ${fileId}: ${progress}%`);
  }

  /**
   * Mark upload as completed and start processing
   */
  startProcessing(fileId: string): void {
    const status = this.statusMap.get(fileId);
    if (!status) return;

    status.status = 'processing';
    status.progress = 10;
    status.lastUpdate = Date.now();

    if (status.processingSteps) {
      // Complete upload step
      const uploadStep = status.processingSteps.find(s => s.step === 'upload');
      if (uploadStep) {
        uploadStep.status = 'completed';
        uploadStep.message = 'File uploaded successfully';
      }

      // Add processing steps
      status.processingSteps.push(
        {
          step: 'extraction',
          status: 'processing',
          message: 'Extracting text content...',
          timestamp: Date.now()
        },
        {
          step: 'ai_processing',
          status: 'pending',
          message: 'Waiting for AI processing...',
          timestamp: Date.now()
        },
        {
          step: 'summary_generation',
          status: 'pending',
          message: 'Generating summary...',
          timestamp: Date.now()
        }
      );
    }

    this.statusMap.set(fileId, status);
  devLog.log(`📁 Processing started for ${fileId}`);
  }

  /**
   * Update processing step
   */
  updateProcessingStep(fileId: string, step: string, stepStatus: 'processing' | 'completed' | 'error', message: string, progress?: number): void {
    const status = this.statusMap.get(fileId);
    if (!status || !status.processingSteps) return;

    const processingStep = status.processingSteps.find(s => s.step === step);
    if (processingStep) {
      processingStep.status = stepStatus;
      processingStep.message = message;
      processingStep.timestamp = Date.now();
    }

    if (progress !== undefined) {
      status.progress = Math.min(100, Math.max(status.progress, progress));
    }

    status.lastUpdate = Date.now();
    this.statusMap.set(fileId, status);
  devLog.log(`📁 Processing step ${step} for ${fileId}: ${stepStatus} - ${message}`);
  }

  /**
   * Mark processing as completed
   */
  completeProcessing(fileId: string, summaryId: string): void {
    const status = this.statusMap.get(fileId);
    if (!status) return;

    status.status = 'completed';
    status.progress = 100;
    status.summaryId = summaryId;
    status.lastUpdate = Date.now();

    if (status.processingSteps) {
      status.processingSteps.forEach(step => {
        if (step.status !== 'completed') {
          step.status = 'completed';
          step.timestamp = Date.now();
        }
      });

      // Add final step
      status.processingSteps.push({
        step: 'completed',
        status: 'completed',
        message: 'Summary generated successfully',
        timestamp: Date.now()
      });
    }

    this.statusMap.set(fileId, status);
  devLog.log(`📁 Processing completed for ${fileId}: ${summaryId}`);
  }

  /**
   * Mark processing as failed
   */
  markError(fileId: string, errorMessage: string, step?: string): void {
    const status = this.statusMap.get(fileId);
    if (!status) return;

    status.status = 'error';
    status.errorMessage = errorMessage;
    status.lastUpdate = Date.now();

    if (status.processingSteps && step) {
      const processingStep = status.processingSteps.find(s => s.step === step);
      if (processingStep) {
        processingStep.status = 'error';
        processingStep.message = errorMessage;
        processingStep.timestamp = Date.now();
      }
    }

    this.statusMap.set(fileId, status);
  devLog.log(`📁 Processing error for ${fileId}: ${errorMessage}`);
  }

  /**
   * Get upload status
   */
  getStatus(fileId: string): UploadStatus | null {
    return this.statusMap.get(fileId) || null;
  }

  /**
   * Get all uploads for a specific user
   */
  getUserUploads(userId: string): UploadStatus[] {
    return Array.from(this.statusMap.values()).filter(status =>
      status.fileId.includes(userId) || status.fileId.startsWith('upload-')
    );
  }

  /**
   * Get all statuses for debugging
   */
  getAllStatuses(): UploadStatus[] {
    return Array.from(this.statusMap.values());
  }

  /**
   * Clean up old entries
   */
  private cleanup(): void {
    const now = Date.now();
    const toDelete: string[] = [];

    for (const [fileId, status] of this.statusMap.entries()) {
      if (now - status.lastUpdate > this.MAX_AGE) {
        toDelete.push(fileId);
      }
    }

    toDelete.forEach(fileId => {
      this.statusMap.delete(fileId);
  devLog.log(`📁 Cleaned up old upload status: ${fileId}`);
    });
  }

  /**
   * Generate mock processing for demo purposes
   */
  simulateProcessing(fileId: string): void {
    setTimeout(() => this.updateProgress(fileId, 25, 'Extracting text...'), 1000);
    setTimeout(() => this.updateProcessingStep(fileId, 'extraction', 'completed', 'Text extracted successfully', 40), 2000);
    setTimeout(() => this.updateProcessingStep(fileId, 'ai_processing', 'processing', 'Processing with AI...', 60), 3000);
    setTimeout(() => this.updateProcessingStep(fileId, 'ai_processing', 'completed', 'AI processing completed', 80), 5000);
    setTimeout(() => this.updateProcessingStep(fileId, 'summary_generation', 'processing', 'Generating summary...', 90), 6000);
    setTimeout(() => this.completeProcessing(fileId, `summary-${Date.now()}`), 8000);
  }
}

// Export singleton instance
export const uploadStatusTracker = new UploadStatusTracker();

export default UploadStatusTracker;
