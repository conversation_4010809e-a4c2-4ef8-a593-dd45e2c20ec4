/**
 * AI-Powered Summarization Service for Public SaaS Mode
 * 
 * Features:
 * ✅ DeepSeek & GPT-4o integration
 * ✅ Speaker labels and red flags detection
 * ✅ Key skills and action items extraction
 * ✅ Smart tagging and multi-language support
 * ✅ Progress tracking and error handling
 * ✅ Public mode support with anonymous users
 * ✅ Fallback mechanisms for API failures
 */

import { analytics } from '@/lib/posthog.client';

export interface SummarizationRequest {
  content: string;
  type: 'slack' | 'file' | 'meeting' | 'document';
  language?: string;
  includeRedFlags?: boolean;
  includeSkills?: boolean;
  includeActionItems?: boolean;
  speakerLabels?: boolean;
  fileName?: string;
  fileType?: string;
  metadata?: Record<string, any>;
  userId?: string; // Anonymous user ID for public mode
}

export interface SummarizationResult {
  id: string;
  summary: string;
  keyPoints: string[];
  actionItems: string[];
  redFlags: string[];
  skills: string[];
  speakers: string[];
  tags: string[];
  language: string;
  confidence: number;
  processingTime: number;
  wordCount: number;
  metadata: Record<string, any>;
  model: string;
  cost: number;
  tokens: number;
}

export interface ProgressCallback {
  (stage: string, progress: number, message?: string): void;
}

// AI Model Configuration with fallback support
const AI_MODELS = {
  deepseek: {
    name: 'DeepSeek R1',
    endpoint: 'https://api.openrouter.ai/api/v1/chat/completions',
    model: 'tngtech/deepseek-r1t2-chimera:free',
    maxTokens: 4000,
    temperature: 0.7,
    cost: 0.0, // Free model
    fallback: null // No fallback for free model
  },
  gpt4o: {
    name: 'GPT-4o',
    endpoint: 'https://api.openrouter.ai/api/v1/chat/completions',
    model: 'openai/gpt-4o',
    maxTokens: 4000,
    temperature: 0.7,
    cost: 0.03, // per 1k tokens
    fallback: 'deepseek'
  },
  gpt4o_mini: {
    name: 'GPT-4o Mini',
    endpoint: 'https://api.openrouter.ai/api/v1/chat/completions',
    model: 'openai/gpt-4o-mini',
    maxTokens: 4000,
    temperature: 0.7,
    cost: 0.015, // per 1k tokens
    fallback: 'deepseek'
  }
};

// Language Detection
export function detectLanguage(text: string): string {
  // Simple language detection based on common words
  const languages = {
    en: ['the', 'and', 'is', 'in', 'to', 'of', 'a', 'that', 'it', 'with'],
    es: ['el', 'la', 'de', 'que', 'y', 'en', 'un', 'es', 'se', 'no'],
    fr: ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir'],
    de: ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich'],
    pt: ['o', 'de', 'a', 'e', 'do', 'da', 'em', 'um', 'para', 'é'],
    it: ['il', 'di', 'che', 'e', 'la', 'per', 'una', 'in', 'del', 'è'],
    ru: ['в', 'и', 'не', 'на', 'я', 'быть', 'он', 'с', 'что', 'а'],
    zh: ['的', '一', '是', '在', '不', '了', '有', '和', '人', '这'],
    ja: ['の', 'に', 'は', 'を', 'た', 'が', 'で', 'て', 'と', 'し'],
    ko: ['의', '이', '가', '을', '는', '에', '와', '로', '으로', '도']
  };

  const words = text.toLowerCase().split(/\s+/).slice(0, 100);
  let maxScore = 0;
  let detectedLang = 'en';

  for (const [lang, commonWords] of Object.entries(languages)) {
    const score = words.filter(word => commonWords.includes(word)).length;
    if (score > maxScore) {
      maxScore = score;
      detectedLang = lang;
    }
  }

  return detectedLang;
}

// Smart Tagging System
export function generateTags(content: string, type: string): string[] {
  const tags: string[] = [];
  
  // Content-based tags
  if (content.toLowerCase().includes('meeting')) tags.push('meeting');
  if (content.toLowerCase().includes('project')) tags.push('project');
  if (content.toLowerCase().includes('deadline')) tags.push('deadline');
  if (content.toLowerCase().includes('budget')) tags.push('budget');
  if (content.toLowerCase().includes('team')) tags.push('team');
  if (content.toLowerCase().includes('client')) tags.push('client');
  if (content.toLowerCase().includes('review')) tags.push('review');
  if (content.toLowerCase().includes('planning')) tags.push('planning');
  
  // Type-based tags
  tags.push(type);
  
  // Language-based tags
  const language = detectLanguage(content);
  if (language !== 'en') tags.push(language);
  
  return [...new Set(tags)]; // Remove duplicates
}

// Main summarization function with public mode support
export async function generateSummary(
  request: SummarizationRequest,
  onProgress?: ProgressCallback
): Promise<SummarizationResult> {
  const startTime = Date.now();
  const userId = request.userId || `anonymous-${Date.now()}`;
  
  try {
    onProgress?.('analyzing', 10, 'Analyzing content...');
    
    // Detect language if not provided
    const language = request.language || detectLanguage(request.content);
    
    // Generate tags
    const tags = generateTags(request.content, request.type);
    
    onProgress?.('processing', 30, 'Generating summary...');
    
    // Try primary model first, then fallback
    let summary = '';
    let model = 'deepseek';
    let cost = 0;
    let tokens = 0;
    
    try {
      // Try GPT-4o first if available
      if (process.env.OPENAI_API_KEY) {
        const result = await callAIAPI(AI_MODELS.gpt4o, request, language);
        summary = result.text;
        model = 'gpt-4o';
        cost = result.cost;
        tokens = result.tokens;
      } else {
        // Fallback to DeepSeek
        const result = await callAIAPI(AI_MODELS.deepseek, request, language);
        summary = result.text;
        model = 'deepseek-r1';
        cost = result.cost;
        tokens = result.tokens;
      }
    } catch (primaryError) {
      console.warn('Primary AI model failed, trying fallback:', primaryError);
      
      // Try fallback model
      try {
        const result = await callAIAPI(AI_MODELS.deepseek, request, language);
        summary = result.text;
        model = 'deepseek-r1';
        cost = result.cost;
        tokens = result.tokens;
      } catch (fallbackError) {
        console.error('All AI models failed:', fallbackError);
        throw new Error(`AI summarization failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`);
      }
    }
    
    onProgress?.('parsing', 70, 'Extracting insights...');
    
    // Parse AI response
    const parsed = parseAIResponse(summary, request);
    
    onProgress?.('finalizing', 90, 'Finalizing summary...');
    
    // Track analytics
    try {
      analytics?.track('summary_generated', {
        userId,
        type: request.type,
        model,
        cost,
        tokens,
        processingTime: Date.now() - startTime,
        language,
        hasRedFlags: parsed.redFlags.length > 0,
        hasActionItems: parsed.actionItems.length > 0,
        hasSkills: parsed.skills.length > 0
      });
    } catch (analyticsError) {
      console.warn('Analytics tracking failed:', analyticsError);
    }
    
    onProgress?.('complete', 100, 'Summary ready!');
    
    return {
      id: `summary-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      summary: parsed.summary,
      keyPoints: parsed.keyPoints,
      actionItems: parsed.actionItems,
      redFlags: parsed.redFlags,
      skills: parsed.skills,
      speakers: parsed.speakers,
      tags,
      language,
      confidence: parsed.confidence || 0.85,
      processingTime: Date.now() - startTime,
      wordCount: request.content.split(/\s+/).length,
      metadata: {
        ...request.metadata,
        model,
        cost,
        tokens,
        userId
      },
      model,
      cost,
      tokens
    };
    
  } catch (error) {
    console.error('Summarization failed:', error);
    
    // Track error analytics
    try {
      analytics?.track('summary_error', {
        userId,
        type: request.type,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime
      });
    } catch (analyticsError) {
      console.warn('Error analytics tracking failed:', analyticsError);
    }
    
    throw error;
  }
}

// Build AI Prompt
function buildSummarizationPrompt(request: SummarizationRequest, language: string): string {
  const basePrompt = `You are an expert AI assistant specializing in content summarization. 
Analyze the following ${request.type} content and provide a comprehensive summary.

Language: ${language}
Content Type: ${request.type}

Please provide your response in the following JSON format:
{
  "summary": "Main summary (2-3 paragraphs)",
  "keyPoints": ["Key point 1", "Key point 2", ...],
  "actionItems": ["Action 1", "Action 2", ...],
  "redFlags": ["Red flag 1", "Red flag 2", ...],
  "skills": ["Skill 1", "Skill 2", ...],
  "speakers": ["Speaker 1", "Speaker 2", ...],
  "confidence": 0.95
}

Requirements:
- Summary should be concise but comprehensive
- Extract 3-7 key points
- Identify actionable items and next steps
- Flag potential issues, risks, or concerns
- Identify skills, technologies, or competencies mentioned
- List speakers/participants if identifiable
- Provide confidence score (0-1)

${request.speakerLabels ? '- Include speaker identification and labels' : ''}
${request.includeRedFlags ? '- Pay special attention to red flags and risks' : ''}
${request.includeSkills ? '- Extract technical skills and competencies' : ''}
${request.includeActionItems ? '- Focus on actionable items and next steps' : ''}

Content to analyze:`;

  return basePrompt;
}

// Call AI API
async function callAIAPI(model: any, request: SummarizationRequest, language: string): Promise<any> {
  const prompt = buildSummarizationPrompt(request, language);
  const content = request.content;

  const response = await fetch(model.endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
      'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
      'X-Title': 'Slack Summary Scribe'
    },
    body: JSON.stringify({
      model: model.model,
      messages: [
        { role: 'system', content: prompt },
        { role: 'user', content: content }
      ],
      max_tokens: model.maxTokens,
      temperature: model.temperature
    })
  });

  if (!response.ok) {
    throw new Error(`AI API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  const text = data.choices[0]?.message?.content || '';

  // Attempt to extract cost and tokens from the response if available
  let cost = 0;
  let tokens = 0;
  if (data.usage) {
    cost = data.usage.total_tokens / 1000 * (model.cost || 0); // Estimate cost based on model's cost per token
    tokens = data.usage.total_tokens;
  }

  return { text, cost, tokens };
}

// Parse AI Response
function parseAIResponse(aiResponse: string, request: SummarizationRequest): any {
  try {
    // Try to extract JSON from the response
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    // Fallback parsing if JSON is not found
    return {
      summary: aiResponse.substring(0, 500) + '...',
      keyPoints: ['Summary generated successfully'],
      actionItems: [],
      redFlags: [],
      skills: [],
      speakers: [],
      confidence: 0.8
    };
  } catch (error) {
    console.error('Failed to parse AI response:', error);
    return {
      summary: 'Summary generated but could not be parsed properly.',
      keyPoints: ['Content processed'],
      actionItems: [],
      redFlags: [],
      skills: [],
      speakers: [],
      confidence: 0.6
    };
  }
}
