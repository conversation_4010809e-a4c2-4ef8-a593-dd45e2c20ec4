#!/usr/bin/env node

/**
 * Quick script to add essential environment variables to Vercel
 */

const { execSync } = require('child_process');

const envVars = [
  { name: 'NEXT_PUBLIC_SUPABASE_ANON_KEY', value: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhvbHVwcHdlanpjcXdyYmRiZ2tmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjA2MzI3NzEsImV4cCI6MjAzNjIwODc3MX0.Hs5Ql0LLzXZZHQKMXEfxEsKXxCcGjVx9NwMYKjwrRLs' },
  { name: 'CLERK_SECRET_KEY', value: 'sk_test_8TjixrSxF8YYKEF0VCu3NKwklbHAexuBhsyjdmjnAS' },
  { name: 'OPENROUTER_API_KEY', value: '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' },
  { name: 'NEXT_PUBLIC_POSTHOG_KEY', value: 'phx_U0sBXanQWe8km5wFnYFB6P90fPMdKCIzrggeyU5ZQZ8DPkK' },
  { name: 'NEXT_PUBLIC_SENTRY_DSN', value: 'https://<EMAIL>/4509565394419712' }
];

console.log('🚀 Adding essential environment variables to Vercel...\n');

envVars.forEach(({ name, value }) => {
  try {
    console.log(`Adding ${name}...`);
    execSync(`echo "${value}" | vercel env add ${name} production`, { stdio: 'inherit' });
    console.log(`✅ Added ${name}\n`);
  } catch (error) {
    console.log(`❌ Failed to add ${name}: ${error.message}\n`);
  }
});

console.log('🎉 Environment variables setup complete!');
