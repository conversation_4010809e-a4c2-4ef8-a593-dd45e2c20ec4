/**
 * Test Static Asset Serving
 */

const { default: fetch } = require('node-fetch');

const testStaticAssets = async () => {
  try {
    console.log('🧪 Testing Static Asset Serving...');
    
    // Test favicon
    console.log('\n🎨 Testing Favicon...');
    try {
      const faviconResponse = await fetch('http://localhost:3000/favicon.ico');
      console.log(`✅ Favicon: ${faviconResponse.status} ${faviconResponse.statusText}`);
      console.log(`📄 Content-Type: ${faviconResponse.headers.get('content-type')}`);
    } catch (error) {
      console.log('❌ Favicon Error:', error.message);
    }
    
    // Test manifest
    console.log('\n📱 Testing Manifest...');
    try {
      const manifestResponse = await fetch('http://localhost:3000/manifest.json');
      console.log(`✅ Manifest: ${manifestResponse.status} ${manifestResponse.statusText}`);
      console.log(`📄 Content-Type: ${manifestResponse.headers.get('content-type')}`);
    } catch (error) {
      console.log('❌ Manifest Error:', error.message);
    }
    
    // Test robots.txt
    console.log('\n🤖 Testing Robots.txt...');
    try {
      const robotsResponse = await fetch('http://localhost:3000/robots.txt');
      console.log(`✅ Robots.txt: ${robotsResponse.status} ${robotsResponse.statusText}`);
      console.log(`📄 Content-Type: ${robotsResponse.headers.get('content-type')}`);
    } catch (error) {
      console.log('❌ Robots.txt Error:', error.message);
    }
    
    // Test Next.js static assets
    console.log('\n⚡ Testing Next.js Static Assets...');
    try {
      // Test if we can access the main page to see if chunks load
      const pageResponse = await fetch('http://localhost:3000/');
      console.log(`✅ Main Page: ${pageResponse.status} ${pageResponse.statusText}`);
      
      const pageContent = await pageResponse.text();
      
      // Check for chunk references in the HTML
      const chunkMatches = pageContent.match(/_next\/static\/chunks\/[^"]+/g);
      if (chunkMatches) {
        console.log(`📦 Found ${chunkMatches.length} chunk references`);
        
        // Test first few chunks
        for (let i = 0; i < Math.min(3, chunkMatches.length); i++) {
          const chunkPath = chunkMatches[i];
          try {
            const chunkResponse = await fetch(`http://localhost:3000/${chunkPath}`);
            console.log(`✅ Chunk ${i + 1}: ${chunkResponse.status} ${chunkResponse.statusText} - ${chunkPath}`);
          } catch (error) {
            console.log(`❌ Chunk ${i + 1} Error: ${error.message} - ${chunkPath}`);
          }
        }
      } else {
        console.log('📦 No chunk references found in HTML');
      }
      
    } catch (error) {
      console.log('❌ Page Loading Error:', error.message);
    }
    
    // Test CSS files
    console.log('\n🎨 Testing CSS Assets...');
    try {
      const pageResponse = await fetch('http://localhost:3000/');
      const pageContent = await pageResponse.text();
      
      const cssMatches = pageContent.match(/_next\/static\/css\/[^"]+/g);
      if (cssMatches) {
        console.log(`🎨 Found ${cssMatches.length} CSS references`);
        
        for (let i = 0; i < Math.min(2, cssMatches.length); i++) {
          const cssPath = cssMatches[i];
          try {
            const cssResponse = await fetch(`http://localhost:3000/${cssPath}`);
            console.log(`✅ CSS ${i + 1}: ${cssResponse.status} ${cssResponse.statusText} - ${cssPath}`);
            console.log(`📄 Content-Type: ${cssResponse.headers.get('content-type')}`);
          } catch (error) {
            console.log(`❌ CSS ${i + 1} Error: ${error.message} - ${cssPath}`);
          }
        }
      } else {
        console.log('🎨 No CSS references found in HTML');
      }
      
    } catch (error) {
      console.log('❌ CSS Test Error:', error.message);
    }
    
    // Test API endpoints for MIME types
    console.log('\n🔌 Testing API Endpoints...');
    try {
      const apiResponse = await fetch('http://localhost:3000/api/dashboard');
      console.log(`✅ Dashboard API: ${apiResponse.status} ${apiResponse.statusText}`);
      console.log(`📄 Content-Type: ${apiResponse.headers.get('content-type')}`);
    } catch (error) {
      console.log('❌ API Error:', error.message);
    }
    
    console.log('\n✅ Static Asset Testing Complete!');
    
  } catch (error) {
    console.log('❌ Static Asset Test ERROR');
    console.error('Error:', error);
  }
};

// Run the test
testStaticAssets();
