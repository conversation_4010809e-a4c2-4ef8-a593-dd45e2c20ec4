import { devLog } from '@/lib/console-cleaner';
/**
 * Slack Post Processor
 * Background job processor for executing scheduled Slack posts
 */

import { getDuePosts, updateScheduledPost, ScheduledPost } from '@/lib/slack-scheduler';
import { getUserPostTemplates, renderTemplate } from '@/lib/post-templates';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { SentryTracker } from '@/lib/sentry.client';

export interface PostExecutionResult {
  success: boolean;
  postId: string;
  messageTs?: string;
  error?: string;
}

/**
 * Process all due scheduled posts
 */
export async function processDuePosts(): Promise<PostExecutionResult[]> {
  try {
  devLog.log('🔄 Processing due scheduled posts...');
    
    const duePosts = await getDuePosts();
  devLog.log(`📋 Found ${duePosts.length} due posts`);

    if (duePosts.length === 0) {
      return [];
    }

    const results: PostExecutionResult[] = [];

    for (const post of duePosts) {
      try {
        const result = await executeScheduledPost(post);
        results.push(result);

        // Update the post's next execution time and last posted time
        if (result.success) {
          const nextPostAt = calculateNextPostTime(post);
          await updateScheduledPost(post.id, post.user_id, {
            last_posted_at: new Date().toISOString(),
            next_post_at: nextPostAt.toISOString(),
          });
        }

      } catch (error) {
        console.error(`Error executing post ${post.id}:`, error);
        results.push({
          success: false,
          postId: post.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }
  devLog.log(`✅ Processed ${results.length} posts: ${results.filter(r => r.success).length} successful, ${results.filter(r => !r.success).length} failed`);
    
    return results;

  } catch (error) {
    console.error('Error processing due posts:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return [];
  }
}

/**
 * Execute a single scheduled post
 */
async function executeScheduledPost(post: ScheduledPost): Promise<PostExecutionResult> {
  try {
  devLog.log(`📤 Executing scheduled post ${post.id} for channel ${post.slack_channel_name}`);

    // Get the template
    const supabase = await createSupabaseServerClient();
    const { data: template } = await supabase
      .from('post_templates')
      .select('*')
      .eq('id', post.template_id)
      .single();

    if (!template) {
      throw new Error(`Template ${post.template_id} not found`);
    }

    // Get recent summaries for the user/organization
    const summaries = await getRecentSummaries(
      post.user_id,
      post.organization_id,
      post.schedule_type
    );

    // Prepare template variables
    const variables = await prepareTemplateVariables(
      post,
      summaries,
      post.user_id,
      post.organization_id
    );

    // Render the template
    const messageContent = await renderTemplate(template.template_content, variables);

    // Get Slack integration
    const { data: integration } = await supabase
      .from('integrations')
      .select('access_token')
      .eq('user_id', post.user_id)
      .eq('organization_id', post.organization_id)
      .eq('integration_type', 'slack')
      .eq('is_active', true)
      .single();

    if (!integration?.access_token) {
      throw new Error('Slack integration not found or inactive');
    }

    // Post to Slack
    const response = await fetch('https://slack.com/api/chat.postMessage', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${integration.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channel: post.slack_channel_id,
        text: messageContent,
        unfurl_links: false,
        unfurl_media: false,
      }),
    });

    const data = await response.json();

    if (!data.ok) {
      throw new Error(`Slack API error: ${data.error}`);
    }
  devLog.log(`✅ Posted to Slack channel ${post.slack_channel_name}: ${data.ts}`);

    return {
      success: true,
      postId: post.id,
      messageTs: data.ts,
    };

  } catch (error) {
    console.error(`❌ Failed to execute post ${post.id}:`, error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      postId: post.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get recent summaries based on schedule type
 */
async function getRecentSummaries(
  userId: string,
  organizationId: string,
  scheduleType: 'daily' | 'weekly' | 'monthly'
): Promise<any[]> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Calculate date range based on schedule type
    const now = new Date();
    let startDate = new Date();

    switch (scheduleType) {
      case 'daily':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'weekly':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'monthly':
        startDate.setMonth(now.getMonth() - 1);
        break;
    }

    const { data: summaries } = await supabase
      .from('summaries')
      .select('*')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false })
      .limit(10);

    return summaries || [];

  } catch (error) {
    console.error('Error fetching recent summaries:', error);
    return [];
  }
}

/**
 * Prepare template variables with actual values
 */
async function prepareTemplateVariables(
  post: any,
  summaries: any[],
  userId: string,
  organizationId: string
): Promise<Record<string, string>> {
  const now = new Date();
  
  // Get user info
  const supabase = await createSupabaseServerClient();
  const { data: profile } = await supabase
    .from('profiles')
    .select('full_name')
    .eq('id', userId)
    .single();

  const { data: organization } = await supabase
    .from('organizations')
    .select('name')
    .eq('id', organizationId)
    .single();

  // Prepare summary list
  const summaryList = summaries.length > 0
    ? summaries.map((s, index) => `${index + 1}. ${s.title || 'Untitled Summary'}`).join('\n')
    : 'No summaries in this period';

  const topSummary = summaries.length > 0
    ? summaries[0].title || 'Untitled Summary'
    : 'No summaries available';

  return {
    date: now.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }),
    time: now.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    }),
    summary_count: summaries.length.toString(),
    top_summary: topSummary,
    summary_list: summaryList,
    user_name: profile?.full_name || 'Unknown User',
    organization_name: organization?.name || 'Unknown Organization',
    period: post.schedule_type,
  };
}

/**
 * Calculate next post time for a scheduled post
 */
function calculateNextPostTime(post: any): Date {
  const now = new Date();
  const [hours, minutes] = post.schedule_time.split(':').map(Number);
  
  let nextPost = new Date(now);
  nextPost.setHours(hours, minutes, 0, 0);

  switch (post.schedule_type) {
    case 'daily':
      nextPost.setDate(nextPost.getDate() + 1);
      break;

    case 'weekly':
      nextPost.setDate(nextPost.getDate() + 7);
      break;

    case 'monthly':
      nextPost.setMonth(nextPost.getMonth() + 1);
      if (post.schedule_day) {
        nextPost.setDate(post.schedule_day);
      }
      break;
  }

  return nextPost;
}

/**
 * Health check for the post processor
 */
export async function healthCheck(): Promise<{ healthy: boolean; message: string }> {
  try {
    // Check database connectivity
    const supabase = await createSupabaseServerClient();
    const { data } = await supabase
      .from('scheduled_posts')
      .select('id')
      .limit(1);

    return {
      healthy: true,
      message: 'Post processor is healthy',
    };

  } catch (error) {
    console.error('Post processor health check failed:', error);
    return {
      healthy: false,
      message: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
