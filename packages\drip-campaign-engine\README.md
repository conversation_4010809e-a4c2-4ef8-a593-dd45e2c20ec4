# 📧 Drip Campaign Engine

## Overview
Powerful, event-driven drip campaign engine with visual campaign builder, behavioral triggers, and A/B testing capabilities. Perfect for SaaS user retention and growth.

## Features
- ✅ **Visual Campaign Builder**: Drag-and-drop campaign creation
- ✅ **Behavioral Triggers**: Event-based campaign automation
- ✅ **A/B Testing**: Built-in split testing and optimization
- ✅ **Personalization**: Dynamic content based on user data
- ✅ **Multi-Channel**: Email, SMS, in-app, push notifications
- ✅ **Analytics**: Comprehensive campaign performance tracking
- ✅ **Segmentation**: Advanced user targeting and filtering
- ✅ **Template System**: Reusable email and content templates

## Installation

```bash
npm install @your-org/drip-campaign-engine
```

## Quick Start

### **1. Initialize Engine**
```typescript
import { DripCampaignEngine } from '@your-org/drip-campaign-engine';

const engine = new DripCampaignEngine({
  // Event tracking
  eventTracker: new PostHogEventTracker({
    apiKey: process.env.POSTHOG_API_KEY,
    host: process.env.POSTHOG_HOST
  }),
  
  // Email provider
  emailProvider: new ResendEmailProvider({
    apiKey: process.env.RESEND_API_KEY
  }),
  
  // Database storage
  storage: new DatabaseStorage({
    connectionString: process.env.DATABASE_URL
  }),
  
  // Template engine
  templateEngine: new HandlebarsTemplateEngine(),
  
  // A/B testing
  abTesting: new ABTestingEngine({
    defaultSplitRatio: 0.5
  })
});
```

### **2. Create Campaign**
```typescript
// Define campaign structure
const campaign = await engine.createCampaign({
  name: 'Welcome Series',
  description: 'Onboard new users with 5-email sequence',
  
  // Trigger configuration
  trigger: {
    type: 'event',
    event: 'user.signed_up',
    conditions: [
      {
        property: 'plan',
        operator: 'equals',
        value: 'free'
      }
    ]
  },
  
  // Campaign steps
  steps: [
    {
      name: 'Welcome Email',
      type: 'email',
      delay: { hours: 0 }, // Immediate
      template: 'welcome-email',
      subject: 'Welcome to {{companyName}}!',
      abTest: {
        enabled: true,
        variants: [
          { name: 'A', subject: 'Welcome to {{companyName}}!' },
          { name: 'B', subject: 'Get started with {{companyName}}' }
        ]
      }
    },
    {
      name: 'Feature Introduction',
      type: 'email',
      delay: { days: 1 },
      template: 'feature-intro',
      conditions: [
        {
          property: 'hasCompletedOnboarding',
          operator: 'equals',
          value: false
        }
      ]
    },
    {
      name: 'Success Stories',
      type: 'email',
      delay: { days: 3 },
      template: 'success-stories'
    },
    {
      name: 'Upgrade Prompt',
      type: 'email',
      delay: { days: 7 },
      template: 'upgrade-prompt',
      conditions: [
        {
          property: 'plan',
          operator: 'equals',
          value: 'free'
        }
      ]
    }
  ],
  
  // Target segments
  segments: ['new_users', 'free_plan'],
  
  // Campaign settings
  settings: {
    enabled: true,
    timezone: 'UTC',
    sendTime: { hour: 9, minute: 0 }, // 9 AM
    respectUnsubscribes: true,
    maxEmailsPerDay: 1
  }
});
```

### **3. Track Events**
```typescript
// Track user events that trigger campaigns
await engine.trackEvent({
  userId: 'user-123',
  event: 'user.signed_up',
  properties: {
    email: '<EMAIL>',
    plan: 'free',
    source: 'organic',
    companyName: 'Acme Corp'
  },
  timestamp: new Date().toISOString()
});

// Track engagement events
await engine.trackEvent({
  userId: 'user-123',
  event: 'email.opened',
  properties: {
    campaignId: 'campaign-456',
    stepId: 'step-789',
    variant: 'A'
  }
});
```

### **4. Visual Campaign Builder**
```typescript
// Campaign builder component (React)
import { CampaignBuilder } from '@your-org/drip-campaign-engine/react';

function CampaignBuilderPage() {
  return (
    <CampaignBuilder
      engine={engine}
      onSave={(campaign) => {
        console.log('Campaign saved:', campaign);
      }}
      templates={[
        { id: 'welcome', name: 'Welcome Email' },
        { id: 'feature-intro', name: 'Feature Introduction' },
        { id: 'upgrade', name: 'Upgrade Prompt' }
      ]}
      segments={[
        { id: 'new_users', name: 'New Users' },
        { id: 'free_plan', name: 'Free Plan Users' },
        { id: 'power_users', name: 'Power Users' }
      ]}
    />
  );
}
```

## Advanced Features

### **Behavioral Triggers**
```typescript
// Complex trigger conditions
const behaviorCampaign = await engine.createCampaign({
  name: 'Re-engagement Campaign',
  trigger: {
    type: 'behavior',
    conditions: [
      {
        event: 'user.last_active',
        operator: 'older_than',
        value: { days: 7 }
      },
      {
        property: 'totalLogins',
        operator: 'greater_than',
        value: 5
      }
    ],
    frequency: 'once_per_user'
  },
  
  steps: [
    {
      name: 'We Miss You',
      type: 'email',
      delay: { hours: 0 },
      template: 're-engagement',
      personalization: {
        lastFeatureUsed: '{{user.lastFeatureUsed}}',
        daysSinceActive: '{{user.daysSinceActive}}'
      }
    }
  ]
});
```

### **A/B Testing**
```typescript
// Advanced A/B testing
const abTestCampaign = await engine.createCampaign({
  name: 'Conversion Optimization',
  
  steps: [
    {
      name: 'Upgrade Email',
      type: 'email',
      abTest: {
        enabled: true,
        splitRatio: 0.5,
        variants: [
          {
            name: 'Price Focus',
            template: 'upgrade-price-focus',
            subject: 'Save 50% on Pro Plan',
            cta: 'Upgrade Now'
          },
          {
            name: 'Feature Focus',
            template: 'upgrade-feature-focus',
            subject: 'Unlock Advanced Features',
            cta: 'See Features'
          }
        ],
        winnerCriteria: {
          metric: 'conversion_rate',
          minimumSampleSize: 100,
          confidenceLevel: 0.95
        }
      }
    }
  ]
});

// Get A/B test results
const testResults = await engine.getABTestResults('campaign-id', 'step-id');
console.log('Winner:', testResults.winner);
console.log('Confidence:', testResults.confidence);
```

### **Segmentation**
```typescript
// Create user segments
const powerUsersSegment = await engine.createSegment({
  name: 'Power Users',
  description: 'Users with high engagement',
  conditions: [
    {
      property: 'totalLogins',
      operator: 'greater_than',
      value: 20
    },
    {
      property: 'lastActive',
      operator: 'within_days',
      value: 7
    },
    {
      event: 'feature.used',
      operator: 'count_greater_than',
      value: 10,
      timeframe: { days: 30 }
    }
  ]
});

// Dynamic segmentation
const dynamicSegment = await engine.createSegment({
  name: 'At Risk Users',
  conditions: [
    {
      property: 'engagementScore',
      operator: 'less_than',
      value: 30
    },
    {
      property: 'plan',
      operator: 'equals',
      value: 'paid'
    }
  ],
  refreshInterval: { hours: 6 } // Update every 6 hours
});
```

### **Multi-Channel Campaigns**
```typescript
const multiChannelCampaign = await engine.createCampaign({
  name: 'Product Launch',
  
  steps: [
    {
      name: 'Email Announcement',
      type: 'email',
      delay: { hours: 0 },
      template: 'product-launch-email'
    },
    {
      name: 'In-App Notification',
      type: 'in_app',
      delay: { hours: 2 },
      template: 'product-launch-notification',
      conditions: [
        {
          event: 'email.opened',
          operator: 'not_occurred',
          timeframe: { hours: 2 }
        }
      ]
    },
    {
      name: 'Push Notification',
      type: 'push',
      delay: { days: 1 },
      template: 'product-launch-push',
      conditions: [
        {
          event: 'app.opened',
          operator: 'not_occurred',
          timeframe: { hours: 24 }
        }
      ]
    }
  ]
});
```

## Analytics & Reporting

### **Campaign Performance**
```typescript
// Get campaign analytics
const analytics = await engine.getCampaignAnalytics('campaign-id', {
  dateRange: {
    start: '2024-01-01',
    end: '2024-01-31'
  }
});

console.log('Campaign Performance:', {
  totalEnrollments: analytics.totalEnrollments,
  completionRate: analytics.completionRate,
  conversionRate: analytics.conversionRate,
  unsubscribeRate: analytics.unsubscribeRate,
  revenueGenerated: analytics.revenueGenerated
});

// Step-by-step performance
analytics.stepPerformance.forEach(step => {
  console.log(`Step ${step.name}:`, {
    sent: step.sent,
    opened: step.opened,
    clicked: step.clicked,
    converted: step.converted
  });
});
```

### **Real-time Monitoring**
```typescript
// Monitor campaign performance in real-time
engine.on('campaign.step_sent', (event) => {
  console.log('Email sent:', event);
});

engine.on('campaign.step_opened', (event) => {
  console.log('Email opened:', event);
});

engine.on('campaign.conversion', (event) => {
  console.log('Conversion achieved:', event);
});

// Set up alerts
engine.addAlert({
  name: 'Low Open Rate Alert',
  condition: {
    metric: 'open_rate',
    operator: 'less_than',
    value: 0.15,
    timeframe: { hours: 24 }
  },
  action: {
    type: 'webhook',
    url: 'https://your-app.com/alerts/low-open-rate'
  }
});
```

## Template System

### **Email Templates**
```typescript
// Register email template
await engine.registerTemplate({
  id: 'welcome-email',
  name: 'Welcome Email',
  type: 'email',
  subject: 'Welcome to {{companyName}}, {{firstName}}!',
  html: `
    <h1>Welcome {{firstName}}!</h1>
    <p>Thanks for joining {{companyName}}. Here's what you can do next:</p>
    <ul>
      <li>Complete your profile</li>
      <li>Explore our features</li>
      <li>Join our community</li>
    </ul>
    <a href="{{dashboardUrl}}" class="cta-button">Get Started</a>
  `,
  variables: [
    { name: 'firstName', type: 'string', required: true },
    { name: 'companyName', type: 'string', required: true },
    { name: 'dashboardUrl', type: 'url', required: true }
  ]
});

// Template with conditional content
await engine.registerTemplate({
  id: 'personalized-offer',
  name: 'Personalized Offer',
  type: 'email',
  html: `
    <h1>Special offer for you, {{firstName}}!</h1>
    {{#if isPremiumUser}}
      <p>As a premium user, enjoy 20% off your next upgrade.</p>
    {{else}}
      <p>Upgrade to premium and save 30%!</p>
    {{/if}}
    
    {{#each recommendedFeatures}}
      <div class="feature">
        <h3>{{name}}</h3>
        <p>{{description}}</p>
      </div>
    {{/each}}
  `
});
```

## Integration Examples

### **PostHog Integration**
```typescript
import { PostHogEventTracker } from '@your-org/drip-campaign-engine/integrations';

const eventTracker = new PostHogEventTracker({
  apiKey: process.env.POSTHOG_API_KEY,
  host: process.env.POSTHOG_HOST,
  
  // Map PostHog events to campaign triggers
  eventMapping: {
    '$identify': 'user.identified',
    '$pageview': 'page.viewed',
    'subscription_upgraded': 'user.upgraded',
    'feature_used': 'feature.used'
  }
});
```

### **Stripe Integration**
```typescript
import { StripeEventTracker } from '@your-org/drip-campaign-engine/integrations';

const stripeTracker = new StripeEventTracker({
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
  
  // Map Stripe events to campaign triggers
  eventMapping: {
    'customer.subscription.created': 'subscription.created',
    'customer.subscription.updated': 'subscription.updated',
    'invoice.payment_succeeded': 'payment.succeeded',
    'invoice.payment_failed': 'payment.failed'
  }
});
```

## License
MIT

## Contributing
See [CONTRIBUTING.md](./CONTRIBUTING.md) for guidelines.
