/**
 * Comprehensive Error Handling Utilities
 * Provides centralized error handling, logging, and recovery mechanisms
 */

import { toast } from 'sonner';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  url?: string;
  userAgent?: string;
  timestamp?: string;
  metadata?: Record<string, any>;
}

export interface ErrorReport {
  id: string;
  type: ErrorType;
  message: string;
  stack?: string;
  context: ErrorContext;
  severity: ErrorSeverity;
  recoverable: boolean;
}

export enum ErrorType {
  NETWORK = 'network',
  CHUNK_LOADING = 'chunk_loading',
  RUNTIME = 'runtime',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  API = 'api',
  DATABASE = 'database',
  PAYMENT = 'payment',
  UPLOAD = 'upload',
  EXPORT = 'export',
  AI_PROCESSING = 'ai_processing',
  UNKNOWN = 'unknown'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorQueue: ErrorReport[] = [];
  private maxQueueSize = 100;

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle and process errors with comprehensive logging and recovery
   */
  async handleError(
    error: Error | unknown,
    context: ErrorContext = {},
    options: {
      showToast?: boolean;
      logToConsole?: boolean;
      logToAnalytics?: boolean;
      logToSentry?: boolean;
      autoRecover?: boolean;
    } = {}
  ): Promise<ErrorReport> {
    const {
      showToast = true,
      logToConsole = true,
      logToAnalytics = true,
      logToSentry = true,
      autoRecover = false
    } = options;

    // Create error report
    const errorReport = this.createErrorReport(error, context);

    // Add to queue
    this.addToQueue(errorReport);

    // Log to console
    if (logToConsole) {
      this.logToConsole(errorReport);
    }

    // Log to analytics
    if (logToAnalytics) {
      await this.logToAnalytics(errorReport);
    }

    // Log to Sentry
    if (logToSentry) {
      await this.logToSentry(errorReport);
    }

    // Show user notification
    if (showToast) {
      this.showUserNotification(errorReport);
    }

    // Attempt auto-recovery
    if (autoRecover && errorReport.recoverable) {
      await this.attemptRecovery(errorReport);
    }

    return errorReport;
  }

  /**
   * Create a structured error report
   */
  private createErrorReport(error: Error | unknown, context: ErrorContext): ErrorReport {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    const type = this.determineErrorType(errorObj);
    const severity = this.determineSeverity(type, errorObj);
    const recoverable = this.isRecoverable(type);

    return {
      id: this.generateErrorId(),
      type,
      message: errorObj.message || 'Unknown error occurred',
      stack: errorObj.stack,
      context: {
        ...context,
        timestamp: new Date().toISOString(),
        url: typeof window !== 'undefined' ? window.location.href : undefined,
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined
      },
      severity,
      recoverable
    };
  }

  /**
   * Determine error type from error object
   */
  private determineErrorType(error: Error): ErrorType {
    const message = error.message?.toLowerCase() || '';
    const stack = error.stack?.toLowerCase() || '';

    if (message.includes('chunk') || message.includes('loading')) {
      return ErrorType.CHUNK_LOADING;
    }
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return ErrorType.NETWORK;
    }
    if (message.includes('unauthorized') || message.includes('authentication')) {
      return ErrorType.AUTHENTICATION;
    }
    if (message.includes('forbidden') || message.includes('permission')) {
      return ErrorType.AUTHORIZATION;
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return ErrorType.VALIDATION;
    }
    if (message.includes('payment') || message.includes('billing')) {
      return ErrorType.PAYMENT;
    }
    if (message.includes('upload') || message.includes('file')) {
      return ErrorType.UPLOAD;
    }
    if (message.includes('export') || message.includes('download')) {
      return ErrorType.EXPORT;
    }
    if (message.includes('ai') || message.includes('summarization')) {
      return ErrorType.AI_PROCESSING;
    }
    if (message.includes('database') || message.includes('sql')) {
      return ErrorType.DATABASE;
    }
    if (stack.includes('api') || message.includes('api')) {
      return ErrorType.API;
    }
    if (message.includes('reference') || message.includes('undefined')) {
      return ErrorType.RUNTIME;
    }

    return ErrorType.UNKNOWN;
  }

  /**
   * Determine error severity
   */
  private determineSeverity(type: ErrorType, error: Error): ErrorSeverity {
    switch (type) {
      case ErrorType.AUTHENTICATION:
      case ErrorType.AUTHORIZATION:
      case ErrorType.PAYMENT:
      case ErrorType.DATABASE:
        return ErrorSeverity.HIGH;
      
      case ErrorType.CHUNK_LOADING:
      case ErrorType.NETWORK:
        return ErrorSeverity.MEDIUM;
      
      case ErrorType.VALIDATION:
      case ErrorType.UPLOAD:
      case ErrorType.EXPORT:
        return ErrorSeverity.MEDIUM;
      
      case ErrorType.AI_PROCESSING:
      case ErrorType.API:
        return ErrorSeverity.MEDIUM;
      
      case ErrorType.RUNTIME:
        return error.message?.includes('critical') ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM;
      
      default:
        return ErrorSeverity.LOW;
    }
  }

  /**
   * Check if error is recoverable
   */
  private isRecoverable(type: ErrorType): boolean {
    return [
      ErrorType.NETWORK,
      ErrorType.CHUNK_LOADING,
      ErrorType.API,
      ErrorType.UPLOAD,
      ErrorType.EXPORT,
      ErrorType.AI_PROCESSING
    ].includes(type);
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Add error to queue with size management
   */
  private addToQueue(errorReport: ErrorReport): void {
    this.errorQueue.push(errorReport);
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift(); // Remove oldest error
    }
  }

  /**
   * Log error to console with formatting
   */
  private logToConsole(errorReport: ErrorReport): void {
    const emoji = this.getSeverityEmoji(errorReport.severity);
    console.group(`${emoji} Error Report [${errorReport.type.toUpperCase()}]`);
    console.error('Message:', errorReport.message);
    console.error('Severity:', errorReport.severity);
    console.error('Context:', errorReport.context);
    if (errorReport.stack) {
      console.error('Stack:', errorReport.stack);
    }
    console.groupEnd();
  }

  /**
   * Log error to analytics (PostHog)
   */
  private async logToAnalytics(errorReport: ErrorReport): Promise<void> {
    try {
      if (typeof window !== 'undefined' && (window as any).posthog) {
        (window as any).posthog.capture('error_occurred', {
          error_id: errorReport.id,
          error_type: errorReport.type,
          error_message: errorReport.message,
          error_severity: errorReport.severity,
          error_recoverable: errorReport.recoverable,
          component: errorReport.context.component,
          action: errorReport.context.action,
          url: errorReport.context.url
        });
      }
    } catch (e) {
      console.warn('Failed to log error to PostHog:', e);
    }
  }

  /**
   * Log error to Sentry
   */
  private async logToSentry(errorReport: ErrorReport): Promise<void> {
    try {
      if (typeof window !== 'undefined' && (window as any).Sentry) {
        (window as any).Sentry.withScope((scope: any) => {
          scope.setTag('errorType', errorReport.type);
          scope.setLevel(this.getSentryLevel(errorReport.severity));
          scope.setContext('errorReport', {
            id: errorReport.id,
            severity: errorReport.severity,
            recoverable: errorReport.recoverable,
            context: errorReport.context
          });
          
          const error = new Error(errorReport.message);
          if (errorReport.stack) {
            error.stack = errorReport.stack;
          }
          
          (window as any).Sentry.captureException(error);
        });
      }
    } catch (e) {
      console.warn('Failed to log error to Sentry:', e);
    }
  }

  /**
   * Show user-friendly notification
   */
  private showUserNotification(errorReport: ErrorReport): void {
    const message = this.getUserFriendlyMessage(errorReport);
    
    switch (errorReport.severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        toast.error(message, {
          duration: 8000,
          action: {
            label: 'Report',
            onClick: () => this.reportError(errorReport)
          }
        });
        break;
      
      case ErrorSeverity.MEDIUM:
        toast.warning(message, {
          duration: 5000
        });
        break;
      
      case ErrorSeverity.LOW:
        toast.info(message, {
          duration: 3000
        });
        break;
    }
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage(errorReport: ErrorReport): string {
    switch (errorReport.type) {
      case ErrorType.NETWORK:
        return 'Connection issue. Please check your internet and try again.';
      case ErrorType.CHUNK_LOADING:
        return 'Loading issue detected. Refreshing...';
      case ErrorType.AUTHENTICATION:
        return 'Please sign in to continue.';
      case ErrorType.AUTHORIZATION:
        return 'You don\'t have permission to perform this action.';
      case ErrorType.VALIDATION:
        return 'Please check your input and try again.';
      case ErrorType.PAYMENT:
        return 'Payment processing issue. Please try again or contact support.';
      case ErrorType.UPLOAD:
        return 'File upload failed. Please try again.';
      case ErrorType.EXPORT:
        return 'Export failed. Please try again.';
      case ErrorType.AI_PROCESSING:
        return 'AI processing failed. Please try again.';
      case ErrorType.DATABASE:
        return 'Database error. Please try again later.';
      case ErrorType.API:
        return 'Service temporarily unavailable. Please try again.';
      default:
        return 'Something went wrong. Please try again.';
    }
  }

  /**
   * Attempt automatic error recovery
   */
  private async attemptRecovery(errorReport: ErrorReport): Promise<void> {
    try {
      switch (errorReport.type) {
        case ErrorType.CHUNK_LOADING:
          await this.recoverFromChunkError();
          break;
        case ErrorType.NETWORK:
          await this.recoverFromNetworkError();
          break;
        case ErrorType.API:
          await this.recoverFromApiError();
          break;
      }
    } catch (recoveryError) {
      console.warn('Auto-recovery failed:', recoveryError);
    }
  }

  /**
   * Recovery methods
   */
  private async recoverFromChunkError(): Promise<void> {
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
    }
    localStorage.clear();
    sessionStorage.clear();
    
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  }

  private async recoverFromNetworkError(): Promise<void> {
    // Implement retry logic with exponential backoff
    toast.info('Retrying connection...');
  }

  private async recoverFromApiError(): Promise<void> {
    // Implement API retry logic
    toast.info('Retrying request...');
  }

  /**
   * Report error to support
   */
  private reportError(errorReport: ErrorReport): void {
    const subject = encodeURIComponent(`Error Report: ${errorReport.type} - ${errorReport.id}`);
    const body = encodeURIComponent(`Error Report:\n\n${JSON.stringify(errorReport, null, 2)}`);
    const mailtoUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    
    window.open(mailtoUrl, '_blank');
    
    // Log error report
    if ((window as any).posthog) {
      (window as any).posthog.capture('error_reported', {
        error_id: errorReport.id,
        error_type: errorReport.type
      });
    }
  }

  /**
   * Utility methods
   */
  private getSeverityEmoji(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.CRITICAL: return '🚨';
      case ErrorSeverity.HIGH: return '❌';
      case ErrorSeverity.MEDIUM: return '⚠️';
      case ErrorSeverity.LOW: return 'ℹ️';
      default: return '❓';
    }
  }

  private getSentryLevel(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.CRITICAL: return 'fatal';
      case ErrorSeverity.HIGH: return 'error';
      case ErrorSeverity.MEDIUM: return 'warning';
      case ErrorSeverity.LOW: return 'info';
      default: return 'error';
    }
  }

  /**
   * Get error queue for debugging
   */
  getErrorQueue(): ErrorReport[] {
    return [...this.errorQueue];
  }

  /**
   * Clear error queue
   */
  clearErrorQueue(): void {
    this.errorQueue = [];
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Export convenience functions
export const handleError = (error: Error | unknown, context?: ErrorContext) => 
  errorHandler.handleError(error, context);

export const handleErrorWithRecovery = (error: Error | unknown, context?: ErrorContext) => 
  errorHandler.handleError(error, context, { autoRecover: true });

export const handleSilentError = (error: Error | unknown, context?: ErrorContext) => 
  errorHandler.handleError(error, context, { showToast: false });
