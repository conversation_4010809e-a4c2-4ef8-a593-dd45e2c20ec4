import { NextRequest, NextResponse } from 'next/server';
import { runHealthChecks, isSystemHealthyForSignups } from '@/features/monitoring/health-checks';
import { getWebhookStats } from '@/features/monitoring/webhook-monitor';

/**
 * GET /api/monitoring/health
 * Comprehensive system health check endpoint
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const detailed = searchParams.get('detailed') === 'true';
    const service = searchParams.get('service');

    // If specific service requested
    if (service) {
      const health = await runHealthChecks();
      const serviceHealth = health.checks.find(check => check.name === service);
      
      if (!serviceHealth) {
        return NextResponse.json(
          { error: 'Service not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(serviceHealth);
    }

    // Run comprehensive health checks
    const health = await runHealthChecks();
    
    // Add webhook statistics if detailed
    let webhookStats = null;
    if (detailed) {
      webhookStats = {
        stripe: await getWebhookStats('stripe'),
        slack: await getWebhookStats('slack'),
        overall: await getWebhookStats()
      };
    }

    // Check if system is ready for new signups
    const signupsEnabled = await isSystemHealthyForSignups();

    const response = {
      ...health,
      signupsEnabled,
      ...(detailed && { webhookStats }),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    };

    // Set appropriate status code based on health
    const statusCode = health.overall === 'healthy' ? 200 :
                      health.overall === 'degraded' ? 200 : 503;

    return NextResponse.json(response, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        overall: 'unhealthy',
        error: 'Health check system failure',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 503 }
    );
  }
}

/**
 * POST /api/monitoring/health
 * Manual health check trigger (for testing)
 */
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    switch (action) {
      case 'force-check':
        const health = await runHealthChecks();
        return NextResponse.json({
          message: 'Health check completed',
          ...health
        });

      case 'test-alert':
        // Trigger a test alert
        return NextResponse.json({
          message: 'Test alert triggered',
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Health check action failed:', error);
    
    return NextResponse.json(
      { error: 'Action failed', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
