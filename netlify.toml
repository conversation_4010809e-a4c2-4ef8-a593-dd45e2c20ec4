[build]
  command = "NODE_OPTIONS='--max_old_space_size=8192' npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"
  NODE_OPTIONS = "--max_old_space_size=8192"
  NEXT_TELEMETRY_DISABLED = "1"
  NODE_ENV = "production"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[functions]
  node_bundler = "esbuild"
  external_node_modules = ["pdf-parse", "mammoth", "pdfkit", "exceljs"]

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Cache-Control = "public, max-age=31536000, immutable"

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
