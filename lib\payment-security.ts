/**
 * Payment Security and Validation
 * 
 * Comprehensive security utilities for payment processing,
 * fraud detection, and transaction validation.
 */

import { NextRequest } from 'next/server';
import crypto from 'crypto';
import { logSecurityEvent } from './auth-protection';

// Payment security configuration
export const PAYMENT_SECURITY_CONFIG = {
  // Maximum amounts to prevent fraud
  MAX_AMOUNT_USD: 10000, // $100.00
  MAX_AMOUNT_INR: 750000, // ₹7,500.00
  
  // Rate limiting for payment attempts
  MAX_PAYMENT_ATTEMPTS_PER_HOUR: 5,
  MAX_PAYMENT_ATTEMPTS_PER_DAY: 20,
  
  // Webhook security
  WEBHOOK_TOLERANCE_SECONDS: 300, // 5 minutes
  
  // Fraud detection thresholds
  SUSPICIOUS_VELOCITY_THRESHOLD: 3, // payments in 10 minutes
  SUSPICIOUS_AMOUNT_THRESHOLD: 5000, // $50.00
};

// Payment attempt tracking
const paymentAttempts = new Map<string, { count: number; lastAttempt: number; attempts: number[] }>();

/**
 * Validate payment amount for security
 */
export function validatePaymentAmount(amount: number, currency: string): { valid: boolean; error?: string } {
  // Convert to cents/paise for comparison
  const amountInCents = Math.round(amount * 100);
  
  // Check maximum limits
  if (currency === 'USD' && amountInCents > PAYMENT_SECURITY_CONFIG.MAX_AMOUNT_USD) {
    return {
      valid: false,
      error: `Amount exceeds maximum limit of $${PAYMENT_SECURITY_CONFIG.MAX_AMOUNT_USD / 100}`
    };
  }
  
  if (currency === 'INR' && amountInCents > PAYMENT_SECURITY_CONFIG.MAX_AMOUNT_INR) {
    return {
      valid: false,
      error: `Amount exceeds maximum limit of ₹${PAYMENT_SECURITY_CONFIG.MAX_AMOUNT_INR / 100}`
    };
  }
  
  // Check minimum amount (prevent zero or negative amounts)
  if (amountInCents <= 0) {
    return {
      valid: false,
      error: 'Amount must be greater than zero'
    };
  }
  
  // Check for suspicious amounts (too high for typical subscription)
  if (amountInCents > PAYMENT_SECURITY_CONFIG.SUSPICIOUS_AMOUNT_THRESHOLD * 100) {
    return {
      valid: false,
      error: 'Amount is unusually high for a subscription plan'
    };
  }
  
  return { valid: true };
}

/**
 * Check payment rate limiting
 */
export function checkPaymentRateLimit(userId: string, ip?: string): { 
  allowed: boolean; 
  reason?: string; 
  retryAfter?: number 
} {
  const now = Date.now();
  const key = `${userId}:${ip || 'unknown'}`;
  const userAttempts = paymentAttempts.get(key);
  
  if (!userAttempts) {
    // First attempt
    paymentAttempts.set(key, {
      count: 1,
      lastAttempt: now,
      attempts: [now]
    });
    return { allowed: true };
  }
  
  // Clean old attempts (older than 24 hours)
  const dayAgo = now - (24 * 60 * 60 * 1000);
  userAttempts.attempts = userAttempts.attempts.filter(attempt => attempt > dayAgo);
  
  // Check daily limit
  if (userAttempts.attempts.length >= PAYMENT_SECURITY_CONFIG.MAX_PAYMENT_ATTEMPTS_PER_DAY) {
    return {
      allowed: false,
      reason: 'Daily payment attempt limit exceeded',
      retryAfter: 24 * 60 * 60 // 24 hours
    };
  }
  
  // Check hourly limit
  const hourAgo = now - (60 * 60 * 1000);
  const recentAttempts = userAttempts.attempts.filter(attempt => attempt > hourAgo);
  
  if (recentAttempts.length >= PAYMENT_SECURITY_CONFIG.MAX_PAYMENT_ATTEMPTS_PER_HOUR) {
    return {
      allowed: false,
      reason: 'Hourly payment attempt limit exceeded',
      retryAfter: 60 * 60 // 1 hour
    };
  }
  
  // Check velocity (suspicious rapid attempts)
  const tenMinutesAgo = now - (10 * 60 * 1000);
  const rapidAttempts = userAttempts.attempts.filter(attempt => attempt > tenMinutesAgo);
  
  if (rapidAttempts.length >= PAYMENT_SECURITY_CONFIG.SUSPICIOUS_VELOCITY_THRESHOLD) {
    return {
      allowed: false,
      reason: 'Suspicious payment velocity detected',
      retryAfter: 10 * 60 // 10 minutes
    };
  }
  
  // Update attempts
  userAttempts.attempts.push(now);
  userAttempts.count++;
  userAttempts.lastAttempt = now;
  
  return { allowed: true };
}

/**
 * Validate Stripe webhook signature
 */
export function validateStripeWebhook(
  payload: string | Buffer,
  signature: string,
  secret: string
): { valid: boolean; error?: string } {
  try {
    const elements = signature.split(',');
    const signatureElements: { [key: string]: string } = {};
    
    for (const element of elements) {
      const [key, value] = element.split('=');
      signatureElements[key] = value;
    }
    
    const timestamp = signatureElements.t;
    const signatures = [signatureElements.v1];
    
    if (!timestamp || !signatures[0]) {
      return { valid: false, error: 'Invalid signature format' };
    }
    
    // Check timestamp tolerance
    const timestampNumber = parseInt(timestamp, 10);
    const now = Math.floor(Date.now() / 1000);
    
    if (Math.abs(now - timestampNumber) > PAYMENT_SECURITY_CONFIG.WEBHOOK_TOLERANCE_SECONDS) {
      return { valid: false, error: 'Timestamp outside tolerance' };
    }
    
    // Verify signature
    const payloadString = typeof payload === 'string' ? payload : payload.toString('utf8');
    const signedPayload = `${timestamp}.${payloadString}`;
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(signedPayload, 'utf8')
      .digest('hex');
    
    const isValid = signatures.some(signature => 
      crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      )
    );
    
    return { valid: isValid, error: isValid ? undefined : 'Signature verification failed' };
  } catch (error) {
    return { valid: false, error: 'Signature validation error' };
  }
}

/**
 * Validate Cashfree webhook signature
 */
export function validateCashfreeWebhook(
  payload: string,
  signature: string,
  secret: string
): { valid: boolean; error?: string } {
  try {
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload, 'utf8')
      .digest('base64');
    
    const isValid = crypto.timingSafeEqual(
      Buffer.from(signature, 'base64'),
      Buffer.from(expectedSignature, 'base64')
    );
    
    return { valid: isValid, error: isValid ? undefined : 'Signature verification failed' };
  } catch (error) {
    return { valid: false, error: 'Signature validation error' };
  }
}

/**
 * Sanitize payment metadata
 */
export function sanitizePaymentMetadata(metadata: Record<string, any>): Record<string, string> {
  const sanitized: Record<string, string> = {};
  
  for (const [key, value] of Object.entries(metadata)) {
    // Only allow alphanumeric keys
    const sanitizedKey = key.replace(/[^a-zA-Z0-9_]/g, '').substring(0, 50);
    
    if (sanitizedKey && value != null) {
      // Convert value to string and sanitize
      const sanitizedValue = String(value)
        .replace(/[<>]/g, '') // Remove HTML tags
        .substring(0, 500); // Limit length
      
      sanitized[sanitizedKey] = sanitizedValue;
    }
  }
  
  return sanitized;
}

/**
 * Detect suspicious payment patterns
 */
export function detectSuspiciousPayment(
  userId: string,
  amount: number,
  currency: string,
  metadata: Record<string, any>
): { suspicious: boolean; reasons: string[] } {
  const reasons: string[] = [];
  
  // Check amount
  const amountValidation = validatePaymentAmount(amount, currency);
  if (!amountValidation.valid) {
    reasons.push(`Invalid amount: ${amountValidation.error}`);
  }
  
  // Check for unusual metadata patterns
  if (Object.keys(metadata).length > 20) {
    reasons.push('Excessive metadata fields');
  }
  
  // Check for suspicious metadata values
  const suspiciousPatterns = [
    /script/i,
    /javascript/i,
    /eval/i,
    /alert/i,
    /<[^>]*>/,
    /\bon\w+\s*=/i
  ];
  
  for (const [key, value] of Object.entries(metadata)) {
    const stringValue = String(value);
    if (suspiciousPatterns.some(pattern => pattern.test(stringValue))) {
      reasons.push(`Suspicious metadata value in ${key}`);
    }
  }
  
  return {
    suspicious: reasons.length > 0,
    reasons
  };
}

/**
 * Log payment security event
 */
export function logPaymentSecurityEvent(
  userId: string,
  event: string,
  details: any,
  req?: NextRequest
) {
  logSecurityEvent({
    userId,
    action: `PAYMENT_${event}`,
    resource: 'payment_system',
    details,
    ip: req?.headers.get('x-forwarded-for') || req?.headers.get('x-real-ip') || undefined,
    userAgent: req?.headers.get('user-agent') || undefined
  });
}

/**
 * Generate secure payment reference
 */
export function generatePaymentReference(userId: string, planId: string): string {
  const timestamp = Date.now();
  const random = crypto.randomBytes(8).toString('hex');
  const hash = crypto
    .createHash('sha256')
    .update(`${userId}:${planId}:${timestamp}:${random}`)
    .digest('hex')
    .substring(0, 8);
  
  return `pay_${timestamp}_${hash}`;
}

/**
 * Validate payment plan configuration
 */
export function validatePaymentPlan(planId: string): { 
  valid: boolean; 
  plan?: { id: string; name: string; amount: number; currency: string }; 
  error?: string 
} {
  const validPlans = {
    'free': { id: 'free', name: 'Free', amount: 0, currency: 'USD' },
    'pro': { id: 'pro', name: 'Pro', amount: 29, currency: 'USD' },
    'enterprise': { id: 'enterprise', name: 'Enterprise', amount: 99, currency: 'USD' }
  };
  
  const plan = validPlans[planId.toLowerCase() as keyof typeof validPlans];
  
  if (!plan) {
    return { valid: false, error: 'Invalid plan ID' };
  }
  
  return { valid: true, plan };
}
