'use client';

/**
 * LIVE SAAS AUTH GUARD WITH CLERK AUTHENTICATION
 *
 * This component enforces authentication for protected routes using Clerk.
 * Redirects unauthenticated users to sign-in page.
 */

import React from 'react';
import { useAuth, SignedIn, SignedOut, RedirectToSignIn } from '@clerk/nextjs';
import { useLiveAuth } from '@/lib/clerk-auth';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAuth?: boolean;
}

/**
 * Live SaaS Auth Guard - enforces authentication for protected routes
 */
export default function AuthGuard({
  children,
  fallback,
  requireAuth = true
}: AuthGuardProps) {
  const { isLoading, isSignedIn } = useAuth();
  const { user } = useLiveAuth();

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Checking authentication...</p>
          </div>
        </div>
      )
    );
  }

  // If authentication is not required, always show children
  if (!requireAuth) {
    return <>{children}</>;
  }

  // For protected routes, use Clerk's built-in components
  return (
    <>
      <SignedIn>
        {children}
      </SignedIn>
      <SignedOut>
        <RedirectToSignIn />
      </SignedOut>
    </>
  );
}

/**
 * Alternative export for compatibility
 */
export { AuthGuard };
