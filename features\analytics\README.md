# 📊 Analytics & Insights Dashboard

## Overview
Comprehensive analytics system providing detailed insights into usage patterns, team performance, and business metrics for Slack Summary Scribe.

## Features Implemented

### 1. Usage Analytics
- **Summary Generation**: Track summaries created, word counts, AI model usage
- **User Activity**: Monitor active users, session duration, feature adoption
- **Workspace Analytics**: Slack workspace usage, channel activity, message volume
- **Export Analytics**: Track export formats, frequency, and popular content

### 2. Business Metrics
- **Plan Comparison**: Usage patterns across Free, Pro, and Enterprise plans
- **Conversion Tracking**: Trial-to-paid conversion rates and upgrade patterns
- **Revenue Analytics**: MRR, churn rate, customer lifetime value
- **Feature Adoption**: Track which features drive engagement and retention

### 3. Performance Insights
- **Response Times**: API performance, AI processing times, user experience metrics
- **Error Tracking**: Error rates, failure patterns, system health trends
- **Capacity Planning**: Resource usage, scaling indicators, bottleneck identification

### 4. Interactive Dashboards
- **Real-time Charts**: Live updating charts with Recharts integration
- **Customizable Views**: Filter by date range, team, plan, or feature
- **Export Capabilities**: Download reports as PDF, CSV, or images
- **Drill-down Analysis**: Click through from high-level metrics to detailed views

## File Structure
```
/features/analytics/
├── README.md                    # This file
├── types.ts                    # Analytics data types and interfaces
├── data-collection.ts          # Event tracking and data collection
├── metrics-calculation.ts      # Business metrics and KPI calculations
├── chart-data.ts              # Chart data preparation and formatting
├── export-utils.ts            # Report export functionality
├── real-time.ts               # Real-time analytics and live updates
└── components/
    ├── AnalyticsDashboard.tsx  # Main analytics dashboard
    ├── UsageCharts.tsx         # Usage analytics charts
    ├── BusinessMetrics.tsx     # Business KPI displays
    ├── PerformanceCharts.tsx   # Performance monitoring charts
    ├── ExportDialog.tsx        # Report export interface
    ├── DateRangePicker.tsx     # Date range selection
    ├── MetricCard.tsx          # Individual metric display card
    └── ChartContainer.tsx      # Reusable chart wrapper
```

## Database Schema
```sql
-- Analytics events table
CREATE TABLE analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id),
  user_id UUID REFERENCES auth.users(id),
  event_type TEXT NOT NULL,
  event_name TEXT NOT NULL,
  properties JSONB DEFAULT '{}',
  session_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage metrics table
CREATE TABLE usage_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id),
  metric_type TEXT NOT NULL,
  metric_name TEXT NOT NULL,
  value NUMERIC NOT NULL,
  dimensions JSONB DEFAULT '{}',
  period_start TIMESTAMP WITH TIME ZONE NOT NULL,
  period_end TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Business metrics table
CREATE TABLE business_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name TEXT NOT NULL,
  value NUMERIC NOT NULL,
  metadata JSONB DEFAULT '{}',
  period_start TIMESTAMP WITH TIME ZONE NOT NULL,
  period_end TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Environment Variables
```bash
# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=90
REAL_TIME_ANALYTICS=true
EXPORT_MAX_RECORDS=10000
CHART_CACHE_TTL=300
```

## Usage Examples

### Track Custom Event
```typescript
import { trackEvent } from '@/features/analytics/data-collection';

await trackEvent({
  organizationId,
  userId,
  eventType: 'feature_usage',
  eventName: 'summary_created',
  properties: {
    aiModel: 'gpt-4o-mini',
    wordCount: 1250,
    processingTime: 3.2
  }
});
```

### Get Usage Metrics
```typescript
import { getUsageMetrics } from '@/features/analytics/metrics-calculation';

const metrics = await getUsageMetrics({
  organizationId,
  dateRange: { start: '2024-01-01', end: '2024-01-31' },
  metrics: ['summaries_created', 'active_users', 'api_calls']
});
```

### Generate Chart Data
```typescript
import { generateChartData } from '@/features/analytics/chart-data';

const chartData = await generateChartData({
  type: 'line',
  metric: 'summaries_created',
  groupBy: 'day',
  dateRange: { start: '2024-01-01', end: '2024-01-31' }
});
```

## API Endpoints
- `GET /api/analytics/overview` - High-level dashboard metrics
- `GET /api/analytics/usage` - Detailed usage analytics
- `GET /api/analytics/business` - Business metrics and KPIs
- `GET /api/analytics/performance` - Performance monitoring data
- `POST /api/analytics/events` - Track custom events
- `GET /api/analytics/export` - Export analytics reports
- `GET /api/analytics/real-time` - Real-time metrics stream

## Chart Types Supported

### Line Charts
- Time series data (daily/weekly/monthly trends)
- Multiple metrics comparison
- Trend analysis with forecasting

### Bar Charts
- Category comparisons
- Plan usage comparison
- Feature adoption rates

### Pie Charts
- Distribution analysis
- Plan breakdown
- Export format preferences

### Area Charts
- Cumulative metrics
- Stacked comparisons
- Growth visualization

### Heatmaps
- Activity patterns
- Usage intensity
- Time-based analysis

## Key Metrics Tracked

### Usage Metrics
- Summaries created per day/week/month
- Active users and sessions
- Feature usage frequency
- Export activity
- API calls and rate limits

### Business Metrics
- Monthly Recurring Revenue (MRR)
- Customer Acquisition Cost (CAC)
- Customer Lifetime Value (CLV)
- Churn rate and retention
- Trial conversion rates

### Performance Metrics
- API response times
- AI processing duration
- Error rates and types
- System uptime
- Resource utilization

## Best Practices
1. **Privacy First**: Anonymize sensitive data, respect user privacy
2. **Performance**: Use efficient queries, implement caching
3. **Real-time**: Balance real-time updates with system performance
4. **Actionable Insights**: Focus on metrics that drive business decisions
5. **Data Quality**: Validate data, handle edge cases, ensure accuracy
