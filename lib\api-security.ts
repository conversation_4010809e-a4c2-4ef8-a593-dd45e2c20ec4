/**
 * API Security Utilities
 * 
 * Comprehensive security utilities for API routes including
 * authentication, authorization, rate limiting, and audit logging.
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { 
  getCurrentAuthenticatedUser, 
  withAuth, 
  withRateLimit, 
  UserRole, 
  SubscriptionTier,
  logSecurityEvent,
  AuthResult
} from './auth-protection';

// Security configuration
export const SECURITY_CONFIG = {
  // Rate limiting
  DEFAULT_RATE_LIMIT: 100, // requests per minute
  UPLOAD_RATE_LIMIT: 10,   // file uploads per minute
  AI_RATE_LIMIT: 20,       // AI requests per minute
  EXPORT_RATE_LIMIT: 30,   // exports per minute
  
  // File upload limits
  MAX_FILE_SIZE: 20 * 1024 * 1024, // 20MB
  ALLOWED_FILE_TYPES: [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
    'text/plain'
  ],
  
  // Security headers
  SECURITY_HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
  }
};

/**
 * Apply security headers to response
 */
export function applySecurityHeaders(response: NextResponse): NextResponse {
  Object.entries(SECURITY_CONFIG.SECURITY_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  return response;
}

/**
 * Validate request origin and CSRF protection
 */
export function validateRequestOrigin(req: NextRequest): boolean {
  const origin = req.headers.get('origin');
  const referer = req.headers.get('referer');
  const allowedOrigins = [
    process.env.NEXT_PUBLIC_SITE_URL,
    'https://slack-summary-scribe.vercel.app',
    'http://localhost:3000',
    'http://localhost:3001'
  ].filter(Boolean);

  // For same-origin requests
  if (!origin && !referer) {
    return true;
  }

  // Check origin
  if (origin && allowedOrigins.some(allowed => origin.startsWith(allowed!))) {
    return true;
  }

  // Check referer as fallback
  if (referer && allowedOrigins.some(allowed => referer.startsWith(allowed!))) {
    return true;
  }

  return false;
}

/**
 * Validate file upload security
 */
export function validateFileUpload(file: File): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > SECURITY_CONFIG.MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `File size exceeds ${SECURITY_CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB limit`
    };
  }

  // Check file type
  if (!SECURITY_CONFIG.ALLOWED_FILE_TYPES.includes(file.type)) {
    return {
      valid: false,
      error: `File type ${file.type} is not allowed`
    };
  }

  // Check file name for malicious patterns
  const maliciousPatterns = [
    /\.\./,           // Directory traversal
    /[<>:"|?*]/,      // Invalid filename characters
    /\.(exe|bat|cmd|scr|pif|com)$/i  // Executable files
  ];

  if (maliciousPatterns.some(pattern => pattern.test(file.name))) {
    return {
      valid: false,
      error: 'Invalid file name or potentially malicious file'
    };
  }

  return { valid: true };
}

/**
 * Sanitize user input to prevent XSS and injection attacks
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: URLs
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Validate and sanitize API request data
 */
export function validateApiRequest(data: any): { valid: boolean; sanitized?: any; errors?: string[] } {
  const errors: string[] = [];
  const sanitized: any = {};

  try {
    // Recursively sanitize object
    function sanitizeObject(obj: any, path: string = ''): any {
      if (typeof obj === 'string') {
        return sanitizeInput(obj);
      }
      
      if (Array.isArray(obj)) {
        return obj.map((item, index) => sanitizeObject(item, `${path}[${index}]`));
      }
      
      if (obj && typeof obj === 'object') {
        const result: any = {};
        for (const [key, value] of Object.entries(obj)) {
          const sanitizedKey = sanitizeInput(key);
          result[sanitizedKey] = sanitizeObject(value, `${path}.${sanitizedKey}`);
        }
        return result;
      }
      
      return obj;
    }

    const sanitizedData = sanitizeObject(data);
    
    return {
      valid: errors.length === 0,
      sanitized: sanitizedData,
      errors: errors.length > 0 ? errors : undefined
    };
  } catch (error) {
    return {
      valid: false,
      errors: ['Invalid request data format']
    };
  }
}

/**
 * Create a secure API route handler
 */
export function createSecureApiRoute(
  handler: (req: NextRequest, authResult: AuthResult) => Promise<NextResponse>,
  options: {
    requireAuth?: boolean;
    requiredRole?: UserRole;
    requiredSubscription?: SubscriptionTier;
    rateLimit?: number;
    validateOrigin?: boolean;
    auditLog?: boolean;
    allowedMethods?: string[];
  } = {}
) {
  const {
    requireAuth = true,
    validateOrigin = true,
    auditLog = true,
    allowedMethods = ['GET', 'POST', 'PUT', 'DELETE']
  } = options;

  return async (req: NextRequest) => {
    try {
      // Validate HTTP method
      if (!allowedMethods.includes(req.method)) {
        return NextResponse.json(
          { 
            error: 'Method not allowed',
            message: `Method ${req.method} is not allowed for this endpoint`,
            allowedMethods
          },
          { status: 405 }
        );
      }

      // Validate request origin for state-changing operations
      if (validateOrigin && ['POST', 'PUT', 'DELETE'].includes(req.method)) {
        if (!validateRequestOrigin(req)) {
          return NextResponse.json(
            { 
              error: 'Invalid origin',
              message: 'Request origin is not allowed',
              code: 'INVALID_ORIGIN'
            },
            { status: 403 }
          );
        }
      }

      // Handle authentication if required
      if (requireAuth) {
        const authWrapper = options.rateLimit 
          ? withRateLimit(handler, {
              maxRequests: options.rateLimit,
              requiredRole: options.requiredRole,
              requiredSubscription: options.requiredSubscription
            })
          : withAuth(handler, {
              requiredRole: options.requiredRole,
              requiredSubscription: options.requiredSubscription
            });
        
        const response = await authWrapper(req);
        
        // Log security event if enabled
        if (auditLog) {
          const { userId } = auth();
          if (userId) {
            logSecurityEvent({
              userId,
              action: `API_${req.method}`,
              resource: req.nextUrl.pathname,
              details: {
                userAgent: req.headers.get('user-agent'),
                ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip')
              },
              ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || undefined,
              userAgent: req.headers.get('user-agent') || undefined
            });
          }
        }
        
        return applySecurityHeaders(response);
      } else {
        // Handle public routes
        const authResult: AuthResult = { success: false };
        const response = await handler(req, authResult);
        return applySecurityHeaders(response);
      }
    } catch (error) {
      console.error('API security error:', error);
      
      return applySecurityHeaders(
        NextResponse.json(
          { 
            error: 'Internal server error',
            message: 'An unexpected error occurred',
            code: 'INTERNAL_ERROR'
          },
          { status: 500 }
        )
      );
    }
  };
}

/**
 * Create a public API route (no authentication required)
 */
export function createPublicApiRoute(
  handler: (req: NextRequest) => Promise<NextResponse>,
  options: {
    rateLimit?: number;
    validateOrigin?: boolean;
    allowedMethods?: string[];
  } = {}
) {
  return createSecureApiRoute(
    async (req, _authResult) => handler(req),
    {
      ...options,
      requireAuth: false
    }
  );
}

/**
 * Create an admin-only API route
 */
export function createAdminApiRoute(
  handler: (req: NextRequest, authResult: AuthResult) => Promise<NextResponse>,
  options: {
    rateLimit?: number;
    validateOrigin?: boolean;
    allowedMethods?: string[];
  } = {}
) {
  return createSecureApiRoute(handler, {
    ...options,
    requireAuth: true,
    requiredRole: UserRole.ADMIN,
    auditLog: true
  });
}

/**
 * Error response helper
 */
export function createErrorResponse(
  error: string,
  message: string,
  status: number = 400,
  code?: string,
  details?: any
): NextResponse {
  return applySecurityHeaders(
    NextResponse.json(
      {
        error,
        message,
        code,
        details,
        timestamp: new Date().toISOString()
      },
      { status }
    )
  );
}

/**
 * Success response helper
 */
export function createSuccessResponse(
  data: any,
  message?: string,
  status: number = 200
): NextResponse {
  return applySecurityHeaders(
    NextResponse.json(
      {
        success: true,
        data,
        message,
        timestamp: new Date().toISOString()
      },
      { status }
    )
  );
}
