# =============================================================================
# SLACK SUMMARY SCRIBE - PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================

# App Configuration (Production)
NEXT_PUBLIC_APP_URL=https://slack-summary-scribe.vercel.app
NEXT_PUBLIC_SITE_URL=https://slack-summary-scribe.vercel.app
NODE_ENV=production

# Build optimizations
NEXT_TELEMETRY_DISABLED=1
SKIP_ENV_VALIDATION=false
NODE_OPTIONS=--max_old_space_size=8192

# Production URLs
NEXTAUTH_URL=https://slack-summary-scribe.vercel.app

# Feature Flags (Production)
NEXT_PUBLIC_ENVIRONMENT=production
NEXT_PUBLIC_FEATURE_AI_MODEL_ROUTING=true
NEXT_PUBLIC_FEATURE_PREMIUM_AI=true
NEXT_PUBLIC_FEATURE_AI_ANALYTICS=true
NEXT_PUBLIC_FEATURE_ENTERPRISE_SECURITY=true
NEXT_PUBLIC_FEATURE_SLACK_INTEGRATION=true
NEXT_PUBLIC_FEATURE_EMAIL_NOTIFICATIONS=true
NEXT_PUBLIC_FEATURE_EXPORT_SYSTEM=true
ENABLE_ANALYTICS=true
ENABLE_ERROR_REPORTING=true

# Security (Production)
COOKIE_SECURE=true
COOKIE_SAME_SITE=strict

# Performance
NEXT_PUBLIC_FETCH_TIMEOUT=15000

# SEO & Metadata
NEXT_PUBLIC_APP_NAME=Slack Summary Scribe
NEXT_PUBLIC_APP_DESCRIPTION=AI-powered summarization for Slack messages and documents

# NOTE: Sensitive keys should be set in Vercel environment variables:
# - NEXT_PUBLIC_SUPABASE_URL
# - NEXT_PUBLIC_SUPABASE_ANON_KEY
# - SUPABASE_SERVICE_ROLE_KEY
# - OPENROUTER_API_KEY
# - SLACK_CLIENT_ID
# - SLACK_CLIENT_SECRET
# - RESEND_API_KEY
# - STRIPE_SECRET_KEY
# - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
# - NEXT_PUBLIC_POSTHOG_KEY
# - NEXT_PUBLIC_SENTRY_DSN
