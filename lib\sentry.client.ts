import { devLog } from '@/lib/console-cleaner';
/**
 * Safe Sentry Client - No External Dependencies
 * 
 * This provides a safe Sentry interface without requiring actual Sentry
 * to prevent build errors and runtime crashes.
 */

// Safe Sentry interface that doesn't require external dependencies
export interface SafeSentryClient {
  captureException: (error: Error, context?: any) => void;
  captureMessage: (message: string, level?: string) => void;
  addBreadcrumb: (breadcrumb: any) => void;
  setUser: (user: any) => void;
  setTag: (key: string, value: string) => void;
  setContext: (key: string, context: any) => void;
}

// Mock Sentry client for safe operation
const mockSentryClient: SafeSentryClient = {
  captureException: (error: Error, context?: any) => {
    console.error('🔥 Error captured:', error.message, context);
  },
  captureMessage: (message: string, level = 'info') => {
  devLog.log(`📝 [${level}] ${message}`);
  },
  addBreadcrumb: (breadcrumb: any) => {
  devLog.log('🍞 Breadcrumb:', breadcrumb);
  },
  setUser: (user: any) => {
  devLog.log('👤 User set:', user);
  },
  setTag: (key: string, value: string) => {
  devLog.log(`🏷️ Tag set: ${key}=${value}`);
  },
  setContext: (key: string, context: any) => {
  devLog.log(`📋 Context set: ${key}`, context);
  }
};

// Export the safe client
export const SentryTracker = mockSentryClient;

// Safe error tracking function
export function trackError(error: Error, context?: any) {
  console.error('🔥 Error tracked:', {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString()
  });
}

// Safe message tracking function
export function trackMessage(message: string, level = 'info') {
  devLog.log(`📝 [${level}] ${message} - ${new Date().toISOString()}`);
}

// Safe performance tracking
export function trackPerformance(operation: string, duration: number) {
  devLog.log(`⚡ Performance: ${operation} took ${duration}ms`);
}

// Safe user tracking
export function trackUser(user: any) {
  devLog.log('👤 User tracked:', {
    id: user?.id,
    email: user?.email,
    timestamp: new Date().toISOString()
  });
}

// Export default for compatibility
export default SentryTracker;
