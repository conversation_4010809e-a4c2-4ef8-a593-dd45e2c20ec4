/**
 * Feature Flag Management System
 * 
 * Dynamic feature toggling for gradual rollouts and A/B testing
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';

export interface FeatureFlag {
  id: string;
  name: string;
  description?: string;
  enabled: boolean;
  conditions: FlagConditions;
  rolloutPercentage: number;
  targetUsers: string[];
  targetOrganizations: string[];
  environment: string;
  createdAt: string;
  updatedAt: string;
}

export interface FlagConditions {
  plan?: string[];
  userRole?: string[];
  organizationSize?: { min?: number; max?: number };
  userCreatedAfter?: string;
  customAttributes?: Record<string, any>;
}

export interface FlagEvaluationContext {
  userId?: string;
  organizationId?: string;
  userRole?: string;
  plan?: string;
  organizationSize?: number;
  userCreatedAt?: string;
  customAttributes?: Record<string, any>;
  environment?: string;
}

// Cache for feature flags to avoid database hits
let flagCache: Map<string, FeatureFlag> = new Map();
let cacheLastUpdated = 0;
const CACHE_TTL = 60000; // 1 minute

/**
 * Evaluate if a feature flag is enabled for given context
 */
export async function isFeatureEnabled(
  flagName: string,
  context: FlagEvaluationContext = {}
): Promise<boolean> {
  try {
    const flag = await getFeatureFlag(flagName);
    
    if (!flag) {
      // Default to false for unknown flags
      return false;
    }

    // Check if flag is globally disabled
    if (!flag.enabled) {
      return false;
    }

    // Check environment
    if (flag.environment && context.environment && flag.environment !== context.environment) {
      return false;
    }

    // Check user targeting
    if (flag.targetUsers.length > 0 && context.userId) {
      if (flag.targetUsers.includes(context.userId)) {
        return true;
      }
    }

    // Check organization targeting
    if (flag.targetOrganizations.length > 0 && context.organizationId) {
      if (flag.targetOrganizations.includes(context.organizationId)) {
        return true;
      }
    }

    // Check conditions
    if (!evaluateConditions(flag.conditions, context)) {
      return false;
    }

    // Check rollout percentage
    if (flag.rolloutPercentage < 100) {
      const hash = hashString(`${flagName}:${context.userId || context.organizationId || 'anonymous'}`);
      const percentage = hash % 100;
      return percentage < flag.rolloutPercentage;
    }

    return true;

  } catch (error) {
    console.error('Feature flag evaluation failed:', error);
    // Fail open - return false for safety
    return false;
  }
}

/**
 * Get feature flag by name
 */
async function getFeatureFlag(name: string): Promise<FeatureFlag | null> {
  // Check cache first
  if (Date.now() - cacheLastUpdated < CACHE_TTL && flagCache.has(name)) {
    return flagCache.get(name) || null;
  }

  try {
    const supabase = await createSupabaseServerClient();
    
    const { data: flag, error } = await supabase
      .from('feature_flags')
      .select('*')
      .eq('name', name)
      .single();

    if (error || !flag) {
      return null;
    }

    const formattedFlag: FeatureFlag = {
      id: flag.id,
      name: flag.name,
      description: flag.description,
      enabled: flag.enabled,
      conditions: flag.conditions || {},
      rolloutPercentage: flag.rollout_percentage,
      targetUsers: flag.target_users || [],
      targetOrganizations: flag.target_organizations || [],
      environment: flag.environment,
      createdAt: flag.created_at,
      updatedAt: flag.updated_at
    };

    // Update cache
    flagCache.set(name, formattedFlag);
    
    return formattedFlag;

  } catch (error) {
    console.error('Failed to get feature flag:', error);
    return null;
  }
}

/**
 * Evaluate flag conditions against context
 */
function evaluateConditions(
  conditions: FlagConditions,
  context: FlagEvaluationContext
): boolean {
  // Check plan condition
  if (conditions.plan && conditions.plan.length > 0) {
    if (!context.plan || !conditions.plan.includes(context.plan)) {
      return false;
    }
  }

  // Check user role condition
  if (conditions.userRole && conditions.userRole.length > 0) {
    if (!context.userRole || !conditions.userRole.includes(context.userRole)) {
      return false;
    }
  }

  // Check organization size condition
  if (conditions.organizationSize) {
    if (context.organizationSize !== undefined) {
      const { min, max } = conditions.organizationSize;
      if (min !== undefined && context.organizationSize < min) {
        return false;
      }
      if (max !== undefined && context.organizationSize > max) {
        return false;
      }
    }
  }

  // Check user created after condition
  if (conditions.userCreatedAfter && context.userCreatedAt) {
    const userCreated = new Date(context.userCreatedAt);
    const threshold = new Date(conditions.userCreatedAfter);
    if (userCreated < threshold) {
      return false;
    }
  }

  // Check custom attributes
  if (conditions.customAttributes && context.customAttributes) {
    for (const [key, expectedValue] of Object.entries(conditions.customAttributes)) {
      const actualValue = context.customAttributes[key];
      if (actualValue !== expectedValue) {
        return false;
      }
    }
  }

  return true;
}

/**
 * Create or update feature flag
 */
export async function upsertFeatureFlag(
  flag: Omit<FeatureFlag, 'id' | 'createdAt' | 'updatedAt'>
): Promise<{ success: boolean; flagId?: string; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { data, error } = await supabase
      .from('feature_flags')
      .upsert({
        name: flag.name,
        description: flag.description,
        enabled: flag.enabled,
        conditions: flag.conditions,
        rollout_percentage: flag.rolloutPercentage,
        target_users: flag.targetUsers,
        target_organizations: flag.targetOrganizations,
        environment: flag.environment,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'name'
      })
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    // Clear cache for this flag
    flagCache.delete(flag.name);

    return { success: true, flagId: data.id };

  } catch (error) {
    console.error('Failed to upsert feature flag:', error);
    return { success: false, error: 'Failed to save feature flag' };
  }
}

/**
 * Delete feature flag
 */
export async function deleteFeatureFlag(
  flagName: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { error } = await supabase
      .from('feature_flags')
      .delete()
      .eq('name', flagName);

    if (error) {
      return { success: false, error: error.message };
    }

    // Clear cache
    flagCache.delete(flagName);

    return { success: true };

  } catch (error) {
    console.error('Failed to delete feature flag:', error);
    return { success: false, error: 'Failed to delete feature flag' };
  }
}

/**
 * List all feature flags
 */
export async function listFeatureFlags(
  environment?: string
): Promise<{ flags: FeatureFlag[]; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    let query = supabase
      .from('feature_flags')
      .select('*')
      .order('name');

    if (environment) {
      query = query.eq('environment', environment);
    }

    const { data: flags, error } = await query;

    if (error) {
      return { flags: [], error: error.message };
    }

    const formattedFlags: FeatureFlag[] = flags.map(flag => ({
      id: flag.id,
      name: flag.name,
      description: flag.description,
      enabled: flag.enabled,
      conditions: flag.conditions || {},
      rolloutPercentage: flag.rollout_percentage,
      targetUsers: flag.target_users || [],
      targetOrganizations: flag.target_organizations || [],
      environment: flag.environment,
      createdAt: flag.created_at,
      updatedAt: flag.updated_at
    }));

    return { flags: formattedFlags };

  } catch (error) {
    console.error('Failed to list feature flags:', error);
    return { flags: [], error: 'Failed to list feature flags' };
  }
}

/**
 * Get user's enabled features
 */
export async function getUserEnabledFeatures(
  context: FlagEvaluationContext
): Promise<{ features: string[]; error?: string }> {
  try {
    const { flags } = await listFeatureFlags(context.environment);
    const enabledFeatures: string[] = [];

    for (const flag of flags) {
      const enabled = await isFeatureEnabled(flag.name, context);
      if (enabled) {
        enabledFeatures.push(flag.name);
      }
    }

    return { features: enabledFeatures };

  } catch (error) {
    console.error('Failed to get user enabled features:', error);
    return { features: [], error: 'Failed to get enabled features' };
  }
}

/**
 * Refresh feature flag cache
 */
export async function refreshFlagCache(): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { data: flags, error } = await supabase
      .from('feature_flags')
      .select('*');

    if (error) {
      console.error('Failed to refresh flag cache:', error);
      return;
    }

    // Clear and rebuild cache
    flagCache.clear();
    
    flags.forEach(flag => {
      const formattedFlag: FeatureFlag = {
        id: flag.id,
        name: flag.name,
        description: flag.description,
        enabled: flag.enabled,
        conditions: flag.conditions || {},
        rolloutPercentage: flag.rollout_percentage,
        targetUsers: flag.target_users || [],
        targetOrganizations: flag.target_organizations || [],
        environment: flag.environment,
        createdAt: flag.created_at,
        updatedAt: flag.updated_at
      };
      
      flagCache.set(flag.name, formattedFlag);
    });

    cacheLastUpdated = Date.now();

  } catch (error) {
    console.error('Failed to refresh flag cache:', error);
  }
}

/**
 * Hash string for consistent percentage rollouts
 */
function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

/**
 * Feature flag middleware for API routes
 */
export function requireFeature(flagName: string) {
  return async (context: FlagEvaluationContext): Promise<boolean> => {
    return await isFeatureEnabled(flagName, context);
  };
}

/**
 * React hook for feature flags (client-side)
 */
export function createFeatureFlagHook() {
  return function useFeatureFlag(flagName: string, context: FlagEvaluationContext = {}) {
    // This would be implemented as a React hook on the client side
    // For now, it's a placeholder
    return {
      enabled: false,
      loading: true,
      error: null
    };
  };
}

/**
 * Initialize feature flag system
 */
export async function initializeFeatureFlags(): Promise<void> {
  // Refresh cache on startup
  await refreshFlagCache();
  
  // Set up periodic cache refresh
  setInterval(refreshFlagCache, CACHE_TTL);
}
