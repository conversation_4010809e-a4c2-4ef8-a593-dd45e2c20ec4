import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { routeAIRequest } from '@/lib/ai-routing-service';
import { getCurrentUser } from '@/lib/user-management';
import { generateSummary } from '@/lib/ai-summarization';
import { auth } from '@clerk/nextjs/server';
import { withDemoLimitCheck } from '@/lib/demo-middleware';

// Apply demo limit check middleware
export const POST = withDemoLimitCheck('summaries')(async (request: NextRequest, context) => {
  try {
    // Get authenticated user from Clerk
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
  devLog.log(`📝 Summarize: Processing request for user ${userId} (Demo: ${context.isInDemo})`);

    const body = await request.json();
    const { transcriptText, teamId, context: requestContext, preferredModel, organizationId, type = 'slack' } = body;

    // Validate required fields
    if (!transcriptText) {
      return NextResponse.json(
        { error: 'Missing required field: transcriptText' },
        { status: 400 }
      );
    }

    // Extract user info for tracking
    const userTeamId = teamId || 'default';
    const userOrganizationId = organizationId || 'default';

    // Get user plan from context or default to FREE for demo users
    const userPlan = context.isInDemo ? 'FREE' : 'PRO';
  devLog.log(`📊 User context: ${userId} | Team: ${userTeamId} | Plan: ${userPlan} | Demo: ${context.isInDemo}`);

    // Generate summary using AI
    const startTime = Date.now();

    try {
      // Use the enhanced AI summarization service
      const summaryResult = await generateSummary({
        content: transcriptText,
        type: type as 'slack' | 'file' | 'meeting' | 'document',
        language: undefined, // Auto-detect
        includeRedFlags: true,
        includeSkills: true,
        includeActionItems: true,
        speakerLabels: true,
        userId: authenticatedUserId,
        metadata: {
          teamId: userTeamId,
          organizationId: userOrganizationId,
          plan: userPlan,
          preferredModel,
          context
        }
      });

      const processingTime = Date.now() - startTime;
  devLog.log(`✅ Summary generated: ${summaryResult.summary.length} chars in ${processingTime}ms using ${summaryResult.model}`);

      // DEV MODE: No database storage required
      const summaryId = summaryResult.id;
  devLog.log('📄 Dev mode: Skipping database storage for summary');

      // Return successful response with structured data
      return NextResponse.json({
        success: true,
        summary: {
          id: summaryId,
          user_id: authenticatedUserId,
          team_id: userTeamId,
          title: `Summary - ${new Date().toLocaleDateString()}`,
          summary_text: summaryResult.summary,
          summary: {
            text: summaryResult.summary,
            skills: summaryResult.skills,
            redFlags: summaryResult.redFlags,
            actions: summaryResult.actionItems,
            keyPoints: summaryResult.keyPoints,
            participants: summaryResult.speakers,
            sentiment: 'neutral',
            duration: null,
            meetingType: context || 'general'
          },
          source_type: type,
          ai_model: summaryResult.model,
          ai_cost: summaryResult.cost,
          ai_tokens: summaryResult.tokens,
          processing_time: processingTime,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          organization_id: userOrganizationId,
        },
        summaryId,
        model: summaryResult.model,
        cost: summaryResult.cost,
        tokens: summaryResult.tokens,
        processingTime,
        plan: userPlan,
        features: {
          smartTagging: true,
          autoPosting: false,
          crmIntegration: false
        },
        message: 'Summary created successfully in public mode.'
      });

    } catch (aiError) {
      const processingTime = Date.now() - startTime;

      console.error('AI generation error:', aiError);

      // Return a more user-friendly error message
      const errorMessage = aiError instanceof Error 
        ? aiError.message 
        : 'Failed to generate summary. Please try again.';

      return NextResponse.json(
        { 
          error: errorMessage,
          details: process.env.NODE_ENV === 'development' ? aiError : undefined
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Summarize API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
});

export async function GET(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
