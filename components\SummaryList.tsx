/**
 * Summary List Component
 * 
 * Displays a list of recent summaries with status and actions
 */

'use client';

import { useState, useMemo } from 'react';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  FileText, 
  Calendar, 
  Users, 
  Download, 
  ExternalLink,
  MoreHorizontal,
  CheckCircle,
  Clock,
  AlertCircle,
  Search,
  Filter
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';

export interface Summary {
  id: string;
  title: string;
  content: string;
  keyPoints: string[];
  actionItems: string[];
  participants: string[];
  duration?: string;
  createdAt: string;
  updatedAt?: string;
  status: 'completed' | 'processing' | 'failed';
  source?: 'slack' | 'manual' | 'upload';
  workspaceId?: string;
  workspaceName?: string;
  channelName?: string;
  userId: string;
  tags?: string[];
}

interface SummaryListProps {
  summaries: Summary[];
  isLoading?: boolean;
  onSummaryClick?: (summary: Summary) => void;
  onExport?: (summary: Summary) => void;
  showSearch?: boolean;
  showFilters?: boolean;
  maxItems?: number;
  className?: string;
  // Pagination props
  enablePagination?: boolean;
  itemsPerPage?: number;
  onLoadMore?: () => void;
  hasMore?: boolean;
  isLoadingMore?: boolean;
  enableInfiniteScroll?: boolean;
}

export function SummaryList({
  summaries,
  isLoading = false,
  onSummaryClick,
  onExport,
  showSearch = true,
  showFilters = true,
  maxItems,
  className = '',
  enablePagination = false,
  itemsPerPage = 10,
  onLoadMore,
  hasMore = false,
  isLoadingMore = false,
  enableInfiniteScroll = false
}: SummaryListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'completed' | 'processing' | 'failed'>('all');
  const [currentPage, setCurrentPage] = useState(1);

  // Infinite scroll hook
  const { loadingRef } = useInfiniteScroll({
    hasMore: hasMore && enableInfiniteScroll,
    isLoading: isLoadingMore,
    onLoadMore: onLoadMore || (() => {}),
  });

  const filteredSummaries = useMemo(() => {
    let filtered = summaries;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(summary => 
        summary.title.toLowerCase().includes(query) ||
        summary.content.toLowerCase().includes(query) ||
        summary.participants.some(p => p.toLowerCase().includes(query)) ||
        summary.workspaceName?.toLowerCase().includes(query) ||
        summary.channelName?.toLowerCase().includes(query)
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(summary => summary.status === statusFilter);
    }

    // Apply pagination or max items limit
    if (enablePagination) {
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      filtered = filtered.slice(0, endIndex); // Show all items up to current page
    } else if (maxItems) {
      filtered = filtered.slice(0, maxItems);
    }

    return filtered;
  }, [summaries, searchQuery, statusFilter, maxItems, enablePagination, currentPage, itemsPerPage]);

  if (isLoading) {
    return <SummaryListSkeleton className={className} />;
  }

  const getStatusIcon = (status: Summary['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: Summary['status']) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="text-xs">Completed</Badge>;
      case 'processing':
        return <Badge variant="secondary" className="text-xs">Processing</Badge>;
      case 'failed':
        return <Badge variant="destructive" className="text-xs">Failed</Badge>;
      default:
        return null;
    }
  };

  const getSourceBadge = (source: Summary['source']) => {
    switch (source) {
      case 'slack':
        return <Badge variant="outline" className="text-xs">Slack</Badge>;
      case 'manual':
        return <Badge variant="outline" className="text-xs">Manual</Badge>;
      case 'upload':
        return <Badge variant="outline" className="text-xs">Upload</Badge>;
      default:
        return null;
    }
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Recent Summaries</CardTitle>
            <CardDescription>
              {filteredSummaries.length} of {summaries.length} summaries
            </CardDescription>
          </div>
          
          {/* Search and Filters */}
          {(showSearch || showFilters) && (
            <div className="flex items-center space-x-2">
              {showSearch && (
                <div className="relative">
                  <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search summaries..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              )}
              
              {showFilters && (
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="completed">Completed</option>
                  <option value="processing">Processing</option>
                  <option value="failed">Failed</option>
                </select>
              )}
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {filteredSummaries.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
              <FileText className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {searchQuery || statusFilter !== 'all' ? 'No summaries found' : 'No summaries yet'}
            </h3>
            <p className="text-gray-500 mb-6 max-w-sm mx-auto">
              {searchQuery || statusFilter !== 'all'
                ? 'Try adjusting your search criteria or filters to find what you\'re looking for.'
                : 'Get started by creating your first summary from Slack conversations or uploaded documents.'
              }
            </p>
            {!searchQuery && statusFilter === 'all' && (
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button asChild>
                  <Link href="/dashboard/create">
                    <FileText className="h-4 w-4 mr-2" />
                    Create Summary
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/dashboard/upload">
                    <Download className="h-4 w-4 mr-2" />
                    Upload Document
                  </Link>
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredSummaries.map((summary) => (
              <div
                key={summary.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => onSummaryClick?.(summary)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className="flex-shrink-0 mt-1">
                      {getStatusIcon(summary.status)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {summary.title}
                        </h4>
                        {getStatusBadge(summary.status)}
                        {getSourceBadge(summary.source)}
                      </div>
                      
                      <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                        {summary.content.substring(0, 150)}...
                      </p>
                      
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{formatDistanceToNow(new Date(summary.createdAt), { addSuffix: true })}</span>
                        </div>
                        
                        {summary.participants && summary.participants.length > 0 && (
                          <div className="flex items-center space-x-1">
                            <Users className="h-3 w-3" />
                            <span>{summary.participants.length} participants</span>
                          </div>
                        )}
                        
                        {summary.workspaceName && (
                          <div className="flex items-center space-x-1">
                            <span>#{summary.channelName || summary.workspaceName}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    {onExport && summary.status === 'completed' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onExport(summary);
                        }}
                        className="h-8 w-8 p-0"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination Controls */}
        {enablePagination && filteredSummaries.length > 0 && (
          <div className="mt-6 flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="text-sm text-gray-600">
              Showing {Math.min(currentPage * itemsPerPage, filteredSummaries.length)} of {summaries.length} summaries
            </div>

            <div className="flex items-center justify-center space-x-3 pt-4">
              {hasMore && onLoadMore && (
                <Button
                  variant="outline"
                  onClick={onLoadMore}
                  disabled={isLoadingMore}
                  className="text-sm px-6"
                >
                  {isLoadingMore ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                      Loading more...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Load More
                    </>
                  )}
                </Button>
              )}

              {filteredSummaries.length < summaries.length && !hasMore && (
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => prev + 1)}
                  className="text-sm px-6"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Show More ({summaries.length - filteredSummaries.length} remaining)
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Infinite scroll trigger */}
        {enableInfiniteScroll && hasMore && (
          <div ref={loadingRef} className="h-4 w-full" />
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Loading skeleton for summary list
 */
export function SummaryListSkeleton({ className = '' }: { className?: string }) {
  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-6 w-32 mb-2" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-10 w-48" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Skeleton className="h-4 w-4 mt-1" />
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <Skeleton className="h-4 w-48" />
                    <Skeleton className="h-5 w-16 rounded-full" />
                    <Skeleton className="h-5 w-12 rounded-full" />
                  </div>
                  <Skeleton className="h-4 w-full mb-1" />
                  <Skeleton className="h-4 w-3/4 mb-2" />
                  <div className="flex space-x-4">
                    <Skeleton className="h-3 w-20" />
                    <Skeleton className="h-3 w-24" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
