#!/usr/bin/env node

/**
 * CSP and Integration Testing Script
 * Tests all the implemented fixes for CSP, Slack OAuth, and chunk loading
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function logHeader(text) {
  console.log(`\n${colors.cyan}${colors.bright}${text}${colors.reset}`);
  console.log('='.repeat(text.length));
}

function logSuccess(text) {
  console.log(`${colors.green}✅ ${text}${colors.reset}`);
}

function logError(text) {
  console.log(`${colors.red}❌ ${text}${colors.reset}`);
}

function logWarning(text) {
  console.log(`${colors.yellow}⚠️  ${text}${colors.reset}`);
}

function logInfo(text) {
  console.log(`${colors.blue}ℹ️  ${text}${colors.reset}`);
}

// Load environment variables
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  
  if (!fs.existsSync(envPath)) {
    logError('.env.local file not found');
    return {};
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key] = valueParts.join('=');
      }
    }
  });
  
  return env;
}

// Test 1: Environment Mode Detection
function testEnvironmentMode() {
  logHeader('TEST 1: ENVIRONMENT MODE DETECTION');
  
  const env = loadEnvFile();
  const nodeEnv = env.NODE_ENV || 'development';
  const devMode = env.NEXT_PUBLIC_DEV_MODE === 'true';
  const stripeTestMode = env.NEXT_PUBLIC_STRIPE_TEST_MODE === 'true';
  
  const isDevelopment = nodeEnv === 'development' || devMode;
  const isProduction = nodeEnv === 'production' && !devMode;
  
  console.log(`   NODE_ENV: ${nodeEnv}`);
  console.log(`   DEV_MODE: ${devMode}`);
  console.log(`   STRIPE_TEST_MODE: ${stripeTestMode}`);
  console.log(`   Detected Mode: ${isDevelopment ? 'Development' : isProduction ? 'Production' : 'Test'}`);
  
  if (isDevelopment) {
    logSuccess('Development mode detected - CSP will be disabled');
    logSuccess('All integrations will be enabled for testing');
  } else if (isProduction) {
    logSuccess('Production mode detected - Full CSP will be applied');
  }
  
  return true;
}

// Test 2: CSP Configuration
function testCSPConfiguration() {
  logHeader('TEST 2: CSP CONFIGURATION');
  
  const nextConfigPath = path.join(process.cwd(), 'next.config.mjs');
  
  if (!fs.existsSync(nextConfigPath)) {
    logError('next.config.mjs not found');
    return false;
  }
  
  const configContent = fs.readFileSync(nextConfigPath, 'utf8');
  
  // Check for environment-based CSP
  if (configContent.includes('process.env.NODE_ENV === \'production\'')) {
    logSuccess('Environment-based CSP configuration found');
  } else {
    logError('Environment-based CSP configuration missing');
    return false;
  }
  
  // Check for development mode CSP bypass
  if (configContent.includes('Development mode: NO CSP headers')) {
    logSuccess('Development CSP bypass implemented');
  } else {
    logWarning('Development CSP bypass not found');
  }
  
  // Check for required domains in production CSP
  const requiredDomains = [
    'clerk.com',
    'stripe.com',
    'supabase.co',
    'posthog.com',
    'google.com',
    'cashfree.com',
    'slack.com'
  ];
  
  let allDomainsFound = true;
  requiredDomains.forEach(domain => {
    if (configContent.includes(domain)) {
      logSuccess(`CSP includes ${domain}`);
    } else {
      logWarning(`CSP missing ${domain}`);
      allDomainsFound = false;
    }
  });
  
  return allDomainsFound;
}

// Test 3: Slack OAuth Configuration
function testSlackConfiguration() {
  logHeader('TEST 3: SLACK OAUTH CONFIGURATION');
  
  const env = loadEnvFile();
  let valid = true;
  
  const requiredSlackVars = [
    'NEXT_PUBLIC_SLACK_CLIENT_ID',
    'SLACK_CLIENT_ID',
    'SLACK_CLIENT_SECRET',
    'SLACK_SIGNING_SECRET'
  ];
  
  requiredSlackVars.forEach(varName => {
    if (env[varName]) {
      logSuccess(`${varName}: Configured`);
    } else {
      logError(`${varName}: Missing`);
      valid = false;
    }
  });
  
  // Display required redirect URLs
  console.log('\n   Required Slack OAuth Redirect URLs:');
  console.log('   - http://localhost:3000/auth/slack/callback');
  console.log('   - http://localhost:3001/auth/slack/callback');
  if (env.NEXT_PUBLIC_SITE_URL) {
    console.log(`   - ${env.NEXT_PUBLIC_SITE_URL.replace(/\/$/, '')}/auth/slack/callback`);
  }
  
  logInfo('Add these URLs to your Slack app settings under OAuth & Permissions');
  
  return valid;
}

// Test 4: Chunk Error Handling
function testChunkErrorHandling() {
  logHeader('TEST 4: CHUNK ERROR HANDLING');
  
  const files = [
    'components/error-boundaries/ChunkErrorBoundary.tsx',
    'components/ChunkErrorBoundary.tsx',
    'lib/chunk-error-handler.ts',
    'lib/client-initialization.ts'
  ];
  
  let allExist = true;
  
  files.forEach(file => {
    if (fs.existsSync(path.join(process.cwd(), file))) {
      logSuccess(`${file}: Found`);
    } else {
      logError(`${file}: Missing`);
      allExist = false;
    }
  });
  
  // Check layout.tsx for chunk error recovery script
  const layoutPath = path.join(process.cwd(), 'app/layout.tsx');
  if (fs.existsSync(layoutPath)) {
    const layoutContent = fs.readFileSync(layoutPath, 'utf8');
    
    if (layoutContent.includes('__webpack_chunk_load_error_handler__')) {
      logSuccess('Layout includes chunk error recovery script');
    } else {
      logWarning('Layout missing chunk error recovery script');
      allExist = false;
    }
    
    if (layoutContent.includes('Environment and test mode detection')) {
      logSuccess('Layout includes environment detection script');
    } else {
      logWarning('Layout missing environment detection script');
    }
  }
  
  return allExist;
}

// Test 5: Test Mode Detection
function testTestModeDetection() {
  logHeader('TEST 5: TEST MODE DETECTION');
  
  const testModeFile = path.join(process.cwd(), 'lib/test-mode-detection.ts');
  
  if (!fs.existsSync(testModeFile)) {
    logError('test-mode-detection.ts not found');
    return false;
  }
  
  const content = fs.readFileSync(testModeFile, 'utf8');
  
  const requiredFunctions = [
    'getTestModeConfig',
    'shouldAllowStripeOperations',
    'getStripeConfig',
    'validateEnvironmentConfig',
    'getSlackRedirectUrls'
  ];
  
  let allFunctionsFound = true;
  requiredFunctions.forEach(func => {
    if (content.includes(`export function ${func}`) || content.includes(`function ${func}`)) {
      logSuccess(`Function ${func}: Found`);
    } else {
      logError(`Function ${func}: Missing`);
      allFunctionsFound = false;
    }
  });
  
  return allFunctionsFound;
}

// Test 6: Build Test
async function testBuild() {
  logHeader('TEST 6: BUILD TEST');
  
  return new Promise((resolve) => {
    logInfo('Running Next.js build test...');
    
    const buildProcess = spawn('npm', ['run', 'build'], {
      stdio: 'pipe',
      shell: true
    });
    
    let output = '';
    let errorOutput = '';
    
    buildProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    buildProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    buildProcess.on('close', (code) => {
      if (code === 0) {
        logSuccess('Build completed successfully');
        resolve(true);
      } else {
        logError('Build failed');
        console.log('Build output:', output);
        console.log('Build errors:', errorOutput);
        resolve(false);
      }
    });
    
    // Timeout after 2 minutes
    setTimeout(() => {
      buildProcess.kill();
      logWarning('Build test timed out');
      resolve(false);
    }, 120000);
  });
}

// Main test runner
async function runAllTests() {
  logHeader('🧪 CSP AND INTEGRATION FIXES VALIDATION');
  
  const tests = [
    { name: 'Environment Mode Detection', fn: testEnvironmentMode },
    { name: 'CSP Configuration', fn: testCSPConfiguration },
    { name: 'Slack OAuth Configuration', fn: testSlackConfiguration },
    { name: 'Chunk Error Handling', fn: testChunkErrorHandling },
    { name: 'Test Mode Detection', fn: testTestModeDetection },
    { name: 'Build Test', fn: testBuild }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
    } catch (error) {
      logError(`${test.name} failed: ${error.message}`);
      results.push({ name: test.name, success: false });
    }
  }
  
  // Summary
  logHeader('📊 TEST SUMMARY');
  
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  results.forEach(result => {
    if (result.success) {
      logSuccess(`${result.name}: PASSED`);
    } else {
      logError(`${result.name}: FAILED`);
    }
  });
  
  console.log(`\n${colors.bright}Result: ${passed}/${total} tests passed${colors.reset}`);
  
  if (passed === total) {
    logSuccess('🎉 All tests passed! Ready for development and production');
    
    logHeader('🚀 NEXT STEPS');
    console.log('1. Run `npm run dev` to test in development mode');
    console.log('2. Verify all integrations work without CSP errors');
    console.log('3. Test Slack OAuth flow');
    console.log('4. Deploy to production with CSP enabled');
    
    return true;
  } else {
    logError('❌ Some tests failed - please fix issues before proceeding');
    return false;
  }
}

if (require.main === module) {
  runAllTests()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      logError(`Test runner failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { runAllTests };
