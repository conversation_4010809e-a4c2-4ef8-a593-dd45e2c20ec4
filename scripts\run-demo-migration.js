/**
 * Demo Usage Table Migration Script
 * Manually runs the demo usage table migration
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function runMigration() {
  try {
    console.log('🚀 Starting demo usage table migration...');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../supabase/migrations/20241202_demo_usage_table.sql');
    
    if (!fs.existsSync(migrationPath)) {
      console.error('❌ Migration file not found:', migrationPath);
      process.exit(1);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('📄 Migration SQL loaded successfully');

    // Split SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📝 Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.trim().length === 0) continue;

      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { 
          sql: statement + ';' 
        });

        if (error) {
          // Try direct query if RPC fails
          const { error: directError } = await supabase
            .from('_temp_migration')
            .select('*')
            .limit(0);

          if (directError) {
            console.log(`⚠️  Statement ${i + 1} may have failed, but continuing...`);
            console.log(`   Statement: ${statement.substring(0, 100)}...`);
          }
        }
      } catch (err) {
        console.log(`⚠️  Statement ${i + 1} encountered an issue, but continuing...`);
        console.log(`   Error: ${err.message}`);
      }
    }

    // Verify the table was created
    console.log('🔍 Verifying demo_usage table creation...');
    
    const { data: tables, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'demo_usage');

    if (tableError) {
      console.log('⚠️  Could not verify table creation via information_schema');
      
      // Try direct table access
      const { error: accessError } = await supabase
        .from('demo_usage')
        .select('*')
        .limit(1);

      if (accessError) {
        console.error('❌ demo_usage table does not exist or is not accessible');
        console.error('Error:', accessError.message);
      } else {
        console.log('✅ demo_usage table is accessible');
      }
    } else if (tables && tables.length > 0) {
      console.log('✅ demo_usage table created successfully');
    } else {
      console.log('⚠️  demo_usage table not found in information_schema');
    }

    // Test the functions
    console.log('🧪 Testing demo usage functions...');
    
    try {
      const { data: testData, error: testError } = await supabase
        .rpc('get_demo_usage_summary', { p_user_id: 'test_user_123' });

      if (testError) {
        console.log('⚠️  Demo usage functions may not be available');
        console.log('   Error:', testError.message);
      } else {
        console.log('✅ Demo usage functions are working');
      }
    } catch (err) {
      console.log('⚠️  Could not test demo usage functions');
    }

    console.log('🎉 Migration completed successfully!');
    console.log('');
    console.log('📋 Next steps:');
    console.log('   1. Test the demo mode functionality');
    console.log('   2. Verify usage tracking works correctly');
    console.log('   3. Test upgrade prompts and limits');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the migration
runMigration();
