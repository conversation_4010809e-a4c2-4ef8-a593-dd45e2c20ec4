#!/usr/bin/env tsx

/**
 * Setup Validation Script
 * 
 * This script validates that all fixes have been applied correctly
 */

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

interface ValidationResult {
  category: string;
  checks: Array<{
    name: string;
    status: 'PASS' | 'FAIL' | 'WARNING';
    message: string;
  }>;
}

class SetupValidator {
  private results: ValidationResult[] = [];

  private addResult(category: string, name: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string) {
    let categoryResult = this.results.find(r => r.category === category);
    if (!categoryResult) {
      categoryResult = { category, checks: [] };
      this.results.push(categoryResult);
    }
    categoryResult.checks.push({ name, status, message });
  }

  validateFileStructure() {
    const requiredFiles = [
      'app/layout.tsx',
      'app/dashboard/page.tsx',
      'app/upload/page.tsx',
      'app/api/dashboard/route.ts',
      'app/api/upload/route.ts',
      'components/ClerkWrapper.tsx',
      'components/DashboardStats.tsx',
      'components/SummaryForm.tsx',
      'components/SummaryList.tsx',
      'lib/supabase-clerk.ts',
      'lib/supabaseClient.ts',
      'lib/fetch-utils.ts',
      'utils/fetch-utils.ts',
      'middleware.ts',
      'next.config.mjs',
      'scripts/setup-database-schema.sql',
      'scripts/seed.ts',
      'scripts/test-complete-flow.ts'
    ];

    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        this.addResult('File Structure', file, 'PASS', 'File exists');
      } else {
        this.addResult('File Structure', file, 'FAIL', 'File missing');
      }
    }
  }

  validateEnvironmentVariables() {
    const requiredVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
      'CLERK_SECRET_KEY'
    ];

    const optionalVars = [
      'OPENROUTER_API_KEY',
      'RESEND_API_KEY',
      'NOTION_API_TOKEN',
      'SLACK_CLIENT_SECRET'
    ];

    for (const varName of requiredVars) {
      const value = process.env[varName];
      if (value) {
        this.addResult('Environment Variables', varName, 'PASS', 'Variable is set');
      } else {
        this.addResult('Environment Variables', varName, 'FAIL', 'Required variable is missing');
      }
    }

    for (const varName of optionalVars) {
      const value = process.env[varName];
      if (value) {
        this.addResult('Environment Variables', varName, 'PASS', 'Optional variable is set');
      } else {
        this.addResult('Environment Variables', varName, 'WARNING', 'Optional variable is not set');
      }
    }
  }

  validateClerkConfiguration() {
    const publishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;
    const secretKey = process.env.CLERK_SECRET_KEY;

    if (publishableKey) {
      if (publishableKey.startsWith('pk_test_') || publishableKey.startsWith('pk_live_')) {
        this.addResult('Clerk Configuration', 'Publishable Key Format', 'PASS', 'Valid key format');
      } else {
        this.addResult('Clerk Configuration', 'Publishable Key Format', 'FAIL', 'Invalid key format');
      }

      if (publishableKey.length > 50) {
        this.addResult('Clerk Configuration', 'Publishable Key Length', 'PASS', 'Key length is valid');
      } else {
        this.addResult('Clerk Configuration', 'Publishable Key Length', 'FAIL', 'Key appears too short');
      }
    }

    if (secretKey) {
      if (secretKey.startsWith('sk_test_') || secretKey.startsWith('sk_live_')) {
        this.addResult('Clerk Configuration', 'Secret Key Format', 'PASS', 'Valid key format');
      } else {
        this.addResult('Clerk Configuration', 'Secret Key Format', 'FAIL', 'Invalid key format');
      }
    }
  }

  validateCodeFixes() {
    // Check ClerkWrapper fixes
    try {
      const clerkWrapperContent = fs.readFileSync('components/ClerkWrapper.tsx', 'utf8');
      if (clerkWrapperContent.includes('validateClerkKeys') && clerkWrapperContent.includes('publishableKey={publishableKey}')) {
        this.addResult('Code Fixes', 'ClerkWrapper', 'PASS', 'ClerkWrapper has been fixed');
      } else {
        this.addResult('Code Fixes', 'ClerkWrapper', 'FAIL', 'ClerkWrapper fixes not applied');
      }
    } catch (error) {
      this.addResult('Code Fixes', 'ClerkWrapper', 'FAIL', 'Could not read ClerkWrapper file');
    }

    // Check middleware fixes
    try {
      const middlewareContent = fs.readFileSync('middleware.ts', 'utf8');
      if (middlewareContent.includes('try {') && middlewareContent.includes('addSecurityHeaders')) {
        this.addResult('Code Fixes', 'Middleware', 'PASS', 'Middleware has been fixed');
      } else {
        this.addResult('Code Fixes', 'Middleware', 'FAIL', 'Middleware fixes not applied');
      }
    } catch (error) {
      this.addResult('Code Fixes', 'Middleware', 'FAIL', 'Could not read middleware file');
    }

    // Check dashboard API fixes
    try {
      const dashboardContent = fs.readFileSync('app/api/dashboard/route.ts', 'utf8');
      if (dashboardContent.includes('try {') && dashboardContent.includes('summariesData = []')) {
        this.addResult('Code Fixes', 'Dashboard API', 'PASS', 'Dashboard API has been fixed');
      } else {
        this.addResult('Code Fixes', 'Dashboard API', 'FAIL', 'Dashboard API fixes not applied');
      }
    } catch (error) {
      this.addResult('Code Fixes', 'Dashboard API', 'FAIL', 'Could not read dashboard API file');
    }
  }

  validatePackageJson() {
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      if (packageJson.scripts['test:flow']) {
        this.addResult('Package Configuration', 'Test Script', 'PASS', 'test:flow script added');
      } else {
        this.addResult('Package Configuration', 'Test Script', 'FAIL', 'test:flow script missing');
      }

      const requiredDeps = ['@clerk/nextjs', '@supabase/supabase-js', 'next', 'react'];
      for (const dep of requiredDeps) {
        if (packageJson.dependencies[dep]) {
          this.addResult('Package Configuration', dep, 'PASS', 'Dependency present');
        } else {
          this.addResult('Package Configuration', dep, 'FAIL', 'Required dependency missing');
        }
      }
    } catch (error) {
      this.addResult('Package Configuration', 'package.json', 'FAIL', 'Could not read package.json');
    }
  }

  async runValidation() {
    console.log('🔍 Validating Setup and Fixes...\n');

    this.validateFileStructure();
    this.validateEnvironmentVariables();
    this.validateClerkConfiguration();
    this.validateCodeFixes();
    this.validatePackageJson();

    this.printResults();
  }

  private printResults() {
    console.log('📊 Validation Results:');
    console.log('======================\n');

    let totalPassed = 0;
    let totalFailed = 0;
    let totalWarnings = 0;

    for (const result of this.results) {
      console.log(`📁 ${result.category}:`);
      
      for (const check of result.checks) {
        const icon = check.status === 'PASS' ? '✅' : check.status === 'FAIL' ? '❌' : '⚠️';
        console.log(`  ${icon} ${check.name}: ${check.message}`);
        
        if (check.status === 'PASS') totalPassed++;
        else if (check.status === 'FAIL') totalFailed++;
        else totalWarnings++;
      }
      console.log('');
    }

    console.log('📈 Summary:');
    console.log(`✅ Passed: ${totalPassed}`);
    console.log(`❌ Failed: ${totalFailed}`);
    console.log(`⚠️ Warnings: ${totalWarnings}`);

    if (totalFailed === 0) {
      console.log('\n🎉 All critical validations passed!');
      console.log('🚀 Your application is ready to run:');
      console.log('   npm run dev');
    } else {
      console.log('\n🔧 Please fix the failed validations before running the application.');
    }
  }
}

// Run validation
if (require.main === module) {
  const validator = new SetupValidator();
  validator.runValidation()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Validation failed:', error);
      process.exit(1);
    });
}

export { SetupValidator };
