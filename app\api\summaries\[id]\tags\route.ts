import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { extractSummaryTags, getSummaryTags } from '@/lib/ai-tagging';
import { SentryTracker } from '@/lib/sentry.client';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';

/**
 * GET /api/summaries/[id]/tags
 * Get tags for a specific summary
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: summaryId } = await params;

    if (!summaryId) {
      return NextResponse.json(
        { error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    // Production demo mode - use demo user context
  devLog.log('📄 Get Summary Tags API called - Production Demo Mode:', summaryId);

    // Check if this is a demo summary
    if (!summaryId.startsWith('demo-summary-')) {
      return NextResponse.json(
        { error: 'Summary not found' },
        { status: 404 }
      );
    }

    // Return demo tags for demo summaries
    const demoTags = [
      'engineering', 'planning', 'q4', 'microservices', 'architecture',
      'product', 'mobile', 'roadmap', 'features', 'user-feedback',
      'marketing', 'analysis', 'performance', 'campaigns'
    ];

    // Get relevant tags based on summary ID
    let relevantTags = [];
    if (summaryId.includes('1')) {
      relevantTags = ['engineering', 'planning', 'q4', 'microservices', 'architecture'];
    } else if (summaryId.includes('2')) {
      relevantTags = ['product', 'mobile', 'roadmap', 'features', 'user-feedback'];
    } else if (summaryId.includes('3')) {
      relevantTags = ['marketing', 'analysis', 'performance', 'campaigns'];
    } else {
      relevantTags = ['demo', 'generated', 'test'];
    }

    return NextResponse.json({
      success: true,
      data: {
        summary_id: summaryId,
        tags: relevantTags,
        has_tags: true
      }
    });

  } catch (error) {
    console.error('Get summary tags API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/summaries/[id]/tags
 * Generate tags for a specific summary
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: summaryId } = await params;

    if (!summaryId) {
      return NextResponse.json(
        { error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    // Production demo mode - generate tags for demo summaries
  devLog.log('📄 Generate Summary Tags API called - Production Demo Mode:', summaryId);

    // Check if this is a demo summary
    if (!summaryId.startsWith('demo-summary-')) {
      return NextResponse.json(
        { error: 'Summary not found' },
        { status: 404 }
      );
    }

    // Simulate AI tag generation with realistic processing time
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Generate realistic tags based on summary ID
    let generatedTags = [];
    let confidence = 0.92;

    if (summaryId.includes('1')) {
      generatedTags = ['engineering', 'planning', 'q4', 'microservices', 'architecture', 'sprint'];
      confidence = 0.94;
    } else if (summaryId.includes('2')) {
      generatedTags = ['product', 'mobile', 'roadmap', 'features', 'user-feedback', 'ux'];
      confidence = 0.89;
    } else if (summaryId.includes('3')) {
      generatedTags = ['marketing', 'analysis', 'performance', 'campaigns', 'roi', 'metrics'];
      confidence = 0.96;
    } else {
      generatedTags = ['demo', 'generated', 'test', 'ai', 'summary'];
      confidence = 1.0;
    }

    const taggingResult = {
      success: true,
      tags: generatedTags,
      confidence: confidence,
      processing_time: 1500,
      model_used: 'gpt-4o-mini'
    };

    // Demo mode always succeeds
    return NextResponse.json({
      success: true,
      data: {
        summary_id: summaryId,
        tags: taggingResult.tags,
        processing_time_ms: taggingResult.processing_time,
        message: 'Tags extracted successfully'
      }
    });

  } catch (error) {
    console.error('Generate summary tags API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/summaries/[id]/tags
 * Delete tags for a specific summary
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: summaryId } = await params;

    if (!summaryId) {
      return NextResponse.json(
        { error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    // Production demo mode - simulate tag deletion
  devLog.log('📄 Delete Summary Tags API called - Production Demo Mode:', summaryId);

    // Check if this is a demo summary
    if (!summaryId.startsWith('demo-summary-')) {
      return NextResponse.json(
        { error: 'Summary not found' },
        { status: 404 }
      );
    }

    // Simulate tag deletion for demo mode
  devLog.log('📄 Demo tags deleted for summary:', summaryId);

    return NextResponse.json({
      success: true,
      message: 'Tags deleted successfully (Demo Mode)'
    });

  } catch (error) {
    console.error('Delete summary tags API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
