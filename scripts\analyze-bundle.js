#!/usr/bin/env node

/**
 * Bundle Analysis Script for Slack Summary Scribe
 * 
 * This script analyzes the Next.js build output to identify:
 * - Large bundles and chunks
 * - Unused dependencies
 * - Optimization opportunities
 * - Performance recommendations
 */

const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

class BundleAnalyzer {
  constructor() {
    this.buildDir = path.join(process.cwd(), '.next');
    this.staticDir = path.join(this.buildDir, 'static');
    this.chunksDir = path.join(this.staticDir, 'chunks');
    this.results = {
      totalSize: 0,
      chunks: [],
      pages: [],
      recommendations: []
    };
  }

  async analyze() {
    console.log(chalk.blue('🔍 Analyzing bundle size and performance...'));
    console.log('');

    if (!fs.existsSync(this.buildDir)) {
      console.log(chalk.red('❌ Build directory not found. Run `npm run build` first.'));
      return;
    }

    await this.analyzeChunks();
    await this.analyzePages();
    await this.generateRecommendations();
    this.printResults();
  }

  async analyzeChunks() {
    console.log(chalk.yellow('📦 Analyzing chunks...'));
    
    if (!fs.existsSync(this.chunksDir)) {
      console.log(chalk.yellow('⚠️  Chunks directory not found'));
      return;
    }

    const chunkFiles = fs.readdirSync(this.chunksDir)
      .filter(file => file.endsWith('.js'))
      .map(file => {
        const filePath = path.join(this.chunksDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: stats.size,
          sizeKB: Math.round(stats.size / 1024),
          path: filePath
        };
      })
      .sort((a, b) => b.size - a.size);

    this.results.chunks = chunkFiles;
    this.results.totalSize = chunkFiles.reduce((total, chunk) => total + chunk.size, 0);

    // Identify large chunks
    const largeChunks = chunkFiles.filter(chunk => chunk.sizeKB > 100);
    if (largeChunks.length > 0) {
      this.results.recommendations.push({
        type: 'warning',
        title: 'Large chunks detected',
        description: `${largeChunks.length} chunks are larger than 100KB`,
        chunks: largeChunks.map(c => `${c.name} (${c.sizeKB}KB)`)
      });
    }

    console.log(`✅ Analyzed ${chunkFiles.length} chunks (${Math.round(this.results.totalSize / 1024)}KB total)`);
  }

  async analyzePages() {
    console.log(chalk.yellow('📄 Analyzing pages...'));
    
    const pagesManifest = path.join(this.buildDir, 'server', 'pages-manifest.json');
    const appManifest = path.join(this.buildDir, 'app-build-manifest.json');
    
    // Analyze app router pages
    if (fs.existsSync(appManifest)) {
      try {
        const manifest = JSON.parse(fs.readFileSync(appManifest, 'utf8'));
        this.results.pages = Object.entries(manifest.pages || {}).map(([route, data]) => ({
          route,
          type: 'app',
          data
        }));
      } catch (error) {
        console.log(chalk.yellow('⚠️  Could not parse app manifest'));
      }
    }

    console.log(`✅ Analyzed ${this.results.pages.length} pages`);
  }

  async generateRecommendations() {
    console.log(chalk.yellow('💡 Generating recommendations...'));

    // Check for potential optimizations
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

    // Check for heavy dependencies
    const heavyDeps = [
      'moment', 'lodash', 'axios', 'jquery', 'bootstrap'
    ].filter(dep => dependencies[dep]);

    if (heavyDeps.length > 0) {
      this.results.recommendations.push({
        type: 'optimization',
        title: 'Heavy dependencies detected',
        description: 'Consider lighter alternatives',
        items: heavyDeps.map(dep => `${dep} → Consider alternatives like date-fns, native fetch, etc.`)
      });
    }

    // Check for unused dependencies
    const unusedDeps = await this.findUnusedDependencies();
    if (unusedDeps.length > 0) {
      this.results.recommendations.push({
        type: 'cleanup',
        title: 'Potentially unused dependencies',
        description: 'These dependencies might not be used',
        items: unusedDeps
      });
    }

    // Performance recommendations
    this.results.recommendations.push({
      type: 'performance',
      title: 'Performance optimizations',
      description: 'Recommended optimizations',
      items: [
        'Enable gzip compression in production',
        'Use dynamic imports for large components',
        'Implement proper image optimization',
        'Add service worker for caching',
        'Use React.memo for expensive components'
      ]
    });

    console.log(`✅ Generated ${this.results.recommendations.length} recommendations`);
  }

  async findUnusedDependencies() {
    // Simple check for unused dependencies
    // In a real implementation, this would be more sophisticated
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = Object.keys(packageJson.dependencies || {});
    const unusedDeps = [];

    // Check if dependency is imported in any file
    for (const dep of dependencies) {
      const isUsed = await this.isDependencyUsed(dep);
      if (!isUsed) {
        unusedDeps.push(dep);
      }
    }

    return unusedDeps.slice(0, 5); // Limit to 5 for brevity
  }

  async isDependencyUsed(dependency) {
    // Simple check - look for import statements
    // This is a basic implementation
    try {
      const { execSync } = require('child_process');
      // Use findstr on Windows instead of grep
      const isWindows = process.platform === 'win32';

      if (isWindows) {
        try {
          const result = execSync(`findstr /s /m "from '${dependency}'" app\\*.* components\\*.* lib\\*.* 2>nul`, { encoding: 'utf8' });
          const result2 = execSync(`findstr /s /m "import '${dependency}'" app\\*.* components\\*.* lib\\*.* 2>nul`, { encoding: 'utf8' });
          return result.trim().length > 0 || result2.trim().length > 0;
        } catch (error) {
          return true; // Assume used if we can't check
        }
      } else {
        const result = execSync(`grep -r "from '${dependency}'" app/ components/ lib/ 2>/dev/null || true`, { encoding: 'utf8' });
        const result2 = execSync(`grep -r "import '${dependency}'" app/ components/ lib/ 2>/dev/null || true`, { encoding: 'utf8' });
        return result.trim().length > 0 || result2.trim().length > 0;
      }
    } catch (error) {
      return true; // Assume used if we can't check
    }
  }

  printResults() {
    console.log('');
    console.log(chalk.blue('📊 Bundle Analysis Results'));
    console.log('='.repeat(50));

    // Summary
    console.log(chalk.green('📈 Summary:'));
    console.log(`Total bundle size: ${chalk.bold(Math.round(this.results.totalSize / 1024))}KB`);
    console.log(`Number of chunks: ${chalk.bold(this.results.chunks.length)}`);
    console.log(`Number of pages: ${chalk.bold(this.results.pages.length)}`);
    console.log('');

    // Top 10 largest chunks
    console.log(chalk.green('📦 Largest Chunks:'));
    this.results.chunks.slice(0, 10).forEach((chunk, index) => {
      const sizeColor = chunk.sizeKB > 200 ? chalk.red : chunk.sizeKB > 100 ? chalk.yellow : chalk.green;
      console.log(`${index + 1}. ${chunk.name} - ${sizeColor(chunk.sizeKB + 'KB')}`);
    });
    console.log('');

    // Recommendations
    console.log(chalk.green('💡 Recommendations:'));
    this.results.recommendations.forEach((rec, index) => {
      const icon = rec.type === 'warning' ? '⚠️' : rec.type === 'optimization' ? '🚀' : rec.type === 'cleanup' ? '🧹' : '💡';
      console.log(`${icon} ${chalk.bold(rec.title)}`);
      console.log(`   ${rec.description}`);
      
      if (rec.items) {
        rec.items.slice(0, 3).forEach(item => {
          console.log(`   • ${item}`);
        });
        if (rec.items.length > 3) {
          console.log(`   • ... and ${rec.items.length - 3} more`);
        }
      }
      
      if (rec.chunks) {
        rec.chunks.slice(0, 3).forEach(chunk => {
          console.log(`   • ${chunk}`);
        });
      }
      console.log('');
    });

    // Performance score
    const performanceScore = this.calculatePerformanceScore();
    const scoreColor = performanceScore >= 80 ? chalk.green : performanceScore >= 60 ? chalk.yellow : chalk.red;
    console.log(`${scoreColor('🎯 Performance Score:')} ${scoreColor(performanceScore + '/100')}`);
    console.log('');

    // Next steps
    console.log(chalk.blue('🚀 Next Steps:'));
    console.log('1. Run `npm run build` to see latest bundle sizes');
    console.log('2. Use `npm run analyze` for detailed webpack bundle analysis');
    console.log('3. Consider implementing code splitting for large components');
    console.log('4. Review and remove unused dependencies');
    console.log('5. Optimize images and static assets');
  }

  calculatePerformanceScore() {
    let score = 100;
    
    // Penalize large total bundle size
    const totalSizeMB = this.results.totalSize / (1024 * 1024);
    if (totalSizeMB > 2) score -= 20;
    else if (totalSizeMB > 1) score -= 10;
    
    // Penalize large individual chunks
    const largeChunks = this.results.chunks.filter(c => c.sizeKB > 200);
    score -= largeChunks.length * 10;
    
    // Penalize too many chunks
    if (this.results.chunks.length > 50) score -= 15;
    else if (this.results.chunks.length > 30) score -= 10;
    
    return Math.max(0, Math.min(100, score));
  }
}

// CLI execution
if (require.main === module) {
  const analyzer = new BundleAnalyzer();
  analyzer.analyze().catch(error => {
    console.error(chalk.red('❌ Analysis failed:'), error.message);
    process.exit(1);
  });
}

module.exports = BundleAnalyzer;
