# 🧑‍💼 Admin UI

[![npm version](https://badge.fury.io/js/%40saas-kit%2Fadmin-ui.svg)](https://badge.fury.io/js/%40saas-kit%2Fadmin-ui)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/%3C%2F%3E-TypeScript-%230074c1.svg)](http://www.typescriptlang.org/)

Production-ready admin dashboard components for SaaS applications. Built with React, TypeScript, and Tailwind CSS.

## ✨ Features

- 👥 **User Management**: Search, filter, suspend, and analytics
- 📊 **Analytics Dashboard**: Revenue, usage, and growth metrics
- 🎛️ **System Monitoring**: Health checks, logs, and alerts
- 🎫 **Support Tools**: Ticket management and customer communication
- 🚀 **Feature Flags**: Dynamic feature control
- 📧 **Email Campaigns**: Marketing automation interface
- 🔒 **Audit Logs**: Complete action tracking
- 📱 **Responsive**: Mobile-optimized interface

## 🚀 Quick Start

### Installation

```bash
npm install @saas-kit/admin-ui
# or
yarn add @saas-kit/admin-ui
# or
pnpm add @saas-kit/admin-ui
```

### Basic Usage

```tsx
import { AdminDashboard, UserTable, AnalyticsChart } from '@saas-kit/admin-ui';

function App() {
  return (
    <AdminDashboard>
      <UserTable 
        users={users}
        onUserAction={(action, userId) => {
          console.log(`${action} user ${userId}`);
        }}
      />
      
      <AnalyticsChart
        data={analyticsData}
        type="revenue"
        timeRange="30d"
      />
    </AdminDashboard>
  );
}
```

## 📦 Components

### Dashboard Layout

```tsx
import { AdminDashboard, Sidebar, Header } from '@saas-kit/admin-ui';

<AdminDashboard>
  <Sidebar
    navigation={[
      { name: 'Dashboard', href: '/', icon: 'home' },
      { name: 'Users', href: '/users', icon: 'users' },
      { name: 'Analytics', href: '/analytics', icon: 'chart' }
    ]}
  />
  
  <Header
    user={currentUser}
    notifications={notifications}
    onLogout={() => signOut()}
  />
  
  {/* Your content */}
</AdminDashboard>
```

### User Management

```tsx
import { UserTable, UserFilters, UserActions } from '@saas-kit/admin-ui';

<UserTable
  users={users}
  loading={loading}
  pagination={{
    page: 1,
    pageSize: 50,
    total: 1000
  }}
  onUserSelect={(users) => setSelectedUsers(users)}
  onUserAction={(action, userId) => {
    switch (action) {
      case 'suspend':
        suspendUser(userId);
        break;
      case 'delete':
        deleteUser(userId);
        break;
      case 'impersonate':
        impersonateUser(userId);
        break;
    }
  }}
/>

<UserFilters
  filters={filters}
  onFilterChange={setFilters}
  options={{
    plans: ['free', 'pro', 'enterprise'],
    status: ['active', 'suspended', 'deleted'],
    dateRange: true
  }}
/>
```

### Analytics & Metrics

```tsx
import { 
  AnalyticsChart, 
  MetricCard, 
  RevenueChart,
  UserGrowthChart 
} from '@saas-kit/admin-ui';

<div className="grid grid-cols-1 md:grid-cols-4 gap-6">
  <MetricCard
    title="Total Revenue"
    value="$45,231"
    change="+12.5%"
    trend="up"
    icon="dollar-sign"
  />
  
  <MetricCard
    title="Active Users"
    value="2,345"
    change="+5.2%"
    trend="up"
    icon="users"
  />
</div>

<RevenueChart
  data={revenueData}
  timeRange="90d"
  breakdown="monthly"
  showProjection={true}
/>

<UserGrowthChart
  data={userGrowthData}
  cohorts={true}
  retention={true}
/>
```

### System Monitoring

```tsx
import { 
  SystemHealth, 
  LogViewer, 
  AlertPanel,
  ServiceStatus 
} from '@saas-kit/admin-ui';

<SystemHealth
  services={[
    { name: 'Database', status: 'healthy', latency: 45 },
    { name: 'Cache', status: 'degraded', latency: 120 },
    { name: 'API', status: 'healthy', latency: 32 }
  ]}
  onServiceClick={(service) => showServiceDetails(service)}
/>

<LogViewer
  logs={logs}
  filters={{
    level: ['error', 'warn', 'info'],
    service: 'all',
    timeRange: '1h'
  }}
  realTime={true}
/>

<AlertPanel
  alerts={alerts}
  onAlertAction={(action, alertId) => {
    if (action === 'acknowledge') {
      acknowledgeAlert(alertId);
    }
  }}
/>
```

### Support Tools

```tsx
import { 
  TicketList, 
  CustomerProfile, 
  SupportChat 
} from '@saas-kit/admin-ui';

<TicketList
  tickets={tickets}
  filters={{
    status: 'open',
    priority: 'high',
    assignee: 'me'
  }}
  onTicketSelect={(ticket) => setSelectedTicket(ticket)}
/>

<CustomerProfile
  customer={customer}
  tabs={['overview', 'billing', 'usage', 'support']}
  actions={[
    { label: 'Impersonate', action: 'impersonate' },
    { label: 'Refund', action: 'refund' },
    { label: 'Upgrade', action: 'upgrade' }
  ]}
/>
```

## 🎨 Theming

### Custom Theme

```tsx
import { AdminDashboard, ThemeProvider } from '@saas-kit/admin-ui';

const customTheme = {
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444'
  },
  fonts: {
    sans: ['Inter', 'sans-serif'],
    mono: ['JetBrains Mono', 'monospace']
  }
};

<ThemeProvider theme={customTheme}>
  <AdminDashboard>
    {/* Your components */}
  </AdminDashboard>
</ThemeProvider>
```

### Dark Mode

```tsx
import { AdminDashboard } from '@saas-kit/admin-ui';

<AdminDashboard darkMode={true}>
  {/* Components automatically adapt to dark mode */}
</AdminDashboard>
```

## 🔧 Configuration

### Data Providers

```tsx
import { AdminProvider } from '@saas-kit/admin-ui';

const dataProvider = {
  getUsers: async (params) => {
    const response = await fetch('/api/admin/users', {
      method: 'POST',
      body: JSON.stringify(params)
    });
    return response.json();
  },
  
  updateUser: async (userId, data) => {
    const response = await fetch(`/api/admin/users/${userId}`, {
      method: 'PATCH',
      body: JSON.stringify(data)
    });
    return response.json();
  },
  
  getAnalytics: async (params) => {
    const response = await fetch('/api/admin/analytics', {
      method: 'POST',
      body: JSON.stringify(params)
    });
    return response.json();
  }
};

<AdminProvider dataProvider={dataProvider}>
  <AdminDashboard>
    {/* Your components */}
  </AdminDashboard>
</AdminProvider>
```

### Permissions

```tsx
import { PermissionProvider, usePermissions } from '@saas-kit/admin-ui';

const permissions = {
  users: {
    read: true,
    write: true,
    delete: false
  },
  analytics: {
    read: true,
    export: false
  }
};

<PermissionProvider permissions={permissions}>
  <AdminDashboard>
    {/* Components respect permissions automatically */}
  </AdminDashboard>
</PermissionProvider>

// In components
function UserActions() {
  const { can } = usePermissions();
  
  return (
    <div>
      {can('users', 'write') && (
        <button>Edit User</button>
      )}
      {can('users', 'delete') && (
        <button>Delete User</button>
      )}
    </div>
  );
}
```

## 📱 Responsive Design

All components are mobile-responsive by default:

```tsx
// Automatically adapts to screen size
<UserTable
  responsive={true}
  mobileView="cards" // or "table"
  breakpoint="md"
/>

// Custom responsive behavior
<AnalyticsChart
  responsive={{
    mobile: { height: 200, showLegend: false },
    tablet: { height: 300, showLegend: true },
    desktop: { height: 400, showLegend: true }
  }}
/>
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage

# Run Storybook
npm run storybook
```

## 📚 Storybook

View all components in Storybook:

```bash
npm run storybook
```

Visit [http://localhost:6006](http://localhost:6006) to see component documentation and examples.

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📖 [Documentation](https://docs.saas-kit.dev/admin-ui)
- 💬 [Discord Community](https://discord.gg/saas-kit)
- 🐛 [Issue Tracker](https://github.com/saas-kit/admin-ui/issues)

---

**Star ⭐ this repo if you find it helpful!**
