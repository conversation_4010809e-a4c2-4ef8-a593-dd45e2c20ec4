version: '3.8'

services:
  # Main Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - REDIS_URL=redis://redis:6379
      - QUEUE_SERVICE_URL=http://queue-service:3001
    depends_on:
      - redis
      - queue-service
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - saas-network

  # Queue Service
  queue-service:
    build:
      context: ./microservices/queue-service
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - REDIS_URL=redis://redis:6379
      - PORT=3001
      - LOG_LEVEL=info
    depends_on:
      - redis
    volumes:
      - ./microservices/queue-service:/app
      - /app/node_modules
    networks:
      - saas-network
    restart: unless-stopped

  # Redis for caching and queues
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - saas-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Redis Commander (Redis GUI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - saas-network
    profiles:
      - tools

  # Prometheus for metrics
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - saas-network
    profiles:
      - monitoring

  # Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - saas-network
    profiles:
      - monitoring

  # Nginx for load balancing (production-like setup)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
      - queue-service
    networks:
      - saas-network
    profiles:
      - production

volumes:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  saas-network:
    driver: bridge
