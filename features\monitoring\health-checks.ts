/**
 * Comprehensive Health Monitoring System
 * 
 * Monitors all critical systems and provides detailed health reports
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';

export interface HealthCheck {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  message?: string;
  details?: Record<string, any>;
  timestamp: string;
}

export interface SystemHealth {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  checks: HealthCheck[];
  summary: {
    healthy: number;
    degraded: number;
    unhealthy: number;
    totalResponseTime: number;
    averageResponseTime: number;
  };
  timestamp: string;
}

// Health check thresholds
const THRESHOLDS = {
  database: { warning: 500, critical: 2000 }, // ms
  slack: { warning: 2000, critical: 5000 },
  auth: { warning: 300, critical: 1000 },
};

/**
 * Run all health checks and return comprehensive status
 */
export async function runHealthChecks(): Promise<SystemHealth> {
  const startTime = Date.now();
  const checks: HealthCheck[] = [];

  // Run all checks in parallel for speed (excluding Stripe in demo mode)
  const [
    databaseHealth,
    authHealth,
    slackHealth,
    storageHealth
  ] = await Promise.allSettled([
    checkDatabaseHealth(),
    checkAuthHealth(),
    checkSlackHealth(),
    checkStorageHealth()
  ]);

  // Process results
  if (databaseHealth.status === 'fulfilled') checks.push(databaseHealth.value);
  if (authHealth.status === 'fulfilled') checks.push(authHealth.value);
  if (slackHealth.status === 'fulfilled') checks.push(slackHealth.value);
  if (storageHealth.status === 'fulfilled') checks.push(storageHealth.value);

  // Handle failed checks
  if (databaseHealth.status === 'rejected') {
    checks.push(createFailedCheck('database', databaseHealth.reason));
  }
  if (authHealth.status === 'rejected') {
    checks.push(createFailedCheck('auth', authHealth.reason));
  }
  if (slackHealth.status === 'rejected') {
    checks.push(createFailedCheck('slack', slackHealth.reason));
  }
  if (storageHealth.status === 'rejected') {
    checks.push(createFailedCheck('storage', storageHealth.reason));
  }

  // Calculate summary
  const summary = {
    healthy: checks.filter(c => c.status === 'healthy').length,
    degraded: checks.filter(c => c.status === 'degraded').length,
    unhealthy: checks.filter(c => c.status === 'unhealthy').length,
    totalResponseTime: checks.reduce((sum, c) => sum + c.responseTime, 0),
    averageResponseTime: checks.length > 0 ? 
      Math.round(checks.reduce((sum, c) => sum + c.responseTime, 0) / checks.length) : 0
  };

  // Determine overall health
  let overall: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
  if (summary.unhealthy > 0) {
    overall = 'unhealthy';
  } else if (summary.degraded > 0) {
    overall = 'degraded';
  }

  return {
    overall,
    checks,
    summary,
    timestamp: new Date().toISOString()
  };
}

/**
 * Check Supabase database health
 */
async function checkDatabaseHealth(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    const supabase = await createSupabaseServerClient();
    
    // Simple query to test connection and performance
    const { data, error } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    const responseTime = Date.now() - startTime;

    if (error) {
      return {
        name: 'database',
        status: 'unhealthy',
        responseTime,
        message: `Database query failed: ${error.message}`,
        timestamp: new Date().toISOString()
      };
    }

    const status = responseTime > THRESHOLDS.database.critical ? 'unhealthy' :
                  responseTime > THRESHOLDS.database.warning ? 'degraded' : 'healthy';

    return {
      name: 'database',
      status,
      responseTime,
      message: status === 'healthy' ? 'Database responding normally' : 
               `Database response time: ${responseTime}ms`,
      details: { queryTime: responseTime, recordsFound: data?.length || 0 },
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      name: 'database',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      timestamp: new Date().toISOString()
    };
  }
}


/**
 * Check authentication system health
 */
async function checkAuthHealth(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    const supabase = await createSupabaseServerClient();
    
    // Test auth system by checking session
    const { data, error } = await supabase.auth.getSession();
    const responseTime = Date.now() - startTime;

    if (error) {
      return {
        name: 'auth',
        status: 'degraded',
        responseTime,
        message: `Auth check warning: ${error.message}`,
        timestamp: new Date().toISOString()
      };
    }

    const status = responseTime > THRESHOLDS.auth.critical ? 'unhealthy' :
                  responseTime > THRESHOLDS.auth.warning ? 'degraded' : 'healthy';

    return {
      name: 'auth',
      status,
      responseTime,
      message: status === 'healthy' ? 'Auth system responding normally' : 
               `Auth response time: ${responseTime}ms`,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      name: 'auth',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      message: `Auth system failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Check Slack API health (if configured)
 */
async function checkSlackHealth(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    // Simple test - we'll just check if we can make a basic request
    // In production, you might want to test with a real workspace
    const responseTime = Date.now() - startTime;

    return {
      name: 'slack',
      status: 'healthy',
      responseTime,
      message: 'Slack integration available',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      name: 'slack',
      status: 'degraded',
      responseTime: Date.now() - startTime,
      message: `Slack API check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Check file storage health
 */
async function checkStorageHealth(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    const supabase = await createSupabaseServerClient();
    
    // Test storage by listing buckets
    const { data, error } = await supabase.storage.listBuckets();
    const responseTime = Date.now() - startTime;

    if (error) {
      return {
        name: 'storage',
        status: 'degraded',
        responseTime,
        message: `Storage check warning: ${error.message}`,
        timestamp: new Date().toISOString()
      };
    }

    return {
      name: 'storage',
      status: 'healthy',
      responseTime,
      message: 'Storage system responding normally',
      details: { bucketsCount: data?.length || 0 },
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      name: 'storage',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      message: `Storage system failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Create a failed health check
 */
function createFailedCheck(name: string, error: any): HealthCheck {
  return {
    name,
    status: 'unhealthy',
    responseTime: 0,
    message: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    timestamp: new Date().toISOString()
  };
}

/**
 * Get health status for a specific service
 */
export async function getServiceHealth(serviceName: string): Promise<HealthCheck | null> {
  const health = await runHealthChecks();
  return health.checks.find(check => check.name === serviceName) || null;
}

/**
 * Check if system is healthy enough for new user signups
 */
export async function isSystemHealthyForSignups(): Promise<boolean> {
  const health = await runHealthChecks();

  // Require database and auth to be healthy for signups
  const databaseCheck = health.checks.find(c => c.name === 'database');
  const authCheck = health.checks.find(c => c.name === 'auth');

  return databaseCheck?.status === 'healthy' && authCheck?.status === 'healthy';
}
