#!/usr/bin/env node

/**
 * Environment Monitoring Validation Script
 * 
 * This script validates that all monitoring environment variables
 * are properly configured for production deployment.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function logHeader(text) {
  console.log(`\n${colors.cyan}${colors.bright}${text}${colors.reset}`);
  console.log('='.repeat(text.length));
}

function logSuccess(text) {
  console.log(`${colors.green}✅ ${text}${colors.reset}`);
}

function logError(text) {
  console.log(`${colors.red}❌ ${text}${colors.reset}`);
}

function logWarning(text) {
  console.log(`${colors.yellow}⚠️  ${text}${colors.reset}`);
}

// Load environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  
  if (!fs.existsSync(envPath)) {
    logError('.env.local file not found');
    return {};
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key] = valueParts.join('=');
      }
    }
  });
  
  return env;
}

// Validation functions
function validatePostHog(env) {
  logHeader('POSTHOG ANALYTICS VALIDATION');
  
  let valid = true;
  
  if (env.NEXT_PUBLIC_POSTHOG_KEY) {
    if (env.NEXT_PUBLIC_POSTHOG_KEY.startsWith('phc_live_')) {
      logSuccess('NEXT_PUBLIC_POSTHOG_KEY: Production-ready format');
    } else if (env.NEXT_PUBLIC_POSTHOG_KEY.includes('placeholder')) {
      logWarning('NEXT_PUBLIC_POSTHOG_KEY: Contains placeholder - needs real key');
      valid = false;
    } else {
      logSuccess('NEXT_PUBLIC_POSTHOG_KEY: Configured');
    }
  } else {
    logError('NEXT_PUBLIC_POSTHOG_KEY: Missing');
    valid = false;
  }
  
  if (env.NEXT_PUBLIC_POSTHOG_HOST) {
    logSuccess('NEXT_PUBLIC_POSTHOG_HOST: Configured');
  } else {
    logWarning('NEXT_PUBLIC_POSTHOG_HOST: Missing (will use default)');
  }
  
  if (env.POSTHOG_API_KEY) {
    if (env.POSTHOG_API_KEY.startsWith('phx_live_')) {
      logSuccess('POSTHOG_API_KEY: Production-ready format');
    } else {
      logWarning('POSTHOG_API_KEY: May need production key');
    }
  } else {
    logWarning('POSTHOG_API_KEY: Missing (optional for basic analytics)');
  }
  
  return valid;
}

function validateSentry(env) {
  logHeader('SENTRY ERROR MONITORING VALIDATION');
  
  let valid = true;
  
  if (env.NEXT_PUBLIC_SENTRY_DSN) {
    if (env.NEXT_PUBLIC_SENTRY_DSN.startsWith('https://live_')) {
      logSuccess('NEXT_PUBLIC_SENTRY_DSN: Production-ready format');
    } else if (env.NEXT_PUBLIC_SENTRY_DSN.includes('placeholder')) {
      logWarning('NEXT_PUBLIC_SENTRY_DSN: Contains placeholder - needs real DSN');
      valid = false;
    } else if (env.NEXT_PUBLIC_SENTRY_DSN.startsWith('https://')) {
      logSuccess('NEXT_PUBLIC_SENTRY_DSN: Valid format');
    } else {
      logError('NEXT_PUBLIC_SENTRY_DSN: Invalid format');
      valid = false;
    }
  } else {
    logError('NEXT_PUBLIC_SENTRY_DSN: Missing');
    valid = false;
  }
  
  if (env.SENTRY_ORG) {
    logSuccess('SENTRY_ORG: Configured');
  } else {
    logWarning('SENTRY_ORG: Missing (needed for deployment)');
  }
  
  if (env.SENTRY_PROJECT) {
    logSuccess('SENTRY_PROJECT: Configured');
  } else {
    logWarning('SENTRY_PROJECT: Missing (needed for deployment)');
  }
  
  return valid;
}

function validateEnvironmentMode(env) {
  logHeader('ENVIRONMENT MODE VALIDATION');

  const nodeEnv = env.NODE_ENV || 'development';
  const devMode = env.NEXT_PUBLIC_DEV_MODE === 'true';
  const stripeTestMode = env.NEXT_PUBLIC_STRIPE_TEST_MODE === 'true';

  const isDevelopment = nodeEnv === 'development' || devMode;
  const isProduction = nodeEnv === 'production' && !devMode;

  console.log(`   NODE_ENV: ${nodeEnv}`);
  console.log(`   DEV_MODE: ${devMode}`);
  console.log(`   STRIPE_TEST_MODE: ${stripeTestMode}`);
  console.log(`   Detected Mode: ${isDevelopment ? 'Development' : isProduction ? 'Production' : 'Test'}`);

  if (isDevelopment) {
    logSuccess('Development mode detected - CSP headers will be disabled');
    logSuccess('All integrations enabled for testing');
    logSuccess('Stripe test mode: ' + (stripeTestMode ? 'enabled' : 'disabled'));
  } else if (isProduction) {
    logSuccess('Production mode detected - Full CSP headers will be applied');

    if (env.NEXT_PUBLIC_SITE_URL && env.NEXT_PUBLIC_SITE_URL.startsWith('https://')) {
      logSuccess('NEXT_PUBLIC_SITE_URL: Production HTTPS URL');
    } else {
      logError('NEXT_PUBLIC_SITE_URL: Missing or not HTTPS');
      return false;
    }
  }

  return true;
}

function validateSlackConfiguration(env) {
  logHeader('SLACK OAUTH VALIDATION');

  let valid = true;

  if (env.NEXT_PUBLIC_SLACK_CLIENT_ID) {
    logSuccess('NEXT_PUBLIC_SLACK_CLIENT_ID: Configured');
  } else {
    logError('NEXT_PUBLIC_SLACK_CLIENT_ID: Missing');
    valid = false;
  }

  if (env.SLACK_CLIENT_ID) {
    logSuccess('SLACK_CLIENT_ID: Configured');
  } else {
    logError('SLACK_CLIENT_ID: Missing');
    valid = false;
  }

  if (env.SLACK_CLIENT_SECRET) {
    logSuccess('SLACK_CLIENT_SECRET: Configured');
  } else {
    logError('SLACK_CLIENT_SECRET: Missing');
    valid = false;
  }

  if (env.SLACK_SIGNING_SECRET) {
    logSuccess('SLACK_SIGNING_SECRET: Configured');
  } else {
    logError('SLACK_SIGNING_SECRET: Missing');
    valid = false;
  }

  // Check redirect URLs
  console.log('\n   Required Slack OAuth Redirect URLs:');
  console.log('   - http://localhost:3000/auth/slack/callback');
  console.log('   - http://localhost:3001/auth/slack/callback');
  if (env.NEXT_PUBLIC_SITE_URL) {
    console.log(`   - ${env.NEXT_PUBLIC_SITE_URL.replace(/\/$/, '')}/auth/slack/callback`);
  }

  return valid;
}

function validateCSPConfiguration() {
  logHeader('CSP CONFIGURATION VALIDATION');

  const nextConfigPath = path.join(process.cwd(), 'next.config.mjs');

  if (!fs.existsSync(nextConfigPath)) {
    logError('next.config.mjs not found');
    return false;
  }

  const configContent = fs.readFileSync(nextConfigPath, 'utf8');

  // Check for environment-based CSP
  if (configContent.includes('process.env.NODE_ENV === \'production\'')) {
    logSuccess('Environment-based CSP configuration found');
  } else {
    logWarning('Environment-based CSP configuration not found');
  }

  // Check for required domains in CSP
  const requiredDomains = [
    'clerk.com',
    'stripe.com',
    'supabase.co',
    'posthog.com',
    'google.com',
    'cashfree.com'
  ];

  let allDomainsFound = true;
  requiredDomains.forEach(domain => {
    if (configContent.includes(domain)) {
      logSuccess(`CSP includes ${domain}`);
    } else {
      logWarning(`CSP missing ${domain}`);
      allDomainsFound = false;
    }
  });

  return allDomainsFound;
}

function validateAuthServices(env) {
  logHeader('AUTHENTICATION SERVICES VALIDATION');
  
  let valid = true;
  
  // Clerk
  if (env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY) {
    if (env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY.startsWith('pk_live_')) {
      logSuccess('CLERK_PUBLISHABLE_KEY: Production-ready format');
    } else {
      logWarning('CLERK_PUBLISHABLE_KEY: May need production key');
    }
  } else {
    logError('CLERK_PUBLISHABLE_KEY: Missing');
    valid = false;
  }
  
  if (env.CLERK_SECRET_KEY) {
    if (env.CLERK_SECRET_KEY.startsWith('sk_live_')) {
      logSuccess('CLERK_SECRET_KEY: Production-ready format');
    } else {
      logWarning('CLERK_SECRET_KEY: May need production key');
    }
  } else {
    logError('CLERK_SECRET_KEY: Missing');
    valid = false;
  }
  
  return valid;
}

function checkMonitoringFiles() {
  logHeader('MONITORING FILES CHECK');
  
  const files = [
    'sentry.client.config.ts',
    'sentry.server.config.ts',
    'lib/posthog.client.ts',
    'components/providers/PostHogProvider.tsx',
    'app/error.tsx',
    'app/global-error.tsx',
    'components/ChunkErrorBoundary.tsx',
    'lib/chunk-error-handler.ts',
    'middleware.ts',
    'next.config.mjs'
  ];
  
  let allExist = true;
  
  files.forEach(file => {
    if (fs.existsSync(path.join(process.cwd(), file))) {
      logSuccess(`${file}: Found`);
    } else {
      logError(`${file}: Missing`);
      allExist = false;
    }
  });
  
  return allExist;
}

// Main validation
async function main() {
  logHeader('🚀 MONITORING ENVIRONMENT VALIDATION');
  
  const env = loadEnvFile();
  
  const results = {
    environment: validateEnvironmentMode(env),
    posthog: validatePostHog(env),
    sentry: validateSentry(env),
    auth: validateAuthServices(env),
    slack: validateSlackConfiguration(env),
    csp: validateCSPConfiguration(),
    files: checkMonitoringFiles()
  };
  
  logHeader('📊 VALIDATION SUMMARY');
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([category, success]) => {
    if (success) {
      logSuccess(`${category.toUpperCase()}: Ready for production`);
    } else {
      logError(`${category.toUpperCase()}: Needs attention`);
    }
  });
  
  console.log(`\n${colors.bright}Result: ${passed}/${total} categories ready${colors.reset}`);
  
  if (passed === total) {
    logSuccess('🎉 All monitoring services are configured for production!');
    
    logHeader('🚀 DEPLOYMENT READY');
    console.log('✅ PostHog analytics configured');
    console.log('✅ Sentry error monitoring configured');
    console.log('✅ Production mode enabled');
    console.log('✅ Authentication services configured');
    console.log('✅ All monitoring files present');
    
    return true;
  } else {
    logError('❌ Some services need configuration before deployment');
    
    logHeader('🔧 NEXT STEPS');
    console.log('1. Replace placeholder keys with real production keys');
    console.log('2. Ensure all monitoring services are properly set up');
    console.log('3. Test monitoring in staging environment');
    console.log('4. Re-run this validation');
    
    return false;
  }
}

if (require.main === module) {
  main()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      logError(`Validation failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { main };
