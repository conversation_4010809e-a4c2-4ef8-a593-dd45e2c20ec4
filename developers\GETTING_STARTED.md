# 🚀 Developer Getting Started Guide

Welcome to the Slack Summary Scribe development environment! This guide will get you from zero to productive in under 15 minutes.

## 📋 Prerequisites

### Required Software
- **Node.js** 18.0.0+ ([Download](https://nodejs.org/))
- **Git** 2.30.0+ ([Download](https://git-scm.com/))
- **VS Code** (recommended) with extensions:
  - TypeScript and JavaScript Language Features
  - ESLint
  - Prettier - Code formatter
  - Tailwind CSS IntelliSense
  - GitLens — Git supercharged

### Required Accounts
- GitHub account with repository access
- Supabase project access
- Stripe account (test mode)

## ⚡ Quick Setup (5 minutes)

### 1. Clone and Install
```bash
# Clone the repository
git clone https://github.com/your-org/slack-summary-scribe.git
cd slack-summary-scribe

# Install dependencies (this may take 2-3 minutes)
npm install

# Bootstrap development environment
npm run dev:setup
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env.local

# Generate secure secrets
npm run generate-secrets

# The CLI will guide you through setting up:
# - Supabase credentials
# - Stripe test keys
# - OAuth app credentials
# - AI service keys
```

### 3. Database Setup
```bash
# Initialize database
npm run db:setup

# This will:
# - Run migrations
# - Seed test data
# - Create admin user
# - Set up RLS policies
```

### 4. Start Development
```bash
# Start all services
npm run dev

# This starts:
# - Next.js dev server (http://localhost:3000)
# - Storybook (http://localhost:6006)
# - Type checking
# - Linting
```

## 🏗️ Project Architecture

### Monorepo Structure
```
/
├── apps/
│   ├── slack-summary-scribe/    # Main Next.js application
│   ├── admin-dashboard/         # Admin interface
│   └── marketing-site/          # Marketing website
├── packages/
│   ├── integration-sdk/         # OAuth & export utilities
│   ├── admin-ui/               # Admin components
│   ├── drip-campaign-engine/   # Email automation
│   └── ui-kit/                 # Shared UI components
├── tools/
│   ├── eslint-config/          # Shared linting rules
│   └── typescript-config/      # Shared TS config
└── scripts/
    ├── dev-setup.js            # Development bootstrap
    └── generate-feature.js     # Feature scaffolding
```

### Tech Stack
- **Frontend**: Next.js 15, React 18, TypeScript, Tailwind CSS
- **Backend**: Next.js API routes, Supabase (PostgreSQL)
- **Auth**: NextAuth.js with Supabase adapter
- **Payments**: Stripe with webhook handling
- **AI**: OpenAI GPT-4 + DeepSeek for cost optimization
- **Monitoring**: Sentry + PostHog
- **Testing**: Vitest + Playwright + Storybook

## 🛠️ Development Workflow

### Daily Development
```bash
# Pull latest changes
git pull origin main

# Install any new dependencies
npm install

# Start development
npm run dev

# Run tests in watch mode
npm run test:watch
```

### Making Changes
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make your changes...

# Run quality checks
npm run lint:fix
npm run type-check
npm run test

# Commit with conventional format
git commit -m "feat: add your feature description"

# Push and create PR
git push origin feature/your-feature-name
```

### Testing
```bash
# Unit tests
npm run test:unit

# Integration tests  
npm run test:integration

# E2E tests
npm run test:e2e

# Visual regression tests
npm run test:visual

# All tests with coverage
npm run test:coverage
```

## 🎯 Common Development Tasks

### Adding a New Feature
```bash
# Use the feature generator
npm run generate:feature

# Follow the prompts to create:
# - Feature directory structure
# - Component templates
# - Test files
# - API routes
# - Database migrations
```

### Adding a New API Route
```typescript
// apps/slack-summary-scribe/app/api/your-endpoint/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { withAuth } from '@/lib/auth-middleware';

export const GET = withAuth(async (request: NextRequest, { user }) => {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Your logic here
    const { data, error } = await supabase
      .from('your_table')
      .select('*')
      .eq('user_id', user.id);

    if (error) throw error;

    return NextResponse.json({ data });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
```

### Adding a New Component
```bash
# Generate component with tests and stories
npm run generate:component YourComponent

# This creates:
# - components/YourComponent.tsx
# - components/YourComponent.test.tsx
# - components/YourComponent.stories.tsx
```

### Adding a New Integration
```bash
# Generate integration provider
npm run generate:integration your-service

# This creates:
# - features/integrations/providers/your-service.ts
# - OAuth configuration
# - Export interface
# - Test files
```

## 🔧 Development Tools

### VS Code Configuration
The repository includes VS Code settings for optimal development:

```json
// .vscode/settings.json (already configured)
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]
  ]
}
```

### Debugging
```json
// .vscode/launch.json (already configured)
{
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node-terminal",
      "request": "launch",
      "command": "npm run dev"
    },
    {
      "name": "Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000"
    }
  ]
}
```

### Database Management
```bash
# View database in browser
npm run db:studio

# Reset database (careful!)
npm run db:reset

# Create migration
npm run db:migration create_your_table

# Apply migrations
npm run db:migrate

# Seed test data
npm run db:seed
```

## 🧪 Testing Strategy

### Test Types
1. **Unit Tests**: Individual functions and components
2. **Integration Tests**: API routes and database operations
3. **E2E Tests**: Complete user workflows
4. **Visual Tests**: Component appearance and responsive design

### Writing Tests
```typescript
// Unit test example
import { render, screen } from '@testing-library/react';
import { YourComponent } from './YourComponent';

describe('YourComponent', () => {
  it('renders correctly', () => {
    render(<YourComponent title="Test" />);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });
});

// Integration test example
import { testApiHandler } from 'next-test-api-route-handler';
import handler from '@/app/api/your-endpoint/route';

describe('/api/your-endpoint', () => {
  it('returns data for authenticated user', async () => {
    await testApiHandler({
      handler,
      test: async ({ fetch }) => {
        const res = await fetch({
          method: 'GET',
          headers: { authorization: 'Bearer test-token' }
        });
        expect(res.status).toBe(200);
      }
    });
  });
});
```

## 🚀 Deployment

### Staging Deployment
```bash
# Deploy to staging (automatic on push to develop)
git push origin develop

# Manual staging deployment
npm run deploy:staging
```

### Production Deployment
```bash
# Deploy to production (automatic on push to main)
git push origin main

# Manual production deployment
npm run deploy:production
```

### Environment Variables
Required for deployment:
```bash
# Core
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Auth
NEXTAUTH_SECRET=
NEXTAUTH_URL=

# Stripe
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=

# AI
OPENAI_API_KEY=
DEEPSEEK_API_KEY=

# Monitoring
SENTRY_DSN=
NEXT_PUBLIC_POSTHOG_KEY=
```

## 🔍 Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clear Next.js cache
rm -rf .next

# Clear node_modules and reinstall
npm run clean-install

# Check TypeScript errors
npm run type-check
```

#### Database Issues
```bash
# Test database connection
npm run db:test

# Reset database
npm run db:reset

# Check migrations
npm run db:status
```

#### Environment Issues
```bash
# Validate environment variables
npm run validate-env

# Regenerate secrets
npm run generate-secrets
```

### Getting Help
1. **Documentation**: Check `/docs` folder
2. **Team Slack**: #engineering channel
3. **GitHub Issues**: Create issue with bug template
4. **Code Review**: Tag @team-leads for urgent reviews

## 📚 Learning Resources

### Internal Documentation
- [Architecture Guide](../docs/architecture/README.md)
- [API Documentation](../docs/api/README.md)
- [Database Schema](../docs/database/schema.md)
- [Component Library](http://localhost:6006) (Storybook)

### External Resources
- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Supabase Documentation](https://supabase.com/docs)

## ✅ Onboarding Checklist

### Day 1
- [ ] Repository cloned and running locally
- [ ] Environment variables configured
- [ ] Database setup completed
- [ ] First successful login to local app
- [ ] VS Code extensions installed

### Week 1
- [ ] First pull request merged
- [ ] Understand project architecture
- [ ] Complete first feature
- [ ] Set up debugging environment
- [ ] Join team communication channels

### Month 1
- [ ] Ship feature to production
- [ ] Understand all major systems
- [ ] Contribute to documentation
- [ ] Help onboard new team member

## 🎉 You're Ready!

You now have a fully functional development environment. Here are your next steps:

1. **Explore the codebase**: Start with `apps/slack-summary-scribe/app/page.tsx`
2. **Run the test suite**: `npm run test`
3. **Check out Storybook**: `npm run storybook`
4. **Create your first feature**: `npm run generate:feature`
5. **Join the team**: Introduce yourself in Slack!

Happy coding! 🚀
