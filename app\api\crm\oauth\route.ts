/**
 * CRM OAuth API Routes
 * Handles OAuth flows for Notion and HubSpot integrations
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  getCRMOAuthUrl,
  exchangeCRMOAuthCode,
  storeCRMIntegration,
} from '@/lib/crm-integrations';
// Removed: import { SentryTracker } from '@/lib/sentry.client';
import { getCurrentUser } from '@/lib/user-management';

/**
 * GET /api/crm/oauth?type=notion|hubspot
 * Initiate OAuth flow for CRM integration
 */
export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;

    const { searchParams } = new URL(request.url);
    const integrationType = searchParams.get('type') as 'notion' | 'hubspot';
    const organizationId = searchParams.get('organization_id') || `org-${userId}`;

    if (!integrationType || !['notion', 'hubspot'].includes(integrationType)) {
      return NextResponse.json(
        { error: 'Invalid integration type. Must be "notion" or "hubspot"' },
        { status: 400 }
      );
    }

    // Create OAuth URL
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const redirectUri = `${baseUrl}/api/crm/oauth/callback`;

    try {
      const state = Buffer.from(JSON.stringify({ userId, organizationId, integrationType })).toString('base64');
      const oauthUrl = getCRMOAuthUrl(integrationType, state);

      return NextResponse.json({
        success: true,
        oauth_url: oauthUrl,
        integration_type: integrationType,
      });

    } catch (error) {
      console.error('Error creating OAuth URL:', error);
      return NextResponse.json(
        { 
          error: 'Failed to create OAuth URL',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('CRM OAuth initiation error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to initiate OAuth flow' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/crm/oauth
 * Complete OAuth flow with authorization code
 */
export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;

    const body = await request.json();
    const { code, state, integration_type, organization_id } = body;

    if (!code || !integration_type) {
      return NextResponse.json(
        { error: 'Missing required parameters: code and integration_type' },
        { status: 400 }
      );
    }

    if (!['notion', 'hubspot'].includes(integration_type)) {
      return NextResponse.json(
        { error: 'Invalid integration type' },
        { status: 400 }
      );
    }

    try {
      // Exchange code for tokens
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
      const redirectUri = `${baseUrl}/api/crm/oauth/callback`;

      const tokenData = await exchangeCRMOAuthCode(integration_type, code, redirectUri);

      // Store integration
      const orgId = organization_id || `org-${userId}`;
      const integration = await storeCRMIntegration(userId, orgId, integration_type, tokenData);

      return NextResponse.json({
        success: true,
        integration: {
          id: integration.id,
          type: integration_type,
          workspace_name: tokenData.workspace_name,
          workspace_id: tokenData.workspace_id,
          is_active: true,
        },
        message: `${integration_type} integration completed successfully`,
      });

    } catch (error) {
      console.error('OAuth token exchange error:', error);
      return NextResponse.json(
        {
          error: 'Failed to complete OAuth flow',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('CRM OAuth completion error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to complete OAuth flow' },
      { status: 500 }
    );
  }
}
