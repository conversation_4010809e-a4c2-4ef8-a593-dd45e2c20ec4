#!/usr/bin/env node

/**
 * Worker Process Starter
 * 
 * Initializes and starts background job workers with proper monitoring
 */

const { jobManager } = require('../lib/workers/job-manager');
const { allHandlers } = require('../lib/workers/handlers');
const chalk = require('chalk');

class WorkerProcess {
  constructor() {
    this.isShuttingDown = false;
    this.setupSignalHandlers();
  }

  async start() {
    console.log(chalk.blue.bold('🔄 Starting Background Workers...\n'));

    try {
      // Register all job handlers
      console.log(chalk.blue('📋 Registering job handlers...'));
      for (const handler of allHandlers) {
        jobManager.registerHandler(handler);
        console.log(chalk.green(`  ✅ ${handler.type} (concurrency: ${handler.concurrency || 1})`));
      }

      // Setup event listeners
      this.setupEventListeners();

      // Start job manager
      console.log(chalk.blue('\n🚀 Starting job manager...'));
      await jobManager.start();
      
      console.log(chalk.green.bold('\n✅ Workers started successfully!'));
      console.log(chalk.yellow('📊 Monitoring metrics available at /api/admin/workers'));
      console.log(chalk.gray('Press Ctrl+C to stop workers gracefully\n'));

      // Start metrics reporting
      this.startMetricsReporting();

      // Keep process alive
      this.keepAlive();

    } catch (error) {
      console.error(chalk.red('❌ Failed to start workers:'), error);
      process.exit(1);
    }
  }

  setupEventListeners() {
    // Job lifecycle events
    jobManager.on('job:queued', (job) => {
      console.log(chalk.cyan(`📥 Job queued: ${job.type} (${job.id})`));
    });

    jobManager.on('job:started', (job) => {
      console.log(chalk.blue(`🔄 Job started: ${job.type} (${job.id})`));
    });

    jobManager.on('job:completed', (job, result) => {
      const duration = result.duration;
      console.log(chalk.green(`✅ Job completed: ${job.type} (${job.id}) in ${duration}ms`));
    });

    jobManager.on('job:failed', (job, result) => {
      const duration = result.duration;
      console.log(chalk.red(`❌ Job failed: ${job.type} (${job.id}) after ${duration}ms - ${result.error}`));
    });

    jobManager.on('job:retried', (job, lastResult) => {
      const retryCount = job.metadata?.retryCount || 0;
      console.log(chalk.yellow(`🔄 Job retry ${retryCount}: ${job.type} (${job.id}) - ${lastResult.error}`));
    });

    jobManager.on('job:cancelled', (jobId) => {
      console.log(chalk.gray(`🚫 Job cancelled: ${jobId}`));
    });

    // Worker events
    jobManager.on('worker:started', () => {
      console.log(chalk.green('🟢 Worker process started'));
    });

    jobManager.on('worker:stopped', () => {
      console.log(chalk.red('🔴 Worker process stopped'));
    });

    jobManager.on('worker:error', (error) => {
      console.error(chalk.red('💥 Worker error:'), error);
    });

    // Handler events
    jobManager.on('handler:registered', (type) => {
      console.log(chalk.blue(`🔧 Handler registered: ${type}`));
    });
  }

  startMetricsReporting() {
    // Report metrics every 30 seconds
    setInterval(() => {
      if (this.isShuttingDown) return;

      const metrics = jobManager.getMetrics();
      
      console.log(chalk.gray('\n📊 Worker Metrics:'));
      console.log(chalk.gray(`  Processed: ${metrics.processed}`));
      console.log(chalk.gray(`  Failed: ${metrics.failed}`));
      console.log(chalk.gray(`  Retried: ${metrics.retried}`));
      console.log(chalk.gray(`  Active Jobs: ${metrics.activeJobs}`));
      console.log(chalk.gray(`  Queue Size: ${metrics.queueSize}`));
      console.log(chalk.gray(`  Avg Duration: ${Math.round(metrics.avgDuration)}ms`));
      
      if (metrics.processed > 0) {
        const successRate = ((metrics.processed / (metrics.processed + metrics.failed)) * 100).toFixed(1);
        console.log(chalk.gray(`  Success Rate: ${successRate}%`));
      }
      
      console.log(''); // Empty line for readability
    }, 30000);
  }

  setupSignalHandlers() {
    // Graceful shutdown on SIGTERM
    process.on('SIGTERM', () => {
      console.log(chalk.yellow('\n🛑 Received SIGTERM, shutting down gracefully...'));
      this.shutdown();
    });

    // Graceful shutdown on SIGINT (Ctrl+C)
    process.on('SIGINT', () => {
      console.log(chalk.yellow('\n🛑 Received SIGINT, shutting down gracefully...'));
      this.shutdown();
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error(chalk.red('💥 Uncaught Exception:'), error);
      this.shutdown(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error(chalk.red('💥 Unhandled Rejection at:'), promise, 'reason:', reason);
      this.shutdown(1);
    });
  }

  async shutdown(exitCode = 0) {
    if (this.isShuttingDown) {
      console.log(chalk.red('⚠️ Force shutdown...'));
      process.exit(exitCode);
    }

    this.isShuttingDown = true;

    try {
      console.log(chalk.blue('🔄 Stopping job manager...'));
      await jobManager.stop();
      
      console.log(chalk.green('✅ Workers stopped gracefully'));
      process.exit(exitCode);
    } catch (error) {
      console.error(chalk.red('❌ Error during shutdown:'), error);
      process.exit(1);
    }
  }

  keepAlive() {
    // Keep the process alive
    setInterval(() => {
      if (!this.isShuttingDown) {
        // Health check - could ping external monitoring
        const metrics = jobManager.getMetrics();
        
        // Log warning if too many failures
        if (metrics.failed > 0 && metrics.processed > 0) {
          const failureRate = (metrics.failed / (metrics.processed + metrics.failed)) * 100;
          if (failureRate > 20) {
            console.log(chalk.yellow(`⚠️ High failure rate: ${failureRate.toFixed(1)}%`));
          }
        }
      }
    }, 60000); // Every minute
  }
}

// CLI interface
const { program } = require('commander');

program
  .name('start-workers')
  .description('Start background job workers')
  .version('1.0.0');

program
  .command('start')
  .description('Start all workers')
  .option('--env <env>', 'Environment (development, staging, production)', 'development')
  .option('--concurrency <num>', 'Worker concurrency', '5')
  .option('--poll-interval <ms>', 'Job polling interval in ms', '5000')
  .action(async (options) => {
    // Set environment variables from options
    process.env.JOB_CONCURRENCY = options.concurrency;
    process.env.JOB_POLL_INTERVAL = options.pollInterval;
    process.env.NODE_ENV = options.env;

    console.log(chalk.blue(`🌍 Environment: ${options.env}`));
    console.log(chalk.blue(`⚡ Concurrency: ${options.concurrency}`));
    console.log(chalk.blue(`⏱️ Poll Interval: ${options.pollInterval}ms\n`));

    const worker = new WorkerProcess();
    await worker.start();
  });

program
  .command('status')
  .description('Check worker status')
  .action(async () => {
    try {
      const response = await fetch('http://localhost:3000/api/admin/workers/status');
      const data = await response.json();
      
      console.log(chalk.blue.bold('📊 Worker Status:\n'));
      console.log(chalk.green(`Status: ${data.status}`));
      console.log(chalk.blue(`Active Jobs: ${data.metrics.activeJobs}`));
      console.log(chalk.blue(`Queue Size: ${data.metrics.queueSize}`));
      console.log(chalk.blue(`Processed: ${data.metrics.processed}`));
      console.log(chalk.blue(`Failed: ${data.metrics.failed}`));
      
      if (data.metrics.processed > 0) {
        const successRate = ((data.metrics.processed / (data.metrics.processed + data.metrics.failed)) * 100).toFixed(1);
        console.log(chalk.green(`Success Rate: ${successRate}%`));
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to get worker status:'), error.message);
      process.exit(1);
    }
  });

program
  .command('queue')
  .description('Queue a test job')
  .option('-t, --type <type>', 'Job type', 'data-processing')
  .option('-p, --payload <json>', 'Job payload as JSON', '{}')
  .action(async (options) => {
    try {
      const payload = JSON.parse(options.payload);
      
      const response = await fetch('http://localhost:3000/api/admin/workers/queue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: options.type,
          payload,
          priority: 1,
          maxRetries: 3,
          retryDelay: 1000,
          timeout: 30000
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        console.log(chalk.green(`✅ Job queued: ${data.jobId}`));
      } else {
        console.error(chalk.red(`❌ Failed to queue job: ${data.error}`));
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to queue job:'), error.message);
      process.exit(1);
    }
  });

// Start if called directly
if (require.main === module) {
  program.parse();
}

module.exports = WorkerProcess;
