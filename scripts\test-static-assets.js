#!/usr/bin/env node

/**
 * STATIC ASSET LOADING VALIDATION
 * 
 * Tests static asset loading, MIME types, and deployment readiness:
 * ✅ Validate _next/static/ assets load properly
 * ✅ Check for MIME type errors
 * ✅ Ensure CSS and JS files are served correctly
 * ✅ Test application styling and functionality
 * ✅ Verify deployment readiness
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const http = require('http');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logHeader(message) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`🔍 ${message}`, 'cyan');
  log(`${'='.repeat(60)}`, 'cyan');
}

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

function addTestResult(name, status, message) {
  testResults.tests.push({ name, status, message });
  if (status === 'PASS') {
    testResults.passed++;
    logSuccess(`${name}: ${message}`);
  } else if (status === 'FAIL') {
    testResults.failed++;
    logError(`${name}: ${message}`);
  } else if (status === 'WARN') {
    testResults.warnings++;
    logWarning(`${name}: ${message}`);
  }
}

// Test HTTP request to endpoint
async function testHTTPRequest(url, expectedStatus = 200) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: url,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          success: true,
          status: res.statusCode,
          headers: res.headers,
          data: data,
          message: `Status: ${res.statusCode}`
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        success: false,
        status: 0,
        message: `Connection failed: ${error.message}`
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        status: 0,
        message: 'Request timeout'
      });
    });

    req.end();
  });
}

// Check if build artifacts exist
function testBuildArtifacts() {
  logHeader('BUILD ARTIFACTS VALIDATION');
  
  const buildPaths = [
    '.next',
    '.next/static',
    '.next/BUILD_ID',
    '.next/server',
    '.next/standalone'
  ];
  
  buildPaths.forEach(buildPath => {
    if (fs.existsSync(buildPath)) {
      addTestResult(`Build Artifact: ${buildPath}`, 'PASS', 'Build artifact exists');
    } else {
      if (buildPath === '.next/standalone') {
        addTestResult(`Build Artifact: ${buildPath}`, 'WARN', 'Optional artifact missing');
      } else {
        addTestResult(`Build Artifact: ${buildPath}`, 'FAIL', 'Required build artifact missing');
      }
    }
  });
  
  // Check for static files
  if (fs.existsSync('.next/static')) {
    const staticFiles = fs.readdirSync('.next/static');
    if (staticFiles.length > 0) {
      addTestResult('Static Files', 'PASS', `${staticFiles.length} static files generated`);
    } else {
      addTestResult('Static Files', 'FAIL', 'No static files found');
    }
  }
}

// Test main application routes
async function testApplicationRoutes() {
  logHeader('APPLICATION ROUTES VALIDATION');
  
  const routes = [
    { path: '/', name: 'Homepage' },
    { path: '/login', name: 'Login Page' },
    { path: '/signup', name: 'Signup Page' },
    { path: '/dashboard', name: 'Dashboard' },
    { path: '/api/health', name: 'Health API' }
  ];
  
  for (const route of routes) {
    logInfo(`Testing ${route.name}...`);
    const result = await testHTTPRequest(route.path);
    
    if (result.success && result.status < 500) {
      // Check for HTML content
      if (result.data && result.data.includes('<!DOCTYPE html>')) {
        addTestResult(`Route: ${route.name}`, 'PASS', `${result.message} - HTML content served`);
      } else if (route.path.startsWith('/api/')) {
        addTestResult(`Route: ${route.name}`, 'PASS', result.message);
      } else {
        addTestResult(`Route: ${route.name}`, 'WARN', `${result.message} - No HTML content`);
      }
    } else {
      addTestResult(`Route: ${route.name}`, 'FAIL', result.message);
    }
  }
}

// Test static asset serving
async function testStaticAssets() {
  logHeader('STATIC ASSETS VALIDATION');
  
  // Test common static asset paths
  const staticAssets = [
    '/favicon.ico',
    '/_next/static/css',
    '/_next/static/chunks',
    '/robots.txt'
  ];
  
  for (const asset of staticAssets) {
    logInfo(`Testing static asset: ${asset}...`);
    const result = await testHTTPRequest(asset);
    
    if (result.success && result.status === 200) {
      // Check content type headers
      const contentType = result.headers['content-type'] || '';
      
      if (asset.includes('.css') && contentType.includes('text/css')) {
        addTestResult(`Static Asset: ${asset}`, 'PASS', 'CSS served with correct MIME type');
      } else if (asset.includes('.js') && contentType.includes('javascript')) {
        addTestResult(`Static Asset: ${asset}`, 'PASS', 'JS served with correct MIME type');
      } else if (asset.includes('.ico') && contentType.includes('image')) {
        addTestResult(`Static Asset: ${asset}`, 'PASS', 'Icon served with correct MIME type');
      } else if (asset.includes('robots.txt') && contentType.includes('text/plain')) {
        addTestResult(`Static Asset: ${asset}`, 'PASS', 'Text file served with correct MIME type');
      } else {
        addTestResult(`Static Asset: ${asset}`, 'PASS', `Asset served (${contentType})`);
      }
    } else if (result.status === 404) {
      addTestResult(`Static Asset: ${asset}`, 'WARN', 'Asset not found (may be optional)');
    } else {
      addTestResult(`Static Asset: ${asset}`, 'FAIL', result.message);
    }
  }
}

// Test CSS and styling
async function testStyling() {
  logHeader('CSS AND STYLING VALIDATION');
  
  // Test homepage for CSS content
  const homepageResult = await testHTTPRequest('/');
  
  if (homepageResult.success && homepageResult.data) {
    const htmlContent = homepageResult.data;
    
    // Check for CSS links
    if (htmlContent.includes('/_next/static/css/') || htmlContent.includes('<style')) {
      addTestResult('CSS Loading', 'PASS', 'CSS files linked in HTML');
    } else {
      addTestResult('CSS Loading', 'WARN', 'No CSS links found in HTML');
    }
    
    // Check for Tailwind classes
    if (htmlContent.includes('class=') && (htmlContent.includes('bg-') || htmlContent.includes('text-'))) {
      addTestResult('Tailwind CSS', 'PASS', 'Tailwind classes present in HTML');
    } else {
      addTestResult('Tailwind CSS', 'WARN', 'No Tailwind classes detected');
    }
    
    // Check for JavaScript
    if (htmlContent.includes('/_next/static/chunks/') || htmlContent.includes('<script')) {
      addTestResult('JavaScript Loading', 'PASS', 'JavaScript files linked in HTML');
    } else {
      addTestResult('JavaScript Loading', 'FAIL', 'No JavaScript links found in HTML');
    }
    
    // Check for proper meta tags
    if (htmlContent.includes('<meta name="viewport"')) {
      addTestResult('Responsive Meta', 'PASS', 'Viewport meta tag present');
    } else {
      addTestResult('Responsive Meta', 'WARN', 'Viewport meta tag missing');
    }
  }
}

// Test deployment readiness
function testDeploymentReadiness() {
  logHeader('DEPLOYMENT READINESS VALIDATION');
  
  // Check package.json scripts
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  const requiredScripts = ['build', 'start', 'dev'];
  requiredScripts.forEach(script => {
    if (packageJson.scripts && packageJson.scripts[script]) {
      addTestResult(`Package Script: ${script}`, 'PASS', 'Script configured');
    } else {
      addTestResult(`Package Script: ${script}`, 'FAIL', 'Required script missing');
    }
  });
  
  // Check for Vercel configuration
  if (fs.existsSync('vercel.json')) {
    addTestResult('Vercel Config', 'PASS', 'Vercel configuration found');
  } else {
    addTestResult('Vercel Config', 'WARN', 'No Vercel configuration (using defaults)');
  }
  
  // Check environment variables
  if (fs.existsSync('.env.local')) {
    const envContent = fs.readFileSync('.env.local', 'utf8');
    if (envContent.includes('NEXT_PUBLIC_SUPABASE_URL') && envContent.includes('NEXT_PUBLIC_APP_URL')) {
      addTestResult('Environment Config', 'PASS', 'Required environment variables configured');
    } else {
      addTestResult('Environment Config', 'FAIL', 'Missing required environment variables');
    }
  } else {
    addTestResult('Environment Config', 'FAIL', 'No environment configuration found');
  }
}

// Generate test report
function generateReport() {
  logHeader('STATIC ASSET LOADING TEST REPORT');
  
  log(`\n📊 Test Results Summary:`, 'bright');
  log(`   ✅ Passed: ${testResults.passed}`, 'green');
  log(`   ❌ Failed: ${testResults.failed}`, 'red');
  log(`   ⚠️  Warnings: ${testResults.warnings}`, 'yellow');
  log(`   📝 Total Tests: ${testResults.tests.length}`, 'blue');
  
  const successRate = ((testResults.passed / testResults.tests.length) * 100).toFixed(1);
  log(`   📈 Success Rate: ${successRate}%`, successRate >= 80 ? 'green' : 'red');
  
  if (testResults.failed === 0) {
    log(`\n🎉 ALL CRITICAL TESTS PASSED! Static assets are loading correctly.`, 'green');
  } else {
    log(`\n🚨 ${testResults.failed} CRITICAL ISSUES FOUND. Please fix before deployment.`, 'red');
  }
  
  // Deployment readiness assessment
  if (testResults.failed === 0 && successRate >= 80) {
    log(`\n🚀 DEPLOYMENT READY: Application is ready for production deployment.`, 'green');
    log(`   ✅ Static assets loading correctly`, 'green');
    log(`   ✅ No MIME type errors detected`, 'green');
    log(`   ✅ CSS and JavaScript properly served`, 'green');
    log(`   ✅ Application styling functional`, 'green');
  } else {
    log(`\n⚠️  DEPLOYMENT BLOCKED: Fix issues before deploying.`, 'red');
  }
}

// Main execution
async function main() {
  log('🚀 Starting Static Asset Loading Validation...', 'bright');
  
  testBuildArtifacts();
  await testApplicationRoutes();
  await testStaticAssets();
  await testStyling();
  testDeploymentReadiness();
  
  generateReport();
  
  // Exit with appropriate code
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
