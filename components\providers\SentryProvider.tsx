'use client';

import React, { useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import * as Sentry from '@sentry/nextjs';

interface SentryProviderProps {
  children: React.ReactNode;
}

export default function SentryProvider({ children }: SentryProviderProps) {
  const { userId, isSignedIn } = useAuth();

  useEffect(() => {
    if (isSignedIn && userId) {
      // Set user context for Sentry
      Sentry.setUser({
        id: userId,
        authenticated: true,
        auth_provider: 'clerk'
      });
    } else {
      // Clear user context when signed out
      Sentry.setUser(null);
    }
  }, [isSignedIn, userId]);

  return <>{children}</>;
}
