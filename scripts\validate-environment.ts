#!/usr/bin/env tsx

/**
 * Environment Validation Script
 * 
 * Validates all required environment variables and configurations
 * for production-ready deployment of Slack Summary Scribe.
 * 
 * Usage:
 *   npm run validate-env
 *   tsx scripts/validate-environment.ts
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

interface ValidationResult {
  category: string;
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  required: boolean;
}

const results: ValidationResult[] = [];

function validate(
  category: string,
  name: string,
  condition: boolean,
  message: string,
  required = true
): void {
  results.push({
    category,
    name,
    status: condition ? 'pass' : (required ? 'fail' : 'warning'),
    message,
    required
  });
}

function validateUrl(url: string | undefined, name: string): boolean {
  if (!url) return false;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

async function validateEnvironment() {
  console.log('🔍 Validating Environment Configuration for Slack Summary Scribe\n');

  // Core Application
  validate(
    'Core',
    'NODE_ENV',
    !!process.env.NODE_ENV,
    process.env.NODE_ENV ? `Environment: ${process.env.NODE_ENV}` : 'NODE_ENV not set'
  );

  validate(
    'Core',
    'NEXT_PUBLIC_SITE_URL',
    validateUrl(process.env.NEXT_PUBLIC_SITE_URL, 'NEXT_PUBLIC_SITE_URL'),
    process.env.NEXT_PUBLIC_SITE_URL ? 
      `Site URL: ${process.env.NEXT_PUBLIC_SITE_URL}` : 
      'NEXT_PUBLIC_SITE_URL not set or invalid'
  );

  validate(
    'Core',
    'NEXTAUTH_URL',
    validateUrl(process.env.NEXTAUTH_URL, 'NEXTAUTH_URL'),
    process.env.NEXTAUTH_URL ? 
      `NextAuth URL: ${process.env.NEXTAUTH_URL}` : 
      'NEXTAUTH_URL not set or invalid'
  );

  // URL Consistency Check
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
  const nextAuthUrl = process.env.NEXTAUTH_URL;
  validate(
    'Core',
    'URL_CONSISTENCY',
    siteUrl === nextAuthUrl,
    siteUrl === nextAuthUrl ? 
      'Site URL and NextAuth URL are consistent' : 
      'Site URL and NextAuth URL mismatch - this can cause auth issues'
  );

  // Supabase Configuration
  validate(
    'Database',
    'SUPABASE_URL',
    validateUrl(process.env.NEXT_PUBLIC_SUPABASE_URL, 'SUPABASE_URL'),
    process.env.NEXT_PUBLIC_SUPABASE_URL ? 
      'Supabase URL configured' : 
      'NEXT_PUBLIC_SUPABASE_URL not set or invalid'
  );

  validate(
    'Database',
    'SUPABASE_ANON_KEY',
    !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 
      'Supabase anonymous key configured' : 
      'NEXT_PUBLIC_SUPABASE_ANON_KEY not set'
  );

  validate(
    'Database',
    'SUPABASE_SERVICE_ROLE_KEY',
    !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    process.env.SUPABASE_SERVICE_ROLE_KEY ? 
      'Supabase service role key configured' : 
      'SUPABASE_SERVICE_ROLE_KEY not set'
  );

  // Test Supabase Connection
  if (process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    try {
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      );
      
      const { data, error } = await supabase.from('profiles').select('count').limit(1);
      validate(
        'Database',
        'SUPABASE_CONNECTION',
        !error || error.message.includes('No rows'),
        !error || error.message.includes('No rows') ? 
          'Supabase connection successful' : 
          `Supabase connection failed: ${error.message}`
      );
    } catch (error) {
      validate(
        'Database',
        'SUPABASE_CONNECTION',
        false,
        `Supabase connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  // Authentication
  validate(
    'Auth',
    'JWT_SECRET',
    !!process.env.JWT_SECRET && process.env.JWT_SECRET.length >= 32,
    process.env.JWT_SECRET ? 
      (process.env.JWT_SECRET.length >= 32 ? 'JWT secret configured' : 'JWT secret too short (minimum 32 characters)') :
      'JWT_SECRET not set'
  );

  validate(
    'Auth',
    'NEXTAUTH_SECRET',
    !!process.env.NEXTAUTH_SECRET && process.env.NEXTAUTH_SECRET.length >= 32,
    process.env.NEXTAUTH_SECRET ? 
      (process.env.NEXTAUTH_SECRET.length >= 32 ? 'NextAuth secret configured' : 'NextAuth secret too short') :
      'NEXTAUTH_SECRET not set'
  );

  // Google OAuth
  validate(
    'Auth',
    'GOOGLE_CLIENT_ID',
    !!process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_ID ? 'Google OAuth client ID configured' : 'GOOGLE_CLIENT_ID not set',
    false
  );

  validate(
    'Auth',
    'GOOGLE_CLIENT_SECRET',
    !!process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_CLIENT_SECRET ? 'Google OAuth client secret configured' : 'GOOGLE_CLIENT_SECRET not set',
    false
  );

  // Slack Integration
  validate(
    'Slack',
    'SLACK_CLIENT_ID',
    !!process.env.SLACK_CLIENT_ID,
    process.env.SLACK_CLIENT_ID ? 'Slack client ID configured' : 'SLACK_CLIENT_ID not set'
  );

  validate(
    'Slack',
    'SLACK_CLIENT_SECRET',
    !!process.env.SLACK_CLIENT_SECRET,
    process.env.SLACK_CLIENT_SECRET ? 'Slack client secret configured' : 'SLACK_CLIENT_SECRET not set'
  );

  validate(
    'Slack',
    'SLACK_SIGNING_SECRET',
    !!process.env.SLACK_SIGNING_SECRET,
    process.env.SLACK_SIGNING_SECRET ? 'Slack signing secret configured' : 'SLACK_SIGNING_SECRET not set'
  );

  // AI Service
  validate(
    'AI',
    'OPENROUTER_API_KEY',
    !!process.env.OPENROUTER_API_KEY,
    process.env.OPENROUTER_API_KEY ? 'OpenRouter API key configured' : 'OPENROUTER_API_KEY not set'
  );

  // Optional Services
  validate(
    'Optional',
    'RESEND_API_KEY',
    !!process.env.RESEND_API_KEY,
    process.env.RESEND_API_KEY ? 'Resend API key configured' : 'RESEND_API_KEY not set',
    false
  );

  validate(
    'Optional',
    'SENTRY_DSN',
    !!process.env.NEXT_PUBLIC_SENTRY_DSN,
    process.env.NEXT_PUBLIC_SENTRY_DSN ? 'Sentry DSN configured' : 'NEXT_PUBLIC_SENTRY_DSN not set',
    false
  );

  // Frontend Configuration
  validate(
    'Frontend',
    'FETCH_TIMEOUT',
    !!process.env.NEXT_PUBLIC_FETCH_TIMEOUT,
    process.env.NEXT_PUBLIC_FETCH_TIMEOUT ? 
      `Fetch timeout: ${process.env.NEXT_PUBLIC_FETCH_TIMEOUT}ms` : 
      'NEXT_PUBLIC_FETCH_TIMEOUT not set (will use default 10s)',
    false
  );

  // Print Results
  console.log('📊 Validation Results:\n');
  
  const categories = [...new Set(results.map(r => r.category))];
  let hasErrors = false;
  let hasWarnings = false;

  for (const category of categories) {
    console.log(`\n📁 ${category}:`);
    const categoryResults = results.filter(r => r.category === category);
    
    for (const result of categoryResults) {
      const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
      console.log(`  ${icon} ${result.name}: ${result.message}`);
      
      if (result.status === 'fail') hasErrors = true;
      if (result.status === 'warning') hasWarnings = true;
    }
  }

  // Summary
  console.log('\n📋 Summary:');
  const passed = results.filter(r => r.status === 'pass').length;
  const failed = results.filter(r => r.status === 'fail').length;
  const warnings = results.filter(r => r.status === 'warning').length;
  
  console.log(`✅ Passed: ${passed}`);
  if (warnings > 0) console.log(`⚠️ Warnings: ${warnings}`);
  if (failed > 0) console.log(`❌ Failed: ${failed}`);

  if (hasErrors) {
    console.log('\n🚨 Critical issues found! Please fix the failed validations before deployment.');
    process.exit(1);
  } else if (hasWarnings) {
    console.log('\n⚠️ Some optional configurations are missing. The app will work but some features may be limited.');
  } else {
    console.log('\n🎉 All validations passed! Environment is ready for deployment.');
  }
}

// Run validation
validateEnvironment().catch((error) => {
  console.error('❌ Environment validation failed:', error);
  process.exit(1);
});
