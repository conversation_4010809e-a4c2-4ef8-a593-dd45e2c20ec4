#!/usr/bin/env node

/**
 * Development Environment Bootstrap CLI
 * 
 * Interactive setup tool for new developers to get started quickly
 */

const { program } = require('commander');
const inquirer = require('inquirer');
const chalk = require('chalk');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

program
  .name('dev-setup')
  .description('Bootstrap development environment for Slack Summary Scribe')
  .version('1.0.0');

program
  .command('init')
  .description('Initialize development environment')
  .option('--skip-deps', 'Skip dependency installation')
  .option('--skip-db', 'Skip database setup')
  .option('--template <type>', 'Use predefined template (minimal, full)', 'full')
  .action(async (options) => {
    console.log(chalk.blue.bold('🚀 Welcome to Slack Summary Scribe Development Setup!\n'));

    try {
      // Check prerequisites
      await checkPrerequisites();

      // Gather configuration
      const config = await gatherConfiguration(options.template);

      // Install dependencies
      if (!options.skipDeps) {
        await installDependencies();
      }

      // Setup environment
      await setupEnvironment(config);

      // Setup database
      if (!options.skipDb) {
        await setupDatabase(config);
      }

      // Generate test data
      await generateTestData(config);

      // Final setup
      await finalSetup();

      console.log(chalk.green.bold('\n✅ Development environment setup complete!'));
      console.log(chalk.yellow('\n📝 Next steps:'));
      console.log('1. Start development: npm run dev');
      console.log('2. Open browser: http://localhost:3000');
      console.log('3. Check Storybook: http://localhost:6006');
      console.log('4. Read docs: ./developers/GETTING_STARTED.md');

    } catch (error) {
      console.error(chalk.red('❌ Setup failed:'), error.message);
      process.exit(1);
    }
  });

program
  .command('reset')
  .description('Reset development environment')
  .option('--hard', 'Hard reset (removes all data)')
  .action(async (options) => {
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: options.hard 
          ? 'This will delete ALL data and reset everything. Continue?'
          : 'This will reset the development environment. Continue?',
        default: false
      }
    ]);

    if (!confirm) {
      console.log('Reset cancelled.');
      return;
    }

    try {
      if (options.hard) {
        await hardReset();
      } else {
        await softReset();
      }

      console.log(chalk.green('✅ Environment reset complete!'));
    } catch (error) {
      console.error(chalk.red('❌ Reset failed:'), error.message);
      process.exit(1);
    }
  });

program
  .command('test-data')
  .description('Generate test data')
  .option('--users <count>', 'Number of test users', '10')
  .option('--orgs <count>', 'Number of test organizations', '5')
  .option('--summaries <count>', 'Number of test summaries', '50')
  .action(async (options) => {
    try {
      console.log(chalk.blue('🧪 Generating test data...'));
      
      await generateTestData({
        users: parseInt(options.users),
        organizations: parseInt(options.orgs),
        summaries: parseInt(options.summaries)
      });

      console.log(chalk.green('✅ Test data generated!'));
    } catch (error) {
      console.error(chalk.red('❌ Test data generation failed:'), error.message);
      process.exit(1);
    }
  });

async function checkPrerequisites() {
  console.log(chalk.blue('🔍 Checking prerequisites...'));

  const requirements = [
    { name: 'Node.js', command: 'node --version', minVersion: '18.0.0' },
    { name: 'npm', command: 'npm --version', minVersion: '9.0.0' },
    { name: 'Git', command: 'git --version', minVersion: '2.30.0' }
  ];

  for (const req of requirements) {
    try {
      const version = execSync(req.command, { encoding: 'utf8' }).trim();
      console.log(chalk.green(`  ✅ ${req.name}: ${version}`));
    } catch (error) {
      throw new Error(`${req.name} is not installed or not in PATH`);
    }
  }
}

async function gatherConfiguration(template) {
  console.log(chalk.blue('\n⚙️ Configuration setup...'));

  if (template === 'minimal') {
    return getMinimalConfig();
  }

  const questions = [
    {
      type: 'input',
      name: 'projectName',
      message: 'Project name:',
      default: 'slack-summary-scribe'
    },
    {
      type: 'input',
      name: 'supabaseUrl',
      message: 'Supabase URL:',
      validate: (input) => input.startsWith('https://') || 'Must be a valid HTTPS URL'
    },
    {
      type: 'password',
      name: 'supabaseAnonKey',
      message: 'Supabase Anon Key:',
      mask: '*'
    },
    {
      type: 'password',
      name: 'supabaseServiceKey',
      message: 'Supabase Service Role Key:',
      mask: '*'
    },
    {
      type: 'confirm',
      name: 'setupStripe',
      message: 'Setup Stripe integration?',
      default: true
    },
    {
      type: 'input',
      name: 'stripeSecretKey',
      message: 'Stripe Secret Key (test):',
      when: (answers) => answers.setupStripe,
      validate: (input) => input.startsWith('sk_test_') || 'Must be a test secret key'
    },
    {
      type: 'input',
      name: 'stripePublishableKey',
      message: 'Stripe Publishable Key (test):',
      when: (answers) => answers.setupStripe,
      validate: (input) => input.startsWith('pk_test_') || 'Must be a test publishable key'
    },
    {
      type: 'confirm',
      name: 'setupAI',
      message: 'Setup AI services?',
      default: true
    },
    {
      type: 'password',
      name: 'openaiKey',
      message: 'OpenAI API Key:',
      when: (answers) => answers.setupAI,
      mask: '*'
    },
    {
      type: 'confirm',
      name: 'setupMonitoring',
      message: 'Setup monitoring (Sentry, PostHog)?',
      default: false
    },
    {
      type: 'input',
      name: 'sentryDsn',
      message: 'Sentry DSN:',
      when: (answers) => answers.setupMonitoring
    },
    {
      type: 'input',
      name: 'posthogKey',
      message: 'PostHog API Key:',
      when: (answers) => answers.setupMonitoring
    }
  ];

  return await inquirer.prompt(questions);
}

function getMinimalConfig() {
  return {
    projectName: 'slack-summary-scribe',
    supabaseUrl: 'https://your-project.supabase.co',
    supabaseAnonKey: 'your-anon-key',
    supabaseServiceKey: 'your-service-key',
    setupStripe: false,
    setupAI: false,
    setupMonitoring: false
  };
}

async function installDependencies() {
  console.log(chalk.blue('\n📦 Installing dependencies...'));
  
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log(chalk.green('  ✅ Dependencies installed'));
  } catch (error) {
    throw new Error('Failed to install dependencies');
  }
}

async function setupEnvironment(config) {
  console.log(chalk.blue('\n🔧 Setting up environment...'));

  const envContent = generateEnvContent(config);
  
  fs.writeFileSync('.env.local', envContent);
  console.log(chalk.green('  ✅ Environment file created'));

  // Generate NextAuth secret
  const nextAuthSecret = crypto.randomBytes(32).toString('hex');
  fs.appendFileSync('.env.local', `\nNEXTAUTH_SECRET=${nextAuthSecret}\n`);
  console.log(chalk.green('  ✅ NextAuth secret generated'));
}

function generateEnvContent(config) {
  let content = `# Generated by dev-setup CLI
# ${new Date().toISOString()}

# Core Configuration
NEXT_PUBLIC_SUPABASE_URL=${config.supabaseUrl}
NEXT_PUBLIC_SUPABASE_ANON_KEY=${config.supabaseAnonKey}
SUPABASE_SERVICE_ROLE_KEY=${config.supabaseServiceKey}

# Authentication
NEXTAUTH_URL=http://localhost:3000
`;

  if (config.setupStripe) {
    content += `
# Stripe
STRIPE_SECRET_KEY=${config.stripeSecretKey}
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${config.stripePublishableKey}
STRIPE_WEBHOOK_SECRET=whsec_test_placeholder
`;
  }

  if (config.setupAI) {
    content += `
# AI Services
OPENAI_API_KEY=${config.openaiKey}
DEEPSEEK_API_KEY=your_deepseek_key_here
`;
  }

  if (config.setupMonitoring) {
    content += `
# Monitoring
SENTRY_DSN=${config.sentryDsn}
NEXT_PUBLIC_POSTHOG_KEY=${config.posthogKey}
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
`;
  }

  content += `
# Email
RESEND_API_KEY=your_resend_key_here

# Development
NODE_ENV=development
`;

  return content;
}

async function setupDatabase(config) {
  console.log(chalk.blue('\n🗄️ Setting up database...'));

  try {
    // Run migrations
    execSync('npm run db:migrate', { stdio: 'inherit' });
    console.log(chalk.green('  ✅ Database migrations applied'));

    // Setup RLS policies
    execSync('npm run db:setup-rls', { stdio: 'inherit' });
    console.log(chalk.green('  ✅ RLS policies configured'));

  } catch (error) {
    console.log(chalk.yellow('  ⚠️ Database setup skipped (run manually: npm run db:migrate)'));
  }
}

async function generateTestData(config) {
  console.log(chalk.blue('\n🧪 Generating test data...'));

  const testDataScript = `
-- Test data generation
-- Generated by dev-setup CLI

-- Create test admin user
INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at)
VALUES (
  'admin-user-id',
  '<EMAIL>',
  NOW(),
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Create admin profile
INSERT INTO profiles (id, email, full_name, role, created_at, updated_at)
VALUES (
  'admin-user-id',
  '<EMAIL>',
  'Admin User',
  'admin',
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Create test organization
INSERT INTO organizations (id, name, plan, created_at, updated_at)
VALUES (
  'test-org-id',
  'Test Organization',
  'PRO',
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Link admin to organization
INSERT INTO user_organizations (user_id, organization_id, role, created_at)
VALUES (
  'admin-user-id',
  'test-org-id',
  'owner',
  NOW()
) ON CONFLICT (user_id, organization_id) DO NOTHING;
`;

  fs.writeFileSync('temp-test-data.sql', testDataScript);
  
  try {
    execSync('npm run db:execute -- temp-test-data.sql', { stdio: 'inherit' });
    fs.unlinkSync('temp-test-data.sql');
    console.log(chalk.green('  ✅ Test data generated'));
  } catch (error) {
    fs.unlinkSync('temp-test-data.sql');
    console.log(chalk.yellow('  ⚠️ Test data generation skipped'));
  }
}

async function finalSetup() {
  console.log(chalk.blue('\n🎯 Final setup...'));

  // Create development scripts
  const packageJsonPath = 'package.json';
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

  // Add development scripts if they don't exist
  const devScripts = {
    'dev:setup': 'node scripts/dev-setup.js init',
    'dev:reset': 'node scripts/dev-setup.js reset',
    'dev:test-data': 'node scripts/dev-setup.js test-data',
    'generate:secrets': 'node scripts/generate-secrets.js',
    'validate-env': 'node scripts/validate-env.js'
  };

  packageJson.scripts = { ...packageJson.scripts, ...devScripts };
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

  console.log(chalk.green('  ✅ Development scripts added'));

  // Create VS Code settings if they don't exist
  const vscodeDir = '.vscode';
  if (!fs.existsSync(vscodeDir)) {
    fs.mkdirSync(vscodeDir);
  }

  const vscodeSettings = {
    "typescript.preferences.importModuleSpecifier": "relative",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": true
    },
    "tailwindCSS.experimental.classRegex": [
      ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]
    ]
  };

  fs.writeFileSync(
    path.join(vscodeDir, 'settings.json'),
    JSON.stringify(vscodeSettings, null, 2)
  );

  console.log(chalk.green('  ✅ VS Code settings configured'));
}

async function softReset() {
  console.log(chalk.blue('🔄 Performing soft reset...'));

  // Clear Next.js cache
  if (fs.existsSync('.next')) {
    execSync('rm -rf .next');
    console.log(chalk.green('  ✅ Next.js cache cleared'));
  }

  // Clear node_modules and reinstall
  execSync('rm -rf node_modules package-lock.json');
  execSync('npm install');
  console.log(chalk.green('  ✅ Dependencies reinstalled'));

  // Reset database
  try {
    execSync('npm run db:reset');
    console.log(chalk.green('  ✅ Database reset'));
  } catch (error) {
    console.log(chalk.yellow('  ⚠️ Database reset skipped'));
  }
}

async function hardReset() {
  console.log(chalk.blue('💥 Performing hard reset...'));

  await softReset();

  // Remove environment file
  if (fs.existsSync('.env.local')) {
    fs.unlinkSync('.env.local');
    console.log(chalk.green('  ✅ Environment file removed'));
  }

  // Remove VS Code settings
  if (fs.existsSync('.vscode')) {
    execSync('rm -rf .vscode');
    console.log(chalk.green('  ✅ VS Code settings removed'));
  }
}

program.parse();
