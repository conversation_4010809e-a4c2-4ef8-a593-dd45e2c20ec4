/**
 * SSO Providers API Route
 * Manage SSO providers for Enterprise customers
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import {
  add<PERSON><PERSON><PERSON>ider,
  update<PERSON><PERSON><PERSON>ider,
  remove<PERSON><PERSON><PERSON><PERSON>,
  test<PERSON>OP<PERSON>ider,
} from '@/lib/sso-config';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * POST /api/sso/providers
 * Add SSO provider
 */
export async function POST(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // SSO requires Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'SSO providers require Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const body = await req.json();
      const { 
        name, 
        type, 
        enabled, 
        configuration, 
        domains, 
        auto_provision, 
        default_role,
        organization_id 
      } = body;

      if (!name || !type || !configuration) {
        return NextResponse.json(
          { error: 'Name, type, and configuration are required' },
          { status: 400 }
        );
      }

      if (!['saml', 'oidc', 'oauth'].includes(type)) {
        return NextResponse.json(
          { error: 'Invalid provider type. Must be saml, oidc, or oauth' },
          { status: 400 }
        );
      }

      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const organizationId = organization_id || user.id;

      const result = await addSSOProvider(
        organizationId,
        context.userId,
        user.email,
        {
          name,
          type,
          enabled: enabled ?? true,
          configuration,
          domains: domains || [],
          auto_provision: auto_provision ?? false,
          default_role: default_role || 'member',
        }
      );

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        provider: result.provider,
        message: 'SSO provider added successfully',
      });

    } catch (error) {
      console.error('SSO provider creation error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to add SSO provider' },
        { status: 500 }
      );
    }
  });
}

/**
 * PUT /api/sso/providers
 * Update SSO provider
 */
export async function PUT(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // SSO requires Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'SSO providers require Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const body = await req.json();
      const { provider_id, organization_id, ...updates } = body;

      if (!provider_id) {
        return NextResponse.json(
          { error: 'Provider ID is required' },
          { status: 400 }
        );
      }

      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const organizationId = organization_id || user.id;

      const result = await updateSSOProvider(
        organizationId,
        provider_id,
        context.userId,
        user.email,
        updates
      );

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        provider: result.provider,
        message: 'SSO provider updated successfully',
      });

    } catch (error) {
      console.error('SSO provider update error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to update SSO provider' },
        { status: 500 }
      );
    }
  });
}

/**
 * DELETE /api/sso/providers
 * Remove SSO provider
 */
export async function DELETE(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // SSO requires Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'SSO providers require Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const { searchParams } = new URL(req.url);
      const providerId = searchParams.get('provider_id');
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const organizationId = searchParams.get('organization_id') || user.id;

      if (!providerId) {
        return NextResponse.json(
          { error: 'Provider ID is required' },
          { status: 400 }
        );
      }

      const result = await removeSSOProvider(
        organizationId,
        providerId,
        context.userId,
        user.email
      );

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'SSO provider removed successfully',
      });

    } catch (error) {
      console.error('SSO provider removal error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to remove SSO provider' },
        { status: 500 }
      );
    }
  });
}
