#!/usr/bin/env node

/**
 * Production Build Testing Script
 * Tests dynamic imports, chunk loading, and production build reliability
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function logHeader(text) {
  console.log(`\n${colors.cyan}${colors.bright}${text}${colors.reset}`);
  console.log('='.repeat(text.length));
}

function logSuccess(text) {
  console.log(`${colors.green}✅ ${text}${colors.reset}`);
}

function logError(text) {
  console.log(`${colors.red}❌ ${text}${colors.reset}`);
}

function logWarning(text) {
  console.log(`${colors.yellow}⚠️  ${text}${colors.reset}`);
}

function logInfo(text) {
  console.log(`${colors.blue}ℹ️  ${text}${colors.reset}`);
}

// Test 1: Validate Next.js Configuration
function validateNextConfig() {
  logHeader('TEST 1: NEXT.JS CONFIGURATION');
  
  const configPath = path.join(process.cwd(), 'next.config.mjs');
  
  if (!fs.existsSync(configPath)) {
    logError('next.config.mjs not found');
    return false;
  }
  
  const content = fs.readFileSync(configPath, 'utf8');
  
  // Check for production-grade chunk splitting
  if (content.includes('splitChunks') && content.includes('framework') && content.includes('lib')) {
    logSuccess('Production-grade chunk splitting configured');
  } else {
    logError('Enhanced chunk splitting missing');
    return false;
  }
  
  // Check for chunk loading timeout
  if (content.includes('chunkLoadTimeout') && content.includes('120000')) {
    logSuccess('Extended chunk loading timeout configured');
  } else {
    logWarning('Chunk loading timeout may be too short');
  }
  
  // Check for deterministic chunk IDs
  if (content.includes('deterministic')) {
    logSuccess('Deterministic chunk IDs enabled');
  } else {
    logWarning('Deterministic chunk IDs not configured');
  }
  
  // Check for content hash in filenames
  if (content.includes('[contenthash:8]')) {
    logSuccess('Content hash in chunk filenames');
  } else {
    logWarning('Content hash not configured');
  }
  
  return true;
}

// Test 2: Validate Dynamic Import Handler
function validateDynamicImportHandler() {
  logHeader('TEST 2: DYNAMIC IMPORT HANDLER');
  
  const handlerPath = path.join(process.cwd(), 'lib/dynamic-import-handler.ts');
  
  if (!fs.existsSync(handlerPath)) {
    logError('Dynamic import handler not found');
    return false;
  }
  
  const content = fs.readFileSync(handlerPath, 'utf8');
  
  // Check for retry logic
  if (content.includes('maxRetries') && content.includes('retryDelay')) {
    logSuccess('Retry logic implemented');
  } else {
    logError('Retry logic missing');
    return false;
  }
  
  // Check for fallback handling
  if (content.includes('fallback') && content.includes('handleChunkLoadingError')) {
    logSuccess('Fallback handling implemented');
  } else {
    logError('Fallback handling missing');
    return false;
  }
  
  // Check for cache management
  if (content.includes('clearCache') && content.includes('ImportCache')) {
    logSuccess('Cache management implemented');
  } else {
    logError('Cache management missing');
    return false;
  }
  
  return true;
}

// Test 3: Validate Error Boundaries
function validateErrorBoundaries() {
  logHeader('TEST 3: ERROR BOUNDARIES');
  
  const boundaries = [
    'components/ChunkErrorBoundary.tsx',
    'components/error-boundaries/ChunkErrorBoundary.tsx'
  ];
  
  let foundBoundaries = 0;
  
  boundaries.forEach(boundary => {
    const boundaryPath = path.join(process.cwd(), boundary);
    if (fs.existsSync(boundaryPath)) {
      foundBoundaries++;
      logSuccess(`${boundary}: Found`);
      
      const content = fs.readFileSync(boundaryPath, 'utf8');
      if (content.includes('ChunkLoadError') && content.includes('componentDidCatch')) {
        logSuccess(`${boundary}: Chunk error handling implemented`);
      } else {
        logWarning(`${boundary}: Chunk error handling may be incomplete`);
      }
    } else {
      logWarning(`${boundary}: Not found`);
    }
  });
  
  return foundBoundaries > 0;
}

// Test 4: Validate Layout Error Handling
function validateLayoutErrorHandling() {
  logHeader('TEST 4: LAYOUT ERROR HANDLING');
  
  const layoutPath = path.join(process.cwd(), 'app/layout.tsx');
  
  if (!fs.existsSync(layoutPath)) {
    logError('app/layout.tsx not found');
    return false;
  }
  
  const content = fs.readFileSync(layoutPath, 'utf8');
  
  // Check for chunk error recovery script
  if (content.includes('handleDynamicImportError') && content.includes('ChunkLoadError')) {
    logSuccess('Chunk error recovery script in layout');
  } else {
    logError('Chunk error recovery script missing');
    return false;
  }
  
  // Check for retry logic in script
  if (content.includes('maxRetries') && content.includes('retryAttempts')) {
    logSuccess('Retry logic in layout script');
  } else {
    logError('Retry logic missing in layout script');
    return false;
  }
  
  // Check for cache clearing
  if (content.includes('caches.keys()') && content.includes('caches.delete')) {
    logSuccess('Cache clearing in error handler');
  } else {
    logWarning('Cache clearing may be incomplete');
  }
  
  return true;
}

// Test 5: Production Build Test
async function testProductionBuild() {
  logHeader('TEST 5: PRODUCTION BUILD');
  
  return new Promise((resolve) => {
    logInfo('Running production build...');
    
    const buildProcess = spawn('npm', ['run', 'build'], {
      stdio: 'pipe',
      shell: true,
      env: { ...process.env, NODE_ENV: 'production' }
    });
    
    let output = '';
    let errorOutput = '';
    
    buildProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    buildProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    buildProcess.on('close', (code) => {
      if (code === 0) {
        logSuccess('Production build completed successfully');
        
        // Check for chunk files
        const buildDir = path.join(process.cwd(), '.next/static/chunks');
        if (fs.existsSync(buildDir)) {
          const chunks = fs.readdirSync(buildDir);
          const jsChunks = chunks.filter(file => file.endsWith('.js'));
          
          if (jsChunks.length > 0) {
            logSuccess(`Generated ${jsChunks.length} JavaScript chunks`);
            
            // Check for content hashes
            const hashedChunks = jsChunks.filter(file => /\.[a-f0-9]{8}\.js$/.test(file));
            if (hashedChunks.length > 0) {
              logSuccess(`${hashedChunks.length} chunks have content hashes`);
            } else {
              logWarning('No chunks have content hashes');
            }
          } else {
            logWarning('No JavaScript chunks found');
          }
        } else {
          logWarning('Build chunks directory not found');
        }
        
        resolve(true);
      } else {
        logError('Production build failed');
        console.log('Build output:', output);
        console.log('Build errors:', errorOutput);
        resolve(false);
      }
    });
    
    // Timeout after 5 minutes
    setTimeout(() => {
      buildProcess.kill();
      logError('Build test timed out');
      resolve(false);
    }, 300000);
  });
}

// Test 6: Validate Middleware Safety
function validateMiddlewareSafety() {
  logHeader('TEST 6: MIDDLEWARE SAFETY');
  
  const middlewarePath = path.join(process.cwd(), 'middleware.ts');
  
  if (!fs.existsSync(middlewarePath)) {
    logError('middleware.ts not found');
    return false;
  }
  
  const content = fs.readFileSync(middlewarePath, 'utf8');
  
  // Check for dynamic imports
  if (content.includes('await import(') && content.includes('loadMiddlewareDependencies')) {
    logSuccess('Dynamic imports in middleware');
  } else {
    logError('Dynamic imports not implemented in middleware');
    return false;
  }
  
  // Check for fallback implementations
  if (content.includes('fallback') && content.includes('catch (error)')) {
    logSuccess('Fallback implementations in middleware');
  } else {
    logError('Fallback implementations missing');
    return false;
  }
  
  return true;
}

// Main test runner
async function runAllTests() {
  logHeader('🧪 PRODUCTION BUILD AND CHUNK LOADING TESTS');
  
  const tests = [
    { name: 'Next.js Configuration', fn: validateNextConfig },
    { name: 'Dynamic Import Handler', fn: validateDynamicImportHandler },
    { name: 'Error Boundaries', fn: validateErrorBoundaries },
    { name: 'Layout Error Handling', fn: validateLayoutErrorHandling },
    { name: 'Production Build', fn: testProductionBuild },
    { name: 'Middleware Safety', fn: validateMiddlewareSafety }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
    } catch (error) {
      logError(`${test.name} failed: ${error.message}`);
      results.push({ name: test.name, success: false });
    }
  }
  
  // Summary
  logHeader('📊 TEST SUMMARY');
  
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  results.forEach(result => {
    if (result.success) {
      logSuccess(`${result.name}: PASSED`);
    } else {
      logError(`${result.name}: FAILED`);
    }
  });
  
  console.log(`\n${colors.bright}Result: ${passed}/${total} tests passed${colors.reset}`);
  
  if (passed === total) {
    logSuccess('🎉 All tests passed! Production build is ready');
    
    logHeader('🚀 DEPLOYMENT READY');
    console.log('✅ Chunk loading errors handled');
    console.log('✅ Dynamic imports with retry logic');
    console.log('✅ Production build successful');
    console.log('✅ Error boundaries implemented');
    console.log('✅ Cache management configured');
    console.log('✅ Middleware safety ensured');
    
    return true;
  } else {
    logError('❌ Some tests failed - please fix issues before deployment');
    return false;
  }
}

if (require.main === module) {
  runAllTests()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      logError(`Test runner failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { runAllTests };
