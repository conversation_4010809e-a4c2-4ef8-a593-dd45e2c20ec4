#!/usr/bin/env tsx

/**
 * Production Validation Script
 * 
 * Comprehensive end-to-end testing for production deployment
 * Tests all critical functionality without authentication requirements
 */

import { execSync } from 'child_process';
import fetch from 'node-fetch';

interface ValidationResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'WARN';
  message: string;
  details?: any;
}

class ProductionValidator {
  private results: ValidationResult[] = [];
  private baseUrl: string;

  constructor(baseUrl = 'http://localhost:3001') {
    this.baseUrl = baseUrl;
  }

  private log(test: string, status: 'PASS' | 'FAIL' | 'WARN', message: string, details?: any) {
    const result: ValidationResult = { test, status, message, details };
    this.results.push(result);
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} ${test}: ${message}`);
    if (details) {
      console.log(`   Details: ${JSON.stringify(details, null, 2)}`);
    }
  }

  async validateBuildArtifacts() {
    console.log('\n🔍 Validating Build Artifacts...');
    
    try {
      // Check if .next directory exists
      execSync('ls .next', { stdio: 'pipe' });
      this.log('Build Directory', 'PASS', '.next directory exists');
    } catch (error) {
      this.log('Build Directory', 'FAIL', '.next directory not found');
      return;
    }

    try {
      // Check for static chunks
      const chunks = execSync('ls .next/static/chunks', { encoding: 'utf8' });
      const chunkCount = chunks.split('\n').filter(line => line.trim()).length;
      this.log('Static Chunks', 'PASS', `Found ${chunkCount} chunk files`);
    } catch (error) {
      this.log('Static Chunks', 'FAIL', 'Static chunks directory not found');
    }
  }

  async validateRouteAccess() {
    console.log('\n🌐 Validating Route Access...');
    
    const routes = [
      '/',
      '/dashboard',
      '/upload',
      '/support',
      '/pricing',
      '/api/health',
      '/api/dashboard'
    ];

    for (const route of routes) {
      try {
        const response = await fetch(`${this.baseUrl}${route}`, {
          timeout: 10000,
          headers: {
            'User-Agent': 'Production-Validator/1.0'
          }
        });

        if (response.ok) {
          this.log(`Route ${route}`, 'PASS', `Status: ${response.status}`);
        } else {
          this.log(`Route ${route}`, 'FAIL', `Status: ${response.status}`);
        }
      } catch (error) {
        this.log(`Route ${route}`, 'FAIL', `Error: ${error.message}`);
      }
    }
  }

  async validateAPIEndpoints() {
    console.log('\n🔌 Validating API Endpoints...');
    
    const apiTests = [
      {
        endpoint: '/api/health',
        method: 'GET',
        expectedStatus: 200
      },
      {
        endpoint: '/api/dashboard',
        method: 'GET',
        expectedStatus: 200
      },
      {
        endpoint: '/api/summaries',
        method: 'GET',
        expectedStatus: 200
      }
    ];

    for (const test of apiTests) {
      try {
        const response = await fetch(`${this.baseUrl}${test.endpoint}`, {
          method: test.method,
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Production-Validator/1.0'
          }
        });

        if (response.status === test.expectedStatus) {
          this.log(`API ${test.endpoint}`, 'PASS', `Status: ${response.status}`);
        } else {
          this.log(`API ${test.endpoint}`, 'FAIL', `Expected: ${test.expectedStatus}, Got: ${response.status}`);
        }
      } catch (error) {
        this.log(`API ${test.endpoint}`, 'FAIL', `Error: ${error.message}`);
      }
    }
  }

  async validateFileUploadAPI() {
    console.log('\n📁 Validating File Upload API...');
    
    try {
      // Test file upload endpoint with a simple text file
      const formData = new FormData();
      const testFile = new Blob(['Test content for upload validation'], { type: 'text/plain' });
      formData.append('file', testFile, 'test.txt');
      formData.append('fileId', 'validation-test-' + Date.now());

      const response = await fetch(`${this.baseUrl}/api/upload`, {
        method: 'POST',
        body: formData,
        timeout: 30000
      });

      if (response.ok) {
        const result = await response.json();
        this.log('File Upload API', 'PASS', 'Upload endpoint accessible', result);
      } else {
        this.log('File Upload API', 'FAIL', `Status: ${response.status}`);
      }
    } catch (error) {
      this.log('File Upload API', 'FAIL', `Error: ${error.message}`);
    }
  }

  async validateAISummarization() {
    console.log('\n🤖 Validating AI Summarization...');
    
    try {
      const response = await fetch(`${this.baseUrl}/api/summarize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          transcriptText: 'This is a test transcript for validation purposes. It contains sample content to test the AI summarization functionality.',
          context: 'validation_test'
        }),
        timeout: 30000
      });

      if (response.ok) {
        const result = await response.json();
        this.log('AI Summarization', 'PASS', 'Summarization endpoint accessible', result);
      } else {
        this.log('AI Summarization', 'FAIL', `Status: ${response.status}`);
      }
    } catch (error) {
      this.log('AI Summarization', 'FAIL', `Error: ${error.message}`);
    }
  }

  async validateExportFeatures() {
    console.log('\n📊 Validating Export Features...');
    
    const exportTests = [
      { endpoint: '/api/export/pdf', format: 'PDF' },
      { endpoint: '/api/export/excel', format: 'Excel' },
      { endpoint: '/api/export/notion', format: 'Notion' }
    ];

    for (const test of exportTests) {
      try {
        const response = await fetch(`${this.baseUrl}${test.endpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            summaryId: 'demo-summary-validation'
          }),
          timeout: 15000
        });

        if (response.ok || response.status === 404) {
          // 404 is acceptable for demo summaries
          this.log(`${test.format} Export`, 'PASS', `Endpoint accessible (${response.status})`);
        } else {
          this.log(`${test.format} Export`, 'FAIL', `Status: ${response.status}`);
        }
      } catch (error) {
        this.log(`${test.format} Export`, 'FAIL', `Error: ${error.message}`);
      }
    }
  }

  async validateEnvironmentConfiguration() {
    console.log('\n⚙️ Validating Environment Configuration...');
    
    const requiredEnvVars = [
      'NEXT_PUBLIC_SITE_URL',
      'NEXT_PUBLIC_APP_URL',
      'NEXT_PUBLIC_MODE'
    ];

    for (const envVar of requiredEnvVars) {
      if (process.env[envVar]) {
        this.log(`Env ${envVar}`, 'PASS', `Set to: ${process.env[envVar]}`);
      } else {
        this.log(`Env ${envVar}`, 'WARN', 'Not set');
      }
    }

    // Check public mode
    if (process.env.NEXT_PUBLIC_MODE === 'production') {
      this.log('Public Mode', 'PASS', 'Application in public production mode');
    } else {
      this.log('Public Mode', 'WARN', 'Not in public production mode');
    }
  }

  generateReport() {
    console.log('\n📋 PRODUCTION VALIDATION REPORT');
    console.log('================================');
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARN').length;
    const total = this.results.length;

    console.log(`\nSummary: ${passed}/${total} tests passed`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);

    if (failed === 0) {
      console.log('\n🎉 ALL CRITICAL TESTS PASSED - READY FOR PRODUCTION!');
      return true;
    } else {
      console.log('\n🚨 CRITICAL ISSUES DETECTED - REVIEW REQUIRED');
      console.log('\nFailed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`  ❌ ${r.test}: ${r.message}`));
      return false;
    }
  }

  async runAllValidations() {
    console.log('🚀 Starting Production Validation...');
    console.log(`Testing against: ${this.baseUrl}`);
    
    await this.validateBuildArtifacts();
    await this.validateEnvironmentConfiguration();
    await this.validateRouteAccess();
    await this.validateAPIEndpoints();
    await this.validateFileUploadAPI();
    await this.validateAISummarization();
    await this.validateExportFeatures();
    
    return this.generateReport();
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new ProductionValidator();
  validator.runAllValidations()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Validation failed:', error);
      process.exit(1);
    });
}

export { ProductionValidator };
