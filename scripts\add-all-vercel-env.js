#!/usr/bin/env node

/**
 * Complete script to add all production environment variables to Vercel
 */

const { execSync } = require('child_process');

const envVars = [
  // Core App Configuration
  { name: 'NEXT_PUBLIC_ENVIRONMENT', value: 'production' },
  { name: 'NEXT_PUBLIC_MODE', value: 'production' },
  { name: 'NEXT_PUBLIC_DEV_MODE', value: 'false' },
  
  // Site URLs
  { name: 'NEXT_PUBLIC_APP_URL', value: 'https://slack-summary-scribe-auth-5b9e-v1.vercel.app/' },
  
  // Supabase (already added but ensuring consistency)
  { name: 'SUPABASE_SERVICE_ROLE_KEY', value: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhvbHVwcHdlanpjcXdyYmRiZ2tmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyMDYzMjc3MSwiZXhwIjoyMDM2MjA4NzcxfQ.Hs5Ql0LLzXZZHQKMXEfxEsKXxCcGjVx9NwMYKjwrRLs' },
  { name: 'SUP<PERSON>ASE_URL', value: 'https://holuppwejzcqwrbdbgkf.supabase.co' },
  { name: 'DATABASE_URL', value: 'postgresql://postgres.holuppwejzcqwrbdbgkf:[PHUriuFllxEtAI2h]@aws-0-us-west-1.pooler.supabase.com:6543/postgres' },
  
  // Slack Integration
  { name: 'NEXT_PUBLIC_SLACK_CLIENT_ID', value: '8996307659333.8996321533445' },
  { name: 'SLACK_CLIENT_ID', value: '8996307659333.8996321533445' },
  { name: 'SLACK_CLIENT_SECRET', value: '9ebbe3313ae29fb10d31dbb742fed179' },
  { name: 'SLACK_SIGNING_SECRET', value: '8bd4591adb4c6e25e497eb51ee1acd88' },
  { name: 'SLACK_WEBHOOK_URL', value: 'https://f558d673-94d9-4b8b-ad26-d1e7a7ada0bd.lovable.app/auth/slack/callback' },
  
  // Notion Integration
  { name: 'NOTION_API_TOKEN', value: '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' },
  { name: 'NOTION_DATABASE_ID', value: '67cf789e3c1a4f21b979f90ad7270dd8' },
  
  // Email Service
  { name: 'RESEND_API_KEY', value: 're_CFojG8Ne_4JKVu1Memmai8Ti4bVDWNQFn' },
  { name: 'EMAIL_FROM', value: '<EMAIL>' },
  { name: 'EMAIL_REPLY_TO', value: '<EMAIL>' },
  
  // Monitoring
  { name: 'SENTRY_DSN', value: 'https://<EMAIL>/4509565394419712' },
  { name: 'POSTHOG_PROJECT_API_KEY', value: 'phx_U0sBXanQWe8km5wFnYFB6P90fPMdKCIzrggeyU5ZQZ8DPkK' },
  { name: 'POSTHOG_HOST', value: 'https://app.posthog.com' },
  
  // Security
  { name: 'JWT_SECRET', value: 'gytrgxAa2DAc5WeGmhdDUjljPUbXMN0F' },
  
  // OAuth
  { name: 'GOOGLE_CLIENT_ID', value: '************-lo71jjdj3ag775knro90bksjgjmo835m.apps.googleusercontent.com' },
  { name: 'GOOGLE_CLIENT_SECRET', value: '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' },
  
  // Feature Flags
  { name: 'NEXT_PUBLIC_FEATURE_AI_MODEL_ROUTING', value: 'true' },
  { name: 'NEXT_PUBLIC_FEATURE_PREMIUM_AI', value: 'true' },
  { name: 'NEXT_PUBLIC_FEATURE_AI_ANALYTICS', value: 'true' },
  { name: 'NEXT_PUBLIC_FEATURE_ENTERPRISE_SECURITY', value: 'true' },
  { name: 'NEXT_PUBLIC_FETCH_TIMEOUT', value: '10000' }
];

console.log('🚀 Adding all production environment variables to Vercel...\n');

let successCount = 0;
let failCount = 0;

envVars.forEach(({ name, value }) => {
  try {
    console.log(`Adding ${name}...`);
    execSync(`echo "${value}" | vercel env add ${name} production`, { stdio: 'inherit' });
    console.log(`✅ Added ${name}\n`);
    successCount++;
  } catch (error) {
    console.log(`❌ Failed to add ${name}: ${error.message}\n`);
    failCount++;
  }
});

console.log(`🎉 Environment variables setup complete!`);
console.log(`✅ Successfully added: ${successCount}`);
console.log(`❌ Failed to add: ${failCount}`);
