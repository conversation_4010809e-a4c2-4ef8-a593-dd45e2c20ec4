'use client';

import { useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { 
  Menu, 
  Home, 
  Upload, 
  FileText, 
  Settings, 
  User,
  ChevronLeft,
  Smartphone,
  Tablet,
  Monitor,
  Wifi,
  WifiOff,
  Battery,
  BatteryLow
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileOptimizedLayoutProps {
  children: ReactNode;
  title?: string;
  showBackButton?: boolean;
  className?: string;
}

interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  orientation: 'portrait' | 'landscape';
  screenSize: string;
  isOnline: boolean;
  batteryLevel?: number;
  isLowPowerMode: boolean;
}

export default function MobileOptimizedLayout({
  children,
  title,
  showBackButton = false,
  className
}: MobileOptimizedLayoutProps) {
  const router = useRouter();
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    orientation: 'landscape',
    screenSize: 'desktop',
    isOnline: true,
    isLowPowerMode: false
  });
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Device detection and monitoring
  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const isMobile = width < 768;
      const isTablet = width >= 768 && width < 1024;
      const isDesktop = width >= 1024;
      const orientation = width > height ? 'landscape' : 'portrait';

      let screenSize = 'desktop';
      if (isMobile) screenSize = 'mobile';
      else if (isTablet) screenSize = 'tablet';

      setDeviceInfo(prev => ({
        ...prev,
        isMobile,
        isTablet,
        isDesktop,
        orientation,
        screenSize,
        isOnline: navigator.onLine
      }));
    };

    // Initial detection
    updateDeviceInfo();

    // Event listeners
    window.addEventListener('resize', updateDeviceInfo);
    window.addEventListener('orientationchange', updateDeviceInfo);
    window.addEventListener('online', updateDeviceInfo);
    window.addEventListener('offline', updateDeviceInfo);

    // Battery API (if supported)
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        const updateBattery = () => {
          setDeviceInfo(prev => ({
            ...prev,
            batteryLevel: battery.level,
            isLowPowerMode: battery.level < 0.2 && !battery.charging
          }));
        };

        updateBattery();
        battery.addEventListener('levelchange', updateBattery);
        battery.addEventListener('chargingchange', updateBattery);
      });
    }

    return () => {
      window.removeEventListener('resize', updateDeviceInfo);
      window.removeEventListener('orientationchange', updateDeviceInfo);
      window.removeEventListener('online', updateDeviceInfo);
      window.removeEventListener('offline', updateDeviceInfo);
    };
  }, []);

  const navigationItems = [
    { icon: Home, label: 'Dashboard', href: '/dashboard' },
    { icon: Upload, label: 'Upload', href: '/upload' },
    { icon: FileText, label: 'Summaries', href: '/summaries' },
    { icon: Settings, label: 'Settings', href: '/settings' },
    { icon: User, label: 'Profile', href: '/profile' }
  ];

  const DeviceIndicator = () => {
    if (!deviceInfo.isMobile) return null;

    return (
      <div className="fixed top-4 right-4 z-50 flex items-center space-x-2 bg-black/80 text-white px-3 py-1 rounded-full text-xs">
        {deviceInfo.isMobile && <Smartphone className="h-3 w-3" />}
        {deviceInfo.isTablet && <Tablet className="h-3 w-3" />}
        {deviceInfo.isDesktop && <Monitor className="h-3 w-3" />}
        
        {deviceInfo.isOnline ? (
          <Wifi className="h-3 w-3 text-green-400" />
        ) : (
          <WifiOff className="h-3 w-3 text-red-400" />
        )}
        
        {deviceInfo.batteryLevel !== undefined && (
          deviceInfo.isLowPowerMode ? (
            <BatteryLow className="h-3 w-3 text-red-400" />
          ) : (
            <Battery className="h-3 w-3 text-green-400" />
          )
        )}
        
        <span>{deviceInfo.screenSize}</span>
      </div>
    );
  };

  const MobileHeader = () => {
    if (!deviceInfo.isMobile) return null;

    return (
      <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="flex items-center space-x-2">
            {showBackButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            )}
            
            <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Menu className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-64">
                <nav className="flex flex-col space-y-2 mt-6">
                  {navigationItems.map((item) => (
                    <Button
                      key={item.href}
                      variant="ghost"
                      className="justify-start"
                      onClick={() => {
                        router.push(item.href);
                        setIsMenuOpen(false);
                      }}
                    >
                      <item.icon className="h-4 w-4 mr-2" />
                      {item.label}
                    </Button>
                  ))}
                </nav>
              </SheetContent>
            </Sheet>
          </div>
          
          {title && (
            <h1 className="ml-4 text-lg font-semibold truncate">{title}</h1>
          )}
        </div>
      </header>
    );
  };

  const getLayoutClasses = () => {
    const baseClasses = "min-h-screen bg-background";
    
    if (deviceInfo.isMobile) {
      return cn(
        baseClasses,
        "pb-safe-area-inset-bottom",
        deviceInfo.orientation === 'landscape' && "landscape-optimized",
        deviceInfo.isLowPowerMode && "low-power-mode"
      );
    }
    
    return baseClasses;
  };

  const getContentClasses = () => {
    const baseClasses = "container mx-auto px-4";
    
    if (deviceInfo.isMobile) {
      return cn(
        baseClasses,
        "pt-4 pb-6",
        deviceInfo.orientation === 'landscape' && "pt-2 pb-4"
      );
    }
    
    return cn(baseClasses, "py-8");
  };

  return (
    <div className={cn(getLayoutClasses(), className)}>
      <DeviceIndicator />
      <MobileHeader />
      
      <main 
        id="main-content"
        className={getContentClasses()}
        style={{
          // Optimize for mobile performance
          ...(deviceInfo.isMobile && deviceInfo.isLowPowerMode && {
            willChange: 'auto',
            transform: 'translateZ(0)'
          })
        }}
      >
        {children}
      </main>
      
      {/* Mobile-specific optimizations */}
      {deviceInfo.isMobile && (
        <style jsx>{`
          .landscape-optimized {
            padding-top: env(safe-area-inset-top);
            padding-left: env(safe-area-inset-left);
            padding-right: env(safe-area-inset-right);
          }
          
          .low-power-mode * {
            animation-duration: 0s !important;
            transition-duration: 0s !important;
          }
          
          .pb-safe-area-inset-bottom {
            padding-bottom: env(safe-area-inset-bottom);
          }
          
          @media (max-width: 767px) {
            .mobile-full-width {
              width: 100vw;
              margin-left: calc(-50vw + 50%);
            }
            
            .mobile-touch-target {
              min-height: 44px;
              min-width: 44px;
            }
          }
          
          @media (orientation: landscape) and (max-height: 500px) {
            .landscape-optimized .container {
              padding-top: 0.5rem;
              padding-bottom: 0.5rem;
            }
          }
        `}</style>
      )}
    </div>
  );
}

// Hook for mobile-specific optimizations
export function useMobileOptimizations() {
  const [isMobile, setIsMobile] = useState(false);
  const [isLowPowerMode, setIsLowPowerMode] = useState(false);
  const [connectionType, setConnectionType] = useState<string>('unknown');

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    const checkConnection = () => {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      if (connection) {
        setConnectionType(connection.effectiveType || 'unknown');
        setIsLowPowerMode(connection.saveData || connection.effectiveType === 'slow-2g');
      }
    };

    checkMobile();
    checkConnection();

    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  return {
    isMobile,
    isLowPowerMode,
    connectionType,
    shouldOptimizeImages: isLowPowerMode || connectionType === 'slow-2g',
    shouldReduceAnimations: isLowPowerMode,
    shouldLazyLoad: isMobile || isLowPowerMode
  };
}
