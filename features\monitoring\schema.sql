-- Monitoring & Stability Database Schema
-- Run this in your Supabase SQL editor

-- Webhook delivery tracking table
CREATE TABLE IF NOT EXISTS webhook_deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider TEXT NOT NULL CHECK (provider IN ('stripe', 'slack', 'custom')),
  event_type TEXT NOT NULL,
  webhook_id TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'delivered', 'failed', 'retrying')),
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 5,
  last_attempt_at TIMESTAMP WITH TIME ZONE,
  next_retry_at TIMESTAMP WITH TIME ZONE,
  response_code INTEGER,
  response_body TEXT,
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System health logs table
CREATE TABLE IF NOT EXISTS system_health_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  overall_status TEXT NOT NULL CHECK (overall_status IN ('healthy', 'degraded', 'unhealthy')),
  checks JSONB NOT NULL,
  summary JSONB NOT NULL,
  response_time_ms INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name TEXT NOT NULL,
  metric_type TEXT NOT NULL CHECK (metric_type IN ('query', 'api', 'webhook', 'auth')),
  value NUMERIC NOT NULL,
  unit TEXT NOT NULL DEFAULT 'ms',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Alert history table
CREATE TABLE IF NOT EXISTS alert_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_type TEXT NOT NULL,
  severity TEXT NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_provider_status ON webhook_deliveries(provider, status);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_next_retry ON webhook_deliveries(next_retry_at) WHERE status = 'retrying';
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_created_at ON webhook_deliveries(created_at);

CREATE INDEX IF NOT EXISTS idx_system_health_logs_created_at ON system_health_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_system_health_logs_status ON system_health_logs(overall_status);

CREATE INDEX IF NOT EXISTS idx_performance_metrics_name_created ON performance_metrics(metric_name, created_at);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type_created ON performance_metrics(metric_type, created_at);

CREATE INDEX IF NOT EXISTS idx_alert_history_created_at ON alert_history(created_at);
CREATE INDEX IF NOT EXISTS idx_alert_history_severity ON alert_history(severity);

-- RLS Policies
ALTER TABLE webhook_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_health_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE alert_history ENABLE ROW LEVEL SECURITY;

-- Only allow system/admin access to monitoring tables
CREATE POLICY "System access only" ON webhook_deliveries FOR ALL USING (false);
CREATE POLICY "System access only" ON system_health_logs FOR ALL USING (false);
CREATE POLICY "System access only" ON performance_metrics FOR ALL USING (false);
CREATE POLICY "System access only" ON alert_history FOR ALL USING (false);

-- Functions for automated cleanup
CREATE OR REPLACE FUNCTION cleanup_old_monitoring_data()
RETURNS void AS $$
BEGIN
  -- Clean up webhook deliveries older than 30 days
  DELETE FROM webhook_deliveries 
  WHERE created_at < NOW() - INTERVAL '30 days';
  
  -- Clean up health logs older than 7 days
  DELETE FROM system_health_logs 
  WHERE created_at < NOW() - INTERVAL '7 days';
  
  -- Clean up performance metrics older than 30 days
  DELETE FROM performance_metrics 
  WHERE created_at < NOW() - INTERVAL '30 days';
  
  -- Clean up resolved alerts older than 90 days
  DELETE FROM alert_history 
  WHERE resolved_at IS NOT NULL 
  AND resolved_at < NOW() - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to record system health
CREATE OR REPLACE FUNCTION record_system_health(
  p_overall_status TEXT,
  p_checks JSONB,
  p_summary JSONB,
  p_response_time_ms INTEGER
)
RETURNS UUID AS $$
DECLARE
  health_id UUID;
BEGIN
  INSERT INTO system_health_logs (
    overall_status,
    checks,
    summary,
    response_time_ms
  ) VALUES (
    p_overall_status,
    p_checks,
    p_summary,
    p_response_time_ms
  ) RETURNING id INTO health_id;
  
  RETURN health_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to record performance metric
CREATE OR REPLACE FUNCTION record_performance_metric(
  p_metric_name TEXT,
  p_metric_type TEXT,
  p_value NUMERIC,
  p_unit TEXT DEFAULT 'ms',
  p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
  metric_id UUID;
BEGIN
  INSERT INTO performance_metrics (
    metric_name,
    metric_type,
    value,
    unit,
    metadata
  ) VALUES (
    p_metric_name,
    p_metric_type,
    p_value,
    p_unit,
    p_metadata
  ) RETURNING id INTO metric_id;
  
  RETURN metric_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create alert
CREATE OR REPLACE FUNCTION create_alert(
  p_alert_type TEXT,
  p_severity TEXT,
  p_title TEXT,
  p_message TEXT,
  p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
  alert_id UUID;
BEGIN
  INSERT INTO alert_history (
    alert_type,
    severity,
    title,
    message,
    metadata
  ) VALUES (
    p_alert_type,
    p_severity,
    p_title,
    p_message,
    p_metadata
  ) RETURNING id INTO alert_id;
  
  RETURN alert_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update webhook_deliveries updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_webhook_deliveries_updated_at
  BEFORE UPDATE ON webhook_deliveries
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO service_role;
