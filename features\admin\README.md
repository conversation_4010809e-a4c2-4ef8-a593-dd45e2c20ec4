# 🧑‍💼 Admin Dashboard

## Overview
Comprehensive admin dashboard for managing users, organizations, billing, and system health across the entire SaaS platform.

## Features
- ✅ User management and analytics
- ✅ Organization insights and controls
- ✅ Billing and subscription management
- ✅ System health monitoring
- ✅ Support ticket management
- ✅ Feature flag controls
- ✅ Email campaign management
- 🟡 Advanced analytics and reporting
- 🔴 A/B testing dashboard (planned)

## Module Structure
```
/features/admin/
├── README.md                   # This file
├── types.ts                   # Admin-specific types
├── services/
│   ├── users.service.ts       # User management operations
│   ├── organizations.service.ts # Organization management
│   ├── billing.service.ts     # Billing and subscription management
│   ├── system.service.ts      # System health and monitoring
│   └── support.service.ts     # Support ticket management
├── components/
│   ├── AdminDashboard.tsx     # Main admin dashboard
│   ├── UserManagement.tsx     # User management interface
│   ├── OrganizationTable.tsx  # Organization management
│   ├── BillingOverview.tsx    # Billing analytics
│   ├── SystemHealth.tsx       # System monitoring
│   ├── SupportTickets.tsx     # Support management
│   ├── FeatureFlagPanel.tsx   # Feature flag controls
│   └── EmailCampaigns.tsx     # Email campaign management
├── api/
│   └── handlers/
│       ├── users.handler.ts   # User management API
│       ├── organizations.handler.ts # Organization API
│       ├── billing.handler.ts # Billing management API
│       └── system.handler.ts  # System management API
├── hooks/
│   ├── useAdminData.ts        # Admin data fetching
│   ├── useUserManagement.ts   # User management operations
│   └── useSystemHealth.ts     # System health monitoring
└── utils/
    ├── admin-helpers.ts       # Admin utility functions
    ├── data-export.ts         # Data export utilities
    └── audit-logging.ts       # Admin action logging
```

## Database Schema
```sql
-- Admin users table
CREATE TABLE admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('super_admin', 'admin', 'support')),
  permissions JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- Admin audit log
CREATE TABLE admin_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_user_id UUID REFERENCES admin_users(id),
  action TEXT NOT NULL,
  target_type TEXT NOT NULL, -- 'user', 'organization', 'billing', etc.
  target_id TEXT,
  details JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Support tickets
CREATE TABLE support_tickets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  organization_id UUID REFERENCES organizations(id),
  subject TEXT NOT NULL,
  description TEXT NOT NULL,
  status TEXT DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  assigned_to UUID REFERENCES admin_users(id),
  tags TEXT[],
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved_at TIMESTAMP WITH TIME ZONE
);

-- System alerts
CREATE TABLE system_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_type TEXT NOT NULL,
  severity TEXT NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  acknowledged BOOLEAN DEFAULT false,
  acknowledged_by UUID REFERENCES admin_users(id),
  acknowledged_at TIMESTAMP WITH TIME ZONE,
  resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Admin Dashboard Components

### Main Dashboard
```typescript
// features/admin/components/AdminDashboard.tsx
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Building2, 
  CreditCard, 
  Activity, 
  AlertTriangle,
  TrendingUp,
  Mail,
  Settings
} from 'lucide-react';

import UserManagement from './UserManagement';
import OrganizationTable from './OrganizationTable';
import BillingOverview from './BillingOverview';
import SystemHealth from './SystemHealth';
import SupportTickets from './SupportTickets';
import FeatureFlagPanel from './FeatureFlagPanel';
import EmailCampaigns from './EmailCampaigns';

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalOrganizations: number;
  monthlyRevenue: number;
  openTickets: number;
  systemAlerts: number;
  conversionRate: number;
  churnRate: number;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchAdminStats();
  }, []);

  const fetchAdminStats = async () => {
    try {
      const response = await fetch('/api/admin/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch admin stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600">Manage users, organizations, and system health</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={fetchAdminStats}>
            <Activity className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="organizations">Organizations</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="support">Support</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Users</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalUsers.toLocaleString()}</p>
                    <p className="text-sm text-green-600">
                      {stats?.activeUsers} active
                    </p>
                  </div>
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Organizations</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalOrganizations.toLocaleString()}</p>
                    <p className="text-sm text-blue-600">
                      {Math.round((stats?.totalOrganizations || 0) / (stats?.totalUsers || 1) * 100)}% org rate
                    </p>
                  </div>
                  <Building2 className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                    <p className="text-2xl font-bold text-gray-900">${stats?.monthlyRevenue.toLocaleString()}</p>
                    <p className="text-sm text-green-600">
                      {stats?.conversionRate.toFixed(1)}% conversion
                    </p>
                  </div>
                  <CreditCard className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Open Tickets</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.openTickets}</p>
                    <p className="text-sm text-orange-600">
                      {stats?.systemAlerts} system alerts
                    </p>
                  </div>
                  <AlertTriangle className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common administrative tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button variant="outline" onClick={() => setActiveTab('users')}>
                  <Users className="w-4 h-4 mr-2" />
                  Manage Users
                </Button>
                <Button variant="outline" onClick={() => setActiveTab('billing')}>
                  <CreditCard className="w-4 h-4 mr-2" />
                  View Billing
                </Button>
                <Button variant="outline" onClick={() => setActiveTab('support')}>
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  Support Queue
                </Button>
                <Button variant="outline" onClick={() => setActiveTab('system')}>
                  <Activity className="w-4 h-4 mr-2" />
                  System Health
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest system events and user actions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant="secondary">User</Badge>
                    <span className="text-sm">New user registration: <EMAIL></span>
                  </div>
                  <span className="text-xs text-gray-500">2 minutes ago</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant="default">Billing</Badge>
                    <span className="text-sm">Subscription upgraded: Acme Corp → Pro Plan</span>
                  </div>
                  <span className="text-xs text-gray-500">15 minutes ago</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant="destructive">Alert</Badge>
                    <span className="text-sm">High API error rate detected</span>
                  </div>
                  <span className="text-xs text-gray-500">1 hour ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <UserManagement />
        </TabsContent>

        <TabsContent value="organizations">
          <OrganizationTable />
        </TabsContent>

        <TabsContent value="billing">
          <BillingOverview />
        </TabsContent>

        <TabsContent value="support">
          <SupportTickets />
        </TabsContent>

        <TabsContent value="system">
          <SystemHealth />
        </TabsContent>

        <TabsContent value="features">
          <FeatureFlagPanel />
        </TabsContent>

        <TabsContent value="campaigns">
          <EmailCampaigns />
        </TabsContent>
      </Tabs>
    </div>
  );
}
```

## Admin Services

### User Management Service
```typescript
// features/admin/services/users.service.ts
import { createSupabaseServerClient } from '@/lib/supabase-server';

export interface AdminUser {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
  plan: string;
  status: 'active' | 'inactive' | 'suspended';
  lastActiveAt?: string;
  createdAt: string;
  organizationCount: number;
  summaryCount: number;
  billingStatus?: string;
}

export interface UserFilters {
  search?: string;
  plan?: string;
  status?: string;
  dateRange?: { start: string; end: string };
  page?: number;
  limit?: number;
}

/**
 * Get paginated list of users with admin details
 */
export async function getUsers(
  filters: UserFilters = {}
): Promise<{
  users: AdminUser[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  error?: string;
}> {
  try {
    const supabase = await createSupabaseServerClient();
    const { page = 1, limit = 20 } = filters;
    const offset = (page - 1) * limit;

    // Build query with filters
    let query = supabase
      .from('profiles')
      .select(`
        *,
        user_organizations!inner (
          organizations (
            plan
          )
        ),
        summaries (count)
      `, { count: 'exact' });

    // Apply filters
    if (filters.search) {
      query = query.or(`email.ilike.%${filters.search}%,full_name.ilike.%${filters.search}%`);
    }

    if (filters.dateRange) {
      query = query
        .gte('created_at', filters.dateRange.start)
        .lte('created_at', filters.dateRange.end);
    }

    // Get total count
    const { count: total } = await query;

    // Get paginated results
    const { data: users, error } = await query
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    if (error) {
      return {
        users: [],
        pagination: { page, limit, total: 0, pages: 0 },
        error: error.message
      };
    }

    // Format users for admin view
    const formattedUsers: AdminUser[] = users.map(user => ({
      id: user.id,
      email: user.email,
      fullName: user.full_name,
      avatarUrl: user.avatar_url,
      plan: user.user_organizations?.[0]?.organizations?.plan || 'FREE',
      status: user.status || 'active',
      lastActiveAt: user.last_active_at,
      createdAt: user.created_at,
      organizationCount: user.user_organizations?.length || 0,
      summaryCount: user.summaries?.[0]?.count || 0,
    }));

    return {
      users: formattedUsers,
      pagination: {
        page,
        limit,
        total: total || 0,
        pages: Math.ceil((total || 0) / limit)
      }
    };

  } catch (error) {
    console.error('Failed to get users:', error);
    return {
      users: [],
      pagination: { page: 1, limit: 20, total: 0, pages: 0 },
      error: 'Failed to fetch users'
    };
  }
}

/**
 * Get detailed user information
 */
export async function getUserDetails(
  userId: string
): Promise<{ user: AdminUser | null; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    const { data: user, error } = await supabase
      .from('profiles')
      .select(`
        *,
        user_organizations (
          role,
          created_at,
          organizations (
            id,
            name,
            plan
          )
        ),
        summaries (
          id,
          title,
          created_at
        )
      `)
      .eq('id', userId)
      .single();

    if (error || !user) {
      return { user: null, error: error?.message || 'User not found' };
    }

    const formattedUser: AdminUser = {
      id: user.id,
      email: user.email,
      fullName: user.full_name,
      avatarUrl: user.avatar_url,
      plan: user.user_organizations?.[0]?.organizations?.plan || 'FREE',
      status: user.status || 'active',
      lastActiveAt: user.last_active_at,
      createdAt: user.created_at,
      organizationCount: user.user_organizations?.length || 0,
      summaryCount: user.summaries?.length || 0,
    };

    return { user: formattedUser };

  } catch (error) {
    console.error('Failed to get user details:', error);
    return { user: null, error: 'Failed to fetch user details' };
  }
}

/**
 * Update user status (suspend, activate, etc.)
 */
export async function updateUserStatus(
  userId: string,
  status: 'active' | 'inactive' | 'suspended',
  adminUserId: string,
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Update user status
    const { error } = await supabase
      .from('profiles')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      return { success: false, error: error.message };
    }

    // Log admin action
    await logAdminAction(adminUserId, 'user_status_update', 'user', userId, {
      newStatus: status,
      reason
    });

    return { success: true };

  } catch (error) {
    console.error('Failed to update user status:', error);
    return { success: false, error: 'Failed to update user status' };
  }
}

/**
 * Delete user and all associated data
 */
export async function deleteUser(
  userId: string,
  adminUserId: string,
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // This would trigger cascading deletes due to foreign key constraints
    const { error } = await supabase.auth.admin.deleteUser(userId);

    if (error) {
      return { success: false, error: error.message };
    }

    // Log admin action
    await logAdminAction(adminUserId, 'user_delete', 'user', userId, {
      reason
    });

    return { success: true };

  } catch (error) {
    console.error('Failed to delete user:', error);
    return { success: false, error: 'Failed to delete user' };
  }
}

/**
 * Log admin action for audit trail
 */
async function logAdminAction(
  adminUserId: string,
  action: string,
  targetType: string,
  targetId: string,
  details: Record<string, any> = {}
): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();

    await supabase
      .from('admin_audit_log')
      .insert({
        admin_user_id: adminUserId,
        action,
        target_type: targetType,
        target_id: targetId,
        details,
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('Failed to log admin action:', error);
  }
}

/**
 * Get user analytics and insights
 */
export async function getUserAnalytics(): Promise<{
  totalUsers: number;
  activeUsers: number;
  newUsersToday: number;
  usersByPlan: Record<string, number>;
  userGrowth: Array<{ date: string; count: number }>;
  error?: string;
}> {
  try {
    const supabase = await createSupabaseServerClient();

    // Get total and active users
    const { data: userStats } = await supabase
      .from('profiles')
      .select('id, created_at, last_active_at, status');

    if (!userStats) {
      throw new Error('Failed to fetch user stats');
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const totalUsers = userStats.length;
    const activeUsers = userStats.filter(user => 
      user.last_active_at && new Date(user.last_active_at) > thirtyDaysAgo
    ).length;
    const newUsersToday = userStats.filter(user => 
      new Date(user.created_at) >= today
    ).length;

    // Get users by plan
    const { data: planStats } = await supabase
      .from('user_organizations')
      .select(`
        organizations (
          plan
        )
      `);

    const usersByPlan = planStats?.reduce((acc, item) => {
      const plan = item.organizations?.plan || 'FREE';
      acc[plan] = (acc[plan] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Generate user growth data (last 30 days)
    const userGrowth = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      const count = userStats.filter(user => 
        new Date(user.created_at) <= date
      ).length;
      
      userGrowth.push({ date: dateStr, count });
    }

    return {
      totalUsers,
      activeUsers,
      newUsersToday,
      usersByPlan,
      userGrowth
    };

  } catch (error) {
    console.error('Failed to get user analytics:', error);
    return {
      totalUsers: 0,
      activeUsers: 0,
      newUsersToday: 0,
      usersByPlan: {},
      userGrowth: [],
      error: 'Failed to fetch user analytics'
    };
  }
}
