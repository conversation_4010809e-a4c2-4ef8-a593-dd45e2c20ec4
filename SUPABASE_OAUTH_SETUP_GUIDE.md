# 🔐 Supabase OAuth Configuration Setup Guide

## 🚨 CRITICAL: Required Supabase Dashboard Configuration

Your Slack Summary Scribe SaaS authentication system is **technically ready** but requires Supabase OAuth configuration to function.

### 📋 Project Details
- **Project ID**: `holuppwejzcqwrbdbgkf`
- **Supabase URL**: `https://holuppwejzcqwrbdbgkf.supabase.co`
- **Local Development URL**: `http://localhost:3000`
- **OAuth Callback URL**: `http://localhost:3000/api/auth/callback`

---

## 🔧 Step 1: Access Supabase Dashboard

1. **Go to Supabase Dashboard**:
   ```
   https://supabase.com/dashboard/project/holuppwejzcqwrbdbgkf/auth/settings
   ```

2. **Navigate to Authentication → Settings**

---

## 🌐 Step 2: Configure Site URL

1. **Find "Site URL" section**
2. **Set Site URL to**:
   ```
   http://localhost:3000
   ```
3. **Click "Save"**

---

## 🔗 Step 3: Configure Redirect URLs

1. **Find "Redirect URLs" section**
2. **Add these URLs** (one per line):
   ```
   http://localhost:3000/api/auth/callback
   http://localhost:3000/auth/callback
   http://localhost:3000/
   ```
3. **Click "Save"**

---

## 🔐 Step 4: Configure OAuth Providers

### Google OAuth Configuration

1. **Go to Authentication → Providers**
2. **Find "Google" provider**
3. **Enable Google OAuth**
4. **Configure Google OAuth settings**:
   - **Redirect URI**: `http://localhost:3000/api/auth/callback`
   - **Authorized JavaScript origins**: `http://localhost:3000`

### GitHub OAuth Configuration (Optional)

1. **Find "GitHub" provider**
2. **Enable GitHub OAuth**
3. **Configure GitHub OAuth settings**:
   - **Authorization callback URL**: `http://localhost:3000/api/auth/callback`

### Slack OAuth Configuration (Optional)

1. **Find "Slack" provider**
2. **Enable Slack OAuth**
3. **Configure Slack OAuth settings**:
   - **Redirect URLs**: `http://localhost:3000/api/auth/callback`

---

## ✅ Step 5: Verify Configuration

After configuring Supabase OAuth settings:

### 1. Test OAuth Configuration
```bash
Visit: http://localhost:3000/debug-auth
Check: OAuth configuration shows correct callback URL
```

### 2. Test OAuth Flow
```bash
Visit: http://localhost:3000/test-oauth-flow
Click: "Start OAuth Flow with Google"
Verify: Redirects to Google OAuth
Complete: Google authorization
Check: Returns to callback with authorization code
```

### 3. Verify Session Establishment
```bash
Check: Cookies are set (sb-holuppwejzcqwrbdbgkf-auth-token)
Check: Session persists across page refreshes
Check: Dashboard loads without redirect loops
```

---

## 🧪 Testing Sequence

### Before OAuth Configuration
- ❌ OAuth callback receives no authorization code
- ❌ No session cookies are set
- ❌ All test routes show "No active session"

### After OAuth Configuration
- ✅ OAuth callback receives authorization code
- ✅ Session cookies are set correctly
- ✅ All test routes show active session with user data

---

## 🔍 Troubleshooting

### Issue: "No authorization code received"
**Cause**: OAuth redirect URLs not configured in Supabase
**Solution**: Add `http://localhost:3000/api/auth/callback` to Supabase redirect URLs

### Issue: "OAuth provider not enabled"
**Cause**: OAuth provider (Google) not enabled in Supabase
**Solution**: Enable Google OAuth in Supabase Authentication → Providers

### Issue: "Invalid redirect URI"
**Cause**: OAuth provider (Google Console) redirect URI doesn't match
**Solution**: Update Google OAuth Console redirect URI to match Supabase callback

---

## 📋 Configuration Checklist

### Supabase Dashboard
- [ ] Site URL set to `http://localhost:3000`
- [ ] Redirect URLs include `http://localhost:3000/api/auth/callback`
- [ ] Google OAuth provider enabled
- [ ] Google OAuth redirect URI configured

### Google OAuth Console (if using Google)
- [ ] OAuth consent screen configured
- [ ] OAuth client ID and secret generated
- [ ] Authorized redirect URIs include `http://localhost:3000/api/auth/callback`
- [ ] Authorized JavaScript origins include `http://localhost:3000`

### Local Testing
- [ ] Server running on `http://localhost:3000`
- [ ] Environment variables correctly configured
- [ ] OAuth test page loads without errors

---

## 🚀 Expected Results After Configuration

### OAuth Flow
1. User clicks "Sign In with Google"
2. Redirects to Google OAuth authorization
3. User authorizes application
4. Google redirects to `http://localhost:3000/api/auth/callback?code=...`
5. Callback exchanges code for session
6. Cookies set: `sb-holuppwejzcqwrbdbgkf-auth-token`, etc.
7. User redirected to dashboard with active session

### Session Persistence
- ✅ Session survives page refreshes
- ✅ Middleware detects authenticated users
- ✅ Dashboard loads without redirect loops
- ✅ All test routes show active session

### Test Routes Results
- `/debug-auth`: Shows active session with user email
- `/test-oauth-flow`: Completes OAuth flow successfully
- `/test-manual-session`: Works for email/password authentication
- `/test-sync`: Shows client-server session synchronization
- `/test-e2e-auth`: Passes all validation tests
- `/test-cookie-management`: Shows Supabase cookies present

---

## 🎯 Next Steps

1. **Configure Supabase OAuth** (5 minutes)
2. **Test OAuth Flow** with `/test-oauth-flow`
3. **Verify All Test Routes** show active sessions
4. **Deploy to Production** with updated OAuth URLs

Once Supabase OAuth is configured, the authentication system will work immediately!
