import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import ExcelJS from 'exceljs';
import { getCurrentUser } from '@/lib/user-management';
import { trackPublicExport } from '@/lib/analytics';

export async function POST(request: NextRequest) {
  try {
  devLog.log('📊 Excel Export API called - Production Demo Mode');

    // Get user (public mode - no authentication required)
    const user = await getCurrentUser();
    if (!user) {
  devLog.log('📊 Using anonymous user for Excel export');
    }
    const userId = user?.id || 'anonymous-' + Date.now();
    const { summaryId } = await request.json();

    if (!summaryId) {
      return NextResponse.json(
        { success: false, error: 'Summary ID is required' },
        { status: 400 }
      );
    }
  devLog.log('📊 Excel Export: Processing demo summary:', summaryId);

    // Demo summary data - check if it's a demo summary
    if (!summaryId.startsWith('demo-summary-')) {
      return NextResponse.json(
        { success: false, error: 'Summary not found' },
        { status: 404 }
      );
    }

    // Generate demo summary data
    const summary = {
      id: summaryId,
      title: 'Demo Meeting Summary - Q4 Planning Session',
      content: `Q4 Planning Session Summary

Meeting Overview:
- Date: ${new Date().toLocaleDateString()}
- Duration: 45 minutes
- Participants: 8 team members

Key Discussion Points:
1. Revenue Targets - Q4 target: $2.5M ARR
2. Product Roadmap - AI features and mobile app launch
3. Team Expansion - Hiring across engineering and sales

Action Items:
- Sarah: Finalize Q4 marketing budget by Friday
- Mike: Complete technical architecture review
- Lisa: Schedule customer feedback sessions
- Team: Prepare individual Q4 goals by next week

Summary: Productive session with clear alignment on Q4 objectives.`,
      summary_data: {
        summary: 'Productive Q4 planning session with clear objectives and action items',
        key_points: [
          'Q4 revenue target set at $2.5M ARR',
          'Product roadmap includes AI features and mobile app',
          'Team expansion planned across engineering and sales',
          'Clear action items assigned with deadlines'
        ],
        action_items: [
          'Finalize Q4 marketing budget',
          'Complete technical architecture review',
          'Schedule customer feedback sessions',
          'Prepare individual Q4 goals'
        ],
        participants: 8,
        duration: '45 minutes',
        sentiment: 'positive'
      },
      tags: ['planning', 'q4', 'revenue', 'roadmap', 'team'],
      created_at: new Date().toISOString(),
      user_id: userId,
      organization_id: user.id
    };

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    
    // Set workbook properties
    workbook.creator = 'Slack Summary Scribe';
    workbook.lastModifiedBy = 'Demo User';
    workbook.created = new Date();
    workbook.modified = new Date();

    // Create main summary worksheet
    const summarySheet = workbook.addWorksheet('Summary');
    
    // Set column widths
    summarySheet.columns = [
      { header: 'Field', key: 'field', width: 20 },
      { header: 'Value', key: 'value', width: 80 }
    ];

    // Add header styling
    summarySheet.getRow(1).font = { bold: true, size: 12 };
    summarySheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    };

    // Add summary data
    const summaryFields = [
      { field: 'Title', value: summary.title || 'Untitled Summary' },
      { field: 'Created Date', value: new Date(summary.created_at).toLocaleString() },
      { field: 'Source Type', value: 'Demo' },
      { field: 'File Name', value: 'demo-export.xlsx' },
      { field: 'Organization', value: 'Demo Organization' },
      { field: '', value: '' }, // Empty row
      { field: 'Summary Content', value: '' }
    ];

    summaryFields.forEach((row, index) => {
      const rowNum = index + 2;
      summarySheet.addRow(row);
      
      // Make field names bold
      summarySheet.getCell(`A${rowNum}`).font = { bold: true };
      
      // Add border to all cells
      ['A', 'B'].forEach(col => {
        summarySheet.getCell(`${col}${rowNum}`).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    });

    // Add summary content in a merged cell
    const contentStartRow = summaryFields.length + 2;
    summarySheet.mergeCells(`A${contentStartRow}:B${contentStartRow + 20}`);
    const contentCell = summarySheet.getCell(`A${contentStartRow}`);
    contentCell.value = summary.content || 'No content available';
    contentCell.alignment = { 
      vertical: 'top', 
      horizontal: 'left',
      wrapText: true 
    };
    contentCell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };

    // Create metadata worksheet (demo data)
    const demoMetadata = {
      processing_time: '1.25 seconds',
      ai_model: 'deepseek-chat',
      confidence_score: '92%',
      word_count: 450,
      language: 'English'
    };

    if (demoMetadata) {
      const metadataSheet = workbook.addWorksheet('Metadata');
      
      metadataSheet.columns = [
        { header: 'Property', key: 'property', width: 25 },
        { header: 'Value', key: 'value', width: 30 }
      ];

      // Add header styling
      metadataSheet.getRow(1).font = { bold: true, size: 12 };
      metadataSheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6F3FF' }
      };

      // Add metadata
      Object.entries(demoMetadata).forEach(([key, value]) => {
        metadataSheet.addRow({
          property: key.charAt(0).toUpperCase() + key.slice(1),
          value: String(value)
        });
      });

      // Add demo file information
      metadataSheet.addRow({ property: '', value: '' }); // Empty row
      metadataSheet.addRow({ property: 'File Information', value: '' });
      metadataSheet.addRow({ property: 'File Name', value: 'demo-meeting-notes.pdf' });
      metadataSheet.addRow({ property: 'File Size', value: '2.5 MB' });
      metadataSheet.addRow({ property: 'File Type', value: 'PDF' });
      metadataSheet.addRow({ property: 'Upload Date', value: new Date().toLocaleString() });

      // Style metadata sheet
      metadataSheet.eachRow((row, rowNumber) => {
        if (rowNumber > 1) {
          row.getCell(1).font = { bold: true };
        }
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });
    }

    // Generate Excel buffer
    const buffer = Buffer.from(await workbook.xlsx.writeBuffer());

    // Log export activity (demo mode - no actual logging)
  devLog.log('📊 Excel Export: Demo export completed successfully');

    // Create notification (demo mode - no actual notification)
  devLog.log('🔔 Excel Export: Demo notification created');

    // Track export activity for analytics
    trackPublicExport('excel', summaryId);
  devLog.log('📊 Excel Export: Export completed successfully');

    // Return Excel file
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${summary.title || 'summary'}.xlsx"`,
        'Content-Length': buffer.length.toString()
      }
    });

  } catch (error) {
    console.error('Excel export error:', error);

    return NextResponse.json(
      { success: false, error: 'Export failed' },
      { status: 500 }
    );
  }
}
