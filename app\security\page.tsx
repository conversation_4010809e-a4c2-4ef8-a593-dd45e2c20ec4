import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Shield, 
  Lock, 
  Key, 
  Server, 
  Eye, 
  AlertTriangle, 
  CheckCircle, 
  Globe,
  Database,
  Zap
} from 'lucide-react';

export default function SecurityPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
              <Shield className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Security & Trust
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Enterprise-grade security measures protecting your data and conversations
          </p>
          <div className="flex justify-center mt-4 space-x-2">
            <Badge variant="outline" className="text-sm bg-green-50 border-green-200">
              SOC 2 Type II (In Progress)
            </Badge>
            <Badge variant="outline" className="text-sm bg-blue-50 border-blue-200">
              GDPR Compliant
            </Badge>
          </div>
        </div>

        <div className="space-y-6">
          {/* Security Overview */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <CardTitle>Security Overview</CardTitle>
              </div>
              <CardDescription>
                Our comprehensive security framework protects your data at every level
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <Lock className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-green-900 dark:text-green-100">Encryption</h3>
                  <p className="text-sm text-green-700 dark:text-green-300">End-to-end encryption for all data</p>
                </div>
                <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <Key className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-blue-900 dark:text-blue-100">Authentication</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-300">Multi-factor authentication support</p>
                </div>
                <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <Server className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-purple-900 dark:text-purple-100">Infrastructure</h3>
                  <p className="text-sm text-purple-700 dark:text-purple-300">Secure cloud infrastructure</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Data Encryption */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Lock className="h-5 w-5 text-blue-600" />
                <CardTitle>Data Encryption</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <h3>Encryption in Transit</h3>
              <ul>
                <li><strong>TLS 1.3:</strong> All data transmission uses the latest TLS encryption</li>
                <li><strong>HTTPS Everywhere:</strong> All connections are encrypted and authenticated</li>
                <li><strong>API Security:</strong> All API calls use encrypted channels with authentication</li>
                <li><strong>Certificate Pinning:</strong> Protection against man-in-the-middle attacks</li>
              </ul>

              <h3>Encryption at Rest</h3>
              <ul>
                <li><strong>Database Encryption:</strong> AES-256 encryption for all stored data</li>
                <li><strong>File Storage:</strong> Encrypted file storage with access controls</li>
                <li><strong>Backup Encryption:</strong> All backups are encrypted and secured</li>
                <li><strong>Key Management:</strong> Hardware security modules (HSM) for key protection</li>
              </ul>

              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-start space-x-2">
                  <Lock className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-blue-900 dark:text-blue-100">Zero-Knowledge Architecture</p>
                    <p className="text-blue-800 dark:text-blue-200 text-sm mt-1">
                      We implement zero-knowledge principles where possible, ensuring that even our systems 
                      cannot access your raw data without proper authorization.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Access Controls */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Key className="h-5 w-5 text-purple-600" />
                <CardTitle>Access Controls & Authentication</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <h3>User Authentication</h3>
              <ul>
                <li><strong>Clerk Authentication:</strong> Enterprise-grade authentication service</li>
                <li><strong>Multi-Factor Authentication:</strong> Optional MFA for enhanced security</li>
                <li><strong>OAuth Integration:</strong> Secure Slack workspace connections</li>
                <li><strong>Session Management:</strong> Secure session handling with automatic expiration</li>
              </ul>

              <h3>Database Security</h3>
              <ul>
                <li><strong>Row-Level Security (RLS):</strong> Database-level access controls</li>
                <li><strong>Principle of Least Privilege:</strong> Minimal necessary permissions</li>
                <li><strong>API Rate Limiting:</strong> Protection against abuse and attacks</li>
                <li><strong>SQL Injection Prevention:</strong> Parameterized queries and input validation</li>
              </ul>

              <h3>Infrastructure Access</h3>
              <ul>
                <li><strong>VPN Access:</strong> Secure remote access for team members</li>
                <li><strong>SSH Key Management:</strong> Secure server access with key rotation</li>
                <li><strong>Audit Logging:</strong> Complete logs of all system access</li>
                <li><strong>Background Checks:</strong> All team members undergo security screening</li>
              </ul>
            </CardContent>
          </Card>

          {/* Infrastructure Security */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Server className="h-5 w-5 text-green-600" />
                <CardTitle>Infrastructure Security</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <h3>Cloud Infrastructure</h3>
              <ul>
                <li><strong>Supabase:</strong> SOC 2 Type II certified database infrastructure</li>
                <li><strong>Vercel:</strong> Secure deployment platform with edge security</li>
                <li><strong>AWS/GCP:</strong> Enterprise-grade cloud services with compliance certifications</li>
                <li><strong>CDN Security:</strong> DDoS protection and traffic filtering</li>
              </ul>

              <h3>Network Security</h3>
              <ul>
                <li><strong>Firewall Protection:</strong> Multi-layer firewall configuration</li>
                <li><strong>DDoS Mitigation:</strong> Automatic protection against attacks</li>
                <li><strong>Intrusion Detection:</strong> Real-time monitoring and alerting</li>
                <li><strong>Network Segmentation:</strong> Isolated environments for different services</li>
              </ul>

              <h3>Monitoring & Incident Response</h3>
              <ul>
                <li><strong>24/7 Monitoring:</strong> Continuous security monitoring with Sentry</li>
                <li><strong>Automated Alerts:</strong> Real-time notifications for security events</li>
                <li><strong>Incident Response Plan:</strong> Documented procedures for security incidents</li>
                <li><strong>Regular Penetration Testing:</strong> Third-party security assessments</li>
              </ul>
            </CardContent>
          </Card>

          {/* AI & Data Processing Security */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-orange-600" />
                <CardTitle>AI Processing Security</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <h3>AI Provider Security</h3>
              <ul>
                <li><strong>Enterprise APIs:</strong> Business-grade AI services with data protection</li>
                <li><strong>Data Processing Agreements:</strong> Contractual protection for your data</li>
                <li><strong>No Training Data:</strong> Your content is not used to train AI models</li>
                <li><strong>Temporary Processing:</strong> Data is processed and immediately discarded</li>
              </ul>

              <h3>Content Protection</h3>
              <ul>
                <li><strong>Anonymization:</strong> Personal identifiers removed before AI processing</li>
                <li><strong>Content Filtering:</strong> Sensitive information detection and protection</li>
                <li><strong>Processing Logs:</strong> Audit trail of all AI processing activities</li>
                <li><strong>Fallback Systems:</strong> Local processing options for sensitive content</li>
              </ul>

              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-orange-900 dark:text-orange-100">AI Processing Notice</p>
                    <p className="text-orange-800 dark:text-orange-200 text-sm mt-1">
                      We use enterprise-grade AI APIs (DeepSeek, OpenAI) with strict data protection agreements. 
                      Your content is processed securely and never stored by AI providers.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Compliance & Certifications */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Globe className="h-5 w-5 text-indigo-600" />
                <CardTitle>Compliance & Certifications</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <h3>Current Compliance</h3>
              <ul>
                <li><strong>GDPR:</strong> Full compliance with EU data protection regulations</li>
                <li><strong>CCPA:</strong> California Consumer Privacy Act compliance</li>
                <li><strong>PIPEDA:</strong> Canadian privacy law compliance</li>
                <li><strong>ISO 27001:</strong> Information security management (in progress)</li>
              </ul>

              <h3>Upcoming Certifications</h3>
              <ul>
                <li><strong>SOC 2 Type II:</strong> Security audit completion expected Q2 2024</li>
                <li><strong>HIPAA:</strong> Healthcare compliance for enterprise customers</li>
                <li><strong>FedRAMP:</strong> Government security authorization (planned)</li>
              </ul>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div className="text-center p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg border border-indigo-200 dark:border-indigo-800">
                  <Database className="h-8 w-8 text-indigo-600 mx-auto mb-2" />
                  <h4 className="font-semibold text-indigo-900 dark:text-indigo-100">Data Residency</h4>
                  <p className="text-sm text-indigo-700 dark:text-indigo-300">Choose where your data is stored</p>
                </div>
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <Eye className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h4 className="font-semibold text-green-900 dark:text-green-100">Transparency</h4>
                  <p className="text-sm text-green-700 dark:text-green-300">Open security practices and reporting</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security Contact */}
          <Card>
            <CardHeader>
              <CardTitle>Security Contact & Reporting</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <h3>Report Security Issues</h3>
              <p>
                We take security seriously. If you discover a security vulnerability, please report it responsibly:
              </p>
              <ul>
                <li><strong>Security Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
                <li><strong>Bug Bounty:</strong> Responsible disclosure program with rewards</li>
                <li><strong>Response Time:</strong> We respond to security reports within 24 hours</li>
                <li><strong>PGP Key:</strong> Available for encrypted communications</li>
              </ul>

              <div className="flex flex-col sm:flex-row gap-4 mt-6">
                <Button asChild className="flex-1">
                  <a href="mailto:<EMAIL>?subject=Security Report">
                    Report Security Issue
                  </a>
                </Button>
                <Button variant="outline" asChild className="flex-1">
                  <a href="/security-policy.pdf" target="_blank">
                    Download Security Policy
                  </a>
                </Button>
              </div>

              <p className="text-sm text-gray-600 dark:text-gray-400 mt-6">
                This security information is updated regularly. For the latest security updates and advisories, 
                please check our <a href="/status" className="underline">status page</a> or contact our security team.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
