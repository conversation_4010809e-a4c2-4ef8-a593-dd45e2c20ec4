#!/bin/bash

# 🚀 PRODUCTION DEPLOYMENT SCRIPT
# Slack Summary Scribe - Automated Vercel Deployment

set -e  # Exit on any error

echo "🚀 Starting Production Deployment for Slack Summary Scribe..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
echo ""
log_info "Checking prerequisites..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    log_error "package.json not found. Please run this script from the project root."
    exit 1
fi
log_success "Project root directory confirmed"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    log_warning "Vercel CLI not found. Installing..."
    npm install -g vercel
    log_success "Vercel CLI installed"
else
    log_success "Vercel CLI is available"
fi

# Check if user is logged in to Vercel
if ! vercel whoami &> /dev/null; then
    log_error "Not logged in to Vercel. Please run: vercel login"
    exit 1
fi
log_success "Logged in to Vercel"

# Pre-deployment validation
echo ""
log_info "Running pre-deployment validation..."

# Check environment template
if [ -f ".env.production.template" ]; then
    log_success "Production environment template found"
else
    log_warning "No .env.production.template found"
fi

# Run build test
log_info "Testing production build..."
if npm run build; then
    log_success "Build completed successfully"
else
    log_error "Build failed. Please fix build errors before deploying."
    exit 1
fi

# Clean build artifacts for fresh deployment
rm -rf .next
log_success "Cleaned build artifacts"

# Deploy to Vercel
echo ""
log_info "Deploying to Vercel production..."

if vercel --prod --yes; then
    log_success "Deployment completed successfully!"
else
    log_error "Deployment failed. Check Vercel logs for details."
    exit 1
fi

# Get deployment URL
DEPLOYMENT_URL=$(vercel --prod --yes 2>/dev/null | grep -o 'https://[^[:space:]]*' | head -1)

echo ""
echo "=================================================="
log_success "🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!"
echo "=================================================="

echo ""
log_info "📍 Production URL: $DEPLOYMENT_URL"
log_info "🕒 Deployed at: $(date)"

echo ""
echo "📋 NEXT STEPS:"
echo "1. Configure environment variables in Vercel Dashboard"
echo "2. Set up custom domain (if applicable)"
echo "3. Configure Stripe webhooks"
echo "4. Test all critical user flows"
echo "5. Monitor error rates and performance"

echo ""
echo "🔗 IMPORTANT LINKS:"
echo "• Production App: $DEPLOYMENT_URL"
echo "• Vercel Dashboard: https://vercel.com/dashboard"
echo "• Environment Variables: https://vercel.com/dashboard → Settings → Environment Variables"

echo ""
echo "🧪 TESTING CHECKLIST:"
echo "   1. [ ] User registration/login"
echo "   2. [ ] Slack OAuth connection"
echo "   3. [ ] File upload (PDF/DOCX)"
echo "   4. [ ] AI summarization"
echo "   5. [ ] Export functionality"
echo "   6. [ ] Billing/subscription"
echo "   7. [ ] Email notifications"
echo "   8. [ ] Error tracking"
echo "   9. [ ] Analytics tracking"

echo ""
echo "🚨 MONITORING DASHBOARDS:"
echo "• Vercel: https://vercel.com/dashboard"
echo "• Sentry: https://sentry.io/organizations/your-org/"
echo "• PostHog: https://app.posthog.com/"
echo "• Supabase: https://supabase.com/dashboard"
echo "• Stripe: https://dashboard.stripe.com/"

echo ""
log_success "🎯 Your Slack Summary Scribe SaaS is now LIVE! 🚀"
log_success "Ready to serve customers worldwide! 🌍"

echo ""
echo "📖 For detailed setup instructions, see:"
echo "   • VERCEL_PRODUCTION_DEPLOYMENT.md"
echo "   • DEPLOYMENT_CHECKLIST.md"
echo "   • .env.production.template"
