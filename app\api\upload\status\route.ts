import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { getCurrentUser } from '@/lib/user-management';
import { uploadStatusTracker } from '@/lib/upload-status-tracker';

export async function GET(request: NextRequest) {
  try {
  devLog.log('📁 Upload status API called - Public Live Mode');

    // Get user (anonymous mode supported)
    const user = await getCurrentUser();
    const userId = user?.id || 'anonymous-user';

    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get('fileId');

    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'File ID is required' },
        { status: 400 }
      );
    }
  devLog.log('📁 Checking status for file:', fileId);

    // First check in-memory tracker for real-time status
    const trackedStatus = uploadStatusTracker.getStatus(fileId);
    if (trackedStatus) {
  devLog.log('📁 Found tracked status:', trackedStatus.status);
      return NextResponse.json({
        success: true,
        data: {
          status: trackedStatus.status,
          progress: trackedStatus.progress,
          summaryId: trackedStatus.summaryId,
          errorMessage: trackedStatus.errorMessage,
          fileName: trackedStatus.fileName,
          fileSize: trackedStatus.fileSize,
          processingSteps: trackedStatus.processingSteps,
          lastUpdate: trackedStatus.lastUpdate
        }
      });
    }

    // In public mode, if not found in memory tracker, return not found
  devLog.log('📁 Status not found in memory tracker for:', fileId);
    return NextResponse.json(
      { success: false, error: 'Upload record not found or expired' },
      { status: 404 }
    );

  } catch (error) {
    console.error('Status API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
