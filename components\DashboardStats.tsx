/**
 * Dashboard Stats Component
 * 
 * Displays key metrics and statistics for the user's dashboard
 */

'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  BarChart3, 
  FileText, 
  Users, 
  TrendingUp,
  Calendar,
  Zap
} from 'lucide-react';

export interface DashboardStatsData {
  totalSummaries: number;
  workspacesConnected: number;
  summariesThisMonth: number;
  averageResponseTime?: number;
  successRate?: number;
  lastSummaryDate?: string;
}

interface DashboardStatsProps {
  stats: DashboardStatsData;
  isLoading?: boolean;
  className?: string;
}

export function DashboardStats({ stats, isLoading = false, className = '' }: DashboardStatsProps) {
  if (isLoading) {
    return <DashboardStatsSkeleton className={className} />;
  }

  const statItems = [
    {
      title: 'Total Summaries',
      value: stats.totalSummaries,
      description: 'All-time summaries created',
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      trend: stats.totalSummaries > 0 ? '+' + stats.totalSummaries : undefined,
    },
    {
      title: 'Connected Workspaces',
      value: stats.workspacesConnected,
      description: 'Slack workspaces integrated',
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      trend: stats.workspacesConnected > 0 ? 'Active' : 'None',
    },
    {
      title: 'This Month',
      value: stats.summariesThisMonth,
      description: 'Summaries created this month',
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      trend: stats.summariesThisMonth > 0 ? `${stats.summariesThisMonth} new` : 'No activity',
    },
  ];

  // Add performance stats if available
  if (stats.averageResponseTime !== undefined) {
    statItems.push({
      title: 'Avg Response Time',
      value: Math.round(stats.averageResponseTime * 10) / 10, // Round to 1 decimal place
      description: 'Average AI processing time (seconds)',
      icon: Zap,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      trend: stats.averageResponseTime < 3 ? 'Fast' : 'Normal',
    });
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {statItems.map((item, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {item.title}
            </CardTitle>
            <div className={`h-8 w-8 rounded-lg ${item.bgColor} flex items-center justify-center`}>
              <item.icon className={`h-4 w-4 ${item.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {typeof item.value === 'number' ? item.value.toLocaleString() : item.value}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {item.description}
                </p>
              </div>
              {item.trend && (
                <Badge 
                  variant="secondary" 
                  className="text-xs"
                >
                  {item.trend}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

/**
 * Loading skeleton for dashboard stats
 */
export function DashboardStatsSkeleton({ className = '' }: { className?: string }) {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {[1, 2, 3].map((i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-8 w-8 rounded-lg" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-32" />
              </div>
              <Skeleton className="h-5 w-12 rounded-full" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

/**
 * Enhanced stats with performance metrics
 */
export interface EnhancedDashboardStatsData extends DashboardStatsData {
  successRate: number;
  averageResponseTime: number;
  lastSummaryDate: string;
  weeklyGrowth?: number;
  monthlyGrowth?: number;
}

interface EnhancedDashboardStatsProps {
  stats: EnhancedDashboardStatsData;
  isLoading?: boolean;
  showPerformanceMetrics?: boolean;
  className?: string;
}

export function EnhancedDashboardStats({ 
  stats, 
  isLoading = false, 
  showPerformanceMetrics = true,
  className = '' 
}: EnhancedDashboardStatsProps) {
  if (isLoading) {
    return <DashboardStatsSkeleton className={className} />;
  }

  const performanceItems = showPerformanceMetrics ? [
    {
      title: 'Success Rate',
      value: `${stats.successRate}%`,
      description: 'Successful AI generations',
      icon: TrendingUp,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      trend: stats.successRate >= 95 ? 'Excellent' : stats.successRate >= 85 ? 'Good' : 'Needs attention',
    },
    {
      title: 'Response Time',
      value: `${stats.averageResponseTime}s`,
      description: 'Average processing time',
      icon: Zap,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      trend: stats.averageResponseTime < 2 ? 'Fast' : stats.averageResponseTime < 5 ? 'Normal' : 'Slow',
    },
  ] : [];

  return (
    <div className={className}>
      <DashboardStats stats={stats} isLoading={isLoading} />
      
      {showPerformanceMetrics && performanceItems.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          {performanceItems.map((item, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {item.title}
                </CardTitle>
                <div className={`h-8 w-8 rounded-lg ${item.bgColor} flex items-center justify-center`}>
                  <item.icon className={`h-4 w-4 ${item.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">
                      {item.value}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {item.description}
                    </p>
                  </div>
                  <Badge 
                    variant={
                      item.trend === 'Excellent' || item.trend === 'Fast' ? 'default' :
                      item.trend === 'Good' || item.trend === 'Normal' ? 'secondary' : 'destructive'
                    }
                    className="text-xs"
                  >
                    {item.trend}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
