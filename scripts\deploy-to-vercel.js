#!/usr/bin/env node

/**
 * Vercel Deployment Script
 * 
 * This script helps prepare and deploy the Slack Summary Scribe SaaS
 * to Vercel with proper production configuration.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function logHeader(text) {
  console.log(`\n${colors.cyan}${colors.bright}${text}${colors.reset}`);
  console.log('='.repeat(text.length));
}

function logSuccess(text) {
  console.log(`${colors.green}✅ ${text}${colors.reset}`);
}

function logError(text) {
  console.log(`${colors.red}❌ ${text}${colors.reset}`);
}

function logWarning(text) {
  console.log(`${colors.yellow}⚠️  ${text}${colors.reset}`);
}

function logInfo(text) {
  console.log(`${colors.blue}ℹ️  ${text}${colors.reset}`);
}

// Check if Vercel CLI is installed
function checkVercelCLI() {
  try {
    execSync('vercel --version', { stdio: 'pipe' });
    return true;
  } catch (error) {
    return false;
  }
}

// Install Vercel CLI if needed
function installVercelCLI() {
  logInfo('Installing Vercel CLI...');
  try {
    execSync('npm install -g vercel', { stdio: 'inherit' });
    logSuccess('Vercel CLI installed successfully');
    return true;
  } catch (error) {
    logError('Failed to install Vercel CLI');
    return false;
  }
}

// Run pre-deployment checks
function runPreDeploymentChecks() {
  logHeader('PRE-DEPLOYMENT CHECKS');
  
  let allPassed = true;
  
  // Check if build passes
  try {
    logInfo('Running production build...');
    execSync('npm run build', { stdio: 'pipe' });
    logSuccess('Production build successful');
  } catch (error) {
    logError('Production build failed');
    console.log(error.stdout?.toString() || error.message);
    allPassed = false;
  }
  
  // Check environment validation
  try {
    logInfo('Validating environment configuration...');
    execSync('node scripts/validate-env-monitoring.js', { stdio: 'pipe' });
    logSuccess('Environment validation passed');
  } catch (error) {
    logWarning('Environment validation has warnings - check manually');
  }
  
  // Check for required files
  const requiredFiles = [
    'vercel.json',
    'package.json',
    'next.config.js',
    '.env.local'
  ];
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      logSuccess(`${file}: Found`);
    } else {
      logError(`${file}: Missing`);
      allPassed = false;
    }
  });
  
  return allPassed;
}

// Deploy to Vercel
function deployToVercel(production = false) {
  logHeader('DEPLOYING TO VERCEL');
  
  try {
    const deployCommand = production ? 'vercel --prod' : 'vercel';
    logInfo(`Running: ${deployCommand}`);
    
    execSync(deployCommand, { stdio: 'inherit' });
    logSuccess('Deployment completed successfully!');
    
    return true;
  } catch (error) {
    logError('Deployment failed');
    console.log(error.message);
    return false;
  }
}

// Set up environment variables
function setupEnvironmentVariables() {
  logHeader('ENVIRONMENT VARIABLES SETUP');
  
  logInfo('Environment variables need to be set up in Vercel dashboard:');
  console.log('1. Go to https://vercel.com/dashboard');
  console.log('2. Select your project');
  console.log('3. Go to Settings > Environment Variables');
  console.log('4. Add variables from deployment/vercel-env-template.txt');
  console.log('5. Replace placeholder values with real production keys');
  
  logWarning('Make sure to set up these critical variables:');
  console.log('   - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY');
  console.log('   - CLERK_SECRET_KEY');
  console.log('   - NEXT_PUBLIC_SUPABASE_URL');
  console.log('   - NEXT_PUBLIC_SUPABASE_ANON_KEY');
  console.log('   - SUPABASE_SERVICE_ROLE_KEY');
  console.log('   - OPENROUTER_API_KEY');
  console.log('   - NEXT_PUBLIC_POSTHOG_KEY');
  console.log('   - NEXT_PUBLIC_SENTRY_DSN');
}

// Post-deployment checks
function runPostDeploymentChecks(deploymentUrl) {
  logHeader('POST-DEPLOYMENT CHECKS');
  
  logInfo('Manual checks to perform:');
  console.log(`1. Visit ${deploymentUrl}`);
  console.log('2. Test user registration and login');
  console.log('3. Test file upload functionality');
  console.log('4. Test AI summarization');
  console.log('5. Test export features');
  console.log('6. Check Sentry for any errors');
  console.log('7. Check PostHog for analytics events');
  
  logInfo('Automated health checks:');
  // You could add automated health checks here
}

// Main deployment function
async function main() {
  logHeader('🚀 VERCEL DEPLOYMENT SCRIPT');
  
  const args = process.argv.slice(2);
  const isProduction = args.includes('--prod') || args.includes('--production');
  const skipChecks = args.includes('--skip-checks');
  
  // Check Vercel CLI
  if (!checkVercelCLI()) {
    logWarning('Vercel CLI not found');
    if (!installVercelCLI()) {
      process.exit(1);
    }
  } else {
    logSuccess('Vercel CLI is available');
  }
  
  // Run pre-deployment checks
  if (!skipChecks) {
    if (!runPreDeploymentChecks()) {
      logError('Pre-deployment checks failed');
      logInfo('Fix the issues above or use --skip-checks to bypass');
      process.exit(1);
    }
  }
  
  // Show environment setup instructions
  setupEnvironmentVariables();
  
  // Ask for confirmation
  if (isProduction) {
    logWarning('You are about to deploy to PRODUCTION');
    logInfo('Make sure all environment variables are set up in Vercel dashboard');
    
    // In a real script, you might want to add a confirmation prompt here
    console.log('\nPress Ctrl+C to cancel, or continue to deploy...');
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  // Deploy
  const deploymentSuccess = deployToVercel(isProduction);
  
  if (deploymentSuccess) {
    const deploymentUrl = isProduction 
      ? 'https://slack-summary-scribe.vercel.app'
      : 'https://your-preview-url.vercel.app';
    
    logSuccess('🎉 Deployment completed successfully!');
    logInfo(`Deployment URL: ${deploymentUrl}`);
    
    runPostDeploymentChecks(deploymentUrl);
    
    logHeader('🎯 NEXT STEPS');
    console.log('1. Test all functionality on the deployed site');
    console.log('2. Monitor Sentry for any errors');
    console.log('3. Check PostHog for user analytics');
    console.log('4. Set up custom domain if needed');
    console.log('5. Configure any additional integrations');
    
  } else {
    logError('Deployment failed');
    process.exit(1);
  }
}

// Handle command line usage
if (require.main === module) {
  main().catch(error => {
    logError(`Deployment script failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { main };
