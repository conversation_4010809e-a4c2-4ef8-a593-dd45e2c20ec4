/**
 * Demo Mode Middleware
 * Enforces usage limits and provides upgrade prompts
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { 
  getDemoStatus, 
  canPerformAction, 
  incrementDemoUsage,
  recordUpgradePromptShown,
  isUserInDemoMode
} from './demo-mode';
import { getUserSubscription } from './subscription-service';

export interface DemoMiddlewareContext {
  userId: string;
  isInDemo: boolean;
  demoStatus?: any;
  canProceed: boolean;
  upgradePrompt?: {
    message: string;
    reason: string;
    showUpgrade: boolean;
  };
}

/**
 * Middleware to check demo limits before allowing actions
 */
export function withDemoLimitCheck(
  actionType: 'summaries' | 'exports' | 'aiRequests' | 'fileUploads' | 'slackConnections'
) {
  return (
    handler: (req: NextRequest, context: DemoMiddlewareContext) => Promise<NextResponse>
  ) => {
    return async (request: NextRequest): Promise<NextResponse> => {
    try {
      // Get authenticated user
      const { userId } = auth();
      
      if (!userId) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Check if user is in demo mode
      const inDemoMode = await isUserInDemoMode(userId);
      
      if (!inDemoMode) {
        // User has paid subscription, allow action
        return await handler(request, {
          userId,
          isInDemo: false,
          canProceed: true
        });
      }

      // Get demo status and check limits
      const demoStatus = await getDemoStatus(userId);
      const actionCheck = await canPerformAction(userId, actionType);

      if (!actionCheck.allowed) {
        // Record upgrade prompt shown
        await recordUpgradePromptShown(userId);

        return NextResponse.json(
          {
            error: 'Usage limit reached',
            demo_limit_reached: true,
            action_type: actionType,
            reason: actionCheck.reason,
            upgrade_prompt: {
              message: actionCheck.upgradePrompt || 'Upgrade to continue using this feature',
              reason: actionCheck.reason || 'Limit reached',
              showUpgrade: true,
              trial_days_remaining: demoStatus.trialDaysRemaining,
              trial_expired: demoStatus.trialExpired
            },
            usage_status: demoStatus.usage
          },
          { status: 403 }
        );
      }

      // User can proceed, increment usage after successful action
      const context: DemoMiddlewareContext = {
        userId,
        isInDemo: true,
        demoStatus,
        canProceed: true
      };

      // Execute the handler
      const response = await handler(request, context);

      // If the action was successful (2xx status), increment usage
      if (response.status >= 200 && response.status < 300) {
        await incrementDemoUsage(userId, actionType);
      }

      return response;

    } catch (error) {
      console.error('Demo middleware error:', error);
      return NextResponse.json(
        { error: 'Demo validation failed' },
        { status: 500 }
      );
    }
    };
  };
}

/**
 * Middleware to check demo feature access
 */
export function withDemoFeatureCheck(
  feature: 'analytics' | 'customTemplates' | 'teamManagement' | 'advancedAI'
) {
  return (
    handler: (req: NextRequest, context: DemoMiddlewareContext) => Promise<NextResponse>
  ) => {
    return async (request: NextRequest): Promise<NextResponse> => {
    try {
      const { userId } = auth();
      
      if (!userId) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      const inDemoMode = await isUserInDemoMode(userId);
      
      if (!inDemoMode) {
        // Paid user, allow access
        return await handler(request, {
          userId,
          isInDemo: false,
          canProceed: true
        });
      }

      const demoStatus = await getDemoStatus(userId);
      const canUseFeature = demoStatus.canUseFeature(feature);

      if (!canUseFeature) {
        await recordUpgradePromptShown(userId);

        return NextResponse.json(
          {
            error: 'Premium feature',
            demo_feature_locked: true,
            feature,
            upgrade_prompt: {
              message: `${feature} is a premium feature. Upgrade to unlock it.`,
              reason: 'Premium feature',
              showUpgrade: true,
              trial_days_remaining: demoStatus.trialDaysRemaining,
              trial_expired: demoStatus.trialExpired
            }
          },
          { status: 403 }
        );
      }

      return await handler(request, {
        userId,
        isInDemo: true,
        demoStatus,
        canProceed: true
      });

    } catch (error) {
      console.error('Demo feature middleware error:', error);
      return NextResponse.json(
        { error: 'Feature validation failed' },
        { status: 500 }
      );
    }
    };
  };
}

/**
 * Get demo status for API responses
 */
export async function getDemoStatusForAPI(userId: string) {
  try {
    const inDemoMode = await isUserInDemoMode(userId);
    
    if (!inDemoMode) {
      return {
        is_demo: false,
        subscription_type: 'paid'
      };
    }

    const demoStatus = await getDemoStatus(userId);
    
    return {
      is_demo: true,
      trial_days_remaining: demoStatus.trialDaysRemaining,
      trial_expired: demoStatus.trialExpired,
      usage: demoStatus.usage,
      should_show_upgrade_prompt: demoStatus.shouldShowUpgradePrompt,
      upgrade_prompt_message: demoStatus.upgradePromptMessage
    };
  } catch (error) {
    console.error('Error getting demo status for API:', error);
    return {
      is_demo: false,
      subscription_type: 'unknown'
    };
  }
}

/**
 * Check if user should see upgrade prompt
 */
export async function shouldShowUpgradePrompt(userId: string): Promise<{
  show: boolean;
  message: string;
  reason: string;
  urgency: 'low' | 'medium' | 'high';
}> {
  try {
    const inDemoMode = await isUserInDemoMode(userId);
    
    if (!inDemoMode) {
      return { show: false, message: '', reason: '', urgency: 'low' };
    }

    const demoStatus = await getDemoStatus(userId);
    
    if (!demoStatus.shouldShowUpgradePrompt) {
      return { show: false, message: '', reason: '', urgency: 'low' };
    }

    let urgency: 'low' | 'medium' | 'high' = 'low';
    let reason = 'general';

    if (demoStatus.trialExpired) {
      urgency = 'high';
      reason = 'trial_expired';
    } else if (demoStatus.trialDaysRemaining <= 1) {
      urgency = 'high';
      reason = 'trial_ending_soon';
    } else if (Object.values(demoStatus.usage).some(u => u.remaining === 0)) {
      urgency = 'medium';
      reason = 'limit_reached';
    } else if (demoStatus.trialDaysRemaining <= 3) {
      urgency = 'medium';
      reason = 'trial_ending';
    }

    return {
      show: true,
      message: demoStatus.upgradePromptMessage,
      reason,
      urgency
    };
  } catch (error) {
    console.error('Error checking upgrade prompt:', error);
    return { show: false, message: '', reason: '', urgency: 'low' };
  }
}

/**
 * Validate demo action and return appropriate response
 */
export async function validateDemoAction(
  userId: string,
  actionType: 'summaries' | 'exports' | 'aiRequests' | 'fileUploads' | 'slackConnections'
): Promise<{
  allowed: boolean;
  response?: NextResponse;
  incrementUsage?: boolean;
}> {
  try {
    const inDemoMode = await isUserInDemoMode(userId);
    
    if (!inDemoMode) {
      return { allowed: true };
    }

    const actionCheck = await canPerformAction(userId, actionType);
    
    if (!actionCheck.allowed) {
      const demoStatus = await getDemoStatus(userId);
      await recordUpgradePromptShown(userId);

      return {
        allowed: false,
        response: NextResponse.json(
          {
            error: 'Usage limit reached',
            demo_limit_reached: true,
            action_type: actionType,
            reason: actionCheck.reason,
            upgrade_prompt: {
              message: actionCheck.upgradePrompt || 'Upgrade to continue',
              reason: actionCheck.reason || 'Limit reached',
              showUpgrade: true,
              trial_days_remaining: demoStatus.trialDaysRemaining,
              trial_expired: demoStatus.trialExpired
            },
            usage_status: demoStatus.usage
          },
          { status: 403 }
        )
      };
    }

    return { allowed: true, incrementUsage: true };
  } catch (error) {
    console.error('Error validating demo action:', error);
    return { allowed: true }; // Allow on error to avoid blocking users
  }
}
