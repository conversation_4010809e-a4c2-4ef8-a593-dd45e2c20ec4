/**
 * Detailed Smart Tagging Test
 */

const { default: fetch } = require('node-fetch');

const testTaggingDetailed = async () => {
  try {
    console.log('🏷️ Testing Smart Tagging API in Detail...');
    
    const response = await fetch('http://localhost:3000/api/summaries/demo-summary-123/tags', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        summaryText: 'This is a comprehensive meeting summary about React development, TypeScript implementation, project management, and team collaboration. We discussed implementing new features using Next.js, addressing performance issues with database optimization, and planning the next sprint. The team showed enthusiasm and confidence about the upcoming deliverables. Key decisions were made about the technology stack and resource allocation. Action items include code review, testing implementation, and stakeholder communication.'
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Smart Tagging API Response:');
      console.log(JSON.stringify(result, null, 2));
      
      // Test getting the tags
      console.log('\n🏷️ Testing Get Tags API...');
      const getResponse = await fetch('http://localhost:3000/api/summaries/demo-summary-123/tags');
      
      if (getResponse.ok) {
        const getResult = await getResponse.json();
        console.log('✅ Get Tags API Response:');
        console.log(JSON.stringify(getResult, null, 2));
      } else {
        console.log('❌ Get Tags API Failed');
        const error = await getResponse.json();
        console.log('Error:', error);
      }
      
    } else {
      console.log('❌ Smart Tagging API Failed');
      const error = await response.json();
      console.log('Error:', error);
    }
    
  } catch (error) {
    console.log('❌ Test Error:', error.message);
  }
};

testTaggingDetailed();
