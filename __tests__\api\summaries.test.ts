/**
 * Summaries API Route Tests
 * Tests the /api/summaries endpoint functionality
 */

// Import after mocks are set up
const { NextRequest } = require('next/server');
import { GET, POST } from '@/app/api/summaries/route';

// Mock demo user constants
jest.mock('@/lib/demo-constants', () => ({
  DEMO_USER_ID: 'demo-user-12345678-1234-1234-1234-123456789012',
  DEMO_USER: {
    id: 'demo-user-12345678-1234-1234-1234-123456789012',
    name: '<PERSON>',
    email: '<EMAIL>'
  }
}));

// Mock Supabase client
jest.mock('@/lib/supabase-server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

const mockCreateSupabaseClient = require('@/lib/supabase-server').createSupabaseServerClient;

describe('/api/summaries', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/summaries', () => {
    it('should return summaries for demo user', async () => {
      // Demo mode - no authentication required

      const request = new NextRequest('http://localhost:3000/api/summaries');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return summaries for authenticated user', async () => {
      const mockUserId = 'user_123';
      mockAuth.mockResolvedValue({ userId: mockUserId });

      const mockSupabase = {
        from: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        range: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
      };

      const mockSummaries = [
        {
          id: 'summary_1',
          title: 'Test Summary 1',
          content: 'Test content 1',
          created_at: '2024-01-01T00:00:00Z',
          status: 'completed',
        },
        {
          id: 'summary_2',
          title: 'Test Summary 2',
          content: 'Test content 2',
          created_at: '2024-01-02T00:00:00Z',
          status: 'processing',
        },
      ];

      mockSupabase.range.mockResolvedValue({
        data: mockSummaries,
        error: null,
      });

      mockCreateSupabaseClient.mockResolvedValue(mockSupabase);

      const request = new NextRequest('http://localhost:3000/api/summaries');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(2);
      expect(data.data[0].id).toBe('summary_1');
    });

    it('should handle pagination parameters', async () => {
      const mockUserId = 'user_123';
      mockAuth.mockResolvedValue({ userId: mockUserId });

      const mockSupabase = {
        from: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        range: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
      };

      mockSupabase.range.mockResolvedValue({
        data: [],
        error: null,
      });

      mockCreateSupabaseClient.mockResolvedValue(mockSupabase);

      const request = new NextRequest('http://localhost:3000/api/summaries?limit=5&offset=4');
      const response = await GET(request);

      expect(mockSupabase.range).toHaveBeenCalledWith(4, 8); // offset 4, limit 5: range 4-8
    });
  });

  describe('POST /api/summaries', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockAuth.mockResolvedValue({ userId: null });

      const request = new NextRequest('http://localhost:3000/api/summaries', {
        method: 'POST',
        body: JSON.stringify({ content: 'Test content' }),
      });
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should create a new summary successfully', async () => {
      const mockUserId = 'user_123';
      mockAuth.mockResolvedValue({ userId: mockUserId });

      const mockSupabase = {
        from: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn(),
      };

      const mockSummaryData = {
        id: 'summary_new',
        title: 'Test Meeting',
        content: 'Test meeting transcript content',
        user_id: mockUserId,
        source_type: 'manual',
        source_data: {},
        tags: [],
        created_at: '2024-01-01T00:00:00Z',
      };

      mockSupabase.single.mockResolvedValue({
        data: mockSummaryData,
        error: null,
      });

      mockCreateSupabaseClient.mockResolvedValue(mockSupabase);

      const request = new NextRequest('http://localhost:3000/api/summaries', {
        method: 'POST',
        body: JSON.stringify({
          content: 'Test meeting transcript content',
          title: 'Test Meeting',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.id).toBe('summary_new');
      expect(data.data.title).toBe('Test Meeting');
    });

    it('should return 400 for missing required fields', async () => {
      const mockUserId = 'user_123';
      mockAuth.mockResolvedValue({ userId: mockUserId });

      const request = new NextRequest('http://localhost:3000/api/summaries', {
        method: 'POST',
        body: JSON.stringify({ title: 'Test' }), // Missing content
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing required fields: title, content');
    });

    it('should handle missing title field', async () => {
      const mockUserId = 'user_123';
      mockAuth.mockResolvedValue({ userId: mockUserId });

      const request = new NextRequest('http://localhost:3000/api/summaries', {
        method: 'POST',
        body: JSON.stringify({
          content: 'Test content', // Missing title
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing required fields: title, content');
    });

    it('should handle database insertion errors', async () => {
      const mockUserId = 'user_123';
      mockAuth.mockResolvedValue({ userId: mockUserId });

      const mockSupabase = {
        from: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn(),
      };

      mockSupabase.single.mockResolvedValue({
        data: null,
        error: { message: 'Database insertion failed' },
      });

      mockCreateSupabaseClient.mockResolvedValue(mockSupabase);

      const request = new NextRequest('http://localhost:3000/api/summaries', {
        method: 'POST',
        body: JSON.stringify({
          content: 'Test content',
          title: 'Test Meeting',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to create summary');
    });
  });
});
