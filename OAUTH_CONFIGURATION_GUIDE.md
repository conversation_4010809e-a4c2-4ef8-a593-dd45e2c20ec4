# 🔐 OAuth Configuration Guide - Slack Summary Scribe

## 🚨 CRITICAL: Supabase OAuth Settings

Your Supabase project needs to be configured with the correct OAuth redirect URLs. Based on your project configuration:

### Project Details
- **Supabase URL**: `https://holuppwejzcqwrbdbgkf.supabase.co`
- **Project Ref**: `holuppwejzcqwrbdbgkf`
- **Local Development URL**: `http://localhost:3000`

### Required OAuth Configuration

#### 1. Supabase Dashboard Settings
Go to: [Supabase Dashboard](https://supabase.com/dashboard/project/holuppwejzcqwrbdbgkf/auth/settings)

#### 2. Site URL Configuration
Set **Site URL** to:
```
http://localhost:3000
```

#### 3. Redirect URLs Configuration
Add these URLs to **Redirect URLs**:
```
http://localhost:3000/api/auth/callback
http://localhost:3000/auth/callback
http://localhost:3000/
```

#### 4. OAuth Provider Configuration

##### Google OAuth
- **Redirect URI**: `http://localhost:3000/api/auth/callback`
- **Authorized JavaScript origins**: `http://localhost:3000`

##### GitHub OAuth  
- **Authorization callback URL**: `http://localhost:3000/api/auth/callback`

##### Slack OAuth
- **Redirect URLs**: `http://localhost:3000/api/auth/callback`

## 🧪 Testing OAuth Configuration

### Step 1: Verify Configuration
1. Visit: `http://localhost:3000/test-oauth-flow`
2. Check that the redirect URL shows: `http://localhost:3000/api/auth/callback?provider=google&next=%2Ftest-oauth-flow`

### Step 2: Test OAuth Flow
1. Click "Start OAuth Flow with Google"
2. Complete Google authentication
3. Verify you're redirected back to the test page
4. Check logs for session establishment

### Step 3: Verify Cookies
Expected cookies after successful OAuth:
- `sb-holuppwejzcqwrbdbgkf-auth-token`
- `sb-holuppwejzcqwrbdbgkf-auth-token-code-verifier`
- `sb-holuppwejzcqwrbdbgkf-auth-token-refresh-token`

## 🔧 Common Issues & Solutions

### Issue 1: "Invalid redirect URL" Error
**Cause**: OAuth redirect URL not configured in Supabase
**Solution**: Add `http://localhost:3000/api/auth/callback` to Supabase redirect URLs

### Issue 2: OAuth Completes but No Session
**Cause**: Cookie configuration mismatch
**Solution**: Ensure cookies are set with correct domain and path

### Issue 3: Session Not Persisting
**Cause**: Cookie not accessible to client/server
**Solution**: Verify cookie `httpOnly: false` and correct domain

### Issue 4: Middleware Redirect Loop
**Cause**: Middleware not detecting session cookies
**Solution**: Check middleware looks for correct cookie names

## 📋 Verification Checklist

### Supabase Configuration
- [ ] Site URL set to `http://localhost:3000`
- [ ] Redirect URLs include `http://localhost:3000/api/auth/callback`
- [ ] Google OAuth configured with correct redirect URI
- [ ] GitHub OAuth configured with correct callback URL
- [ ] Slack OAuth configured with correct redirect URL

### Local Testing
- [ ] Server running on `http://localhost:3000`
- [ ] Environment variables point to `localhost:3000`
- [ ] OAuth flow test page loads correctly
- [ ] OAuth redirect URL shows correct format
- [ ] Google OAuth completes without errors
- [ ] Session cookies are set after OAuth
- [ ] Session persists across page refreshes
- [ ] Dashboard accessible after login

### Production Readiness
- [ ] HTTPS URLs configured for production
- [ ] Production domain added to Supabase settings
- [ ] OAuth providers configured for production domain
- [ ] Environment variables updated for production

## 🚀 Next Steps

1. **Configure Supabase OAuth Settings** (CRITICAL)
   - Update redirect URLs in Supabase dashboard
   - Verify OAuth provider configurations

2. **Test OAuth Flow**
   - Use `/test-oauth-flow` page to verify complete flow
   - Check browser console for any errors
   - Verify session cookies are set

3. **Validate Dashboard Access**
   - Test login → dashboard navigation
   - Verify session persistence
   - Check middleware behavior

4. **Production Deployment**
   - Update OAuth settings for production domain
   - Test complete flow in production environment

## 📞 Support

If OAuth still doesn't work after configuration:
1. Check browser console for detailed error messages
2. Verify Supabase project settings match exactly
3. Test with incognito mode to rule out cached issues
4. Check server logs for OAuth callback errors
