'use client';

import { devLog } from '@/lib/console-cleaner';

import { useParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Download, Share2, Calendar, FileText, Zap, Star, Crown } from 'lucide-react';
import { toast } from 'sonner';
import AuthGuard from '@/components/AuthGuard';
import ChunkErrorBoundary from '@/components/ChunkErrorBoundary';

interface SummaryData {
  id: string;
  title: string;
  content: string;
  source_type: string;
  source_data?: {
    channel_name?: string;
    message_count?: number;
    participants?: string[];
    filename?: string;
    file_size?: number;
  };
  created_at: string;
  user_id: string;
  ai_model: string;
  quality_score: number;
}

function SummaryPageContent() {
  const params = useParams();
  const router = useRouter();
  const [summary, setSummary] = useState<SummaryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const summaryId = params?.id as string;

  useEffect(() => {
    const fetchSummary = async () => {
      try {
        setLoading(true);
        setError(null);
  devLog.log('📄 Fetching summary for ID:', summaryId);

        // Fetch real summary data from API
        const response = await fetch(`/api/summaries/${summaryId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch summary: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.summary) {
          setSummary(data.summary);
        } else {
          throw new Error(data.error || 'Failed to load summary');
        }
      } catch (err) {
        console.error('Error fetching summary:', err);
        setError('Failed to load summary. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (summaryId) {
      fetchSummary();
    }
  }, [summaryId]);

  // Helper function to format summary content
  const formatSummaryContent = (content: string): string => {
    if (!content) return 'No content available';
    return content;
  };

  // Helper function to get source type display name
  const getSourceTypeDisplay = (sourceType: string): string => {
    switch (sourceType) {
      case 'slack': return 'Slack Thread';
      case 'file_upload': return 'File Upload';
      case 'manual_input': return 'Manual Input';
      default: return 'Unknown Source';
    }
  };

  // Helper function to format date
  const formatDate = (dateString: string): string => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Unknown date';
    }
  };



  const getAIModelIcon = (model: string) => {
    switch (model) {
      case 'deepseek-r1':
        return <Zap className="h-4 w-4 text-blue-500" />;
      case 'gpt-4o-mini':
        return <Star className="h-4 w-4 text-purple-500" />;
      case 'claude-3-5-sonnet':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const handleDownload = () => {
    if (!summary) return;
    
    const content = `# ${summary.title}\n\n${summary.content}\n\n---\nGenerated by SummaryAI on ${new Date(summary.created_at).toLocaleDateString()}`;
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${summary.title.replace(/[^a-zA-Z0-9]/g, '_')}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Summary downloaded successfully!');
  };

  const handleShare = async () => {
    if (!summary) return;
    
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast.success('Summary link copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy link');
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <Skeleton className="h-10 w-32 mb-4" />
            <Skeleton className="h-8 w-96 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error || !summary) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-4xl mx-auto">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Card>
            <CardContent className="text-center py-12">
              <h2 className="text-xl font-semibold mb-2">Summary Not Found</h2>
              <p className="text-gray-600 mb-4">
                {error || 'The summary you\'re looking for doesn\'t exist or has been removed.'}
              </p>
              <Button onClick={() => router.push('/dashboard')}>
                Go to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {summary.title}
              </h1>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(summary.created_at).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-1">
                  {getAIModelIcon(summary.ai_model)}
                  <span>{summary.ai_model}</span>
                </div>
                <Badge variant="secondary">
                  Quality: {(summary.quality_score * 100).toFixed(0)}%
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </div>

        {/* Source Information */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Source Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Source Type</label>
                <p className="text-sm capitalize">{summary.source_type.replace('_', ' ')}</p>
              </div>
              
              {summary.source_data?.channel_name && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Slack Channel</label>
                  <p className="text-sm">{summary.source_data.channel_name}</p>
                </div>
              )}
              
              {summary.source_data?.filename && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Filename</label>
                  <p className="text-sm">{summary.source_data.filename}</p>
                </div>
              )}
              
              {summary.source_data?.file_size && (
                <div>
                  <label className="text-sm font-medium text-gray-600">File Size</label>
                  <p className="text-sm">{formatFileSize(summary.source_data.file_size)}</p>
                </div>
              )}
              
              {summary.source_data?.message_count && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Messages Processed</label>
                  <p className="text-sm">{summary.source_data.message_count}</p>
                </div>
              )}
              
              {summary.source_data?.participants && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Participants</label>
                  <p className="text-sm">{summary.source_data.participants.join(', ')}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Summary Content */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Summary Content</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose max-w-none">
              <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                {summary.content}
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function SummaryPage() {
  return (
    <ChunkErrorBoundary>
      <SummaryPageContent />
    </ChunkErrorBoundary>
  );
}
