import { devLog } from '@/lib/console-cleaner';
/**
 * Database Health Check API
 * Provides database status and validation information
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { 
  performDatabaseHealthCheck,
  validateDatabaseConnectivity,
  validateRequiredTables,
  validateRLSPolicies,
  validateDatabaseFunctions
} from '@/lib/database-validator';
import { logAuditEvent } from '@/lib/audit-logger';

export async function GET(request: NextRequest) {
  try {
    const { userId } = auth();
    
    // Allow unauthenticated health checks for monitoring
    const isAuthenticated = !!userId;
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const detailed = searchParams.get('detailed') === 'true';
    const component = searchParams.get('component');
  devLog.log(`🏥 Database health check requested by ${userId || 'anonymous'}`);

    let healthData;

    if (component) {
      // Check specific component
      switch (component) {
        case 'connectivity':
          healthData = await validateDatabaseConnectivity();
          break;
        case 'tables':
          healthData = await validateRequiredTables();
          break;
        case 'policies':
          healthData = await validateRLSPolicies();
          break;
        case 'functions':
          healthData = await validateDatabaseFunctions();
          break;
        default:
          return NextResponse.json(
            { error: 'Invalid component. Use: connectivity, tables, policies, functions' },
            { status: 400 }
          );
      }
    } else {
      // Comprehensive health check
      healthData = await performDatabaseHealthCheck();
    }

    // Log health check if user is authenticated
    if (isAuthenticated) {
      await logAuditEvent({
        event_type: 'DATABASE_HEALTH_CHECK_API',
        user_id: userId,
        action: `Database health check via API (${component || 'full'})`,
        resource_type: 'database',
        resource_id: 'health_check',
        metadata: {
          component: component || 'full',
          detailed,
          status: healthData.status || (healthData.success ? 'healthy' : 'unhealthy')
        }
      });
    }

    // Return appropriate response based on detail level
    if (detailed) {
      return NextResponse.json({
        success: true,
        timestamp: new Date().toISOString(),
        health_data: healthData
      });
    } else {
      // Simplified response for monitoring
      const isHealthy = healthData.status === 'healthy' || healthData.success === true;
      
      return NextResponse.json({
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        component: component || 'database'
      });
    }

  } catch (error) {
    console.error('Database health check failed:', error);
    
    // Log error if possible
    try {
      const { userId } = auth();
      if (userId) {
        await logAuditEvent({
          event_type: 'DATABASE_HEALTH_CHECK_ERROR',
          user_id: userId,
          action: 'Database health check failed',
          resource_type: 'database',
          resource_id: 'health_check',
          metadata: {
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        });
      }
    } catch (logError) {
      console.error('Failed to log health check error:', logError);
    }

    return NextResponse.json(
      { 
        status: 'unhealthy',
        error: 'Health check failed',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required for database operations' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'validate_policies':
        const policyValidation = await validateRLSPolicies();
        
        await logAuditEvent({
          event_type: 'RLS_POLICY_VALIDATION',
          user_id: userId,
          action: 'RLS policies validated',
          resource_type: 'database',
          resource_id: 'rls_policies',
          metadata: {
            success: policyValidation.success,
            policies_checked: policyValidation.policies.length
          }
        });

        return NextResponse.json({
          success: true,
          validation_result: policyValidation
        });

      case 'test_connectivity':
        const connectivityTest = await validateDatabaseConnectivity();
        
        await logAuditEvent({
          event_type: 'DATABASE_CONNECTIVITY_TEST',
          user_id: userId,
          action: 'Database connectivity tested',
          resource_type: 'database',
          resource_id: 'connectivity',
          metadata: {
            success: connectivityTest.success,
            latency: connectivityTest.latency
          }
        });

        return NextResponse.json({
          success: true,
          connectivity_result: connectivityTest
        });

      case 'full_health_check':
        const fullHealthCheck = await performDatabaseHealthCheck();
        
        await logAuditEvent({
          event_type: 'FULL_DATABASE_HEALTH_CHECK',
          user_id: userId,
          action: 'Full database health check performed',
          resource_type: 'database',
          resource_id: 'full_health',
          metadata: {
            status: fullHealthCheck.status,
            errors: fullHealthCheck.errors.length,
            warnings: fullHealthCheck.warnings.length
          }
        });

        return NextResponse.json({
          success: true,
          health_check_result: fullHealthCheck
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: validate_policies, test_connectivity, full_health_check' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Database health check action failed:', error);
    
    const { userId } = auth();
    if (userId) {
      await logAuditEvent({
        event_type: 'DATABASE_HEALTH_ACTION_ERROR',
        user_id: userId,
        action: 'Database health check action failed',
        resource_type: 'database',
        resource_id: 'health_action',
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }

    return NextResponse.json(
      { error: 'Health check action failed' },
      { status: 500 }
    );
  }
}
