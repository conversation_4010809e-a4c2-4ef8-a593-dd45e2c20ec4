# 🧪 Testing Infrastructure

## Overview
Comprehensive testing framework for enterprise-grade SaaS validation across all layers.

## Testing Strategy
- **Unit Tests**: Individual functions and components (Vitest)
- **Integration Tests**: API endpoints and database operations (Vitest + Supertest)
- **E2E Tests**: Complete user workflows (Playwright)
- **Visual Tests**: UI component regression (Chromatic/Percy)
- **Performance Tests**: Load testing and benchmarks (k6)

## Test Structure
```
/tests/
├── README.md                   # This file
├── setup/                      # Test configuration and setup
│   ├── vitest.config.ts       # Vitest configuration
│   ├── playwright.config.ts   # Playwright configuration
│   ├── test-db.ts             # Test database setup
│   └── msw-handlers.ts        # Mock Service Worker handlers
├── e2e/                       # End-to-end tests
│   ├── auth/                  # Authentication flows
│   ├── summaries/             # Summary generation workflows
│   ├── billing/               # Stripe billing flows
│   ├── integrations/          # Third-party integrations
│   └── admin/                 # Admin dashboard tests
├── integration/               # Integration tests
│   ├── api/                   # API endpoint tests
│   ├── webhooks/              # Webhook handling tests
│   └── database/              # Database operation tests
├── unit/                      # Unit tests
│   ├── features/              # Feature module tests
│   ├── shared/                # Shared utility tests
│   └── components/            # Component tests
├── fixtures/                  # Test data and fixtures
│   ├── users.json            # Sample user data
│   ├── organizations.json    # Sample organization data
│   ├── summaries.json        # Sample summary data
│   └── slack-threads.json    # Sample Slack data
├── utils/                     # Testing utilities
│   ├── test-helpers.ts       # Common test utilities
│   ├── db-helpers.ts         # Database test helpers
│   └── auth-helpers.ts       # Authentication test helpers
└── performance/               # Performance tests
    ├── load-tests/           # Load testing scripts
    └── benchmarks/           # Performance benchmarks
```

## Environment Configuration

### Test Database Setup
```typescript
// tests/setup/test-db.ts
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_TEST_URL!;
const supabaseKey = process.env.SUPABASE_TEST_ANON_KEY!;

export const testDb = createClient(supabaseUrl, supabaseKey);

export async function setupTestDb() {
  // Clean all tables
  await testDb.from('summaries').delete().neq('id', '00000000-0000-0000-0000-000000000000');
  await testDb.from('organizations').delete().neq('id', '00000000-0000-0000-0000-000000000000');
  await testDb.from('profiles').delete().neq('id', '00000000-0000-0000-0000-000000000000');
  
  // Seed test data
  await seedTestData();
}

export async function teardownTestDb() {
  // Clean up test data
  await testDb.from('summaries').delete().neq('id', '00000000-0000-0000-0000-000000000000');
  await testDb.from('organizations').delete().neq('id', '00000000-0000-0000-0000-000000000000');
}

async function seedTestData() {
  // Insert test users, organizations, etc.
  const testUsers = [
    {
      id: 'test-user-1',
      email: '<EMAIL>',
      full_name: 'Test User 1'
    },
    {
      id: 'test-user-2', 
      email: '<EMAIL>',
      full_name: 'Test User 2'
    }
  ];
  
  await testDb.from('profiles').insert(testUsers);
}
```

### Vitest Configuration
```typescript
// tests/setup/vitest.config.ts
import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup/test-setup.ts'],
    include: ['tests/unit/**/*.test.ts', 'tests/integration/**/*.test.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*'
      ]
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '../../'),
    },
  },
});
```

### Playwright Configuration
```typescript
// tests/setup/playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

## Test Environment Variables
```bash
# tests/.env.test
# Test Database
SUPABASE_TEST_URL=https://your-test-project.supabase.co
SUPABASE_TEST_ANON_KEY=your-test-anon-key
SUPABASE_TEST_SERVICE_ROLE_KEY=your-test-service-key

# Test Stripe
STRIPE_TEST_SECRET_KEY=sk_test_...
STRIPE_TEST_WEBHOOK_SECRET=whsec_test_...

# Test AI Services
OPENAI_TEST_API_KEY=sk-test-...
DEEPSEEK_TEST_API_KEY=test-key

# Test Email
RESEND_TEST_API_KEY=re_test_...

# Test OAuth
GOOGLE_TEST_CLIENT_ID=test-client-id
NOTION_TEST_CLIENT_ID=test-client-id

# Playwright
PLAYWRIGHT_BASE_URL=http://localhost:3000
```

## E2E Test Examples

### Complete User Journey
```typescript
// tests/e2e/complete-workflow.spec.ts
import { test, expect } from '@playwright/test';
import { setupTestUser, cleanupTestUser } from '../utils/auth-helpers';

test.describe('Complete User Workflow', () => {
  let testUser: any;

  test.beforeEach(async () => {
    testUser = await setupTestUser();
  });

  test.afterEach(async () => {
    await cleanupTestUser(testUser.id);
  });

  test('signup → slack connect → summary → export → billing', async ({ page }) => {
    // 1. Sign up
    await page.goto('/signup');
    await page.fill('[data-testid=email]', testUser.email);
    await page.fill('[data-testid=password]', 'TestPassword123!');
    await page.click('[data-testid=signup-button]');
    
    await expect(page).toHaveURL('/dashboard');

    // 2. Connect Slack workspace
    await page.click('[data-testid=connect-slack]');
    // Mock Slack OAuth flow
    await page.route('**/api/auth/slack', async route => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({ success: true, workspaceId: 'test-workspace' })
      });
    });
    
    await expect(page.locator('[data-testid=slack-connected]')).toBeVisible();

    // 3. Generate summary
    await page.click('[data-testid=generate-summary]');
    await page.fill('[data-testid=slack-url]', 'https://test.slack.com/archives/C123/p123');
    await page.click('[data-testid=generate-button]');
    
    await expect(page.locator('[data-testid=summary-content]')).toBeVisible();

    // 4. Export summary
    await page.click('[data-testid=export-button]');
    await page.selectOption('[data-testid=export-format]', 'pdf');
    await page.click('[data-testid=confirm-export]');
    
    await expect(page.locator('[data-testid=export-success]')).toBeVisible();

    // 5. Upgrade billing
    await page.goto('/billing');
    await page.click('[data-testid=upgrade-pro]');
    
    // Mock Stripe checkout
    await page.route('**/api/billing/checkout', async route => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({ url: 'https://checkout.stripe.com/test' })
      });
    });
    
    await expect(page).toHaveURL(/checkout\.stripe\.com/);
  });
});
```

### API Integration Tests
```typescript
// tests/integration/api/summaries.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import { app } from '@/app';
import { setupTestDb, teardownTestDb } from '../../setup/test-db';
import { createTestUser, createTestOrganization } from '../../utils/test-helpers';

describe('Summaries API', () => {
  let testUser: any;
  let testOrg: any;
  let authToken: string;

  beforeEach(async () => {
    await setupTestDb();
    testUser = await createTestUser();
    testOrg = await createTestOrganization(testUser.id);
    authToken = await getAuthToken(testUser);
  });

  afterEach(async () => {
    await teardownTestDb();
  });

  describe('POST /api/summaries', () => {
    it('should create a new summary', async () => {
      const summaryData = {
        title: 'Test Summary',
        content: 'This is a test summary content',
        sourceType: 'slack',
        sourceData: { threadUrl: 'https://test.slack.com/thread' }
      };

      const response = await request(app)
        .post('/api/summaries')
        .set('Authorization', `Bearer ${authToken}`)
        .send(summaryData)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          id: expect.any(String),
          title: summaryData.title,
          content: summaryData.content,
          userId: testUser.id,
          organizationId: testOrg.id
        }
      });
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/summaries')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('title')
      });
    });

    it('should enforce rate limits', async () => {
      // Create 10 summaries quickly
      const promises = Array(11).fill(null).map(() =>
        request(app)
          .post('/api/summaries')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            title: 'Rate limit test',
            content: 'Test content'
          })
      );

      const responses = await Promise.all(promises);
      const rateLimitedResponse = responses.find(r => r.status === 429);
      
      expect(rateLimitedResponse).toBeDefined();
    });
  });

  describe('GET /api/summaries', () => {
    it('should list user summaries with pagination', async () => {
      // Create test summaries
      await createTestSummaries(testUser.id, testOrg.id, 15);

      const response = await request(app)
        .get('/api/summaries?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          summaries: expect.arrayContaining([
            expect.objectContaining({
              id: expect.any(String),
              title: expect.any(String),
              userId: testUser.id
            })
          ]),
          pagination: {
            page: 1,
            limit: 10,
            total: 15,
            pages: 2
          }
        }
      });
    });

    it('should filter summaries by date range', async () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      const response = await request(app)
        .get(`/api/summaries?startDate=${yesterday.toISOString()}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.summaries).toHaveLength(0);
    });
  });
});
```

## Unit Test Examples

### Service Layer Tests
```typescript
// tests/unit/features/summaries/generation.service.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { generateSummary } from '@/features/summaries/services/generation.service';
import * as aiService from '@/lib/ai/openai';

vi.mock('@/lib/ai/openai');

describe('Summary Generation Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should generate summary from Slack content', async () => {
    const mockAiResponse = 'Generated summary content';
    vi.mocked(aiService.generateText).mockResolvedValue(mockAiResponse);

    const result = await generateSummary({
      content: 'Slack thread content',
      type: 'slack',
      userId: 'test-user',
      organizationId: 'test-org'
    });

    expect(result).toMatchObject({
      success: true,
      summary: expect.objectContaining({
        content: mockAiResponse,
        sourceType: 'slack'
      })
    });

    expect(aiService.generateText).toHaveBeenCalledWith(
      expect.stringContaining('Slack thread content'),
      expect.objectContaining({
        model: expect.any(String),
        maxTokens: expect.any(Number)
      })
    );
  });

  it('should handle AI service errors gracefully', async () => {
    vi.mocked(aiService.generateText).mockRejectedValue(new Error('AI service error'));

    const result = await generateSummary({
      content: 'Test content',
      type: 'slack',
      userId: 'test-user',
      organizationId: 'test-org'
    });

    expect(result).toMatchObject({
      success: false,
      error: expect.stringContaining('AI service error')
    });
  });

  it('should respect plan limits', async () => {
    const result = await generateSummary({
      content: 'Test content',
      type: 'slack',
      userId: 'test-user',
      organizationId: 'test-org',
      plan: 'FREE'
    });

    // Should check plan limits before generation
    expect(result.success).toBe(false);
    expect(result.error).toContain('plan limit');
  });
});
```

## Test Utilities

### Database Helpers
```typescript
// tests/utils/db-helpers.ts
import { testDb } from '../setup/test-db';

export async function createTestUser(overrides: Partial<any> = {}) {
  const userData = {
    id: `test-user-${Date.now()}`,
    email: `test-${Date.now()}@example.com`,
    full_name: 'Test User',
    ...overrides
  };

  const { data, error } = await testDb
    .from('profiles')
    .insert(userData)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function createTestOrganization(ownerId: string, overrides: Partial<any> = {}) {
  const orgData = {
    id: `test-org-${Date.now()}`,
    name: 'Test Organization',
    plan: 'PRO',
    ...overrides
  };

  const { data: org, error: orgError } = await testDb
    .from('organizations')
    .insert(orgData)
    .select()
    .single();

  if (orgError) throw orgError;

  // Add owner membership
  await testDb
    .from('user_organizations')
    .insert({
      user_id: ownerId,
      organization_id: org.id,
      role: 'owner'
    });

  return org;
}

export async function createTestSummary(userId: string, organizationId: string, overrides: Partial<any> = {}) {
  const summaryData = {
    user_id: userId,
    organization_id: organizationId,
    title: 'Test Summary',
    content: 'Test summary content',
    source_type: 'slack',
    ...overrides
  };

  const { data, error } = await testDb
    .from('summaries')
    .insert(summaryData)
    .select()
    .single();

  if (error) throw error;
  return data;
}
```

## Performance Testing

### Load Test Example
```javascript
// tests/performance/load-tests/api-load.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 20 }, // Ramp up to 20 users
    { duration: '5m', target: 20 }, // Stay at 20 users
    { duration: '2m', target: 0 },  // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    http_req_failed: ['rate<0.1'],    // Error rate under 10%
  },
};

export default function () {
  const baseUrl = 'https://your-app.vercel.app';
  
  // Test summary creation
  const payload = JSON.stringify({
    title: 'Load Test Summary',
    content: 'This is a load test summary',
    sourceType: 'slack'
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${__ENV.TEST_AUTH_TOKEN}`,
    },
  };

  const response = http.post(`${baseUrl}/api/summaries`, payload, params);
  
  check(response, {
    'status is 201': (r) => r.status === 201,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });

  sleep(1);
}
```

## CI/CD Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run test:unit
      - run: npm run test:coverage
      
      - uses: codecov/codecov-action@v3
        with:
          file: ./coverage/coverage-final.json

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run test:integration
        env:
          SUPABASE_TEST_URL: ${{ secrets.SUPABASE_TEST_URL }}
          SUPABASE_TEST_ANON_KEY: ${{ secrets.SUPABASE_TEST_ANON_KEY }}

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npx playwright install --with-deps
      - run: npm run build
      - run: npm run test:e2e
        env:
          PLAYWRIGHT_BASE_URL: http://localhost:3000
      
      - uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/
```

## Package.json Scripts
```json
{
  "scripts": {
    "test": "vitest",
    "test:unit": "vitest run tests/unit",
    "test:integration": "vitest run tests/integration",
    "test:e2e": "playwright test",
    "test:coverage": "vitest run --coverage",
    "test:watch": "vitest watch",
    "test:ui": "vitest --ui",
    "test:setup": "node tests/setup/setup-test-db.js",
    "test:teardown": "node tests/setup/teardown-test-db.js"
  }
}
```
