#!/usr/bin/env node

/**
 * Integration SDK CLI
 * 
 * Command-line interface for testing and managing integrations
 */

import { program } from 'commander';
import { IntegrationSDK } from './index';
import { DatabaseTokenStorage } from './storage/database';
import fs from 'fs';
import path from 'path';
import chalk from 'chalk';

interface CLIConfig {
  providers: Record<string, { clientId: string; clientSecret: string }>;
  database?: string;
  redis?: string;
}

program
  .name('integration-sdk')
  .description('CLI for SaaS Integration SDK')
  .version('1.0.0');

// Initialize command
program
  .command('init')
  .description('Initialize integration SDK in current project')
  .option('-t, --template <type>', 'Template type (express, nextjs, fastify)', 'express')
  .action(async (options) => {
    console.log(chalk.blue('🚀 Initializing Integration SDK...'));
    
    // Create config file
    const configTemplate: CLIConfig = {
      providers: {
        notion: {
          clientId: 'your_notion_client_id',
          clientSecret: 'your_notion_client_secret'
        },
        slack: {
          clientId: 'your_slack_client_id',
          clientSecret: 'your_slack_client_secret'
        },
        'google-drive': {
          clientId: 'your_google_client_id',
          clientSecret: 'your_google_client_secret'
        }
      },
      database: 'postgresql://user:pass@localhost:5432/db',
      redis: 'redis://localhost:6379'
    };

    fs.writeFileSync(
      'integration-sdk.config.json',
      JSON.stringify(configTemplate, null, 2)
    );

    // Create example files based on template
    const templates = {
      express: createExpressExample,
      nextjs: createNextJSExample,
      fastify: createFastifyExample
    };

    const templateFn = templates[options.template as keyof typeof templates];
    if (templateFn) {
      templateFn();
    }

    console.log(chalk.green('✅ Integration SDK initialized!'));
    console.log(chalk.yellow('📝 Next steps:'));
    console.log('1. Update integration-sdk.config.json with your credentials');
    console.log('2. Install dependencies: npm install @saas-kit/integration-sdk');
    console.log('3. Run example: node example.js');
  });

// Test auth command
program
  .command('test-auth')
  .description('Test OAuth authentication flow')
  .requiredOption('-p, --provider <provider>', 'Provider to test (notion, slack, etc.)')
  .option('-u, --user-id <userId>', 'User ID for testing', 'test-user')
  .option('-r, --redirect <uri>', 'Redirect URI', 'http://localhost:3000/callback')
  .action(async (options) => {
    try {
      const config = loadConfig();
      const sdk = createSDK(config);

      console.log(chalk.blue(`🔐 Testing OAuth for ${options.provider}...`));

      const { authUrl, state } = await sdk.generateAuthUrl({
        provider: options.provider,
        userId: options.userId,
        redirectUri: options.redirect
      });

      console.log(chalk.green('✅ Auth URL generated successfully!'));
      console.log(chalk.cyan('🔗 Auth URL:'), authUrl);
      console.log(chalk.cyan('🎫 State:'), state);
      console.log(chalk.yellow('📝 Visit the URL above to complete OAuth flow'));

    } catch (error) {
      console.error(chalk.red('❌ Auth test failed:'), error);
      process.exit(1);
    }
  });

// Test export command
program
  .command('test-export')
  .description('Test content export')
  .requiredOption('-p, --provider <provider>', 'Provider to test')
  .requiredOption('-c, --content <content>', 'Content to export')
  .option('-u, --user-id <userId>', 'User ID for testing', 'test-user')
  .option('-f, --format <format>', 'Content format', 'markdown')
  .option('-t, --title <title>', 'Content title', 'Test Export')
  .action(async (options) => {
    try {
      const config = loadConfig();
      const sdk = createSDK(config);

      console.log(chalk.blue(`📤 Testing export to ${options.provider}...`));

      const result = await sdk.export({
        provider: options.provider,
        userId: options.userId,
        payload: {
          title: options.title,
          content: options.content,
          format: options.format,
          metadata: {
            createdAt: new Date().toISOString(),
            tags: ['test', 'cli'],
            author: { id: options.userId, name: 'CLI Test' }
          }
        }
      });

      if (result.success) {
        console.log(chalk.green('✅ Export successful!'));
        console.log(chalk.cyan('🔗 Destination URL:'), result.destinationUrl);
      } else {
        console.log(chalk.red('❌ Export failed:'), result.error);
      }

    } catch (error) {
      console.error(chalk.red('❌ Export test failed:'), error);
      process.exit(1);
    }
  });

// List providers command
program
  .command('providers')
  .description('List available providers')
  .action(() => {
    console.log(chalk.blue('📋 Available Providers:'));
    
    const providers = [
      { name: 'notion', features: ['OAuth', 'Export', 'Webhooks'] },
      { name: 'slack', features: ['OAuth', 'Export', 'Webhooks'] },
      { name: 'google-drive', features: ['OAuth', 'Export'] },
      { name: 'hubspot', features: ['OAuth', 'Export', 'Webhooks'] },
      { name: 'salesforce', features: ['OAuth', 'Export', 'Webhooks'] },
      { name: 'airtable', features: ['OAuth', 'Export'] },
      { name: 'trello', features: ['OAuth', 'Export', 'Webhooks'] },
      { name: 'asana', features: ['OAuth', 'Export', 'Webhooks'] }
    ];

    providers.forEach(provider => {
      console.log(chalk.green(`  ✅ ${provider.name}`));
      console.log(chalk.gray(`     Features: ${provider.features.join(', ')}`));
    });
  });

// Health check command
program
  .command('health')
  .description('Check integration health')
  .option('-p, --provider <provider>', 'Specific provider to check')
  .action(async (options) => {
    try {
      const config = loadConfig();
      const sdk = createSDK(config);

      console.log(chalk.blue('🏥 Running health checks...'));

      if (options.provider) {
        const health = await sdk.testConnection({
          userId: 'health-check',
          provider: options.provider
        });

        console.log(chalk.green(`✅ ${options.provider}: ${health.success ? 'Healthy' : 'Unhealthy'}`));
        if (!health.success) {
          console.log(chalk.red(`   Error: ${health.error}`));
        }
      } else {
        // Check all configured providers
        const providers = Object.keys(config.providers);
        
        for (const provider of providers) {
          try {
            const health = await sdk.testConnection({
              userId: 'health-check',
              provider: provider as any
            });

            const status = health.success ? chalk.green('✅ Healthy') : chalk.red('❌ Unhealthy');
            console.log(`  ${provider}: ${status} (${health.responseTime}ms)`);
            
            if (!health.success) {
              console.log(chalk.red(`    Error: ${health.error}`));
            }
          } catch (error) {
            console.log(`  ${provider}: ${chalk.red('❌ Error')} - ${error}`);
          }
        }
      }

    } catch (error) {
      console.error(chalk.red('❌ Health check failed:'), error);
      process.exit(1);
    }
  });

// Generate types command
program
  .command('generate-types')
  .description('Generate TypeScript types for providers')
  .option('-o, --output <file>', 'Output file', 'integration-types.ts')
  .action((options) => {
    console.log(chalk.blue('🔧 Generating TypeScript types...'));

    const typesContent = `
// Generated by @saas-kit/integration-sdk CLI
// Do not edit manually

export interface IntegrationConfig {
  providers: {
    notion?: ProviderConfig;
    slack?: ProviderConfig;
    'google-drive'?: ProviderConfig;
    hubspot?: ProviderConfig;
    salesforce?: ProviderConfig;
    airtable?: ProviderConfig;
    trello?: ProviderConfig;
    asana?: ProviderConfig;
  };
  tokenStorage: TokenStorage;
  rateLimiter?: RateLimiter;
}

export interface ProviderConfig {
  clientId: string;
  clientSecret: string;
  scopes?: string[];
  customParams?: Record<string, string>;
}

export interface ExportPayload {
  title: string;
  content: string;
  format: 'markdown' | 'html' | 'plain' | 'rich';
  metadata: {
    createdAt: string;
    updatedAt?: string;
    tags: string[];
    author: {
      id: string;
      name: string;
      email?: string;
    };
  };
  attachments?: Attachment[];
}

export interface ExportResult {
  success: boolean;
  destinationId?: string;
  destinationUrl?: string;
  exportedAt: string;
  format: string;
  error?: string;
}
`;

    fs.writeFileSync(options.output, typesContent.trim());
    console.log(chalk.green(`✅ Types generated: ${options.output}`));
  });

// Helper functions
function loadConfig(): CLIConfig {
  const configPath = path.join(process.cwd(), 'integration-sdk.config.json');
  
  if (!fs.existsSync(configPath)) {
    console.error(chalk.red('❌ Config file not found. Run "integration-sdk init" first.'));
    process.exit(1);
  }

  return JSON.parse(fs.readFileSync(configPath, 'utf8'));
}

function createSDK(config: CLIConfig): IntegrationSDK {
  const tokenStorage = new DatabaseTokenStorage({
    connectionString: config.database || 'sqlite://./tokens.db'
  });

  return new IntegrationSDK({
    providers: config.providers,
    tokenStorage
  });
}

function createExpressExample(): void {
  const exampleContent = `
const express = require('express');
const { IntegrationSDK, DatabaseTokenStorage } = require('@saas-kit/integration-sdk');
const config = require('./integration-sdk.config.json');

const app = express();
app.use(express.json());

const sdk = new IntegrationSDK({
  providers: config.providers,
  tokenStorage: new DatabaseTokenStorage({
    connectionString: config.database
  })
});

// OAuth initiation
app.get('/auth/:provider', async (req, res) => {
  const { authUrl } = await sdk.generateAuthUrl({
    provider: req.params.provider,
    userId: req.query.userId,
    redirectUri: 'http://localhost:3000/auth/callback'
  });
  
  res.redirect(authUrl);
});

// OAuth callback
app.get('/auth/callback', async (req, res) => {
  const result = await sdk.exchangeCodeForToken({
    provider: req.query.provider,
    code: req.query.code,
    state: req.query.state,
    redirectUri: 'http://localhost:3000/auth/callback'
  });
  
  res.json(result);
});

// Export endpoint
app.post('/export/:provider', async (req, res) => {
  const result = await sdk.export({
    provider: req.params.provider,
    userId: req.body.userId,
    payload: req.body.payload
  });
  
  res.json(result);
});

app.listen(3000, () => {
  console.log('Server running on http://localhost:3000');
});
`;

  fs.writeFileSync('example.js', exampleContent.trim());
  console.log(chalk.green('📝 Created Express example: example.js'));
}

function createNextJSExample(): void {
  const exampleContent = `
// pages/api/auth/[provider].ts
import { NextApiRequest, NextApiResponse } from 'next';
import { IntegrationSDK, DatabaseTokenStorage } from '@saas-kit/integration-sdk';
import config from '../../../integration-sdk.config.json';

const sdk = new IntegrationSDK({
  providers: config.providers,
  tokenStorage: new DatabaseTokenStorage({
    connectionString: config.database
  })
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { provider } = req.query;
  
  if (req.method === 'GET') {
    const { authUrl } = await sdk.generateAuthUrl({
      provider: provider as string,
      userId: req.query.userId as string,
      redirectUri: \`\${process.env.NEXTAUTH_URL}/api/auth/callback\`
    });
    
    res.redirect(authUrl);
  }
}
`;

  fs.writeFileSync('nextjs-example.ts', exampleContent.trim());
  console.log(chalk.green('📝 Created Next.js example: nextjs-example.ts'));
}

function createFastifyExample(): void {
  const exampleContent = `
const fastify = require('fastify')({ logger: true });
const { IntegrationSDK, DatabaseTokenStorage } = require('@saas-kit/integration-sdk');
const config = require('./integration-sdk.config.json');

const sdk = new IntegrationSDK({
  providers: config.providers,
  tokenStorage: new DatabaseTokenStorage({
    connectionString: config.database
  })
});

fastify.get('/auth/:provider', async (request, reply) => {
  const { authUrl } = await sdk.generateAuthUrl({
    provider: request.params.provider,
    userId: request.query.userId,
    redirectUri: 'http://localhost:3000/auth/callback'
  });
  
  reply.redirect(authUrl);
});

fastify.post('/export/:provider', async (request, reply) => {
  const result = await sdk.export({
    provider: request.params.provider,
    userId: request.body.userId,
    payload: request.body.payload
  });
  
  return result;
});

const start = async () => {
  try {
    await fastify.listen({ port: 3000 });
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start();
`;

  fs.writeFileSync('fastify-example.js', exampleContent.trim());
  console.log(chalk.green('📝 Created Fastify example: fastify-example.js'));
}

program.parse();
