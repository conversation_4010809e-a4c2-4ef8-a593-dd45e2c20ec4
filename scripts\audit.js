#!/usr/bin/env node

/**
 * SaaS Audit Toolkit
 * 
 * Comprehensive validation script for enterprise readiness:
 * - Environment variable completeness
 * - Supabase table policies and RLS
 * - Storage and token cleanup
 * - Integration token refresh states
 * - Rate limit status
 * - Security configuration
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

class SaaSAuditor {
  constructor() {
    this.results = {
      environment: { passed: 0, failed: 0, warnings: 0, issues: [] },
      database: { passed: 0, failed: 0, warnings: 0, issues: [] },
      security: { passed: 0, failed: 0, warnings: 0, issues: [] },
      integrations: { passed: 0, failed: 0, warnings: 0, issues: [] },
      performance: { passed: 0, failed: 0, warnings: 0, issues: [] },
      compliance: { passed: 0, failed: 0, warnings: 0, issues: [] }
    };
    
    this.supabase = null;
    this.startTime = Date.now();
  }

  async run() {
    console.log(chalk.blue.bold('🔍 Starting SaaS Enterprise Audit...\n'));

    try {
      await this.auditEnvironment();
      await this.auditDatabase();
      await this.auditSecurity();
      await this.auditIntegrations();
      await this.auditPerformance();
      await this.auditCompliance();
      
      this.generateReport();
    } catch (error) {
      console.error(chalk.red('❌ Audit failed:'), error.message);
      process.exit(1);
    }
  }

  async auditEnvironment() {
    console.log(chalk.yellow('📋 Auditing Environment Configuration...'));

    const requiredEnvVars = [
      // Core
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY',
      
      // Authentication
      'NEXTAUTH_SECRET',
      'NEXTAUTH_URL',
      
      // Stripe
      'STRIPE_SECRET_KEY',
      'STRIPE_WEBHOOK_SECRET',
      'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
      
      // AI Services
      'OPENAI_API_KEY',
      'DEEPSEEK_API_KEY',
      
      // Email
      'RESEND_API_KEY',
      
      // Monitoring
      'SENTRY_DSN',
      'NEXT_PUBLIC_POSTHOG_KEY',
      'NEXT_PUBLIC_POSTHOG_HOST',
      
      // Integrations
      'NOTION_CLIENT_ID',
      'NOTION_CLIENT_SECRET',
      'GOOGLE_CLIENT_ID',
      'GOOGLE_CLIENT_SECRET',
      'SLACK_CLIENT_ID',
      'SLACK_CLIENT_SECRET',
      
      // Multi-region (optional)
      'SUPABASE_EU_URL',
      'SUPABASE_EU_ANON_KEY'
    ];

    const optionalEnvVars = [
      'HUBSPOT_CLIENT_ID',
      'SALESFORCE_CLIENT_ID',
      'DROPBOX_APP_KEY',
      'AIRTABLE_CLIENT_ID'
    ];

    // Check required environment variables
    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        this.addIssue('environment', 'error', `Missing required environment variable: ${envVar}`);
      } else if (process.env[envVar].includes('your_') || process.env[envVar].includes('placeholder')) {
        this.addIssue('environment', 'error', `Environment variable ${envVar} contains placeholder value`);
      } else {
        this.results.environment.passed++;
      }
    }

    // Check optional environment variables
    for (const envVar of optionalEnvVars) {
      if (!process.env[envVar]) {
        this.addIssue('environment', 'warning', `Optional environment variable not set: ${envVar}`);
      } else {
        this.results.environment.passed++;
      }
    }

    // Validate environment-specific configurations
    const nodeEnv = process.env.NODE_ENV;
    if (nodeEnv === 'production') {
      if (process.env.NEXTAUTH_URL?.includes('localhost')) {
        this.addIssue('environment', 'error', 'NEXTAUTH_URL contains localhost in production');
      }
      
      if (!process.env.NEXTAUTH_SECRET || process.env.NEXTAUTH_SECRET.length < 32) {
        this.addIssue('environment', 'error', 'NEXTAUTH_SECRET is too short for production');
      }
    }

    // Check .env file structure
    const envFiles = ['.env.local', '.env.production', '.env'];
    let envFileFound = false;
    
    for (const envFile of envFiles) {
      if (fs.existsSync(envFile)) {
        envFileFound = true;
        this.results.environment.passed++;
        break;
      }
    }
    
    if (!envFileFound) {
      this.addIssue('environment', 'warning', 'No .env file found');
    }

    console.log(chalk.green('✅ Environment audit completed\n'));
  }

  async auditDatabase() {
    console.log(chalk.yellow('🗄️ Auditing Database Configuration...'));

    try {
      this.supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
      );

      // Test database connection
      const { data, error } = await this.supabase
        .from('profiles')
        .select('id')
        .limit(1);

      if (error) {
        this.addIssue('database', 'error', `Database connection failed: ${error.message}`);
        return;
      } else {
        this.results.database.passed++;
      }

      // Check RLS policies
      await this.checkRLSPolicies();
      
      // Check table structure
      await this.checkTableStructure();
      
      // Check indexes
      await this.checkDatabaseIndexes();
      
      // Check storage policies
      await this.checkStoragePolicies();

    } catch (error) {
      this.addIssue('database', 'error', `Database audit failed: ${error.message}`);
    }

    console.log(chalk.green('✅ Database audit completed\n'));
  }

  async checkRLSPolicies() {
    const tables = [
      'profiles',
      'organizations',
      'user_organizations',
      'summaries',
      'oauth_tokens',
      'export_jobs',
      'webhook_endpoints'
    ];

    for (const table of tables) {
      try {
        // Check if RLS is enabled
        const { data: rlsStatus } = await this.supabase.rpc('check_rls_enabled', { table_name: table });
        
        if (!rlsStatus) {
          this.addIssue('database', 'error', `RLS not enabled on table: ${table}`);
        } else {
          this.results.database.passed++;
        }

        // Check if policies exist
        const { data: policies } = await this.supabase.rpc('get_table_policies', { table_name: table });
        
        if (!policies || policies.length === 0) {
          this.addIssue('database', 'error', `No RLS policies found for table: ${table}`);
        } else {
          this.results.database.passed++;
        }

      } catch (error) {
        this.addIssue('database', 'warning', `Could not check RLS for table ${table}: ${error.message}`);
      }
    }
  }

  async checkTableStructure() {
    const requiredTables = [
      'profiles',
      'organizations',
      'user_organizations',
      'summaries',
      'oauth_tokens',
      'export_jobs',
      'webhook_endpoints',
      'feature_flags',
      'admin_audit_log'
    ];

    for (const table of requiredTables) {
      try {
        const { data, error } = await this.supabase
          .from(table)
          .select('*')
          .limit(0);

        if (error) {
          this.addIssue('database', 'error', `Table ${table} does not exist or is not accessible`);
        } else {
          this.results.database.passed++;
        }
      } catch (error) {
        this.addIssue('database', 'error', `Failed to check table ${table}: ${error.message}`);
      }
    }
  }

  async checkDatabaseIndexes() {
    const criticalIndexes = [
      { table: 'summaries', column: 'user_id' },
      { table: 'summaries', column: 'organization_id' },
      { table: 'summaries', column: 'created_at' },
      { table: 'oauth_tokens', column: 'user_id' },
      { table: 'oauth_tokens', column: 'provider' },
      { table: 'export_jobs', column: 'user_id' },
      { table: 'export_jobs', column: 'status' }
    ];

    for (const index of criticalIndexes) {
      try {
        // Check if index exists (simplified check)
        const { data } = await this.supabase.rpc('check_index_exists', {
          table_name: index.table,
          column_name: index.column
        });

        if (data) {
          this.results.database.passed++;
        } else {
          this.addIssue('database', 'warning', `Missing index on ${index.table}.${index.column}`);
        }
      } catch (error) {
        this.addIssue('database', 'warning', `Could not check index for ${index.table}.${index.column}`);
      }
    }
  }

  async checkStoragePolicies() {
    try {
      const { data: buckets } = await this.supabase.storage.listBuckets();
      
      if (!buckets || buckets.length === 0) {
        this.addIssue('database', 'warning', 'No storage buckets found');
        return;
      }

      for (const bucket of buckets) {
        // Check bucket policies
        const { data: policies } = await this.supabase.rpc('get_storage_policies', {
          bucket_name: bucket.name
        });

        if (!policies || policies.length === 0) {
          this.addIssue('database', 'warning', `No storage policies for bucket: ${bucket.name}`);
        } else {
          this.results.database.passed++;
        }
      }
    } catch (error) {
      this.addIssue('database', 'warning', `Could not check storage policies: ${error.message}`);
    }
  }

  async auditSecurity() {
    console.log(chalk.yellow('🔒 Auditing Security Configuration...'));

    // Check JWT secret strength
    const jwtSecret = process.env.NEXTAUTH_SECRET;
    if (jwtSecret) {
      if (jwtSecret.length < 32) {
        this.addIssue('security', 'error', 'JWT secret is too short (minimum 32 characters)');
      } else if (!/[A-Z]/.test(jwtSecret) || !/[a-z]/.test(jwtSecret) || !/[0-9]/.test(jwtSecret)) {
        this.addIssue('security', 'warning', 'JWT secret should contain uppercase, lowercase, and numbers');
      } else {
        this.results.security.passed++;
      }
    }

    // Check HTTPS configuration
    const nextAuthUrl = process.env.NEXTAUTH_URL;
    if (nextAuthUrl && !nextAuthUrl.startsWith('https://') && process.env.NODE_ENV === 'production') {
      this.addIssue('security', 'error', 'NEXTAUTH_URL should use HTTPS in production');
    } else if (nextAuthUrl && nextAuthUrl.startsWith('https://')) {
      this.results.security.passed++;
    }

    // Check Stripe webhook secret
    const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (stripeWebhookSecret && stripeWebhookSecret.startsWith('whsec_')) {
      this.results.security.passed++;
    } else {
      this.addIssue('security', 'error', 'Invalid Stripe webhook secret format');
    }

    // Check for sensitive data in logs
    const logFiles = ['logs/app.log', 'logs/error.log', '.next/trace'];
    for (const logFile of logFiles) {
      if (fs.existsSync(logFile)) {
        const content = fs.readFileSync(logFile, 'utf8');
        const sensitivePatterns = [
          /sk_live_[a-zA-Z0-9]+/g, // Stripe live keys
          /sk_test_[a-zA-Z0-9]+/g, // Stripe test keys
          /password["\s]*[:=]["\s]*[^"\s]+/gi,
          /secret["\s]*[:=]["\s]*[^"\s]+/gi
        ];

        for (const pattern of sensitivePatterns) {
          if (pattern.test(content)) {
            this.addIssue('security', 'error', `Sensitive data found in ${logFile}`);
            break;
          }
        }
      }
    }

    console.log(chalk.green('✅ Security audit completed\n'));
  }

  async auditIntegrations() {
    console.log(chalk.yellow('🔌 Auditing Integration Status...'));

    try {
      // Check OAuth token health
      const { data: tokens, error } = await this.supabase
        .from('oauth_tokens')
        .select('*')
        .eq('status', 'active');

      if (error) {
        this.addIssue('integrations', 'error', `Failed to fetch OAuth tokens: ${error.message}`);
        return;
      }

      let expiredTokens = 0;
      let healthyTokens = 0;

      for (const token of tokens || []) {
        if (token.expires_at && new Date(token.expires_at) < new Date()) {
          expiredTokens++;
        } else {
          healthyTokens++;
        }
      }

      if (expiredTokens > 0) {
        this.addIssue('integrations', 'warning', `${expiredTokens} OAuth tokens are expired`);
      }

      if (healthyTokens > 0) {
        this.results.integrations.passed++;
      }

      // Check webhook endpoints
      const { data: webhooks } = await this.supabase
        .from('webhook_endpoints')
        .select('*')
        .eq('enabled', true);

      for (const webhook of webhooks || []) {
        try {
          const response = await fetch(webhook.url, { method: 'HEAD', timeout: 5000 });
          if (response.ok) {
            this.results.integrations.passed++;
          } else {
            this.addIssue('integrations', 'warning', `Webhook endpoint unreachable: ${webhook.url}`);
          }
        } catch (error) {
          this.addIssue('integrations', 'warning', `Webhook endpoint error: ${webhook.url}`);
        }
      }

    } catch (error) {
      this.addIssue('integrations', 'error', `Integration audit failed: ${error.message}`);
    }

    console.log(chalk.green('✅ Integration audit completed\n'));
  }

  async auditPerformance() {
    console.log(chalk.yellow('⚡ Auditing Performance Configuration...'));

    // Check Next.js configuration
    const nextConfigPath = path.join(process.cwd(), 'next.config.js');
    if (fs.existsSync(nextConfigPath)) {
      const nextConfig = require(nextConfigPath);
      
      if (nextConfig.images && nextConfig.images.domains) {
        this.results.performance.passed++;
      } else {
        this.addIssue('performance', 'warning', 'Image optimization domains not configured');
      }

      if (nextConfig.compress !== false) {
        this.results.performance.passed++;
      } else {
        this.addIssue('performance', 'warning', 'Compression is disabled');
      }
    }

    // Check for bundle analysis
    const bundleAnalyzerPath = path.join(process.cwd(), 'package.json');
    if (fs.existsSync(bundleAnalyzerPath)) {
      const packageJson = JSON.parse(fs.readFileSync(bundleAnalyzerPath, 'utf8'));
      
      if (packageJson.scripts && packageJson.scripts['analyze']) {
        this.results.performance.passed++;
      } else {
        this.addIssue('performance', 'warning', 'Bundle analyzer script not configured');
      }
    }

    // Check caching configuration
    if (process.env.REDIS_URL || process.env.UPSTASH_REDIS_REST_URL) {
      this.results.performance.passed++;
    } else {
      this.addIssue('performance', 'warning', 'No Redis cache configured');
    }

    console.log(chalk.green('✅ Performance audit completed\n'));
  }

  async auditCompliance() {
    console.log(chalk.yellow('📋 Auditing Compliance Requirements...'));

    // Check GDPR compliance features
    const gdprFeatures = [
      'data export functionality',
      'data deletion functionality',
      'consent management',
      'privacy policy',
      'cookie policy'
    ];

    // Check if privacy policy exists
    const privacyPolicyPath = path.join(process.cwd(), 'pages/privacy.tsx');
    if (fs.existsSync(privacyPolicyPath) || fs.existsSync(path.join(process.cwd(), 'app/privacy/page.tsx'))) {
      this.results.compliance.passed++;
    } else {
      this.addIssue('compliance', 'warning', 'Privacy policy page not found');
    }

    // Check for terms of service
    const termsPath = path.join(process.cwd(), 'pages/terms.tsx');
    if (fs.existsSync(termsPath) || fs.existsSync(path.join(process.cwd(), 'app/terms/page.tsx'))) {
      this.results.compliance.passed++;
    } else {
      this.addIssue('compliance', 'warning', 'Terms of service page not found');
    }

    // Check audit logging
    try {
      const { data: auditLogs } = await this.supabase
        .from('admin_audit_log')
        .select('id')
        .limit(1);

      if (auditLogs) {
        this.results.compliance.passed++;
      } else {
        this.addIssue('compliance', 'warning', 'Audit logging table exists but no logs found');
      }
    } catch (error) {
      this.addIssue('compliance', 'error', 'Audit logging not properly configured');
    }

    console.log(chalk.green('✅ Compliance audit completed\n'));
  }

  addIssue(category, severity, message) {
    this.results[category].issues.push({ severity, message });
    
    if (severity === 'error') {
      this.results[category].failed++;
    } else if (severity === 'warning') {
      this.results[category].warnings++;
    }
  }

  generateReport() {
    const duration = Date.now() - this.startTime;
    
    console.log(chalk.blue.bold('\n📊 AUDIT REPORT'));
    console.log(chalk.gray('='.repeat(50)));
    
    let totalPassed = 0;
    let totalFailed = 0;
    let totalWarnings = 0;

    for (const [category, results] of Object.entries(this.results)) {
      totalPassed += results.passed;
      totalFailed += results.failed;
      totalWarnings += results.warnings;

      console.log(`\n${chalk.yellow.bold(category.toUpperCase())}`);
      console.log(`  ✅ Passed: ${chalk.green(results.passed)}`);
      console.log(`  ❌ Failed: ${chalk.red(results.failed)}`);
      console.log(`  ⚠️  Warnings: ${chalk.yellow(results.warnings)}`);

      if (results.issues.length > 0) {
        console.log('  Issues:');
        for (const issue of results.issues) {
          const icon = issue.severity === 'error' ? '❌' : '⚠️';
          const color = issue.severity === 'error' ? chalk.red : chalk.yellow;
          console.log(`    ${icon} ${color(issue.message)}`);
        }
      }
    }

    console.log(chalk.gray('\n' + '='.repeat(50)));
    console.log(chalk.blue.bold('SUMMARY'));
    console.log(`✅ Total Passed: ${chalk.green(totalPassed)}`);
    console.log(`❌ Total Failed: ${chalk.red(totalFailed)}`);
    console.log(`⚠️  Total Warnings: ${chalk.yellow(totalWarnings)}`);
    console.log(`⏱️  Duration: ${chalk.gray(duration + 'ms')}`);

    const score = totalPassed / (totalPassed + totalFailed + totalWarnings) * 100;
    console.log(`📊 Overall Score: ${this.getScoreColor(score)(Math.round(score))}%`);

    if (totalFailed > 0) {
      console.log(chalk.red.bold('\n❌ AUDIT FAILED - Critical issues found'));
      process.exit(1);
    } else if (totalWarnings > 0) {
      console.log(chalk.yellow.bold('\n⚠️  AUDIT PASSED WITH WARNINGS'));
    } else {
      console.log(chalk.green.bold('\n✅ AUDIT PASSED - All checks successful'));
    }

    // Save report to file
    const reportPath = path.join(process.cwd(), 'audit-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      duration,
      score: Math.round(score),
      results: this.results,
      summary: {
        totalPassed,
        totalFailed,
        totalWarnings
      }
    }, null, 2));

    console.log(chalk.gray(`\n📄 Report saved to: ${reportPath}`));
  }

  getScoreColor(score) {
    if (score >= 90) return chalk.green;
    if (score >= 70) return chalk.yellow;
    return chalk.red;
  }
}

// Run audit if called directly
if (require.main === module) {
  const auditor = new SaaSAuditor();
  auditor.run().catch(console.error);
}

module.exports = SaaSAuditor;
