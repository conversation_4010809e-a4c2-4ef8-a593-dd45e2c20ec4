import { devLog } from '@/lib/console-cleaner';
/**
 * Database Fallback and Graceful Failure Handling
 * Provides fallback mechanisms when database operations fail
 */

import { PostgrestError } from '@supabase/supabase-js';

export interface DatabaseOperation<T> {
  operation: () => Promise<T>;
  fallback?: () => T | Promise<T>;
  retryAttempts?: number;
  retryDelay?: number;
  operationName?: string;
}

export interface DatabaseError {
  code: string;
  message: string;
  details?: string;
  hint?: string;
  isRetryable: boolean;
  isFallbackable: boolean;
}

/**
 * Classify database errors for appropriate handling
 */
export function classifyDatabaseError(error: any): DatabaseError {
  // Handle Supabase/PostgreSQL errors
  if (error && typeof error === 'object') {
    const code = error.code || error.error_code || 'UNKNOWN';
    const message = error.message || 'Unknown database error';
    const details = error.details || error.error_description;
    const hint = error.hint;

    // Network and connection errors (retryable)
    if (
      code === 'NETWORK_ERROR' ||
      code === 'CONNECTION_ERROR' ||
      code === 'TIMEOUT' ||
      message.includes('network') ||
      message.includes('timeout') ||
      message.includes('connection')
    ) {
      return {
        code,
        message,
        details,
        hint,
        isRetryable: true,
        isFallbackable: true
      };
    }

    // Authentication errors (not retryable, fallback to public mode)
    if (
      code === 'INVALID_JWT' ||
      code === 'JWT_EXPIRED' ||
      code === 'UNAUTHORIZED' ||
      message.includes('JWT') ||
      message.includes('unauthorized')
    ) {
      return {
        code,
        message,
        details,
        hint,
        isRetryable: false,
        isFallbackable: true
      };
    }

    // Permission errors (not retryable, fallback possible)
    if (
      code === 'INSUFFICIENT_PRIVILEGE' ||
      code === 'RLS_VIOLATION' ||
      message.includes('permission') ||
      message.includes('privilege')
    ) {
      return {
        code,
        message,
        details,
        hint,
        isRetryable: false,
        isFallbackable: true
      };
    }

    // Data validation errors (not retryable, no fallback)
    if (
      code === 'INVALID_INPUT' ||
      code === 'CHECK_VIOLATION' ||
      code === 'FOREIGN_KEY_VIOLATION' ||
      code === 'UNIQUE_VIOLATION' ||
      message.includes('violates')
    ) {
      return {
        code,
        message,
        details,
        hint,
        isRetryable: false,
        isFallbackable: false
      };
    }

    // Server errors (retryable)
    if (
      code === 'INTERNAL_ERROR' ||
      code === 'SERVICE_UNAVAILABLE' ||
      code.startsWith('5') ||
      message.includes('internal error')
    ) {
      return {
        code,
        message,
        details,
        hint,
        isRetryable: true,
        isFallbackable: true
      };
    }
  }

  // Default classification for unknown errors
  return {
    code: 'UNKNOWN',
    message: error?.message || 'Unknown error',
    details: error?.details,
    hint: error?.hint,
    isRetryable: true,
    isFallbackable: true
  };
}

/**
 * Execute database operation with retry and fallback logic
 */
export async function executeWithFallback<T>(
  config: DatabaseOperation<T>
): Promise<{
  success: boolean;
  data?: T;
  error?: DatabaseError;
  usedFallback: boolean;
  attempts: number;
}> {
  const {
    operation,
    fallback,
    retryAttempts = 3,
    retryDelay = 1000,
    operationName = 'database operation'
  } = config;

  let lastError: DatabaseError | null = null;
  let attempts = 0;

  // Retry loop
  for (let attempt = 1; attempt <= retryAttempts; attempt++) {
    attempts = attempt;
    
    try {
  devLog.log(`🔄 Attempting ${operationName} (attempt ${attempt}/${retryAttempts})`);
      
      const result = await operation();
  devLog.log(`✅ ${operationName} succeeded on attempt ${attempt}`);
      
      return {
        success: true,
        data: result,
        usedFallback: false,
        attempts
      };
    } catch (error) {
      lastError = classifyDatabaseError(error);
      
      console.warn(`⚠️ ${operationName} failed on attempt ${attempt}:`, {
        code: lastError.code,
        message: lastError.message,
        isRetryable: lastError.isRetryable
      });

      // If error is not retryable, break out of retry loop
      if (!lastError.isRetryable) {
  devLog.log(`❌ ${operationName} failed with non-retryable error`);
        break;
      }

      // If this is not the last attempt, wait before retrying
      if (attempt < retryAttempts) {
        const delay = retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
  devLog.log(`⏳ Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // If we get here, all retries failed
  console.error(`❌ ${operationName} failed after ${attempts} attempts`);

  // Try fallback if available and error is fallbackable
  if (fallback && lastError?.isFallbackable) {
    try {
  devLog.log(`🔄 Attempting fallback for ${operationName}`);
      
      const fallbackResult = await fallback();
  devLog.log(`✅ Fallback succeeded for ${operationName}`);
      
      return {
        success: true,
        data: fallbackResult,
        usedFallback: true,
        attempts
      };
    } catch (fallbackError) {
      console.error(`❌ Fallback failed for ${operationName}:`, fallbackError);
      
      return {
        success: false,
        error: classifyDatabaseError(fallbackError),
        usedFallback: true,
        attempts
      };
    }
  }

  return {
    success: false,
    error: lastError || {
      code: 'UNKNOWN',
      message: 'Operation failed',
      isRetryable: false,
      isFallbackable: false
    },
    usedFallback: false,
    attempts
  };
}

/**
 * Common fallback data for different operations
 */
export const fallbackData = {
  // User profile fallback
  userProfile: (userId: string) => ({
    id: userId,
    email: '<EMAIL>',
    name: 'User',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    subscription_tier: 'free',
    usage_count: 0
  }),

  // Empty summaries list
  summariesList: () => [],

  // Empty files list
  filesList: () => [],

  // Empty analytics data
  analyticsData: () => ({
    total_summaries: 0,
    total_files: 0,
    total_exports: 0,
    recent_activity: [],
    usage_by_day: [],
    popular_tags: []
  }),

  // Default smart tags
  smartTags: () => [],

  // Empty audit logs
  auditLogs: () => [],

  // Default subscription info
  subscriptionInfo: () => ({
    tier: 'free',
    status: 'active',
    usage_limit: 10,
    usage_count: 0,
    expires_at: null
  })
};

/**
 * Wrapper for common database operations with built-in fallbacks
 */
export class DatabaseFallbackService {
  /**
   * Get user profile with fallback
   */
  static async getUserProfile(
    operation: () => Promise<any>,
    userId: string
  ) {
    return executeWithFallback({
      operation,
      fallback: () => fallbackData.userProfile(userId),
      operationName: 'get user profile',
      retryAttempts: 2
    });
  }

  /**
   * Get summaries list with fallback
   */
  static async getSummariesList(
    operation: () => Promise<any[]>
  ) {
    return executeWithFallback({
      operation,
      fallback: () => fallbackData.summariesList(),
      operationName: 'get summaries list',
      retryAttempts: 2
    });
  }

  /**
   * Get analytics data with fallback
   */
  static async getAnalyticsData(
    operation: () => Promise<any>
  ) {
    return executeWithFallback({
      operation,
      fallback: () => fallbackData.analyticsData(),
      operationName: 'get analytics data',
      retryAttempts: 2
    });
  }

  /**
   * Insert operation with retry (no fallback)
   */
  static async insertData<T>(
    operation: () => Promise<T>,
    operationName: string = 'insert data'
  ) {
    return executeWithFallback({
      operation,
      operationName,
      retryAttempts: 3,
      retryDelay: 1000
    });
  }

  /**
   * Update operation with retry (no fallback)
   */
  static async updateData<T>(
    operation: () => Promise<T>,
    operationName: string = 'update data'
  ) {
    return executeWithFallback({
      operation,
      operationName,
      retryAttempts: 2,
      retryDelay: 500
    });
  }
}
