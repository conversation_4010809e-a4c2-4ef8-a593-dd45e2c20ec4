import { devLog } from '@/lib/console-cleaner';
/**
 * Demo Payment Gateway Service
 * Public demo mode - no actual payment processing
 */

import { SentryTracker } from '@/lib/sentry.client';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import Stripe from 'stripe';

export interface PaymentGateway {
  id: 'stripe' | 'cashfree' | 'demo';
  name: string;
  enabled: boolean;
  regions: string[];
  priority: number;
}

export interface PaymentRequest {
  amount: number;
  currency: string;
  customer_email: string;
  customer_id: string;
  plan_id: string;
  success_url: string;
  cancel_url: string;
  metadata?: Record<string, string>;
}

export interface PaymentResponse {
  success: boolean;
  gateway_used: 'stripe' | 'cashfree' | 'demo';
  checkout_url?: string;
  session_id?: string;
  payment_id?: string;
  amount?: number;
  currency?: string;
  error?: string;
  fallback_attempted?: boolean;
}

export interface WebhookEvent {
  gateway: 'stripe' | 'cashfree' | 'demo';
  event_type: string;
  event_id: string;
  data: any;
  timestamp: string;
}

// Demo Stripe instance (for type compatibility only)
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_demo', {
  apiVersion: '2025-06-30.basil',
});

// Demo payment gateway configurations
const PAYMENT_GATEWAYS: PaymentGateway[] = [
  {
    id: 'demo',
    name: 'Demo Gateway',
    enabled: true,
    regions: ['ALL'],
    priority: 1,
  },
];

/**
 * Get preferred payment gateway based on region
 */
export function getPreferredGateway(region?: string): PaymentGateway {
  if (!region) {
    // Default to Stripe if no region specified
    return PAYMENT_GATEWAYS.find(g => g.id === 'stripe') || PAYMENT_GATEWAYS[0];
  }

  // Find gateway that supports the region with highest priority
  const supportedGateways = PAYMENT_GATEWAYS
    .filter(g => g.enabled && g.regions.includes(region))
    .sort((a, b) => a.priority - b.priority);

  return supportedGateways[0] || PAYMENT_GATEWAYS.find(g => g.id === 'stripe') || PAYMENT_GATEWAYS[0];
}

/**
 * Demo checkout session (no actual payment processing)
 */
export async function createCheckoutSession(
  request: PaymentRequest,
  region?: string
): Promise<PaymentResponse> {
  try {
  devLog.log(`🎭 Demo mode: Simulating checkout for plan ${request.plan_id}`);

    // Simulate successful checkout
    return {
      success: true,
      gateway_used: 'demo',
      checkout_url: `${request.success_url}?payment=demo_success&plan=${request.plan_id}`,
      session_id: `demo_session_${Date.now()}`,
      amount: request.amount,
      currency: request.currency,
    };

  } catch (error) {
    console.error('Demo payment error:', error);

    return {
      success: false,
      gateway_used: 'demo',
      error: error instanceof Error ? error.message : 'Demo payment error',
    };
  }
}

/**
 * Create checkout session with specific gateway
 */
async function createCheckoutWithGateway(
  request: PaymentRequest,
  gatewayId: 'stripe' | 'cashfree'
): Promise<PaymentResponse> {
  switch (gatewayId) {
    case 'stripe':
      return createStripeCheckout(request);
    case 'cashfree':
      return createCashfreeCheckout(request);
    default:
      throw new Error(`Unsupported gateway: ${gatewayId}`);
  }
}

/**
 * Create Stripe checkout session
 */
async function createStripeCheckout(request: PaymentRequest): Promise<PaymentResponse> {
  try {
    const session = await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      customer_email: request.customer_email,
      line_items: [
        {
          price: request.plan_id, // Stripe price ID
          quantity: 1,
        },
      ],
      success_url: request.success_url,
      cancel_url: request.cancel_url,
      metadata: {
        customer_id: request.customer_id,
        ...request.metadata,
      },
      subscription_data: {
        metadata: {
          customer_id: request.customer_id,
          ...request.metadata,
        },
      },
    });

    return {
      success: true,
      gateway_used: 'stripe',
      checkout_url: session.url!,
      session_id: session.id,
    };

  } catch (error) {
    console.error('Stripe checkout error:', error);
    throw error;
  }
}

/**
 * Create Cashfree checkout session
 */
async function createCashfreeCheckout(request: PaymentRequest): Promise<PaymentResponse> {
  try {
    // Cashfree API integration
    const cashfreeRequest = {
      order_amount: request.amount,
      order_currency: request.currency,
      customer_details: {
        customer_id: request.customer_id,
        customer_email: request.customer_email,
      },
      order_meta: {
        return_url: request.success_url,
        notify_url: `${process.env.NEXT_PUBLIC_APP_URL}/api/webhooks/cashfree`,
        payment_methods: 'cc,dc,nb,upi,wallet',
      },
      order_note: `Subscription: ${request.plan_id}`,
    };

    const response = await fetch('https://api.cashfree.com/pg/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-version': '2023-08-01',
        'x-client-id': process.env.CASHFREE_APP_ID!,
        'x-client-secret': process.env.CASHFREE_SECRET_KEY!,
      },
      body: JSON.stringify(cashfreeRequest),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Cashfree API error');
    }

    return {
      success: true,
      gateway_used: 'cashfree',
      checkout_url: data.payment_link,
      payment_id: data.order_id,
    };

  } catch (error) {
    console.error('Cashfree checkout error:', error);
    throw error;
  }
}

/**
 * Handle webhook events from both gateways
 */
export async function handleWebhookEvent(
  gateway: 'stripe' | 'cashfree',
  rawBody: string,
  signature: string
): Promise<{ success: boolean; event?: WebhookEvent; error?: string }> {
  try {
    let event: WebhookEvent;

    if (gateway === 'stripe') {
      const stripeEvent = stripe.webhooks.constructEvent(
        rawBody,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );

      event = {
        gateway: 'stripe',
        event_type: stripeEvent.type,
        event_id: stripeEvent.id,
        data: stripeEvent.data,
        timestamp: new Date(stripeEvent.created * 1000).toISOString(),
      };
    } else {
      // Cashfree webhook verification
      const cashfreeEvent = JSON.parse(rawBody);
      
      // Verify Cashfree signature (implement based on their docs)
      if (!verifyCashfreeSignature(rawBody, signature)) {
        throw new Error('Invalid Cashfree webhook signature');
      }

      event = {
        gateway: 'cashfree',
        event_type: cashfreeEvent.type,
        event_id: cashfreeEvent.order_id,
        data: cashfreeEvent,
        timestamp: new Date().toISOString(),
      };
    }

    // Store webhook event
    await storeWebhookEvent(event);

    // Process the event
    await processWebhookEvent(event);

    return { success: true, event };

  } catch (error) {
    console.error(`${gateway} webhook error:`, error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Webhook processing failed',
    };
  }
}

/**
 * Process webhook events
 */
async function processWebhookEvent(event: WebhookEvent): Promise<void> {
  const supabase = await createSupabaseServerClient();

  switch (event.gateway) {
    case 'stripe':
      await processStripeEvent(event, supabase);
      break;
    case 'cashfree':
      await processCashfreeEvent(event, supabase);
      break;
  }
}

/**
 * Process Stripe webhook events
 */
async function processStripeEvent(event: WebhookEvent, supabase: any): Promise<void> {
  switch (event.event_type) {
    case 'checkout.session.completed':
      const session = event.data.object;
      await handleSubscriptionCreated(session.metadata.customer_id, session.subscription, 'stripe', supabase);
      break;

    case 'customer.subscription.updated':
    case 'customer.subscription.deleted':
      const subscription = event.data.object;
      await handleSubscriptionUpdated(subscription.metadata.customer_id, subscription, 'stripe', supabase);
      break;

    case 'invoice.payment_succeeded':
    case 'invoice.payment_failed':
      const invoice = event.data.object;
      await handlePaymentEvent(invoice.customer_email, invoice, 'stripe', supabase);
      break;
  }
}

/**
 * Process Cashfree webhook events
 */
async function processCashfreeEvent(event: WebhookEvent, supabase: any): Promise<void> {
  switch (event.event_type) {
    case 'PAYMENT_SUCCESS':
      await handleSubscriptionCreated(event.data.customer_id, event.data, 'cashfree', supabase);
      break;

    case 'PAYMENT_FAILED':
      await handlePaymentEvent(event.data.customer_email, event.data, 'cashfree', supabase);
      break;
  }
}

/**
 * Store webhook event in database
 */
async function storeWebhookEvent(event: WebhookEvent): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();
    
    await supabase.from('webhook_events').insert({
      gateway: event.gateway,
      event_type: event.event_type,
      event_id: event.event_id,
      event_data: event.data,
      processed_at: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error storing webhook event:', error);
  }
}

/**
 * Handle subscription creation
 */
async function handleSubscriptionCreated(
  customerId: string,
  subscriptionData: any,
  gateway: 'stripe' | 'cashfree',
  supabase: any
): Promise<void> {
  // Implementation depends on your subscription data structure
  devLog.log(`Subscription created via ${gateway} for customer ${customerId}`);
}

/**
 * Handle subscription updates
 */
async function handleSubscriptionUpdated(
  customerId: string,
  subscriptionData: any,
  gateway: 'stripe' | 'cashfree',
  supabase: any
): Promise<void> {
  // Implementation depends on your subscription data structure
  devLog.log(`Subscription updated via ${gateway} for customer ${customerId}`);
}

/**
 * Handle payment events
 */
async function handlePaymentEvent(
  customerEmail: string,
  paymentData: any,
  gateway: 'stripe' | 'cashfree',
  supabase: any
): Promise<void> {
  // Implementation depends on your payment data structure
  devLog.log(`Payment event via ${gateway} for customer ${customerEmail}`);
}

/**
 * Verify Cashfree webhook signature
 */
function verifyCashfreeSignature(rawBody: string, signature: string): boolean {
  // Implement Cashfree signature verification based on their documentation
  // This is a placeholder implementation
  return true;
}

/**
 * Get payment gateway status
 */
export async function getPaymentGatewayStatus(): Promise<{
  gateways: PaymentGateway[];
  health_check: Record<string, boolean>;
}> {
  const healthCheck: Record<string, boolean> = {};

  // Check Stripe health
  try {
    await stripe.accounts.retrieve();
    healthCheck.stripe = true;
  } catch {
    healthCheck.stripe = false;
  }

  // Check Cashfree health
  try {
    const response = await fetch('https://api.cashfree.com/pg/orders', {
      method: 'GET',
      headers: {
        'x-api-version': '2023-08-01',
        'x-client-id': process.env.CASHFREE_APP_ID!,
        'x-client-secret': process.env.CASHFREE_SECRET_KEY!,
      },
    });
    healthCheck.cashfree = response.ok;
  } catch {
    healthCheck.cashfree = false;
  }

  return {
    gateways: PAYMENT_GATEWAYS,
    health_check: healthCheck,
  };
}
