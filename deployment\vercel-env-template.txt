# =============================================================================
# VERCEL ENVIRONMENT VARIABLES TEMPLATE
# =============================================================================
# Copy these environment variables to your Vercel project settings
# Replace placeholder values with real production keys
# =============================================================================

# Production Mode Configuration
NODE_ENV=production
NEXT_PUBLIC_MODE=production
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_ENVIRONMENT=production

# Site URLs (Update with your actual domain)
NEXT_PUBLIC_SITE_URL=https://slack-summary-scribe.vercel.app
NEXT_PUBLIC_APP_URL=https://slack-summary-scribe.vercel.app

# =============================================================================
# AUTHENTICATION - CLERK (REQUIRED)
# =============================================================================
# Get these from: https://dashboard.clerk.com/
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_YOUR_CLERK_PUBLISHABLE_KEY
CLERK_SECRET_KEY=sk_live_YOUR_CLERK_SECRET_KEY
CLERK_WEBHOOK_SECRET=whsec_YOUR_CLERK_WEBHOOK_SECRET

# =============================================================================
# DATABASE - SUPABASE (REQUIRED)
# =============================================================================
# Get these from: https://supabase.com/dashboard/
NEXT_PUBLIC_SUPABASE_URL=https://YOUR_PROJECT_ID.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=YOUR_SUPABASE_SERVICE_ROLE_KEY
SUPABASE_URL=https://YOUR_PROJECT_ID.supabase.co
DATABASE_URL=***********************************************************************/postgres

# =============================================================================
# AI SERVICES - OPENROUTER (REQUIRED)
# =============================================================================
# Get API key from: https://openrouter.ai/
OPENROUTER_API_KEY=sk-or-v1-YOUR_OPENROUTER_API_KEY

# =============================================================================
# MONITORING & ANALYTICS (REQUIRED FOR PRODUCTION)
# =============================================================================

# PostHog Analytics - Get from: https://app.posthog.com/
NEXT_PUBLIC_POSTHOG_KEY=phc_YOUR_POSTHOG_PROJECT_KEY
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
POSTHOG_KEY=phc_YOUR_POSTHOG_PROJECT_KEY
POSTHOG_API_KEY=phx_YOUR_POSTHOG_API_KEY

# Sentry Error Monitoring - Get from: https://sentry.io/
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/4509565394419712
SENTRY_DSN=https://<EMAIL>/4509565394419712
SENTRY_ORG=slack-summary-scribe
SENTRY_PROJECT=slack-summary-scribe
SENTRY_AUTH_TOKEN=YOUR_SENTRY_AUTH_TOKEN

# =============================================================================
# SLACK INTEGRATION (OPTIONAL)
# =============================================================================
# Get these from: https://api.slack.com/apps/
NEXT_PUBLIC_SLACK_CLIENT_ID=YOUR_SLACK_CLIENT_ID
SLACK_CLIENT_ID=YOUR_SLACK_CLIENT_ID
SLACK_CLIENT_SECRET=YOUR_SLACK_CLIENT_SECRET
SLACK_SIGNING_SECRET=YOUR_SLACK_SIGNING_SECRET
SLACK_WEBHOOK_URL=https://slack-summary-scribe.vercel.app/api/slack/oauth/callback

# =============================================================================
# EXPORT INTEGRATIONS (OPTIONAL)
# =============================================================================

# Notion API - Get from: https://www.notion.so/my-integrations/
NOTION_API_TOKEN=secret_YOUR_NOTION_INTEGRATION_TOKEN
NOTION_DATABASE_ID=YOUR_NOTION_DATABASE_ID

# Email Service - Resend - Get from: https://resend.com/
RESEND_API_KEY=re_YOUR_RESEND_API_KEY
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# =============================================================================
# PAYMENT PROCESSING (OPTIONAL)
# =============================================================================

# Stripe - Get from: https://dashboard.stripe.com/
# STRIPE_SECRET_KEY=sk_live_YOUR_STRIPE_SECRET_KEY
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_STRIPE_PUBLISHABLE_KEY
# STRIPE_WEBHOOK_SECRET=whsec_YOUR_STRIPE_WEBHOOK_SECRET

# Cashfree - Get from: https://www.cashfree.com/
# CASHFREE_APP_ID=YOUR_CASHFREE_APP_ID
# CASHFREE_SECRET_KEY=YOUR_CASHFREE_SECRET_KEY

# =============================================================================
# NEXTAUTH (BACKUP AUTHENTICATION)
# =============================================================================
NEXTAUTH_SECRET=YOUR_NEXTAUTH_SECRET_32_CHARS_MINIMUM
NEXTAUTH_URL=https://slack-summary-scribe.vercel.app

# =============================================================================
# FEATURE FLAGS & CONFIGURATION
# =============================================================================
NEXT_PUBLIC_FETCH_TIMEOUT=10000
NEXT_PUBLIC_FEATURE_AI_MODEL_ROUTING=true
NEXT_PUBLIC_FEATURE_PREMIUM_AI=true
NEXT_PUBLIC_FEATURE_AI_ANALYTICS=true
NEXT_PUBLIC_FEATURE_ENTERPRISE_SECURITY=true

# =============================================================================
# VERCEL SPECIFIC (AUTO-POPULATED)
# =============================================================================
NEXT_PUBLIC_VERCEL_ENV=production
NEXT_PUBLIC_VERCEL_URL=slack-summary-scribe.vercel.app

# =============================================================================
# DEPLOYMENT INSTRUCTIONS
# =============================================================================
# 1. Go to https://vercel.com/dashboard
# 2. Select your project
# 3. Go to Settings > Environment Variables
# 4. Add each variable above with real production values
# 5. Deploy your project
# 
# SECURITY NOTES:
# - Never commit real API keys to version control
# - Use different keys for development and production
# - Regularly rotate sensitive keys
# - Monitor usage and set up alerts
# =============================================================================
