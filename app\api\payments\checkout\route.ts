import { devLog } from '@/lib/console-cleaner';
/**
 * Secure Payment Checkout API Route
 * Production-ready Stripe integration with comprehensive security
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSecureApiRoute, createSuccessResponse, createErrorResponse } from '@/lib/api-security';
import {
  validatePaymentAmount,
  checkPaymentRateLimit,
  sanitizePaymentMetadata,
  detectSuspiciousPayment,
  logPaymentSecurityEvent,
  generatePaymentReference,
  validatePaymentPlan
} from '@/lib/payment-security';
import { stripe } from '@/lib/stripe-config';
import { UserRole } from '@/lib/auth-protection';

/**
 * POST /api/payments/checkout
 * Create secure Stripe checkout session with fraud protection
 */
export const POST = createSecureApiRoute(
  async (request: NextRequest, authResult) => {
    try {
      const { userId } = authResult;

      const body = await request.json();
      const {
        plan_id,
        success_url,
        cancel_url,
        metadata = {}
      } = body;

      // Validate plan
      const planValidation = validatePaymentPlan(plan_id);
      if (!planValidation.valid) {
        return createErrorResponse(
          'Invalid plan',
          planValidation.error!,
          400,
          'INVALID_PLAN'
        );
      }

      const plan = planValidation.plan!;

      // Validate payment amount
      const amountValidation = validatePaymentAmount(plan.amount, plan.currency);
      if (!amountValidation.valid) {
        return createErrorResponse(
          'Invalid amount',
          amountValidation.error!,
          400,
          'INVALID_AMOUNT'
        );
      }

      // Check rate limiting
      const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip');
      const rateLimitCheck = checkPaymentRateLimit(userId, ip || undefined);

      if (!rateLimitCheck.allowed) {
        logPaymentSecurityEvent(userId, 'RATE_LIMIT_EXCEEDED', {
          reason: rateLimitCheck.reason,
          ip
        }, request);

        return createErrorResponse(
          'Rate limit exceeded',
          rateLimitCheck.reason!,
          429,
          'RATE_LIMIT_EXCEEDED',
          { retryAfter: rateLimitCheck.retryAfter }
        );
      }

      // Sanitize metadata
      const sanitizedMetadata = sanitizePaymentMetadata(metadata);

      // Detect suspicious patterns
      const suspiciousCheck = detectSuspiciousPayment(userId, plan.amount, plan.currency, sanitizedMetadata);
      if (suspiciousCheck.suspicious) {
        logPaymentSecurityEvent(userId, 'SUSPICIOUS_PAYMENT_DETECTED', {
          reasons: suspiciousCheck.reasons,
          plan_id,
          amount: plan.amount
        }, request);

        return createErrorResponse(
          'Payment validation failed',
          'This payment request appears suspicious and has been blocked',
          403,
          'SUSPICIOUS_PAYMENT'
        );
      }

      // Generate secure payment reference
      const paymentReference = generatePaymentReference(userId, plan_id);
  devLog.log(`Creating secure Stripe checkout for plan ${plan_id} for user ${userId}`);

      // Create Stripe checkout session
      const session = await stripe.checkout.sessions.create({
        mode: 'subscription',
        payment_method_types: ['card'],
        line_items: [
          {
            price_data: {
              currency: plan.currency.toLowerCase(),
              product_data: {
                name: `Slack Summary Scribe ${plan.name}`,
                description: `${plan.name} subscription plan`,
                metadata: {
                  plan_id: plan.id,
                  tier: plan.name.toUpperCase()
                }
              },
              unit_amount: plan.amount * 100, // Convert to cents
              recurring: {
                interval: 'month'
              }
            },
            quantity: 1
          }
        ],
        customer_email: authResult.user?.emailAddresses?.[0]?.emailAddress,
        success_url: success_url || `${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?payment=success&session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: cancel_url || `${process.env.NEXT_PUBLIC_SITE_URL}/pricing?payment=cancelled`,
        metadata: {
          user_id: userId,
          plan_id: plan.id,
          payment_reference: paymentReference,
          ...sanitizedMetadata
        },
        subscription_data: {
          metadata: {
            user_id: userId,
            plan_id: plan.id,
            payment_reference: paymentReference
          }
        },
        allow_promotion_codes: true,
        billing_address_collection: 'required',
        payment_intent_data: {
          metadata: {
            user_id: userId,
            plan_id: plan.id,
            payment_reference: paymentReference
          }
        }
      });

      // Log successful checkout creation
      logPaymentSecurityEvent(userId, 'CHECKOUT_CREATED', {
        session_id: session.id,
        plan_id: plan.id,
        amount: plan.amount,
        payment_reference: paymentReference
      }, request);

      return createSuccessResponse({
        checkout_url: session.url,
        session_id: session.id,
        payment_reference: paymentReference,
        plan: plan
      }, 'Checkout session created successfully');

    } catch (error) {
      console.error('Payment checkout error:', error);

      logPaymentSecurityEvent(userId, 'CHECKOUT_ERROR', {
        error: error instanceof Error ? error.message : 'Unknown error',
        plan_id
      }, request);

      return createErrorResponse(
        'Checkout failed',
        'Failed to create checkout session. Please try again.',
        500,
        'CHECKOUT_ERROR'
      );
    }
}, {
  requireAuth: true,
  rateLimit: 10, // 10 checkout attempts per minute
  auditLog: true,
  allowedMethods: ['POST']
});

/**
 * Get user's region from request
 */
function getUserRegion(request: NextRequest): string {
  // Try to get region from Cloudflare header
  const cfCountry = request.headers.get('cf-ipcountry');
  if (cfCountry) {
    return cfCountry;
  }

  // Try to get region from Vercel header
  const vercelCountry = request.headers.get('x-vercel-ip-country');
  if (vercelCountry) {
    return vercelCountry;
  }

  // Try to get from custom header
  const customRegion = request.headers.get('x-user-region');
  if (customRegion) {
    return customRegion;
  }

  // Default to US
  return 'US';
}
