# 📤 Workflow & Export Extensions

## Overview
Comprehensive integration and export system enabling seamless workflows with popular productivity tools, CRM systems, and cloud storage platforms.

## Features Implemented

### 1. Third-Party Integrations
- **Notion**: Export summaries as pages, sync with databases
- **HubSpot**: Create deals, contacts, and activities from summaries
- **Salesforce**: Sync meeting notes, opportunities, and leads
- **Slack**: Auto-post summaries to channels, thread responses
- **Microsoft Teams**: Share summaries and insights

### 2. Cloud Storage Exports
- **Google Drive**: Save summaries as docs, organize in folders
- **Dropbox**: Backup summaries, team sharing
- **OneDrive**: Enterprise document management
- **Box**: Secure enterprise file sharing

### 3. Advanced Export Formats
- **PDF**: Professional reports with branding
- **DOCX**: Editable Word documents
- **Markdown**: Developer-friendly format
- **JSON**: API-friendly structured data
- **CSV**: Spreadsheet analysis
- **HTML**: Web-ready content

### 4. Automated Workflows
- **Scheduled Exports**: Daily/weekly automated exports
- **Trigger-based**: Export on summary creation
- **Bulk Operations**: Export multiple summaries
- **Template System**: Customizable export templates

## File Structure
```
/features/integrations/
├── README.md                    # This file
├── types.ts                    # Integration types and interfaces
├── oauth-manager.ts            # OAuth flow management
├── export-engine.ts            # Core export functionality
├── integrations/
│   ├── notion.ts              # Notion integration
│   ├── hubspot.ts             # HubSpot CRM integration
│   ├── salesforce.ts          # Salesforce integration
│   ├── google-drive.ts        # Google Drive integration
│   ├── dropbox.ts             # Dropbox integration
│   └── slack-posting.ts       # Slack auto-posting
├── exporters/
│   ├── pdf-exporter.ts        # PDF generation
│   ├── docx-exporter.ts       # Word document export
│   ├── markdown-exporter.ts   # Markdown export
│   └── csv-exporter.ts        # CSV export
└── components/
    ├── IntegrationDashboard.tsx # Integration management UI
    ├── ExportDialog.tsx         # Export configuration
    ├── OAuthConnect.tsx         # OAuth connection flow
    └── ExportHistory.tsx        # Export history and status
```

## Database Schema
```sql
-- Integration connections
CREATE TABLE integration_connections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  provider TEXT NOT NULL,
  provider_user_id TEXT,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  scopes TEXT[],
  metadata JSONB DEFAULT '{}',
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'expired', 'revoked')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Export jobs
CREATE TABLE export_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id),
  export_type TEXT NOT NULL,
  format TEXT NOT NULL,
  destination TEXT, -- 'download', 'notion', 'drive', etc.
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  summary_ids UUID[],
  configuration JSONB DEFAULT '{}',
  result_url TEXT,
  error_message TEXT,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Automated workflows
CREATE TABLE automated_workflows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  trigger_type TEXT NOT NULL, -- 'schedule', 'summary_created', 'manual'
  trigger_config JSONB NOT NULL,
  actions JSONB NOT NULL, -- Array of actions to perform
  enabled BOOLEAN DEFAULT true,
  last_run_at TIMESTAMP WITH TIME ZONE,
  next_run_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Environment Variables
```bash
# Integration API Keys
NOTION_CLIENT_ID=your_notion_client_id
NOTION_CLIENT_SECRET=your_notion_client_secret
HUBSPOT_CLIENT_ID=your_hubspot_client_id
HUBSPOT_CLIENT_SECRET=your_hubspot_client_secret
SALESFORCE_CLIENT_ID=your_salesforce_client_id
SALESFORCE_CLIENT_SECRET=your_salesforce_client_secret
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
DROPBOX_APP_KEY=your_dropbox_app_key
DROPBOX_APP_SECRET=your_dropbox_app_secret

# Export Configuration
EXPORT_MAX_FILE_SIZE=50MB
EXPORT_RETENTION_DAYS=30
PDF_TEMPLATE_PATH=/templates/pdf
DOCX_TEMPLATE_PATH=/templates/docx
```

## Usage Examples

### Connect to Notion
```typescript
import { initiateOAuthFlow } from '@/features/integrations/oauth-manager';

const authUrl = await initiateOAuthFlow('notion', {
  userId,
  organizationId,
  scopes: ['read_content', 'insert_content']
});
// Redirect user to authUrl
```

### Export to PDF
```typescript
import { exportSummaries } from '@/features/integrations/export-engine';

const exportJob = await exportSummaries({
  summaryIds: ['summary-1', 'summary-2'],
  format: 'pdf',
  destination: 'download',
  configuration: {
    template: 'professional',
    includeBranding: true,
    pageSize: 'A4'
  }
});
```

### Auto-post to Slack
```typescript
import { createAutomatedWorkflow } from '@/features/integrations/workflows';

await createAutomatedWorkflow({
  name: 'Daily Summary to Slack',
  triggerType: 'schedule',
  triggerConfig: { cron: '0 9 * * *' },
  actions: [{
    type: 'slack_post',
    config: {
      channel: '#general',
      template: 'daily_summary'
    }
  }]
});
```

## API Endpoints
- `GET /api/integrations` - List available integrations
- `POST /api/integrations/connect` - Initiate OAuth connection
- `DELETE /api/integrations/:id` - Disconnect integration
- `POST /api/exports` - Create export job
- `GET /api/exports/:id` - Get export status
- `GET /api/exports/:id/download` - Download export file
- `POST /api/workflows` - Create automated workflow
- `PUT /api/workflows/:id` - Update workflow
- `POST /api/workflows/:id/run` - Manually trigger workflow

## Supported Integrations

### Productivity Tools
- **Notion**: Create pages, update databases, sync properties
- **Google Workspace**: Drive storage, Docs creation, Calendar events
- **Microsoft 365**: OneDrive, Word, Teams integration
- **Slack**: Channel posting, thread responses, DM notifications

### CRM Systems
- **HubSpot**: Contacts, deals, activities, notes
- **Salesforce**: Leads, opportunities, accounts, tasks
- **Pipedrive**: Deals, activities, notes
- **Airtable**: Base records, field updates

### Cloud Storage
- **Google Drive**: Folder organization, sharing permissions
- **Dropbox**: Team folders, file sharing
- **OneDrive**: Enterprise document management
- **Box**: Secure file sharing, compliance

## Export Templates

### PDF Templates
- **Professional**: Clean, branded layout
- **Executive**: High-level summary format
- **Detailed**: Comprehensive analysis
- **Meeting Notes**: Meeting-specific format

### Integration Templates
- **Notion Page**: Rich text with embedded media
- **HubSpot Activity**: CRM-formatted notes
- **Slack Message**: Channel-optimized format
- **Email Digest**: Newsletter-style layout

## Workflow Triggers

### Schedule-based
- **Daily**: 9 AM summary exports
- **Weekly**: Monday morning team digests
- **Monthly**: Comprehensive reports
- **Custom**: User-defined cron schedules

### Event-based
- **Summary Created**: Immediate export/sharing
- **Team Member Added**: Welcome package
- **Plan Upgraded**: Feature showcase
- **Milestone Reached**: Celebration content

## Security & Compliance

### OAuth Security
- **Secure Token Storage**: Encrypted at rest
- **Token Refresh**: Automatic renewal
- **Scope Limitation**: Minimal required permissions
- **Audit Logging**: All integration activities logged

### Data Privacy
- **User Consent**: Explicit permission for each integration
- **Data Minimization**: Only necessary data shared
- **Retention Policies**: Automatic cleanup of old exports
- **GDPR Compliance**: Right to deletion, data portability

## Best Practices
1. **User Experience**: Seamless OAuth flows, clear permissions
2. **Error Handling**: Graceful failures, retry mechanisms
3. **Performance**: Async processing, progress indicators
4. **Security**: Token encryption, scope validation
5. **Monitoring**: Integration health, usage analytics
