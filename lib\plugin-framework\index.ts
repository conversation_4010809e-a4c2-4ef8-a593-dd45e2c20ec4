/**
 * Plugin Framework for Feature Lifecycle Management
 * 
 * Provides a clean, extensible architecture for adding new features
 * with automatic registration, lifecycle management, and dependency injection.
 */

export interface PluginMetadata {
  name: string;
  version: string;
  description: string;
  author: string;
  dependencies?: string[];
  permissions?: string[];
  category: 'integration' | 'ui' | 'analytics' | 'admin' | 'core';
  tags?: string[];
}

export interface PluginConfig {
  enabled: boolean;
  settings?: Record<string, any>;
  environment?: 'development' | 'staging' | 'production' | 'all';
}

export interface PluginContext {
  app: any;
  database: any;
  cache: any;
  logger: any;
  events: EventEmitter;
  config: PluginConfig;
  utils: PluginUtils;
}

export interface PluginLifecycle {
  onInstall?(context: PluginContext): Promise<void>;
  onEnable?(context: PluginContext): Promise<void>;
  onDisable?(context: PluginContext): Promise<void>;
  onUninstall?(context: PluginContext): Promise<void>;
  onUpdate?(context: PluginContext, oldVersion: string): Promise<void>;
}

export interface PluginHooks {
  beforeRequest?(context: PluginContext, request: any): Promise<any>;
  afterRequest?(context: PluginContext, request: any, response: any): Promise<any>;
  beforeRender?(context: PluginContext, props: any): Promise<any>;
  afterRender?(context: PluginContext, result: any): Promise<any>;
  onError?(context: PluginContext, error: Error): Promise<void>;
}

export interface PluginAPI {
  registerRoute?(routes: RouteDefinition[]): void;
  registerComponent?(components: ComponentDefinition[]): void;
  registerService?(services: ServiceDefinition[]): void;
  registerMiddleware?(middleware: MiddlewareDefinition[]): void;
  registerCronJob?(jobs: CronJobDefinition[]): void;
}

export abstract class BasePlugin implements PluginLifecycle, PluginHooks, PluginAPI {
  abstract metadata: PluginMetadata;
  protected context?: PluginContext;

  // Lifecycle methods (optional overrides)
  async onInstall(context: PluginContext): Promise<void> {
    // Default implementation
  }

  async onEnable(context: PluginContext): Promise<void> {
    this.context = context;
  }

  async onDisable(context: PluginContext): Promise<void> {
    // Default implementation
  }

  async onUninstall(context: PluginContext): Promise<void> {
    // Default implementation
  }

  async onUpdate(context: PluginContext, oldVersion: string): Promise<void> {
    // Default implementation
  }

  // Hook methods (optional overrides)
  async beforeRequest(context: PluginContext, request: any): Promise<any> {
    return request;
  }

  async afterRequest(context: PluginContext, request: any, response: any): Promise<any> {
    return response;
  }

  async beforeRender(context: PluginContext, props: any): Promise<any> {
    return props;
  }

  async afterRender(context: PluginContext, result: any): Promise<any> {
    return result;
  }

  async onError(context: PluginContext, error: Error): Promise<void> {
    context.logger.error(`Plugin ${this.metadata.name} error:`, error);
  }

  // API registration methods (optional overrides)
  registerRoute?(routes: RouteDefinition[]): void;
  registerComponent?(components: ComponentDefinition[]): void;
  registerService?(services: ServiceDefinition[]): void;
  registerMiddleware?(middleware: MiddlewareDefinition[]): void;
  registerCronJob?(jobs: CronJobDefinition[]): void;

  // Utility methods
  protected log(message: string, level: 'info' | 'warn' | 'error' = 'info'): void {
    if (this.context) {
      this.context.logger[level](`[${this.metadata.name}] ${message}`);
    }
  }

  protected emit(event: string, data?: any): void {
    if (this.context) {
      this.context.events.emit(`plugin:${this.metadata.name}:${event}`, data);
    }
  }

  protected getSetting<T>(key: string, defaultValue?: T): T {
    return this.context?.config.settings?.[key] ?? defaultValue;
  }
}

export class PluginManager {
  private plugins: Map<string, BasePlugin> = new Map();
  private pluginConfigs: Map<string, PluginConfig> = new Map();
  private dependencyGraph: Map<string, string[]> = new Map();
  private context: PluginContext;

  constructor(context: PluginContext) {
    this.context = context;
  }

  /**
   * Register a plugin
   */
  async registerPlugin(plugin: BasePlugin, config: PluginConfig): Promise<void> {
    const { name } = plugin.metadata;

    // Validate plugin
    this.validatePlugin(plugin);

    // Check dependencies
    await this.checkDependencies(plugin);

    // Store plugin and config
    this.plugins.set(name, plugin);
    this.pluginConfigs.set(name, config);
    this.dependencyGraph.set(name, plugin.metadata.dependencies || []);

    // Install plugin
    await plugin.onInstall?.(this.context);

    // Enable if configured
    if (config.enabled) {
      await this.enablePlugin(name);
    }

    this.context.logger.info(`Plugin ${name} registered successfully`);
  }

  /**
   * Enable a plugin
   */
  async enablePlugin(name: string): Promise<void> {
    const plugin = this.plugins.get(name);
    const config = this.pluginConfigs.get(name);

    if (!plugin || !config) {
      throw new Error(`Plugin ${name} not found`);
    }

    if (config.enabled) {
      return; // Already enabled
    }

    // Enable dependencies first
    const dependencies = this.dependencyGraph.get(name) || [];
    for (const dep of dependencies) {
      await this.enablePlugin(dep);
    }

    // Enable plugin
    await plugin.onEnable?.(this.context);
    config.enabled = true;

    this.context.logger.info(`Plugin ${name} enabled`);
  }

  /**
   * Disable a plugin
   */
  async disablePlugin(name: string): Promise<void> {
    const plugin = this.plugins.get(name);
    const config = this.pluginConfigs.get(name);

    if (!plugin || !config) {
      throw new Error(`Plugin ${name} not found`);
    }

    if (!config.enabled) {
      return; // Already disabled
    }

    // Check if other plugins depend on this one
    const dependents = this.getDependents(name);
    if (dependents.length > 0) {
      throw new Error(`Cannot disable ${name}: required by ${dependents.join(', ')}`);
    }

    // Disable plugin
    await plugin.onDisable?.(this.context);
    config.enabled = false;

    this.context.logger.info(`Plugin ${name} disabled`);
  }

  /**
   * Uninstall a plugin
   */
  async uninstallPlugin(name: string): Promise<void> {
    const plugin = this.plugins.get(name);

    if (!plugin) {
      throw new Error(`Plugin ${name} not found`);
    }

    // Disable first
    await this.disablePlugin(name);

    // Uninstall
    await plugin.onUninstall?.(this.context);

    // Remove from registry
    this.plugins.delete(name);
    this.pluginConfigs.delete(name);
    this.dependencyGraph.delete(name);

    this.context.logger.info(`Plugin ${name} uninstalled`);
  }

  /**
   * Get all plugins
   */
  getPlugins(): Array<{ plugin: BasePlugin; config: PluginConfig }> {
    return Array.from(this.plugins.entries()).map(([name, plugin]) => ({
      plugin,
      config: this.pluginConfigs.get(name)!
    }));
  }

  /**
   * Get enabled plugins
   */
  getEnabledPlugins(): Array<{ plugin: BasePlugin; config: PluginConfig }> {
    return this.getPlugins().filter(({ config }) => config.enabled);
  }

  /**
   * Execute hook across all enabled plugins
   */
  async executeHook<T>(
    hookName: keyof PluginHooks,
    ...args: any[]
  ): Promise<T[]> {
    const results: T[] = [];
    const enabledPlugins = this.getEnabledPlugins();

    for (const { plugin } of enabledPlugins) {
      try {
        const hook = plugin[hookName] as Function;
        if (hook) {
          const result = await hook.call(plugin, this.context, ...args);
          if (result !== undefined) {
            results.push(result);
          }
        }
      } catch (error) {
        this.context.logger.error(`Hook ${hookName} failed for plugin ${plugin.metadata.name}:`, error);
        await plugin.onError?.(this.context, error as Error);
      }
    }

    return results;
  }

  /**
   * Get plugin by name
   */
  getPlugin(name: string): BasePlugin | undefined {
    return this.plugins.get(name);
  }

  /**
   * Check if plugin is enabled
   */
  isPluginEnabled(name: string): boolean {
    const config = this.pluginConfigs.get(name);
    return config?.enabled ?? false;
  }

  // Private methods

  private validatePlugin(plugin: BasePlugin): void {
    const { name, version } = plugin.metadata;

    if (!name || !version) {
      throw new Error('Plugin must have name and version');
    }

    if (this.plugins.has(name)) {
      throw new Error(`Plugin ${name} is already registered`);
    }
  }

  private async checkDependencies(plugin: BasePlugin): Promise<void> {
    const dependencies = plugin.metadata.dependencies || [];

    for (const dep of dependencies) {
      if (!this.plugins.has(dep)) {
        throw new Error(`Plugin ${plugin.metadata.name} requires ${dep} which is not installed`);
      }
    }
  }

  private getDependents(pluginName: string): string[] {
    const dependents: string[] = [];

    for (const [name, deps] of this.dependencyGraph.entries()) {
      if (deps.includes(pluginName) && this.isPluginEnabled(name)) {
        dependents.push(name);
      }
    }

    return dependents;
  }
}

// Type definitions for plugin registration

export interface RouteDefinition {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  handler: (req: any, res: any) => Promise<any>;
  middleware?: string[];
  permissions?: string[];
}

export interface ComponentDefinition {
  name: string;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
  slot?: string;
}

export interface ServiceDefinition {
  name: string;
  service: any;
  singleton?: boolean;
}

export interface MiddlewareDefinition {
  name: string;
  middleware: (req: any, res: any, next: any) => void;
  order?: number;
}

export interface CronJobDefinition {
  name: string;
  schedule: string;
  handler: () => Promise<void>;
  enabled?: boolean;
}

export interface PluginUtils {
  generateId(): string;
  validateEmail(email: string): boolean;
  formatDate(date: Date): string;
  encrypt(data: string): string;
  decrypt(data: string): string;
  hash(data: string): string;
}

// Event emitter for plugin communication
import { EventEmitter } from 'events';

export { EventEmitter };
