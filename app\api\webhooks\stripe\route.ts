import { devLog } from '@/lib/console-cleaner';
/**
 * Secure Stripe Webhook Handler
 * 
 * Handles Stripe webhook events with comprehensive security validation,
 * subscription management, and audit logging.
 */

import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe-config';
import { validateStripeWebhook, logPaymentSecurityEvent } from '@/lib/payment-security';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { createPublicApiRoute, createSuccessResponse, createErrorResponse } from '@/lib/api-security';

export const POST = createPublicApiRoute(
  async (request: NextRequest) => {
    try {
      const body = await request.text();
      const signature = request.headers.get('stripe-signature');
      
      if (!signature) {
        return createErrorResponse(
          'Missing signature',
          'Stripe signature header is required',
          400,
          'MISSING_SIGNATURE'
        );
      }

      // Validate webhook signature
      const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
      if (!webhookSecret) {
        console.error('Stripe webhook secret not configured');
        return createErrorResponse(
          'Configuration error',
          'Webhook secret not configured',
          500,
          'CONFIG_ERROR'
        );
      }

      const signatureValidation = validateStripeWebhook(body, signature, webhookSecret);
      if (!signatureValidation.valid) {
        console.error('Invalid Stripe webhook signature:', signatureValidation.error);
        return createErrorResponse(
          'Invalid signature',
          'Webhook signature validation failed',
          401,
          'INVALID_SIGNATURE'
        );
      }

      // Parse the event
      let event;
      try {
        event = JSON.parse(body);
      } catch (error) {
        console.error('Invalid JSON in webhook body:', error);
        return createErrorResponse(
          'Invalid JSON',
          'Webhook body must be valid JSON',
          400,
          'INVALID_JSON'
        );
      }
  devLog.log(`🔔 Stripe webhook received: ${event.type}`);

      // Handle different event types
      switch (event.type) {
        case 'checkout.session.completed':
          await handleCheckoutCompleted(event.data.object);
          break;
          
        case 'customer.subscription.created':
          await handleSubscriptionCreated(event.data.object);
          break;
          
        case 'customer.subscription.updated':
          await handleSubscriptionUpdated(event.data.object);
          break;
          
        case 'customer.subscription.deleted':
          await handleSubscriptionDeleted(event.data.object);
          break;
          
        case 'invoice.payment_succeeded':
          await handlePaymentSucceeded(event.data.object);
          break;
          
        case 'invoice.payment_failed':
          await handlePaymentFailed(event.data.object);
          break;
          
        case 'customer.subscription.trial_will_end':
          await handleTrialWillEnd(event.data.object);
          break;
          
        default:
  devLog.log(`Unhandled Stripe event type: ${event.type}`);
      }

      return createSuccessResponse(
        { received: true },
        'Webhook processed successfully'
      );

    } catch (error) {
      console.error('Stripe webhook error:', error);
      return createErrorResponse(
        'Webhook processing failed',
        'An error occurred while processing the webhook',
        500,
        'WEBHOOK_ERROR'
      );
    }
  },
  {
    rateLimit: 100, // Allow high rate for webhooks
    validateOrigin: false, // Webhooks come from Stripe
    allowedMethods: ['POST']
  }
);

/**
 * Handle successful checkout completion
 */
async function handleCheckoutCompleted(session: any) {
  try {
    const userId = session.metadata?.user_id;
    const planId = session.metadata?.plan_id;
    const paymentReference = session.metadata?.payment_reference;

    if (!userId || !planId) {
      console.error('Missing required metadata in checkout session:', session.id);
      return;
    }
  devLog.log(`✅ Checkout completed for user ${userId}, plan ${planId}`);

    // Log security event
    logPaymentSecurityEvent(userId, 'CHECKOUT_COMPLETED', {
      session_id: session.id,
      plan_id: planId,
      amount_total: session.amount_total,
      payment_reference: paymentReference
    });

    // Get Supabase client
    const supabase = await createSupabaseServerClient();

    // Create or update subscription
    const subscriptionData = {
      user_id: userId,
      subscription_tier: planId.toUpperCase(),
      status: 'active',
      stripe_customer_id: session.customer,
      stripe_subscription_id: session.subscription,
      stripe_session_id: session.id,
      payment_reference: paymentReference,
      current_period_start: new Date(),
      current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      monthly_summary_limit: planId === 'pro' ? 100 : planId === 'enterprise' ? 1000 : 10,
      monthly_summaries_used: 0,
      cancel_at_period_end: false,
      updated_at: new Date().toISOString()
    };

    const { error } = await supabase
      .from('subscriptions')
      .upsert(subscriptionData, { onConflict: 'user_id' });

    if (error) {
      console.error('Error updating subscription:', error);
      throw error;
    }
  devLog.log(`✅ Subscription updated for user ${userId}`);

  } catch (error) {
    console.error('Error handling checkout completion:', error);
    throw error;
  }
}

/**
 * Handle subscription creation
 */
async function handleSubscriptionCreated(subscription: any) {
  try {
    const userId = subscription.metadata?.user_id;
    const planId = subscription.metadata?.plan_id;

    if (!userId) {
      console.error('Missing user_id in subscription metadata:', subscription.id);
      return;
    }
  devLog.log(`📋 Subscription created for user ${userId}`);

    logPaymentSecurityEvent(userId, 'SUBSCRIPTION_CREATED', {
      subscription_id: subscription.id,
      plan_id: planId,
      status: subscription.status
    });

    // Additional subscription setup logic here
    
  } catch (error) {
    console.error('Error handling subscription creation:', error);
    throw error;
  }
}

/**
 * Handle subscription updates
 */
async function handleSubscriptionUpdated(subscription: any) {
  try {
    const userId = subscription.metadata?.user_id;

    if (!userId) {
      console.error('Missing user_id in subscription metadata:', subscription.id);
      return;
    }
  devLog.log(`🔄 Subscription updated for user ${userId}`);

    logPaymentSecurityEvent(userId, 'SUBSCRIPTION_UPDATED', {
      subscription_id: subscription.id,
      status: subscription.status,
      cancel_at_period_end: subscription.cancel_at_period_end
    });

    const supabase = await createSupabaseServerClient();

    // Update subscription status
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: subscription.status,
        cancel_at_period_end: subscription.cancel_at_period_end,
        current_period_start: new Date(subscription.current_period_start * 1000),
        current_period_end: new Date(subscription.current_period_end * 1000),
        updated_at: new Date().toISOString()
      })
      .eq('stripe_subscription_id', subscription.id);

    if (error) {
      console.error('Error updating subscription:', error);
      throw error;
    }

  } catch (error) {
    console.error('Error handling subscription update:', error);
    throw error;
  }
}

/**
 * Handle subscription deletion/cancellation
 */
async function handleSubscriptionDeleted(subscription: any) {
  try {
    const userId = subscription.metadata?.user_id;

    if (!userId) {
      console.error('Missing user_id in subscription metadata:', subscription.id);
      return;
    }
  devLog.log(`❌ Subscription cancelled for user ${userId}`);

    logPaymentSecurityEvent(userId, 'SUBSCRIPTION_CANCELLED', {
      subscription_id: subscription.id,
      cancelled_at: subscription.canceled_at
    });

    const supabase = await createSupabaseServerClient();

    // Update subscription to cancelled
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: 'cancelled',
        cancelled_at: new Date(subscription.canceled_at * 1000),
        updated_at: new Date().toISOString()
      })
      .eq('stripe_subscription_id', subscription.id);

    if (error) {
      console.error('Error updating cancelled subscription:', error);
      throw error;
    }

  } catch (error) {
    console.error('Error handling subscription deletion:', error);
    throw error;
  }
}

/**
 * Handle successful payment
 */
async function handlePaymentSucceeded(invoice: any) {
  try {
    const subscriptionId = invoice.subscription;
    
    if (!subscriptionId) {
  devLog.log('Payment succeeded for non-subscription invoice:', invoice.id);
      return;
    }
  devLog.log(`💰 Payment succeeded for subscription ${subscriptionId}`);

    // Additional payment success logic here
    
  } catch (error) {
    console.error('Error handling payment success:', error);
    throw error;
  }
}

/**
 * Handle failed payment
 */
async function handlePaymentFailed(invoice: any) {
  try {
    const subscriptionId = invoice.subscription;
    
    if (!subscriptionId) {
  devLog.log('Payment failed for non-subscription invoice:', invoice.id);
      return;
    }
  devLog.log(`❌ Payment failed for subscription ${subscriptionId}`);

    // Additional payment failure logic here (e.g., send notification email)
    
  } catch (error) {
    console.error('Error handling payment failure:', error);
    throw error;
  }
}

/**
 * Handle trial ending soon
 */
async function handleTrialWillEnd(subscription: any) {
  try {
    const userId = subscription.metadata?.user_id;

    if (!userId) {
      console.error('Missing user_id in subscription metadata:', subscription.id);
      return;
    }
  devLog.log(`⏰ Trial ending soon for user ${userId}`);

    // Send trial ending notification
    
  } catch (error) {
    console.error('Error handling trial will end:', error);
    throw error;
  }
}
