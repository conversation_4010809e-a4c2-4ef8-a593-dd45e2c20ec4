/**
 * SSO Test API Route
 * Test SSO provider connections
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { testSSOProvider } from '@/lib/sso-config';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * POST /api/sso/test
 * Test SSO provider connection
 */
export async function POST(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // SSO requires Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'SSO testing requires Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const body = await req.json();
      const { provider_id, organization_id } = body;

      if (!provider_id) {
        return NextResponse.json(
          { error: 'Provider ID is required' },
          { status: 400 }
        );
      }

      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const organizationId = organization_id || user.id;

      const result = await testSSOProvider(organizationId, provider_id);

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        test_result: result.test_result,
        message: 'SSO provider test completed',
      });

    } catch (error) {
      console.error('SSO test error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to test SSO provider' },
        { status: 500 }
      );
    }
  });
}
