'use client';

import { useCallback } from 'react';
import { toast } from 'sonner';

interface NotificationOptions {
  type?: 'success' | 'info' | 'warning' | 'error';
  title: string;
  message: string;
  category?: 'summary' | 'upload' | 'export' | 'system' | 'slack';
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  persistent?: boolean;
  sound?: boolean;
  actionUrl?: string;
  actionLabel?: string;
  metadata?: Record<string, any>;
}

export function useNotifications() {
  // Trigger notification through global API or fallback to toast
  const notify = useCallback((options: NotificationOptions) => {
    const { type = 'info', title, message, ...rest } = options;

    // Try to use global notification system first
    if (typeof window !== 'undefined' && (window as any).triggerNotification) {
      (window as any).triggerNotification({
        type,
        title,
        message,
        ...rest
      });
      return;
    }

    // Fallback to simple toast
    const toastOptions = {
      description: message,
      duration: options.persistent ? Infinity : 5000,
      action: options.actionUrl ? {
        label: options.actionLabel || 'View',
        onClick: () => window.open(options.actionUrl, '_blank')
      } : undefined
    };

    switch (type) {
      case 'success':
        toast.success(title, toastOptions);
        break;
      case 'error':
        toast.error(title, toastOptions);
        break;
      case 'warning':
        toast.warning(title, toastOptions);
        break;
      default:
        toast.info(title, toastOptions);
    }
  }, []);

  // Convenience methods for common notification types
  const notifySuccess = useCallback((title: string, message: string, options?: Partial<NotificationOptions>) => {
    notify({ type: 'success', title, message, ...options });
  }, [notify]);

  const notifyError = useCallback((title: string, message: string, options?: Partial<NotificationOptions>) => {
    notify({ type: 'error', title, message, ...options });
  }, [notify]);

  const notifyWarning = useCallback((title: string, message: string, options?: Partial<NotificationOptions>) => {
    notify({ type: 'warning', title, message, ...options });
  }, [notify]);

  const notifyInfo = useCallback((title: string, message: string, options?: Partial<NotificationOptions>) => {
    notify({ type: 'info', title, message, ...options });
  }, [notify]);

  // Specific notification types for common use cases
  const notifySummaryComplete = useCallback((summaryId: string, fileName: string) => {
    notify({
      type: 'success',
      title: 'Summary Complete',
      message: `Your summary for "${fileName}" is ready to view.`,
      category: 'summary',
      priority: 'high',
      sound: true,
      actionUrl: `/dashboard/summaries/${summaryId}`,
      actionLabel: 'View Summary',
      metadata: { summaryId, fileName }
    });
  }, [notify]);

  const notifyUploadProgress = useCallback((fileName: string, progress: number) => {
    if (progress === 100) {
      notify({
        type: 'success',
        title: 'Upload Complete',
        message: `"${fileName}" has been uploaded successfully.`,
        category: 'upload',
        priority: 'normal',
        metadata: { fileName, progress }
      });
    } else {
      notify({
        type: 'info',
        title: 'Upload Progress',
        message: `Uploading "${fileName}" - ${progress}% complete`,
        category: 'upload',
        priority: 'low',
        sound: false,
        metadata: { fileName, progress }
      });
    }
  }, [notify]);

  const notifyExportReady = useCallback((exportType: string, downloadUrl: string) => {
    notify({
      type: 'success',
      title: 'Export Ready',
      message: `Your ${exportType} export is ready for download.`,
      category: 'export',
      priority: 'high',
      sound: true,
      actionUrl: downloadUrl,
      actionLabel: 'Download',
      metadata: { exportType, downloadUrl }
    });
  }, [notify]);

  const notifySlackIntegration = useCallback((action: string, success: boolean, details?: string) => {
    notify({
      type: success ? 'success' : 'error',
      title: `Slack ${action}`,
      message: success 
        ? `Slack ${action.toLowerCase()} completed successfully.`
        : `Failed to ${action.toLowerCase()} with Slack. ${details || ''}`,
      category: 'slack',
      priority: success ? 'normal' : 'high',
      sound: !success,
      metadata: { action, success, details }
    });
  }, [notify]);

  const notifySystemUpdate = useCallback((title: string, message: string, urgent = false) => {
    notify({
      type: urgent ? 'warning' : 'info',
      title,
      message,
      category: 'system',
      priority: urgent ? 'urgent' : 'normal',
      persistent: urgent,
      sound: urgent,
      metadata: { urgent }
    });
  }, [notify]);

  const notifyError500 = useCallback((operation: string, errorCode?: string) => {
    notify({
      type: 'error',
      title: 'Server Error',
      message: `Failed to ${operation}. Please try again or contact support if the issue persists.`,
      category: 'system',
      priority: 'high',
      persistent: true,
      sound: true,
      metadata: { operation, errorCode }
    });
  }, [notify]);

  const notifyNetworkError = useCallback((operation: string) => {
    notify({
      type: 'warning',
      title: 'Connection Issue',
      message: `Unable to ${operation} due to network connectivity. Please check your connection and try again.`,
      category: 'system',
      priority: 'high',
      sound: true,
      metadata: { operation }
    });
  }, [notify]);

  return {
    notify,
    notifySuccess,
    notifyError,
    notifyWarning,
    notifyInfo,
    notifySummaryComplete,
    notifyUploadProgress,
    notifyExportReady,
    notifySlackIntegration,
    notifySystemUpdate,
    notifyError500,
    notifyNetworkError
  };
}

// Global notification functions for use outside React components
export const globalNotifications = {
  success: (title: string, message: string) => {
    if (typeof window !== 'undefined' && (window as any).triggerNotification) {
      (window as any).triggerNotification({ type: 'success', title, message });
    } else {
      toast.success(title, { description: message });
    }
  },
  error: (title: string, message: string) => {
    if (typeof window !== 'undefined' && (window as any).triggerNotification) {
      (window as any).triggerNotification({ type: 'error', title, message });
    } else {
      toast.error(title, { description: message });
    }
  },
  warning: (title: string, message: string) => {
    if (typeof window !== 'undefined' && (window as any).triggerNotification) {
      (window as any).triggerNotification({ type: 'warning', title, message });
    } else {
      toast.warning(title, { description: message });
    }
  },
  info: (title: string, message: string) => {
    if (typeof window !== 'undefined' && (window as any).triggerNotification) {
      (window as any).triggerNotification({ type: 'info', title, message });
    } else {
      toast.info(title, { description: message });
    }
  }
};
