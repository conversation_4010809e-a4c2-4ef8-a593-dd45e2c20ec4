#!/usr/bin/env tsx

/**
 * Comprehensive Route Validation Script
 * 
 * Tests all authentication routes and provides a complete status report
 * for the Slack Summary Scribe SaaS authentication system.
 */

import { config } from 'dotenv'
import fetch from 'node-fetch'

// Load environment variables
config({ path: '.env.local' })

interface RouteTest {
  name: string
  path: string
  expectedStatus: number
  description: string
  category: 'test' | 'api' | 'protected' | 'public'
}

interface TestResult {
  route: RouteTest
  status: 'PASS' | 'FAIL' | 'WARNING'
  actualStatus: number
  responseTime: number
  message: string
}

const routes: RouteTest[] = [
  // Test Pages
  { name: 'Debug Auth', path: '/debug-auth', expectedStatus: 200, description: 'OAuth configuration debugging', category: 'test' },
  { name: 'Test Session', path: '/test-session', expectedStatus: 200, description: 'Session persistence testing', category: 'test' },
  { name: 'Test OAuth Flow', path: '/test-oauth-flow', expectedStatus: 200, description: 'OAuth flow testing', category: 'test' },
  { name: 'Test Manual Session', path: '/test-manual-session', expectedStatus: 200, description: 'Email/password authentication', category: 'test' },
  { name: 'Test Sync', path: '/test-sync', expectedStatus: 200, description: 'Client-server synchronization', category: 'test' },
  { name: 'Test E2E Auth', path: '/test-e2e-auth', expectedStatus: 200, description: 'End-to-end authentication', category: 'test' },
  { name: 'Test Cookie Management', path: '/test-cookie-management', expectedStatus: 200, description: 'Cookie management testing', category: 'test' },
  
  // API Endpoints
  { name: 'Auth Test API', path: '/api/auth/test', expectedStatus: 200, description: 'OAuth configuration verification', category: 'api' },
  { name: 'Session API', path: '/api/auth/session', expectedStatus: 200, description: 'Server-side session checking', category: 'api' },
  { name: 'OAuth Callback', path: '/api/auth/callback', expectedStatus: 307, description: 'OAuth callback handling', category: 'api' },
  
  // Public Pages
  { name: 'Login Page', path: '/login', expectedStatus: 200, description: 'Login page', category: 'public' },
  { name: 'Home Page', path: '/', expectedStatus: 200, description: 'Landing page', category: 'public' },
  
  // Protected Pages (should redirect when not authenticated)
  { name: 'Dashboard', path: '/dashboard', expectedStatus: 307, description: 'Protected dashboard (should redirect)', category: 'protected' },
]

async function testRoute(route: RouteTest, baseUrl: string): Promise<TestResult> {
  const startTime = Date.now()
  
  try {
    const response = await fetch(`${baseUrl}${route.path}`, {
      method: 'GET',
      redirect: 'manual', // Don't follow redirects automatically
      headers: {
        'User-Agent': 'Route-Validator/1.0'
      }
    })
    
    const responseTime = Date.now() - startTime
    const actualStatus = response.status
    
    let status: 'PASS' | 'FAIL' | 'WARNING' = 'PASS'
    let message = `Status: ${actualStatus}, Time: ${responseTime}ms`
    
    if (actualStatus !== route.expectedStatus) {
      if (route.category === 'protected' && (actualStatus === 302 || actualStatus === 307)) {
        // Protected routes can redirect with either 302 or 307
        status = 'PASS'
        message = `Redirect (${actualStatus}) - Expected for protected route`
      } else if (route.category === 'api' && route.path === '/api/auth/callback' && actualStatus === 302) {
        // OAuth callback can redirect with 302 or 307
        status = 'PASS'
        message = `Redirect (${actualStatus}) - Expected for OAuth callback`
      } else {
        status = 'FAIL'
        message = `Expected ${route.expectedStatus}, got ${actualStatus}`
      }
    }
    
    if (responseTime > 5000) {
      status = status === 'PASS' ? 'WARNING' : status
      message += ' (Slow response)'
    }
    
    return {
      route,
      status,
      actualStatus,
      responseTime,
      message
    }
    
  } catch (error: any) {
    const responseTime = Date.now() - startTime
    
    return {
      route,
      status: 'FAIL',
      actualStatus: 0,
      responseTime,
      message: `Error: ${error.message}`
    }
  }
}

async function validateAllRoutes() {
  console.log('🧪 Comprehensive Route Validation')
  console.log('=' .repeat(60))
  
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  console.log(`Base URL: ${baseUrl}`)
  console.log(`Testing ${routes.length} routes...\n`)
  
  const results: TestResult[] = []
  
  // Test all routes
  for (const route of routes) {
    process.stdout.write(`Testing ${route.name}... `)
    const result = await testRoute(route, baseUrl)
    results.push(result)
    
    const icon = result.status === 'PASS' ? '✅' : result.status === 'WARNING' ? '⚠️' : '❌'
    console.log(`${icon} ${result.message}`)
  }
  
  console.log('\n📊 Results Summary')
  console.log('=' .repeat(60))
  
  // Group results by category
  const categories = ['test', 'api', 'public', 'protected'] as const
  
  for (const category of categories) {
    const categoryResults = results.filter(r => r.route.category === category)
    if (categoryResults.length === 0) continue
    
    const passed = categoryResults.filter(r => r.status === 'PASS').length
    const warnings = categoryResults.filter(r => r.status === 'WARNING').length
    const failed = categoryResults.filter(r => r.status === 'FAIL').length
    
    console.log(`\n📋 ${category.toUpperCase()} Routes:`)
    console.log(`   ✅ Passed: ${passed}`)
    console.log(`   ⚠️  Warnings: ${warnings}`)
    console.log(`   ❌ Failed: ${failed}`)
    
    // Show failed routes
    const failedRoutes = categoryResults.filter(r => r.status === 'FAIL')
    if (failedRoutes.length > 0) {
      console.log('   Failed routes:')
      failedRoutes.forEach(result => {
        console.log(`     - ${result.route.name}: ${result.message}`)
      })
    }
  }
  
  // Overall statistics
  const totalPassed = results.filter(r => r.status === 'PASS').length
  const totalWarnings = results.filter(r => r.status === 'WARNING').length
  const totalFailed = results.filter(r => r.status === 'FAIL').length
  
  console.log('\n🎯 Overall Results:')
  console.log(`   ✅ Total Passed: ${totalPassed}/${routes.length}`)
  console.log(`   ⚠️  Total Warnings: ${totalWarnings}/${routes.length}`)
  console.log(`   ❌ Total Failed: ${totalFailed}/${routes.length}`)
  
  // Performance analysis
  const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length
  const slowRoutes = results.filter(r => r.responseTime > 2000)
  
  console.log('\n⚡ Performance Analysis:')
  console.log(`   Average response time: ${avgResponseTime.toFixed(0)}ms`)
  if (slowRoutes.length > 0) {
    console.log(`   Slow routes (>2s): ${slowRoutes.length}`)
    slowRoutes.forEach(result => {
      console.log(`     - ${result.route.name}: ${result.responseTime}ms`)
    })
  }
  
  // Authentication system status
  console.log('\n🔐 Authentication System Status:')
  
  const testRoutes = results.filter(r => r.route.category === 'test')
  const apiRoutes = results.filter(r => r.route.category === 'api')
  const protectedRoutes = results.filter(r => r.route.category === 'protected')
  
  const testRoutesWorking = testRoutes.every(r => r.status === 'PASS')
  const apiRoutesWorking = apiRoutes.every(r => r.status === 'PASS')
  const protectedRoutesWorking = protectedRoutes.every(r => r.status === 'PASS')
  
  console.log(`   Test Framework: ${testRoutesWorking ? '✅ Working' : '❌ Issues'}`)
  console.log(`   API Endpoints: ${apiRoutesWorking ? '✅ Working' : '❌ Issues'}`)
  console.log(`   Route Protection: ${protectedRoutesWorking ? '✅ Working' : '❌ Issues'}`)
  
  // Next steps
  console.log('\n🚀 Next Steps:')
  
  if (totalFailed === 0) {
    console.log('   ✅ All routes are working correctly!')
    console.log('   🔧 Configure Supabase OAuth settings to enable authentication')
    console.log('   🧪 Test OAuth flow with /test-oauth-flow')
    console.log('   📊 Validate dashboard access after authentication')
  } else {
    console.log('   ❌ Fix failed routes before proceeding')
    console.log('   🔍 Check server logs for detailed error information')
    console.log('   🔧 Verify environment variables and configuration')
  }
  
  console.log('\n✅ Route validation completed!')
  
  return {
    totalRoutes: routes.length,
    passed: totalPassed,
    warnings: totalWarnings,
    failed: totalFailed,
    avgResponseTime,
    allWorking: totalFailed === 0
  }
}

// Run validation
validateAllRoutes().catch(console.error)
