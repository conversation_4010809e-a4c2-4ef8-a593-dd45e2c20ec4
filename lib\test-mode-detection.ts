import { devLog } from '@/lib/console-cleaner';
/**
 * Test Mode Detection Utility
 * Prevents Stripe checkout blocks during development and testing
 */

export interface TestModeConfig {
  isTestMode: boolean;
  isDevelopment: boolean;
  isProduction: boolean;
  stripeTestMode: boolean;
  allowCheckout: boolean;
  environment: 'development' | 'production' | 'test';
}

/**
 * Detect current environment and test mode status
 */
export function getTestModeConfig(): TestModeConfig {
  const nodeEnv = process.env.NODE_ENV || 'development';
  const stripeTestMode = process.env.NEXT_PUBLIC_STRIPE_TEST_MODE === 'true';
  const devMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true';
  
  const isDevelopment = nodeEnv === 'development' || devMode;
  const isProduction = nodeEnv === 'production' && !devMode;
  const isTestMode = stripeTestMode || isDevelopment;
  
  // Allow checkout in production or when explicitly enabled in test mode
  const allowCheckout = isProduction || (isTestMode && stripeTestMode);
  
  return {
    isTestMode,
    isDevelopment,
    isProduction,
    stripeTestMode,
    allowCheckout,
    environment: isDevelopment ? 'development' : isProduction ? 'production' : 'test',
  };
}

/**
 * Check if Stripe operations should be allowed
 */
export function shouldAllowStripeOperations(): boolean {
  const config = getTestModeConfig();
  return config.allowCheckout;
}

/**
 * Get appropriate Stripe configuration based on environment
 */
export function getStripeConfig() {
  const config = getTestModeConfig();
  
  return {
    publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '',
    secretKey: process.env.STRIPE_SECRET_KEY || '',
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
    testMode: config.stripeTestMode,
    environment: config.environment,
    allowOperations: config.allowCheckout,
  };
}

/**
 * Display test mode warning in development
 */
export function logTestModeStatus(): void {
  if (typeof window === 'undefined') return;
  
  const config = getTestModeConfig();
  
  if (config.isDevelopment) {
  devLog.log('🔧 Development Mode Active');
  devLog.log('- CSP headers disabled');
  devLog.log('- All integrations enabled for testing');
  devLog.log('- Stripe test mode:', config.stripeTestMode);
  }
  
  if (config.isTestMode && !config.isDevelopment) {
  devLog.log('🧪 Test Mode Active');
  devLog.log('- Stripe operations:', config.allowCheckout ? 'enabled' : 'disabled');
  }
  
  if (config.isProduction) {
  devLog.log('🚀 Production Mode Active');
  devLog.log('- CSP headers enabled');
  devLog.log('- All security measures active');
  }
}

/**
 * Client-side test mode detection hook
 */
export function useTestModeConfig() {
  if (typeof window === 'undefined') {
    return getTestModeConfig();
  }
  
  // Client-side detection
  const config = getTestModeConfig();
  
  // Log status on first load
  if (typeof window !== 'undefined' && !window.__test_mode_logged__) {
    logTestModeStatus();
    (window as any).__test_mode_logged__ = true;
  }
  
  return config;
}

/**
 * Validate environment configuration
 */
export function validateEnvironmentConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const config = getTestModeConfig();
  
  // Check required environment variables
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
    errors.push('NEXT_PUBLIC_SUPABASE_URL is required');
  }
  
  if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY is required');
  }
  
  if (!process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY) {
    errors.push('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY is required');
  }
  
  if (!process.env.CLERK_SECRET_KEY) {
    errors.push('CLERK_SECRET_KEY is required');
  }
  
  // Check Stripe configuration
  if (config.allowCheckout) {
    if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
      errors.push('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is required for Stripe operations');
    }
    
    if (!process.env.STRIPE_SECRET_KEY) {
      errors.push('STRIPE_SECRET_KEY is required for Stripe operations');
    }
  }
  
  // Check Slack configuration
  if (!process.env.NEXT_PUBLIC_SLACK_CLIENT_ID) {
    warnings.push('NEXT_PUBLIC_SLACK_CLIENT_ID is missing - Slack integration will not work');
  }
  
  if (!process.env.SLACK_CLIENT_SECRET) {
    warnings.push('SLACK_CLIENT_SECRET is missing - Slack OAuth will not work');
  }
  
  // Check monitoring configuration
  if (config.isProduction) {
    if (!process.env.NEXT_PUBLIC_POSTHOG_KEY) {
      warnings.push('NEXT_PUBLIC_POSTHOG_KEY is missing - Analytics will not work');
    }
    
    if (!process.env.NEXT_PUBLIC_SENTRY_DSN) {
      warnings.push('NEXT_PUBLIC_SENTRY_DSN is missing - Error monitoring will not work');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Environment-specific redirect URLs for Slack OAuth
 */
export function getSlackRedirectUrls(): string[] {
  const config = getTestModeConfig();
  const baseUrls: string[] = [];
  
  if (config.isDevelopment) {
    baseUrls.push(
      'http://localhost:3000/auth/slack/callback',
      'http://localhost:3001/auth/slack/callback'
    );
  }
  
  if (config.isProduction) {
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_APP_URL;
    if (siteUrl) {
      baseUrls.push(`${siteUrl.replace(/\/$/, '')}/auth/slack/callback`);
    }
  }
  
  return baseUrls;
}
