/**
 * Role-Based Access Control (RBAC) System
 * 
 * Comprehensive permission management for team features
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';
import { 
  TeamRole, 
  Permission, 
  DEFAULT_ROLE_PERMISSIONS, 
  ROLE_HIERARCHY,
  TeamMember 
} from './types';

/**
 * Check if a user has a specific permission in an organization
 */
export async function checkPermission(
  userId: string,
  organizationId: string,
  permission: Permission
): Promise<boolean> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get user's role and permissions in the organization
    const { data: membership, error } = await supabase
      .from('user_organizations')
      .select('role, permissions')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .single();

    if (error || !membership) {
      return false;
    }

    // Check custom permissions first
    if (membership.permissions && membership.permissions[permission] !== undefined) {
      return membership.permissions[permission];
    }

    // Check default role permissions
    const rolePermissions = DEFAULT_ROLE_PERMISSIONS[membership.role as TeamRole] || [];
    return rolePermissions.includes(permission);

  } catch (error) {
    console.error('Permission check failed:', error);
    return false;
  }
}

/**
 * Check if a user has any of the specified permissions
 */
export async function checkAnyPermission(
  userId: string,
  organizationId: string,
  permissions: Permission[]
): Promise<boolean> {
  for (const permission of permissions) {
    if (await checkPermission(userId, organizationId, permission)) {
      return true;
    }
  }
  return false;
}

/**
 * Check if a user has all of the specified permissions
 */
export async function checkAllPermissions(
  userId: string,
  organizationId: string,
  permissions: Permission[]
): Promise<boolean> {
  for (const permission of permissions) {
    if (!(await checkPermission(userId, organizationId, permission))) {
      return false;
    }
  }
  return true;
}

/**
 * Get all permissions for a user in an organization
 */
export async function getUserPermissions(
  userId: string,
  organizationId: string
): Promise<Permission[]> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { data: membership, error } = await supabase
      .from('user_organizations')
      .select('role, permissions')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .single();

    if (error || !membership) {
      return [];
    }

    // Start with default role permissions
    const rolePermissions = DEFAULT_ROLE_PERMISSIONS[membership.role as TeamRole] || [];
    const permissions = new Set(rolePermissions);

    // Apply custom permissions
    if (membership.permissions) {
      Object.entries(membership.permissions).forEach(([permission, granted]) => {
        if (granted) {
          permissions.add(permission as Permission);
        } else {
          permissions.delete(permission as Permission);
        }
      });
    }

    return Array.from(permissions);

  } catch (error) {
    console.error('Failed to get user permissions:', error);
    return [];
  }
}

/**
 * Check if a role can perform an action on another role
 */
export function canManageRole(managerRole: TeamRole, targetRole: TeamRole): boolean {
  const managerLevel = ROLE_HIERARCHY[managerRole];
  const targetLevel = ROLE_HIERARCHY[targetRole];
  
  // Owners can manage everyone except other owners
  if (managerRole === 'owner') {
    return targetRole !== 'owner';
  }
  
  // Admins can manage editors and viewers
  if (managerRole === 'admin') {
    return targetLevel < ROLE_HIERARCHY.admin;
  }
  
  // Editors and viewers cannot manage roles
  return false;
}

/**
 * Get the highest role a user can assign
 */
export function getMaxAssignableRole(userRole: TeamRole): TeamRole {
  switch (userRole) {
    case 'owner':
      return 'admin'; // Owners can assign up to admin
    case 'admin':
      return 'editor'; // Admins can assign up to editor
    default:
      return 'viewer'; // Others cannot assign roles
  }
}

/**
 * Validate role change request
 */
export async function validateRoleChange(
  requesterId: string,
  organizationId: string,
  targetUserId: string,
  newRole: TeamRole
): Promise<{ valid: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get requester's role
    const { data: requesterMembership } = await supabase
      .from('user_organizations')
      .select('role')
      .eq('user_id', requesterId)
      .eq('organization_id', organizationId)
      .single();

    if (!requesterMembership) {
      return { valid: false, error: 'Requester is not a member of this organization' };
    }

    // Get target user's current role
    const { data: targetMembership } = await supabase
      .from('user_organizations')
      .select('role')
      .eq('user_id', targetUserId)
      .eq('organization_id', organizationId)
      .single();

    if (!targetMembership) {
      return { valid: false, error: 'Target user is not a member of this organization' };
    }

    const requesterRole = requesterMembership.role as TeamRole;
    const currentTargetRole = targetMembership.role as TeamRole;

    // Check if requester can manage the target's current role
    if (!canManageRole(requesterRole, currentTargetRole)) {
      return { valid: false, error: 'Insufficient permissions to change this user\'s role' };
    }

    // Check if requester can assign the new role
    const maxAssignable = getMaxAssignableRole(requesterRole);
    if (ROLE_HIERARCHY[newRole] > ROLE_HIERARCHY[maxAssignable]) {
      return { valid: false, error: `Cannot assign role higher than ${maxAssignable}` };
    }

    // Prevent self-demotion for owners
    if (requesterId === targetUserId && requesterRole === 'owner' && newRole !== 'owner') {
      return { valid: false, error: 'Owners cannot demote themselves' };
    }

    return { valid: true };

  } catch (error) {
    console.error('Role change validation failed:', error);
    return { valid: false, error: 'Validation failed' };
  }
}

/**
 * Grant custom permission to a user
 */
export async function grantPermission(
  userId: string,
  organizationId: string,
  permission: Permission,
  grantedBy: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Check if granter has permission to grant this permission
    const canGrant = await checkPermission(grantedBy, organizationId, 'members:edit_roles');
    if (!canGrant) {
      return { success: false, error: 'Insufficient permissions to grant permissions' };
    }

    const supabase = await createSupabaseServerClient();
    
    // Get current permissions
    const { data: membership } = await supabase
      .from('user_organizations')
      .select('permissions')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .single();

    if (!membership) {
      return { success: false, error: 'User is not a member of this organization' };
    }

    const currentPermissions = membership.permissions || {};
    const updatedPermissions = {
      ...currentPermissions,
      [permission]: true
    };

    // Update permissions
    const { error } = await supabase
      .from('user_organizations')
      .update({ 
        permissions: updatedPermissions,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('organization_id', organizationId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };

  } catch (error) {
    console.error('Failed to grant permission:', error);
    return { success: false, error: 'Failed to grant permission' };
  }
}

/**
 * Revoke custom permission from a user
 */
export async function revokePermission(
  userId: string,
  organizationId: string,
  permission: Permission,
  revokedBy: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Check if revoker has permission to revoke this permission
    const canRevoke = await checkPermission(revokedBy, organizationId, 'members:edit_roles');
    if (!canRevoke) {
      return { success: false, error: 'Insufficient permissions to revoke permissions' };
    }

    const supabase = await createSupabaseServerClient();
    
    // Get current permissions
    const { data: membership } = await supabase
      .from('user_organizations')
      .select('permissions')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .single();

    if (!membership) {
      return { success: false, error: 'User is not a member of this organization' };
    }

    const currentPermissions = membership.permissions || {};
    const updatedPermissions = {
      ...currentPermissions,
      [permission]: false
    };

    // Update permissions
    const { error } = await supabase
      .from('user_organizations')
      .update({ 
        permissions: updatedPermissions,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('organization_id', organizationId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };

  } catch (error) {
    console.error('Failed to revoke permission:', error);
    return { success: false, error: 'Failed to revoke permission' };
  }
}

/**
 * Check if user is organization owner
 */
export async function isOrganizationOwner(
  userId: string,
  organizationId: string
): Promise<boolean> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { data: membership } = await supabase
      .from('user_organizations')
      .select('role')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .single();

    return membership?.role === 'owner';

  } catch (error) {
    console.error('Failed to check owner status:', error);
    return false;
  }
}

/**
 * Get user's role in organization
 */
export async function getUserRole(
  userId: string,
  organizationId: string
): Promise<TeamRole | null> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { data: membership } = await supabase
      .from('user_organizations')
      .select('role')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .single();

    return membership?.role as TeamRole || null;

  } catch (error) {
    console.error('Failed to get user role:', error);
    return null;
  }
}

/**
 * Permission middleware for API routes
 */
export function requirePermission(permission: Permission) {
  return async (userId: string, organizationId: string): Promise<boolean> => {
    return await checkPermission(userId, organizationId, permission);
  };
}

/**
 * Role middleware for API routes
 */
export function requireRole(requiredRole: TeamRole) {
  return async (userId: string, organizationId: string): Promise<boolean> => {
    const userRole = await getUserRole(userId, organizationId);
    if (!userRole) return false;

    return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole];
  };
}
