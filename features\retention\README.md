# 🔁 User Retention & Engagement Features

## Overview
Comprehensive user retention system designed to increase engagement, reduce churn, and drive long-term user success through automated workflows, personalized experiences, and proactive engagement.

## Features Implemented

### 1. Scheduled Summaries
- **Automated Daily Summaries**: Cron-triggered daily workspace summaries
- **Weekly Digest**: Comprehensive weekly team activity reports
- **Custom Schedules**: User-defined summary frequencies and timing
- **Smart Scheduling**: AI-optimized timing based on user activity patterns

### 2. Re-engagement Campaigns
- **Inactivity Detection**: Identify users inactive for 7, 14, 30 days
- **Progressive Email Campaigns**: Escalating re-engagement email sequences
- **Personalized Content**: Tailored messages based on user behavior and preferences
- **Win-back Offers**: Special incentives for churned or at-risk users

### 3. Onboarding Enhancement
- **Interactive Tours**: Step-by-step guided tours using React-tour
- **Progress Tracking**: Monitor onboarding completion rates
- **Demo Mode**: Showcase features with sample data for new users
- **Achievement System**: Gamified onboarding with progress milestones

### 4. Referral System
- **Affiliate Tracking**: Unique referral codes and attribution
- **Credit System**: Reward credits for successful referrals
- **Social Sharing**: Easy sharing tools for referral links
- **Leaderboards**: Gamified referral competitions

### 5. Engagement Loops
- **Habit Formation**: Daily/weekly engagement triggers
- **Social Features**: Team collaboration and sharing
- **Notifications**: Smart, non-intrusive engagement reminders
- **Feedback Loops**: Continuous improvement based on user feedback

## File Structure
```
/features/retention/
├── README.md                    # This file
├── types.ts                    # Retention system types
├── scheduled-summaries.ts      # Automated summary generation
├── re-engagement.ts            # User re-engagement campaigns
├── onboarding.ts              # Enhanced onboarding flows
├── referrals.ts               # Referral and affiliate system
├── notifications.ts           # Smart notification system
├── engagement-tracking.ts     # User engagement analytics
└── components/
    ├── OnboardingTour.tsx     # Interactive onboarding tour
    ├── ReferralDashboard.tsx  # Referral management interface
    ├── NotificationCenter.tsx # Notification management
    ├── EngagementMetrics.tsx  # Engagement analytics display
    └── ScheduleManager.tsx    # Summary scheduling interface
```

## Database Schema
```sql
-- Scheduled summaries table
CREATE TABLE scheduled_summaries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  schedule_type TEXT NOT NULL CHECK (schedule_type IN ('daily', 'weekly', 'custom')),
  frequency JSONB NOT NULL, -- Cron-like schedule definition
  workspace_filters JSONB DEFAULT '{}',
  last_run_at TIMESTAMP WITH TIME ZONE,
  next_run_at TIMESTAMP WITH TIME ZONE NOT NULL,
  enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User engagement tracking
CREATE TABLE user_engagement (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id),
  last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  total_sessions INTEGER DEFAULT 0,
  total_time_spent INTEGER DEFAULT 0, -- in seconds
  features_used JSONB DEFAULT '{}',
  onboarding_completed BOOLEAN DEFAULT false,
  onboarding_step INTEGER DEFAULT 0,
  engagement_score NUMERIC DEFAULT 0,
  risk_level TEXT DEFAULT 'low' CHECK (risk_level IN ('low', 'medium', 'high')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Re-engagement campaigns
CREATE TABLE re_engagement_campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  campaign_type TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('scheduled', 'sent', 'opened', 'clicked', 'converted')),
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE,
  opened_at TIMESTAMP WITH TIME ZONE,
  clicked_at TIMESTAMP WITH TIME ZONE,
  converted_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Referral system
CREATE TABLE referrals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  referrer_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  referee_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  referral_code TEXT UNIQUE NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'credited')),
  credits_awarded INTEGER DEFAULT 0,
  conversion_value NUMERIC DEFAULT 0,
  referred_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  converted_at TIMESTAMP WITH TIME ZONE,
  credited_at TIMESTAMP WITH TIME ZONE
);

-- Notification preferences
CREATE TABLE notification_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email_notifications JSONB DEFAULT '{}',
  push_notifications JSONB DEFAULT '{}',
  slack_notifications JSONB DEFAULT '{}',
  frequency_settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Environment Variables
```bash
# Retention Configuration
RETENTION_ENABLED=true
ONBOARDING_TOUR_ENABLED=true
REFERRAL_SYSTEM_ENABLED=true
RE_ENGAGEMENT_ENABLED=true

# Email Configuration
RE_ENGAGEMENT_EMAIL_TEMPLATE=re-engagement
WEEKLY_DIGEST_EMAIL_TEMPLATE=weekly-digest
REFERRAL_EMAIL_TEMPLATE=referral-invite

# Scheduling
SUMMARY_GENERATION_CRON=0 9 * * *
ENGAGEMENT_CHECK_CRON=0 10 * * *
CLEANUP_CRON=0 2 * * 0

# Referral Settings
REFERRAL_CREDIT_AMOUNT=10
REFERRAL_CONVERSION_THRESHOLD=30
MAX_REFERRAL_CREDITS=100
```

## Usage Examples

### Schedule Daily Summary
```typescript
import { scheduleRecurringSummary } from '@/features/retention/scheduled-summaries';

await scheduleRecurringSummary({
  userId,
  organizationId,
  scheduleType: 'daily',
  frequency: { hour: 9, minute: 0 },
  workspaceFilters: { channels: ['general', 'dev'] }
});
```

### Track User Engagement
```typescript
import { updateEngagement } from '@/features/retention/engagement-tracking';

await updateEngagement(userId, {
  sessionDuration: 1800, // 30 minutes
  featuresUsed: ['summary_creation', 'export'],
  engagementScore: 85
});
```

### Create Referral Code
```typescript
import { createReferralCode } from '@/features/retention/referrals';

const referralCode = await createReferralCode(userId);
// Returns: "SLACK-SUMMARY-ABC123"
```

### Send Re-engagement Email
```typescript
import { triggerReEngagement } from '@/features/retention/re-engagement';

await triggerReEngagement(userId, {
  campaignType: 'inactive_7_days',
  personalizedContent: {
    lastFeatureUsed: 'summary_creation',
    teamName: 'Engineering Team'
  }
});
```

## API Endpoints
- `GET /api/retention/engagement` - Get user engagement metrics
- `POST /api/retention/schedule` - Schedule recurring summaries
- `GET /api/retention/referrals` - Get referral dashboard data
- `POST /api/retention/referrals/create` - Create new referral code
- `PUT /api/retention/notifications` - Update notification preferences
- `POST /api/retention/onboarding/complete` - Mark onboarding step complete
- `GET /api/retention/analytics` - Get retention analytics

## Retention Strategies

### 1. Onboarding Optimization
- **First 5 Minutes**: Quick value demonstration
- **First Session**: Complete one full workflow
- **First Week**: Establish daily usage habit
- **First Month**: Achieve team collaboration milestone

### 2. Engagement Triggers
- **Daily**: Morning summary notifications
- **Weekly**: Team performance insights
- **Monthly**: Usage analytics and optimization tips
- **Quarterly**: Feature updates and roadmap sharing

### 3. Churn Prevention
- **Early Warning**: Engagement score drops below 50
- **Intervention**: Personalized outreach and support
- **Win-back**: Special offers and feature highlights
- **Exit Survey**: Feedback collection for improvements

### 4. Growth Loops
- **Viral**: Referral incentives and social sharing
- **Content**: Valuable insights drive organic sharing
- **Network**: Team collaboration increases stickiness
- **Habit**: Daily workflows become indispensable

## Success Metrics
- **Day 1 Retention**: 70%+ users return next day
- **Week 1 Retention**: 40%+ users active in first week
- **Month 1 Retention**: 25%+ users active after 30 days
- **Engagement Score**: Average 75+ across active users
- **Referral Rate**: 15%+ of users make referrals
- **Onboarding Completion**: 80%+ complete core workflow

## Best Practices
1. **Personalization**: Tailor experiences to user behavior and preferences
2. **Timing**: Send communications when users are most likely to engage
3. **Value First**: Always lead with user value, not company needs
4. **Progressive Disclosure**: Gradually introduce advanced features
5. **Feedback Loops**: Continuously optimize based on user response data
