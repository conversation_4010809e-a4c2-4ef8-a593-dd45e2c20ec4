/**
 * Test OpenRouter API directly
 */

const testOpenRouter = async () => {
  const apiKey = '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';
  
  try {
    console.log('🧪 Testing OpenRouter API...');
    
    // Test 1: Check available models
    console.log('📋 Checking available models...');
    const modelsResponse = await fetch('https://openrouter.ai/api/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Slack Summary Scribe'
      }
    });
    
    if (modelsResponse.ok) {
      const models = await modelsResponse.json();
      console.log('✅ Models API working. Available models:', models.data?.length || 0);
      
      // Find DeepSeek model
      const deepseekModel = models.data?.find(m => m.id.includes('deepseek'));
      if (deepseekModel) {
        console.log('✅ DeepSeek model found:', deepseekModel.id);
      } else {
        console.log('⚠️ DeepSeek model not found');
      }
    } else {
      console.log('❌ Models API failed:', modelsResponse.status, modelsResponse.statusText);
      return;
    }
    
    // Test 2: Simple completion
    console.log('🤖 Testing AI completion...');
    const completionResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Slack Summary Scribe'
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-r1:free',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that summarizes text.'
          },
          {
            role: 'user',
            content: 'Summarize this: John said hello to Sarah. Sarah replied with a greeting. They discussed the weather.'
          }
        ],
        max_tokens: 100,
        temperature: 0.3
      })
    });
    
    if (completionResponse.ok) {
      const completion = await completionResponse.json();
      console.log('✅ AI Completion successful');
      console.log('📝 Response:', completion.choices?.[0]?.message?.content || 'No content');
    } else {
      const error = await completionResponse.text();
      console.log('❌ AI Completion failed:', completionResponse.status, error);
    }
    
  } catch (error) {
    console.log('❌ OpenRouter Test ERROR');
    console.error('Error:', error);
  }
};

// Run the test
testOpenRouter();
