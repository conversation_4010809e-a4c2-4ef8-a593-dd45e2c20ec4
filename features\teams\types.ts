/**
 * Team Management Types
 * 
 * TypeScript interfaces and types for team management system
 */

export type TeamRole = 'owner' | 'admin' | 'editor' | 'viewer';

export type Permission = 
  | 'billing:manage'
  | 'members:invite'
  | 'members:remove'
  | 'members:edit_roles'
  | 'workspaces:share'
  | 'workspaces:manage'
  | 'summaries:create'
  | 'summaries:edit_own'
  | 'summaries:edit_all'
  | 'summaries:view'
  | 'summaries:delete_own'
  | 'summaries:delete_all'
  | 'exports:create'
  | 'settings:view'
  | 'settings:edit'
  | 'analytics:view';

export interface TeamMember {
  id: string;
  userId: string;
  organizationId: string;
  role: TeamRole;
  permissions: Record<string, boolean>;
  invitedBy?: string;
  invitedAt?: string;
  acceptedAt?: string;
  createdAt: string;
  updatedAt: string;
  
  // User details
  user: {
    id: string;
    email: string;
    fullName?: string;
    avatarUrl?: string;
  };
}

export interface TeamInvitation {
  id: string;
  organizationId: string;
  email: string;
  role: TeamRole;
  invitedBy: string;
  token: string;
  expiresAt: string;
  acceptedAt?: string;
  createdAt: string;
  
  // Inviter details
  inviter: {
    id: string;
    email: string;
    fullName?: string;
  };
  
  // Organization details
  organization: {
    id: string;
    name: string;
  };
}

export interface SharedWorkspace {
  id: string;
  organizationId: string;
  slackTeamId: string;
  slackTeamName: string;
  sharedBy: string;
  permissions: WorkspacePermissions;
  createdAt: string;
  
  // Sharer details
  sharer: {
    id: string;
    email: string;
    fullName?: string;
  };
}

export interface WorkspacePermissions {
  canSummarize: boolean;
  canExport: boolean;
  canViewHistory: boolean;
  canManageSettings: boolean;
}

export interface OrganizationSettings {
  // General settings
  name: string;
  description?: string;
  website?: string;
  
  // Team settings
  allowMemberInvites: boolean;
  requireApprovalForInvites: boolean;
  defaultMemberRole: TeamRole;
  maxMembers: number;
  
  // Workspace settings
  allowWorkspaceSharing: boolean;
  defaultWorkspacePermissions: WorkspacePermissions;
  
  // Summary settings
  defaultSummaryVisibility: 'private' | 'team' | 'organization';
  allowExternalSharing: boolean;
  retentionDays: number;
  
  // Notification settings
  emailNotifications: {
    newMember: boolean;
    newSummary: boolean;
    weeklyDigest: boolean;
    billingUpdates: boolean;
  };
  
  // Integration settings
  enabledIntegrations: string[];
  slackNotifications: {
    enabled: boolean;
    channel?: string;
    events: string[];
  };
}

export interface TeamStats {
  totalMembers: number;
  membersByRole: Record<TeamRole, number>;
  totalWorkspaces: number;
  totalSummaries: number;
  summariesThisMonth: number;
  activeMembers: number; // Members active in last 30 days
  storageUsed: number; // In bytes
  apiCallsThisMonth: number;
}

export interface InvitationRequest {
  email: string;
  role: TeamRole;
  message?: string;
  permissions?: Partial<Record<Permission, boolean>>;
}

export interface RoleChangeRequest {
  memberId: string;
  newRole: TeamRole;
  reason?: string;
}

export interface WorkspaceSharingRequest {
  slackTeamId: string;
  slackTeamName: string;
  permissions: WorkspacePermissions;
  notifyTeam?: boolean;
}

// Role hierarchy for permission inheritance
export const ROLE_HIERARCHY: Record<TeamRole, number> = {
  viewer: 1,
  editor: 2,
  admin: 3,
  owner: 4
};

// Default permissions for each role
export const DEFAULT_ROLE_PERMISSIONS: Record<TeamRole, Permission[]> = {
  owner: [
    'billing:manage',
    'members:invite',
    'members:remove',
    'members:edit_roles',
    'workspaces:share',
    'workspaces:manage',
    'summaries:create',
    'summaries:edit_own',
    'summaries:edit_all',
    'summaries:view',
    'summaries:delete_own',
    'summaries:delete_all',
    'exports:create',
    'settings:view',
    'settings:edit',
    'analytics:view'
  ],
  admin: [
    'members:invite',
    'members:remove',
    'workspaces:share',
    'workspaces:manage',
    'summaries:create',
    'summaries:edit_own',
    'summaries:edit_all',
    'summaries:view',
    'summaries:delete_own',
    'summaries:delete_all',
    'exports:create',
    'settings:view',
    'settings:edit',
    'analytics:view'
  ],
  editor: [
    'workspaces:share',
    'summaries:create',
    'summaries:edit_own',
    'summaries:view',
    'summaries:delete_own',
    'exports:create',
    'settings:view'
  ],
  viewer: [
    'summaries:view',
    'settings:view'
  ]
};

// Plan limits
export const PLAN_LIMITS = {
  FREE: {
    maxMembers: 3,
    maxWorkspaces: 1,
    maxSummariesPerMonth: 50,
    storageLimit: 100 * 1024 * 1024, // 100MB
    features: ['basic_summaries', 'email_support']
  },
  PRO: {
    maxMembers: 10,
    maxWorkspaces: 5,
    maxSummariesPerMonth: 500,
    storageLimit: 1024 * 1024 * 1024, // 1GB
    features: ['advanced_summaries', 'priority_support', 'analytics', 'integrations']
  },
  ENTERPRISE: {
    maxMembers: 50,
    maxWorkspaces: -1, // Unlimited
    maxSummariesPerMonth: -1, // Unlimited
    storageLimit: 10 * 1024 * 1024 * 1024, // 10GB
    features: ['premium_summaries', '24_7_support', 'advanced_analytics', 'custom_integrations', 'sso']
  }
};

export interface TeamActivity {
  id: string;
  organizationId: string;
  userId: string;
  action: string;
  target: string;
  targetId?: string;
  metadata: Record<string, any>;
  createdAt: string;
  
  user: {
    id: string;
    email: string;
    fullName?: string;
  };
}

export type TeamActivityAction = 
  | 'member_invited'
  | 'member_joined'
  | 'member_removed'
  | 'role_changed'
  | 'workspace_shared'
  | 'workspace_removed'
  | 'summary_created'
  | 'summary_shared'
  | 'settings_updated'
  | 'plan_upgraded'
  | 'plan_downgraded';

// Utility types
export type TeamMemberWithoutUser = Omit<TeamMember, 'user'>;
export type TeamInvitationWithoutDetails = Omit<TeamInvitation, 'inviter' | 'organization'>;
export type SharedWorkspaceWithoutSharer = Omit<SharedWorkspace, 'sharer'>;
