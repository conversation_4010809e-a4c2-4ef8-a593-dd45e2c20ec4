# Clean Build Script for Next.js 15 App Router
# Fixes ChunkLoadError issues by cleaning all caches and rebuilding

Write-Host "🧹 Cleaning Next.js build and caches..." -ForegroundColor Cyan

# Remove build directories
Write-Host "Removing .next directory..." -ForegroundColor Yellow
Remove-Item -Recurse -Force .next -ErrorAction SilentlyContinue

# Remove node_modules cache
Write-Host "Removing node_modules cache..." -ForegroundColor Yellow
Remove-Item -Recurse -Force node_modules/.cache -ErrorAction SilentlyContinue

# Remove npm cache
Write-Host "Clearing npm cache..." -ForegroundColor Yellow
npm cache clean --force

# Remove package-lock.json to ensure clean dependency resolution
Write-Host "Removing package-lock.json..." -ForegroundColor Yellow
Remove-Item package-lock.json -ErrorAction SilentlyContinue

# Reinstall dependencies
Write-Host "Reinstalling dependencies..." -ForegroundColor Yellow
npm install

# Build the project
Write-Host "Building project..." -ForegroundColor Yellow
npm run build

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Build completed successfully!" -ForegroundColor Green
    Write-Host "🚀 You can now run 'npm run start' to test the production build" -ForegroundColor Green
} else {
    Write-Host "❌ Build failed. Please check the error messages above." -ForegroundColor Red
    exit 1
}
