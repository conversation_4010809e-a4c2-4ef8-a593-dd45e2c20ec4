-- =============================================================================
-- SLACK SUMMARY SCRIBE - CLERK INTEGRATION DATABASE SCHEMA
-- =============================================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================================================
-- USERS TABLE (Clerk Integration)
-- =============================================================================

CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY, -- Clerk user ID (e.g., user_2abc123def456)
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    avatar_url TEXT,
    clerk_user_id TEXT UNIQUE NOT NULL, -- Redundant but explicit
    settings JSONB DEFAULT '{}',
    plan VARCHAR(50) DEFAULT 'free' CHECK (plan IN ('free', 'pro', 'enterprise')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- ORGANIZATIONS TABLE (Clerk Integration)
-- =============================================================================

CREATE TABLE IF NOT EXISTS organizations (
    id TEXT PRIMARY KEY, -- Clerk organization ID
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    plan VARCHAR(50) DEFAULT 'free' CHECK (plan IN ('free', 'pro', 'enterprise')),
    clerk_org_id TEXT UNIQUE, -- Clerk organization ID
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- USER ORGANIZATIONS (Many-to-Many with Clerk)
-- =============================================================================

CREATE TABLE IF NOT EXISTS user_organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
    organization_id TEXT REFERENCES organizations(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, organization_id)
);

-- =============================================================================
-- SUMMARIES TABLE (Updated for Clerk)
-- =============================================================================

CREATE TABLE IF NOT EXISTS summaries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
    organization_id TEXT REFERENCES organizations(id) ON DELETE SET NULL,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    source_type VARCHAR(50) DEFAULT 'slack' CHECK (source_type IN ('slack', 'file', 'text')),
    source_data JSONB DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    ai_model VARCHAR(100),
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- FILES TABLE (Updated for Clerk)
-- =============================================================================

CREATE TABLE IF NOT EXISTS files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
    organization_id TEXT REFERENCES organizations(id) ON DELETE SET NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size INTEGER NOT NULL,
    storage_path TEXT NOT NULL,
    upload_status VARCHAR(50) DEFAULT 'pending' CHECK (upload_status IN ('pending', 'processing', 'completed', 'failed')),
    processing_result JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- ANALYTICS TABLE (Updated for Clerk)
-- =============================================================================

CREATE TABLE IF NOT EXISTS analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
    organization_id TEXT REFERENCES organizations(id) ON DELETE SET NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB DEFAULT '{}',
    session_id TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- FEEDBACK TABLE (Updated for Clerk)
-- =============================================================================

CREATE TABLE IF NOT EXISTS feedback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN ('satisfaction', 'feature_request', 'bug_report', 'general')),
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    message TEXT,
    metadata JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- USER ONBOARDING TABLE (Clerk Integration)
-- =============================================================================

CREATE TABLE IF NOT EXISTS user_onboarding (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
    completed_steps TEXT[] DEFAULT '{}',
    current_step INTEGER DEFAULT 0,
    tour_completed BOOLEAN DEFAULT FALSE,
    last_active_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    skip_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id)
);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES FOR CLERK
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE files ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_onboarding ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own data" ON users
    FOR ALL USING (id = current_setting('request.jwt.claims', true)::json->>'sub');

-- Organizations: users can only see organizations they belong to
CREATE POLICY "Users can view own organizations" ON organizations
    FOR ALL USING (
        id IN (
            SELECT organization_id 
            FROM user_organizations 
            WHERE user_id = current_setting('request.jwt.claims', true)::json->>'sub'
        )
    );

-- User organizations: users can only see their own memberships
CREATE POLICY "Users can view own memberships" ON user_organizations
    FOR ALL USING (user_id = current_setting('request.jwt.claims', true)::json->>'sub');

-- Summaries: users can only see their own summaries
CREATE POLICY "Users can view own summaries" ON summaries
    FOR ALL USING (user_id = current_setting('request.jwt.claims', true)::json->>'sub');

-- Files: users can only see their own files
CREATE POLICY "Users can view own files" ON files
    FOR ALL USING (user_id = current_setting('request.jwt.claims', true)::json->>'sub');

-- Analytics: users can only see their own analytics
CREATE POLICY "Users can view own analytics" ON analytics
    FOR ALL USING (user_id = current_setting('request.jwt.claims', true)::json->>'sub');

-- Feedback: users can only see their own feedback
CREATE POLICY "Users can view own feedback" ON feedback
    FOR ALL USING (user_id = current_setting('request.jwt.claims', true)::json->>'sub');

-- User onboarding: users can only see their own onboarding progress
CREATE POLICY "Users can view own onboarding" ON user_onboarding
    FOR ALL USING (user_id = current_setting('request.jwt.claims', true)::json->>'sub');

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- User indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_clerk_id ON users(clerk_user_id);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Organization indexes
CREATE INDEX IF NOT EXISTS idx_organizations_slug ON organizations(slug);
CREATE INDEX IF NOT EXISTS idx_organizations_clerk_id ON organizations(clerk_org_id);

-- Summary indexes
CREATE INDEX IF NOT EXISTS idx_summaries_user_id ON summaries(user_id);
CREATE INDEX IF NOT EXISTS idx_summaries_org_id ON summaries(organization_id);
CREATE INDEX IF NOT EXISTS idx_summaries_created_at ON summaries(created_at);
CREATE INDEX IF NOT EXISTS idx_summaries_source_type ON summaries(source_type);

-- File indexes
CREATE INDEX IF NOT EXISTS idx_files_user_id ON files(user_id);
CREATE INDEX IF NOT EXISTS idx_files_status ON files(upload_status);
CREATE INDEX IF NOT EXISTS idx_files_created_at ON files(created_at);

-- Analytics indexes
CREATE INDEX IF NOT EXISTS idx_analytics_user_id ON analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_event_type ON analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_created_at ON analytics(created_at);

-- Feedback indexes
CREATE INDEX IF NOT EXISTS idx_feedback_user_id ON feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_feedback_type ON feedback(type);
CREATE INDEX IF NOT EXISTS idx_feedback_status ON feedback(status);

-- User onboarding indexes
CREATE INDEX IF NOT EXISTS idx_user_onboarding_user_id ON user_onboarding(user_id);
CREATE INDEX IF NOT EXISTS idx_user_onboarding_tour_completed ON user_onboarding(tour_completed);
CREATE INDEX IF NOT EXISTS idx_user_onboarding_last_active ON user_onboarding(last_active_date);

-- =============================================================================
-- FUNCTIONS AND TRIGGERS
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_summaries_updated_at BEFORE UPDATE ON summaries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_files_updated_at BEFORE UPDATE ON files
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_feedback_updated_at BEFORE UPDATE ON feedback
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_onboarding_updated_at BEFORE UPDATE ON user_onboarding
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- INITIAL DATA
-- =============================================================================

-- Create default organization for new users
INSERT INTO organizations (id, name, slug, plan) 
VALUES ('org_default', 'Personal Workspace', 'personal', 'free')
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- USER SETTINGS TABLE
-- =============================================

-- Create user_settings table for storing user preferences
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
    ai_model TEXT DEFAULT 'deepseek-r1',
    export_format TEXT DEFAULT 'pdf' CHECK (export_format IN ('pdf', 'excel', 'notion')),
    notifications JSONB DEFAULT '{
        "email": true,
        "slack": false,
        "browser": true,
        "weekly": false,
        "summaryComplete": true,
        "exportReady": true,
        "systemUpdates": true
    }'::jsonb,
    preferences JSONB DEFAULT '{
        "showOnboarding": true,
        "autoExport": false,
        "includeMetadata": true,
        "includeCharts": false,
        "theme": "system"
    }'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id)
);

-- Enable RLS on user_settings table
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for user_settings table
CREATE POLICY "Users can manage their own settings" ON user_settings
    FOR ALL USING (user_id = current_setting('request.jwt.claims', true)::json->>'sub');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_settings_ai_model ON user_settings(ai_model);
CREATE INDEX IF NOT EXISTS idx_user_settings_export_format ON user_settings(export_format);

-- Create trigger for user_settings updated_at
CREATE TRIGGER update_user_settings_updated_at BEFORE UPDATE ON user_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMENT ON TABLE users IS 'User accounts integrated with Clerk authentication';
COMMENT ON TABLE organizations IS 'Organizations/workspaces with Clerk integration';
COMMENT ON TABLE summaries IS 'AI-generated summaries with user ownership';
COMMENT ON TABLE files IS 'Uploaded files with processing status';
COMMENT ON TABLE analytics IS 'User activity and usage analytics';
COMMENT ON TABLE feedback IS 'User feedback and feature requests';
COMMENT ON TABLE user_onboarding IS 'User onboarding progress and tour completion status';
COMMENT ON TABLE user_settings IS 'User preferences and settings with Clerk integration';
