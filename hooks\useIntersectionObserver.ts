'use client';

import { useEffect, useRef, useState } from 'react';

interface UseIntersectionObserverOptions {
  threshold?: number | number[];
  root?: Element | null;
  rootMargin?: string;
  freezeOnceVisible?: boolean;
}

export function useIntersectionObserver(
  options: UseIntersectionObserverOptions = {}
) {
  const {
    threshold = 0,
    root = null,
    rootMargin = '0%',
    freezeOnceVisible = false
  } = options;

  const [entry, setEntry] = useState<IntersectionObserverEntry>();
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<Element>();

  const frozen = entry?.isIntersecting && freezeOnceVisible;

  const updateEntry = ([entry]: IntersectionObserverEntry[]): void => {
    setEntry(entry);
    setIsVisible(entry.isIntersecting);
  };

  useEffect(() => {
    const node = elementRef?.current; // DOM Ref
    const hasIOSupport = !!window.IntersectionObserver;

    if (!hasIOSupport || frozen || !node) return;

    const observerParams = { threshold, root, rootMargin };
    const observer = new IntersectionObserver(updateEntry, observerParams);

    observer.observe(node);

    return () => observer.disconnect();
  }, [elementRef, threshold, root, rootMargin, frozen]);

  return { ref: elementRef, entry, isVisible };
}

// Hook for lazy loading components
export function useLazyLoad(enabled: boolean = true) {
  const { ref, isVisible } = useIntersectionObserver({
    threshold: 0.1,
    freezeOnceVisible: true
  });

  return {
    ref,
    shouldLoad: enabled ? isVisible : true
  };
}

// Hook for performance monitoring
export function usePerformanceObserver() {
  const [metrics, setMetrics] = useState<{
    renderTime?: number;
    loadTime?: number;
    interactionTime?: number;
  }>({});

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const startTime = performance.now();

    // Measure render time
    const measureRenderTime = () => {
      const renderTime = performance.now() - startTime;
      setMetrics(prev => ({ ...prev, renderTime }));
    };

    // Measure load time
    const measureLoadTime = () => {
      if (document.readyState === 'complete') {
        const loadTime = performance.now() - startTime;
        setMetrics(prev => ({ ...prev, loadTime }));
      }
    };

    // Measure first interaction
    const measureInteraction = () => {
      const interactionTime = performance.now() - startTime;
      setMetrics(prev => ({ ...prev, interactionTime }));
      
      // Remove listeners after first interaction
      document.removeEventListener('click', measureInteraction);
      document.removeEventListener('touchstart', measureInteraction);
    };

    measureRenderTime();
    measureLoadTime();
    
    document.addEventListener('click', measureInteraction);
    document.addEventListener('touchstart', measureInteraction);

    return () => {
      document.removeEventListener('click', measureInteraction);
      document.removeEventListener('touchstart', measureInteraction);
    };
  }, []);

  return metrics;
}
