/**
 * Workspace Limits API Route
 * Check workspace limits and usage
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { checkWorkspaceLimits } from '@/lib/team-management';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/team/limits
 * Get workspace limits and current usage
 */
export async function GET(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      const { searchParams } = new URL(req.url);
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const organizationId = searchParams.get('organization_id') || user.id;

      const result = await checkWorkspaceLimits(organizationId);

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        limits: result.limits,
      });

    } catch (error) {
      console.error('Workspace limits API error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to fetch workspace limits' },
        { status: 500 }
      );
    }
  });
}
