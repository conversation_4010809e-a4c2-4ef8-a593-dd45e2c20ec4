# 🔧 Supabase Server/Client Separation Guide

This guide explains the proper separation of Supabase clients in Next.js 15 to avoid build errors and ensure optimal performance.

## 🚨 **Problem Solved**

### **Before (Problematic)**
```typescript
// ❌ This caused build errors
// lib/supabase-client.ts
import { cookies } from 'next/headers' // Server-only import
export function createClientSupabaseClient() {
  // Used by both server and client code
}
```

**Issues:**
- `next/headers` import in files used by client components
- Build error: "You're importing a component that needs 'next/headers'..."
- Multiple Supabase client instances
- Session/cookie conflicts

### **After (Fixed)**
```typescript
// ✅ Server-only client
// lib/supabase-server.ts
import { cookies } from 'next/headers'
export function createSupabaseServerClient() {
  // Only used by server components, API routes, middleware
}

// ✅ Client-only client  
// lib/supabase-browser.ts
'use client'
export function createBrowserSupabaseClient() {
  // Only used by client components, React hooks
}
```

## 📁 **File Structure**

```
/lib
├── supabase-server.ts   ⚡ SERVER ONLY
├── supabase-browser.ts  🌐 CLIENT ONLY
└── supabase.ts         📦 LEGACY (kept for compatibility)

/middleware.ts           ⚡ SERVER ONLY
```

## 🔧 **Usage Guidelines**

### **Server-Side Code** ⚡
Use `lib/supabase-server.ts` in:
- ✅ Server Components
- ✅ API Routes (`app/api/*/route.ts`)
- ✅ Middleware (`middleware.ts`)
- ✅ Server Actions

```typescript
// app/api/example/route.ts
import { createSupabaseServerClient } from '@/lib/supabase-server'

export async function GET() {
  const supabase = await createSupabaseServerClient()
  // Server-side operations
}
```

### **Client-Side Code** 🌐
Use `lib/supabase-browser.ts` in:
- ✅ Client Components (`'use client'`)
- ✅ React Hooks
- ✅ Browser-side JavaScript

```typescript
// app/login/page.tsx
'use client'
import { createBrowserSupabaseClient } from '@/lib/supabase-browser'

export default function LoginPage() {
  const supabase = createBrowserSupabaseClient()
  // Client-side operations
}
```

## 🚫 **What NOT to Do**

### **❌ Don't Mix Imports**
```typescript
// ❌ WRONG: Client component importing server client
'use client'
import { createSupabaseServerClient } from '@/lib/supabase-server'

// ❌ WRONG: API route importing browser client
import { createBrowserSupabaseClient } from '@/lib/supabase-browser'
```

### **❌ Don't Import next/headers in Client Code**
```typescript
// ❌ WRONG: This will cause build errors
'use client'
import { cookies } from 'next/headers'
```

## 🔍 **How to Identify Which Client to Use**

### **Check 1: File Location**
- `app/api/*` → Use **server client**
- `middleware.ts` → Use **server client**
- Components with `'use client'` → Use **browser client**

### **Check 2: Environment**
- Server-side rendering → Use **server client**
- Browser interactions → Use **browser client**
- Authentication flows → Use **browser client**

### **Check 3: Features Needed**
- Cookie management → Use **server client**
- Real-time subscriptions → Use **browser client**
- Admin operations → Use **server client** (service role)

## 🧪 **Testing Your Setup**

### **Run Separation Test**
```bash
npm run test:separation
```

This will check:
- ✅ Proper file separation
- ✅ No import conflicts
- ✅ Build passes without errors
- ✅ Correct usage patterns

### **Manual Checks**
```bash
# 1. Build should pass
npm run build

# 2. No next/headers errors
npm run dev

# 3. Authentication should work
# Test login/logout flows
```

## 🔧 **Migration Guide**

### **Step 1: Update Imports**
```typescript
// Before
import { createClientSupabaseClient } from '@/lib/supabase-client'

// After (Client Components)
import { createBrowserSupabaseClient } from '@/lib/supabase-browser'

// After (Server Components/API Routes)
import { createSupabaseServerClient } from '@/lib/supabase-server'
```

### **Step 2: Update Function Calls**
```typescript
// Before
const supabase = createClientSupabaseClient()

// After (Client)
const supabase = createBrowserSupabaseClient()

// After (Server)
const supabase = await createSupabaseServerClient()
```

### **Step 3: Add 'use client' Directives**
```typescript
// Add to all client components
'use client'

import { createBrowserSupabaseClient } from '@/lib/supabase-browser'
```

## 🎯 **Benefits of Proper Separation**

### **✅ Build Reliability**
- No more "next/headers" import errors
- Clean builds in development and production
- Proper TypeScript support

### **✅ Performance**
- Singleton pattern prevents multiple client instances
- Optimized bundle sizes (server/client separation)
- Better caching and session management

### **✅ Maintainability**
- Clear separation of concerns
- Easier debugging and testing
- Future-proof architecture

### **✅ Security**
- Proper cookie handling
- Secure session management
- Environment-aware configuration

## 🚀 **Advanced Usage**

### **Custom Hooks**
```typescript
// hooks/useSupabase.ts
'use client'
import { createBrowserSupabaseClient } from '@/lib/supabase-browser'

export function useSupabase() {
  return createBrowserSupabaseClient()
}
```

### **Server Actions**
```typescript
// app/actions.ts
import { createSupabaseServerClient } from '@/lib/supabase-server'

export async function serverAction() {
  'use server'
  const supabase = await createSupabaseServerClient()
  // Server action logic
}
```

### **Middleware Integration**
```typescript
// middleware.ts
import { createServerClient } from '@supabase/ssr'

export async function middleware(request) {
  // Custom middleware logic with proper server client
}
```

## 📞 **Troubleshooting**

### **Build Errors**
1. Run `npm run test:separation`
2. Check for mixed imports
3. Ensure `'use client'` directives are present
4. Verify no `next/headers` in client code

### **Authentication Issues**
1. Check cookie configuration
2. Verify client/server separation
3. Test in incognito mode
4. Check environment variables

### **Performance Issues**
1. Ensure singleton pattern is working
2. Check for multiple client instances
3. Verify proper caching

## 📚 **Resources**

- [Next.js 15 Documentation](https://nextjs.org/docs)
- [Supabase SSR Documentation](https://supabase.com/docs/guides/auth/server-side-rendering)
- [Test Script](../scripts/test-server-client-separation.js)

---

**✅ Your Supabase client separation is now production-ready!**
