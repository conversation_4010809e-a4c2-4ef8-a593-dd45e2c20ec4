'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRight, 
  ArrowLeft, 
  X, 
  Play, 
  Pause, 
  RotateCcw,
  CheckCircle,
  Circle,
  Lightbulb,
  Target,
  Zap,
  Users,
  FileText,
  Download,
  Settings,
  Bell
} from 'lucide-react';
import { useLiveAuth } from '@/lib/clerk-auth';
import { useSupabase } from '@/lib/supabase-browser';
import { useNotifications } from '@/hooks/useNotifications';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  target: string; // CSS selector for the element to highlight
  position: 'top' | 'bottom' | 'left' | 'right' | 'center';
  icon: React.ReactNode;
  action?: {
    type: 'click' | 'hover' | 'scroll' | 'wait';
    duration?: number;
  };
  optional?: boolean;
  category: 'basics' | 'features' | 'advanced';
}

interface OnboardingProgress {
  userId: string;
  completedSteps: string[];
  currentStep: number;
  tourCompleted: boolean;
  lastActiveDate: string;
  skipCount: number;
}

const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Slack Summary Scribe!',
    description: 'Let\'s take a quick tour to help you get the most out of your new AI-powered summarization tool.',
    target: '.dashboard-header',
    position: 'bottom',
    icon: <Zap className="h-5 w-5" />,
    category: 'basics'
  },
  {
    id: 'file-upload',
    title: 'Upload Your Files',
    description: 'Drag and drop Slack exports, meeting transcripts, or documents here. We support PDF, DOCX, and TXT formats.',
    target: '.file-upload-zone',
    position: 'top',
    icon: <FileText className="h-5 w-5" />,
    category: 'basics'
  },
  {
    id: 'ai-summarization',
    title: 'AI-Powered Summaries',
    description: 'Our AI analyzes your content and generates intelligent summaries with key insights, action items, and sentiment analysis.',
    target: '.summary-section',
    position: 'left',
    icon: <Target className="h-5 w-5" />,
    category: 'features'
  },
  {
    id: 'export-options',
    title: 'Export Your Results',
    description: 'Export summaries to PDF, Excel, or Notion. Perfect for sharing with your team or keeping records.',
    target: '.export-panel',
    position: 'top',
    icon: <Download className="h-5 w-5" />,
    category: 'features'
  },
  {
    id: 'notifications',
    title: 'Stay Updated',
    description: 'Get real-time notifications when your summaries are ready. Customize your notification preferences in settings.',
    target: '.notification-bell',
    position: 'bottom',
    icon: <Bell className="h-5 w-5" />,
    category: 'features'
  },
  {
    id: 'settings',
    title: 'Customize Your Experience',
    description: 'Adjust AI models, notification preferences, and export formats to match your workflow.',
    target: '.settings-panel',
    position: 'left',
    icon: <Settings className="h-5 w-5" />,
    category: 'advanced',
    optional: true
  }
];

interface OnboardingSystemProps {
  autoStart?: boolean;
  showProgress?: boolean;
  allowSkip?: boolean;
  onComplete?: () => void;
  onSkip?: () => void;
}

export default function OnboardingSystem({
  autoStart = true,
  showProgress = true,
  allowSkip = true,
  onComplete,
  onSkip
}: OnboardingSystemProps) {
  const { user } = useLiveAuth();
  const { supabase } = useSupabase();
  const { notifySuccess, notifyInfo } = useNotifications();
  
  const [isActive, setIsActive] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState<OnboardingProgress | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [highlightedElement, setHighlightedElement] = useState<Element | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [isPaused, setIsPaused] = useState(false);
  
  const overlayRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Load onboarding progress from Supabase
  useEffect(() => {
    if (user) {
      loadOnboardingProgress();
    }
  }, [user]);

  // Auto-start onboarding for new users
  useEffect(() => {
    if (autoStart && progress && !progress.tourCompleted && progress.completedSteps.length === 0) {
      setTimeout(() => startOnboarding(), 1000);
    }
  }, [autoStart, progress]);

  const loadOnboardingProgress = async () => {
    if (!user || !supabase) return;

    try {
      const { data, error } = await supabase
        .from('user_onboarding')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading onboarding progress:', error);
        return;
      }

      if (data) {
        setProgress(data);
        setCurrentStep(data.current_step || 0);
      } else {
        // Create new onboarding record
        const newProgress: OnboardingProgress = {
          userId: user.id,
          completedSteps: [],
          currentStep: 0,
          tourCompleted: false,
          lastActiveDate: new Date().toISOString(),
          skipCount: 0
        };
        
        await saveOnboardingProgress(newProgress);
        setProgress(newProgress);
      }
    } catch (error) {
      console.error('Error in loadOnboardingProgress:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveOnboardingProgress = async (progressData: OnboardingProgress) => {
    if (!user || !supabase) return;

    try {
      const { error } = await supabase
        .from('user_onboarding')
        .upsert({
          user_id: progressData.userId,
          completed_steps: progressData.completedSteps,
          current_step: progressData.currentStep,
          tour_completed: progressData.tourCompleted,
          last_active_date: progressData.lastActiveDate,
          skip_count: progressData.skipCount
        });

      if (error) {
        console.error('Error saving onboarding progress:', error);
      }
    } catch (error) {
      console.error('Error in saveOnboardingProgress:', error);
    }
  };

  const startOnboarding = useCallback(() => {
    setIsActive(true);
    setCurrentStep(0);
    setIsPaused(false);
    updateHighlight(0);
    
    notifyInfo(
      'Onboarding Started',
      'Welcome! Let\'s explore the key features together.',
      { category: 'system', sound: false }
    );
  }, [notifyInfo]);

  const updateHighlight = useCallback((stepIndex: number) => {
    const step = ONBOARDING_STEPS[stepIndex];
    if (!step) return;

    const element = document.querySelector(step.target);
    if (element) {
      setHighlightedElement(element);
      updateTooltipPosition(element, step.position);
      
      // Scroll element into view
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center',
        inline: 'center'
      });
    }
  }, []);

  const updateTooltipPosition = (element: Element, position: string) => {
    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltipRef.current?.getBoundingClientRect();
    
    let x = 0, y = 0;
    
    switch (position) {
      case 'top':
        x = rect.left + rect.width / 2;
        y = rect.top - (tooltipRect?.height || 0) - 20;
        break;
      case 'bottom':
        x = rect.left + rect.width / 2;
        y = rect.bottom + 20;
        break;
      case 'left':
        x = rect.left - (tooltipRect?.width || 0) - 20;
        y = rect.top + rect.height / 2;
        break;
      case 'right':
        x = rect.right + 20;
        y = rect.top + rect.height / 2;
        break;
      case 'center':
        x = window.innerWidth / 2;
        y = window.innerHeight / 2;
        break;
    }
    
    // Ensure tooltip stays within viewport
    x = Math.max(20, Math.min(x, window.innerWidth - (tooltipRect?.width || 300) - 20));
    y = Math.max(20, Math.min(y, window.innerHeight - (tooltipRect?.height || 200) - 20));
    
    setTooltipPosition({ x, y });
  };

  const nextStep = useCallback(async () => {
    const currentStepData = ONBOARDING_STEPS[currentStep];
    
    if (progress) {
      const updatedProgress = {
        ...progress,
        completedSteps: [...progress.completedSteps, currentStepData.id],
        currentStep: currentStep + 1,
        lastActiveDate: new Date().toISOString()
      };
      
      setProgress(updatedProgress);
      await saveOnboardingProgress(updatedProgress);
    }

    if (currentStep < ONBOARDING_STEPS.length - 1) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      updateHighlight(newStep);
    } else {
      completeOnboarding();
    }
  }, [currentStep, progress]);

  const previousStep = useCallback(() => {
    if (currentStep > 0) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      updateHighlight(newStep);
    }
  }, [currentStep]);

  const completeOnboarding = useCallback(async () => {
    if (progress) {
      const completedProgress = {
        ...progress,
        tourCompleted: true,
        lastActiveDate: new Date().toISOString()
      };
      
      setProgress(completedProgress);
      await saveOnboardingProgress(completedProgress);
    }

    setIsActive(false);
    setHighlightedElement(null);
    
    notifySuccess(
      'Onboarding Complete!',
      'You\'re all set! Start uploading files to create your first summary.',
      { category: 'system', priority: 'high' }
    );
    
    onComplete?.();
  }, [progress, onComplete, notifySuccess]);

  const skipOnboarding = useCallback(async () => {
    if (progress) {
      const skippedProgress = {
        ...progress,
        skipCount: progress.skipCount + 1,
        lastActiveDate: new Date().toISOString()
      };
      
      setProgress(skippedProgress);
      await saveOnboardingProgress(skippedProgress);
    }

    setIsActive(false);
    setHighlightedElement(null);
    onSkip?.();
  }, [progress, onSkip]);

  const restartOnboarding = useCallback(() => {
    setCurrentStep(0);
    startOnboarding();
  }, [startOnboarding]);

  if (isLoading || !progress) {
    return null;
  }

  const currentStepData = ONBOARDING_STEPS[currentStep];
  const completionPercentage = Math.round((progress.completedSteps.length / ONBOARDING_STEPS.length) * 100);

  return (
    <>
      {/* Onboarding trigger button for completed users */}
      {!isActive && progress.tourCompleted && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="fixed bottom-6 right-6 z-40"
        >
          <Button
            onClick={restartOnboarding}
            variant="outline"
            size="sm"
            className="bg-white dark:bg-gray-800 shadow-lg border-blue-200 dark:border-blue-800"
          >
            <Lightbulb className="h-4 w-4 mr-2" />
            Tour
          </Button>
        </motion.div>
      )}

      {/* Onboarding overlay and tooltip */}
      <AnimatePresence>
        {isActive && currentStepData && (
          <>
            {/* Overlay with highlight */}
            <motion.div
              ref={overlayRef}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 pointer-events-none"
              style={{
                background: highlightedElement 
                  ? `radial-gradient(circle at ${tooltipPosition.x}px ${tooltipPosition.y}px, transparent 100px, rgba(0,0,0,0.7) 200px)`
                  : 'rgba(0,0,0,0.7)'
              }}
            />

            {/* Tooltip */}
            <motion.div
              ref={tooltipRef}
              initial={{ opacity: 0, scale: 0.8, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 20 }}
              className="fixed z-50 pointer-events-auto"
              style={{
                left: tooltipPosition.x,
                top: tooltipPosition.y,
                transform: 'translate(-50%, -50%)'
              }}
            >
              <Card className="w-80 shadow-2xl border-blue-200 dark:border-blue-800">
                <CardContent className="p-6">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        {currentStepData.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">{currentStepData.title}</h3>
                        <Badge variant="secondary" className="text-xs">
                          Step {currentStep + 1} of {ONBOARDING_STEPS.length}
                        </Badge>
                      </div>
                    </div>
                    {allowSkip && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={skipOnboarding}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  {/* Content */}
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    {currentStepData.description}
                  </p>

                  {/* Progress bar */}
                  {showProgress && (
                    <div className="mb-6">
                      <div className="flex justify-between text-sm text-gray-500 mb-2">
                        <span>Progress</span>
                        <span>{completionPercentage}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <motion.div
                          className="bg-blue-600 h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${completionPercentage}%` }}
                          transition={{ duration: 0.5 }}
                        />
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={previousStep}
                        disabled={currentStep === 0}
                      >
                        <ArrowLeft className="h-4 w-4 mr-1" />
                        Back
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsPaused(!isPaused)}
                      >
                        {isPaused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
                      </Button>
                    </div>

                    <Button
                      onClick={currentStep === ONBOARDING_STEPS.length - 1 ? completeOnboarding : nextStep}
                      size="sm"
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {currentStep === ONBOARDING_STEPS.length - 1 ? (
                        <>
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Complete
                        </>
                      ) : (
                        <>
                          Next
                          <ArrowRight className="h-4 w-4 ml-1" />
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
}
