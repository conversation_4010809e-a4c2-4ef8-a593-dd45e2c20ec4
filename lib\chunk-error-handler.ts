import { devLog } from '@/lib/console-cleaner';
/**
 * Comprehensive Chunk Loading Error Handler
 * 
 * Handles ChunkLoadError, runtime.js failures, and other chunk-related issues
 * with automatic retry mechanisms and fallback strategies.
 */

interface ChunkErrorConfig {
  maxRetries: number;
  retryDelay: number;
  reloadOnFailure: boolean;
  logErrors: boolean;
}

class ChunkErrorHandler {
  private retryCount = 0;
  private config: ChunkErrorConfig;
  private failedChunks = new Set<string>();

  constructor(config: Partial<ChunkErrorConfig> = {}) {
    this.config = {
      maxRetries: 3,
      retryDelay: 1000,
      reloadOnFailure: true,
      logErrors: true,
      ...config,
    };

    this.initializeErrorHandlers();
  }

  private initializeErrorHandlers() {
    if (typeof window === 'undefined') return;

    // Handle global JavaScript errors
    window.addEventListener('error', this.handleGlobalError.bind(this));
    
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));

    // Handle chunk loading errors specifically
    this.setupChunkLoadingInterceptor();
  }

  private handleGlobalError(event: ErrorEvent) {
    const error = event.error;
    const message = event.message || '';

    if (this.isChunkLoadError(error, message)) {
      event.preventDefault();
      this.handleChunkError(error, message);
    }
  }

  private handleUnhandledRejection(event: PromiseRejectionEvent) {
    const reason = event.reason;
    
    if (this.isChunkLoadError(reason, reason?.message || '')) {
      event.preventDefault();
      this.handleChunkError(reason, reason?.message || 'Promise rejection');
    }
  }

  private isChunkLoadError(error: any, message: string): boolean {
    const chunkErrorPatterns = [
      'ChunkLoadError',
      'Loading chunk',
      'Loading CSS chunk',
      'Failed to import',
      'Cannot read properties of undefined (reading \'call\')',
      'Script error',
      'Network error',
      'Failed to fetch',
      'Loading module',
      'runtime.js',
      'MIME type',
    ];

    const errorString = (error?.message || message || '').toLowerCase();
    const errorName = (error?.name || '').toLowerCase();

    return chunkErrorPatterns.some(pattern => 
      errorString.includes(pattern.toLowerCase()) || 
      errorName.includes(pattern.toLowerCase())
    );
  }

  private handleChunkError(error: any, message: string) {
    if (this.config.logErrors) {
      console.warn('🔄 Chunk loading error detected:', {
        error: error?.message || message,
        name: error?.name,
        stack: error?.stack,
        retryCount: this.retryCount,
        url: window.location.href,
        userAgent: navigator.userAgent,
      });
    }

    // Extract chunk name if possible
    const chunkName = this.extractChunkName(error?.message || message);
    if (chunkName) {
      this.failedChunks.add(chunkName);
    }

    // Immediate cache clearing for critical errors
    if (this.isCriticalChunkError(error, message)) {
      this.clearAllCaches().then(() => {
        this.performPageReload();
      });
      return;
    }

    // Attempt retry or reload
    if (this.retryCount < this.config.maxRetries) {
      this.retryChunkLoading();
    } else if (this.config.reloadOnFailure) {
      this.performPageReload();
    }
  }

  private isCriticalChunkError(error: any, message: string): boolean {
    const criticalPatterns = [
      'runtime.js',
      'main.js',
      'framework.js',
      'Cannot read properties of undefined (reading \'call\')',
      'Cannot read property \'call\' of undefined',
      'TypeError: Cannot read properties of undefined',
      'ChunkLoadError',
      'Loading chunk',
      'Script error',
      'Module not found',
      'Failed to import',
    ];

    const errorString = (error?.message || message || '').toLowerCase();
    const stackString = (error?.stack || '').toLowerCase();

    return criticalPatterns.some(pattern =>
      errorString.includes(pattern.toLowerCase()) ||
      stackString.includes(pattern.toLowerCase())
    );
  }

  private extractChunkName(message: string): string | null {
    const chunkMatches = message.match(/chunk[s]?[\/\\]([^\/\\]+)/i);
    if (chunkMatches) {
      return chunkMatches[1];
    }

    const moduleMatches = message.match(/Loading module[s]?[\/\\]([^\/\\]+)/i);
    if (moduleMatches) {
      return moduleMatches[1];
    }

    return null;
  }

  private retryChunkLoading() {
    this.retryCount++;
    
    if (this.config.logErrors) {
  devLog.log(`🔄 Retrying chunk loading (attempt ${this.retryCount}/${this.config.maxRetries})`);
    }

    setTimeout(() => {
      // Clear module cache for failed chunks
      this.clearFailedChunksFromCache();
      
      // Attempt to reload the current route
      if (typeof window !== 'undefined' && window.location) {
        window.location.reload();
      }
    }, this.config.retryDelay * this.retryCount);
  }

  private clearFailedChunksFromCache() {
    if (typeof window === 'undefined') return;

    // Clear any cached modules that might be causing issues
    this.failedChunks.forEach(chunkName => {
      // Remove from browser cache if possible
      if ('caches' in window) {
        caches.keys().then(cacheNames => {
          cacheNames.forEach(cacheName => {
            if (cacheName.includes('next') || cacheName.includes('chunk')) {
              caches.delete(cacheName);
            }
          });
        });
      }
    });
  }

  private performPageReload() {
    if (this.config.logErrors) {
  devLog.log('🔄 Maximum retries reached, performing page reload...');
    }

    setTimeout(() => {
      if (typeof window !== 'undefined') {
        // Clear all caches before reload
        this.clearAllCaches().then(() => {
          window.location.reload();
        });
      }
    }, 500);
  }

  private async clearAllCaches() {
    if (typeof window === 'undefined' || !('caches' in window)) return;

    try {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
    } catch (error) {
      console.warn('Failed to clear caches:', error);
    }
  }

  private setupChunkLoadingInterceptor() {
    if (typeof window === 'undefined') return;

    // Intercept dynamic imports
    const originalImport = window.__webpack_require__;
    if (originalImport) {
      // This would require more complex webpack integration
      // For now, we rely on the global error handlers
    }

    // Monitor for script loading failures
    document.addEventListener('error', (event) => {
      const target = event.target as HTMLElement;
      if (target?.tagName === 'SCRIPT' || target?.tagName === 'LINK') {
        const src = (target as any).src || (target as any).href;
        if (src && (src.includes('_next') || src.includes('chunk'))) {
          this.handleChunkError(new Error(`Failed to load: ${src}`), `Script/CSS loading failed: ${src}`);
        }
      }
    }, true);
  }

  // Public methods
  public reset() {
    this.retryCount = 0;
    this.failedChunks.clear();
  }

  public getFailedChunks() {
    return Array.from(this.failedChunks);
  }

  public getRetryCount() {
    return this.retryCount;
  }
}

// Create and export singleton instance
export const chunkErrorHandler = new ChunkErrorHandler({
  maxRetries: 3,
  retryDelay: 1000,
  reloadOnFailure: true,
  logErrors: process.env.NODE_ENV === 'development',
});

// Enhanced global error handler for production resilience
if (typeof window !== 'undefined') {
  // Add a small delay to ensure DOM is ready
  setTimeout(() => {
    chunkErrorHandler.reset();
  }, 100);

  // Global error listener for runtime crashes
  window.addEventListener('error', (e) => {
    if (
      e.message?.includes("Cannot read properties of undefined (reading 'call')") ||
      e.message?.includes('ChunkLoadError') ||
      e.message?.includes('Loading chunk') ||
      e.message?.includes('runtime.js') ||
      e.message?.includes('main.js') ||
      e.message?.includes('framework.js')
    ) {
  devLog.log('🔄 Critical chunk error detected, clearing caches and reloading...');

      caches.keys().then(function (names) {
        for (let name of names) caches.delete(name);
      }).finally(() => {
        // Clear other storage
        try {
          localStorage.clear();
          sessionStorage.clear();
        } catch (err) {
          console.warn('Could not clear storage:', err);
        }

        // Force reload
        window.location.reload();
      });
    }
  });

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (e) => {
    const reason = e.reason;
    if (reason && typeof reason === 'object' && reason.message) {
      if (
        reason.message.includes('ChunkLoadError') ||
        reason.message.includes('Loading chunk') ||
        reason.message.includes('Cannot read properties of undefined')
      ) {
  devLog.log('🔄 Chunk promise rejection detected, clearing caches and reloading...');

        caches.keys().then(function (names) {
          for (let name of names) caches.delete(name);
        }).finally(() => {
          window.location.reload();
        });
      }
    }
  });
}

export default ChunkErrorHandler;
