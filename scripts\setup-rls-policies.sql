-- Comprehensive Row Level Security (RLS) Policies
-- Production-ready security for Slack Summary Scribe

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;

DROP POLICY IF EXISTS "Users can view organizations they belong to" ON organizations;
DROP POLICY IF EXISTS "Users can update organizations they own" ON organizations;
DROP POLICY IF EXISTS "Users can create organizations" ON organizations;

DROP POLICY IF EXISTS "Users can view their organization memberships" ON user_organizations;
DROP POLICY IF EXISTS "Users can manage their organization memberships" ON user_organizations;

DROP POLICY IF EXISTS "Users can view summaries from their organizations" ON summaries;
DROP POLICY IF EXISTS "Users can create summaries in their organizations" ON summaries;
DROP POLICY IF EXISTS "Users can update their own summaries" ON summaries;
DROP POLICY IF EXISTS "Users can delete their own summaries" ON summaries;

DROP POLICY IF EXISTS "Users can view their notifications" ON notifications;
DROP POLICY IF EXISTS "Users can update their notifications" ON notifications;

-- PROFILES TABLE POLICIES
-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own profile (for new signups)
CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- ORGANIZATIONS TABLE POLICIES
-- Users can view organizations they belong to
CREATE POLICY "Users can view organizations they belong to" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT organization_id 
      FROM user_organizations 
      WHERE user_id = auth.uid()
    )
  );

-- Users can update organizations they own
CREATE POLICY "Users can update organizations they own" ON organizations
  FOR UPDATE USING (
    id IN (
      SELECT organization_id 
      FROM user_organizations 
      WHERE user_id = auth.uid() 
      AND role = 'owner'
    )
  );

-- Users can create organizations
CREATE POLICY "Users can create organizations" ON organizations
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- USER_ORGANIZATIONS TABLE POLICIES
-- Users can view their organization memberships
CREATE POLICY "Users can view their organization memberships" ON user_organizations
  FOR SELECT USING (
    user_id = auth.uid() OR 
    organization_id IN (
      SELECT organization_id 
      FROM user_organizations 
      WHERE user_id = auth.uid() 
      AND role IN ('owner', 'admin')
    )
  );

-- Users can manage memberships in organizations they own/admin
CREATE POLICY "Users can manage organization memberships" ON user_organizations
  FOR ALL USING (
    user_id = auth.uid() OR 
    organization_id IN (
      SELECT organization_id 
      FROM user_organizations 
      WHERE user_id = auth.uid() 
      AND role IN ('owner', 'admin')
    )
  );

-- SUMMARIES TABLE POLICIES
-- Users can view summaries from their organizations
CREATE POLICY "Users can view summaries from their organizations" ON summaries
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM user_organizations 
      WHERE user_id = auth.uid()
    )
  );

-- Users can create summaries in their organizations
CREATE POLICY "Users can create summaries in their organizations" ON summaries
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM user_organizations 
      WHERE user_id = auth.uid()
    ) AND
    user_id = auth.uid()
  );

-- Users can update their own summaries
CREATE POLICY "Users can update their own summaries" ON summaries
  FOR UPDATE USING (
    user_id = auth.uid() AND
    organization_id IN (
      SELECT organization_id 
      FROM user_organizations 
      WHERE user_id = auth.uid()
    )
  );

-- Users can delete their own summaries or if they're org admin
CREATE POLICY "Users can delete summaries" ON summaries
  FOR DELETE USING (
    user_id = auth.uid() OR
    organization_id IN (
      SELECT organization_id 
      FROM user_organizations 
      WHERE user_id = auth.uid() 
      AND role IN ('owner', 'admin')
    )
  );

-- NOTIFICATIONS TABLE POLICIES
-- Users can view their own notifications
CREATE POLICY "Users can view their notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid());

-- Users can update their own notifications (mark as read, etc.)
CREATE POLICY "Users can update their notifications" ON notifications
  FOR UPDATE USING (user_id = auth.uid());

-- Users can delete their own notifications
CREATE POLICY "Users can delete their notifications" ON notifications
  FOR DELETE USING (user_id = auth.uid());

-- SECURITY FUNCTIONS
-- Function to check if user is organization owner
CREATE OR REPLACE FUNCTION is_organization_owner(org_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM user_organizations 
    WHERE organization_id = org_id 
    AND user_organizations.user_id = is_organization_owner.user_id 
    AND role = 'owner'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is organization admin or owner
CREATE OR REPLACE FUNCTION is_organization_admin(org_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM user_organizations 
    WHERE organization_id = org_id 
    AND user_organizations.user_id = is_organization_admin.user_id 
    AND role IN ('owner', 'admin')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's organizations
CREATE OR REPLACE FUNCTION get_user_organizations(user_id UUID)
RETURNS TABLE(organization_id UUID, role TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT uo.organization_id, uo.role
  FROM user_organizations uo
  WHERE uo.user_id = get_user_organizations.user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- AUDIT TRIGGERS
-- Create audit log table
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name TEXT NOT NULL,
  operation TEXT NOT NULL,
  old_data JSONB,
  new_data JSONB,
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on audit logs
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Only admins can view audit logs
CREATE POLICY "Only admins can view audit logs" ON audit_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 
      FROM user_organizations 
      WHERE user_id = auth.uid() 
      AND role = 'owner'
    )
  );

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO audit_logs (table_name, operation, old_data, new_data, user_id)
  VALUES (
    TG_TABLE_NAME,
    TG_OP,
    CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END,
    auth.uid()
  );
  
  RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit triggers for important tables
DROP TRIGGER IF EXISTS audit_profiles ON profiles;
CREATE TRIGGER audit_profiles
  AFTER INSERT OR UPDATE OR DELETE ON profiles
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

DROP TRIGGER IF EXISTS audit_organizations ON organizations;
CREATE TRIGGER audit_organizations
  AFTER INSERT OR UPDATE OR DELETE ON organizations
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

DROP TRIGGER IF EXISTS audit_user_organizations ON user_organizations;
CREATE TRIGGER audit_user_organizations
  AFTER INSERT OR UPDATE OR DELETE ON user_organizations
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- RATE LIMITING TABLE
CREATE TABLE IF NOT EXISTS rate_limits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  endpoint TEXT NOT NULL,
  requests_count INTEGER DEFAULT 1,
  window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, endpoint, window_start)
);

-- Enable RLS on rate limits
ALTER TABLE rate_limits ENABLE ROW LEVEL SECURITY;

-- Users can only see their own rate limit data
CREATE POLICY "Users can view own rate limits" ON rate_limits
  FOR SELECT USING (user_id = auth.uid());

-- SECURITY VIEWS
-- Create a secure view for user profiles with organization info
CREATE OR REPLACE VIEW user_profile_with_orgs AS
SELECT 
  p.*,
  COALESCE(
    json_agg(
      json_build_object(
        'organization_id', o.id,
        'organization_name', o.name,
        'role', uo.role,
        'joined_at', uo.created_at
      )
    ) FILTER (WHERE o.id IS NOT NULL),
    '[]'::json
  ) as organizations
FROM profiles p
LEFT JOIN user_organizations uo ON p.id = uo.user_id
LEFT JOIN organizations o ON uo.organization_id = o.id
WHERE p.id = auth.uid()
GROUP BY p.id, p.email, p.full_name, p.avatar_url, p.created_at, p.updated_at;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
