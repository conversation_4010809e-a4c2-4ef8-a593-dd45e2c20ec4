export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON>son | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      summaries: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          title: string
          content: string
          source: string
          status: 'pending' | 'processing' | 'completed' | 'error'
          metadata: Json
          team_id?: string
          channel_id?: string
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          title: string
          content: string
          source: string
          status?: 'pending' | 'processing' | 'completed' | 'error'
          metadata?: Json
          team_id?: string
          channel_id?: string
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          title?: string
          content?: string
          source?: string
          status?: 'pending' | 'processing' | 'completed' | 'error'
          metadata?: Json
          team_id?: string
          channel_id?: string
        }
      }
      users: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          email: string
          name?: string
          avatar_url?: string
          metadata: <PERSON><PERSON>
          clerk_id: string
          stripe_customer_id?: string
          subscription_status?: 'active' | 'trialing' | 'past_due' | 'canceled' | 'incomplete'
          subscription_tier?: 'free' | 'pro' | 'enterprise'
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          email: string
          name?: string
          avatar_url?: string
          metadata?: Json
          clerk_id: string
          stripe_customer_id?: string
          subscription_status?: 'active' | 'trialing' | 'past_due' | 'canceled' | 'incomplete'
          subscription_tier?: 'free' | 'pro' | 'enterprise'
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          email?: string
          name?: string
          avatar_url?: string
          metadata?: Json
          clerk_id?: string
          stripe_customer_id?: string
          subscription_status?: 'active' | 'trialing' | 'past_due' | 'canceled' | 'incomplete'
          subscription_tier?: 'free' | 'pro' | 'enterprise'
        }
      }
      teams: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          owner_id: string
          metadata: Json
          slack_team_id?: string
          subscription_status?: 'active' | 'trialing' | 'past_due' | 'canceled' | 'incomplete'
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          owner_id: string
          metadata?: Json
          slack_team_id?: string
          subscription_status?: 'active' | 'trialing' | 'past_due' | 'canceled' | 'incomplete'
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          owner_id?: string
          metadata?: Json
          slack_team_id?: string
          subscription_status?: 'active' | 'trialing' | 'past_due' | 'canceled' | 'incomplete'
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
