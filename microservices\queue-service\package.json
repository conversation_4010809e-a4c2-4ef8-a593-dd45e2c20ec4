{"name": "@saas-kit/queue-service", "version": "1.0.0", "description": "Dedicated queue processing microservice for background jobs", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "docker:build": "docker build -t queue-service .", "docker:run": "docker run -p 3001:3001 queue-service", "deploy": "npm run build && npm run docker:build"}, "keywords": ["queue", "background-jobs", "microservice", "worker", "redis", "typescript"], "author": "SaaS Kit Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "ioredis": "^5.3.2", "bull": "^4.12.0", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "prom-client": "^15.1.0", "winston": "^3.11.0", "@supabase/supabase-js": "^2.38.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.0", "typescript": "^5.3.0", "tsx": "^4.6.0", "vitest": "^1.0.0", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0"}, "engines": {"node": ">=18.0.0"}}