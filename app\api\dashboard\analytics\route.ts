import { devLog } from '@/lib/console-cleaner';
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { getCurrentUser } from '@/lib/user-management';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '7d';

    // Calculate date range
    const now = new Date();
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

    // Initialize Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
  devLog.log('📄 Fetching real analytics data from Supabase');

    // Initialize data arrays
    let summaries: any[] = [];
    let exports: any[] = [];
    let files: any[] = [];

    try {
      // Get real data from Supabase with fallback to empty arrays
      const [summariesResult, exportsResult, filesResult] = await Promise.allSettled([
        supabase
          .from('summaries')
          .select('id, created_at, processing_time_ms, word_count, language, source_type')
          .gte('created_at', startDate.toISOString())
          .order('created_at', { ascending: false }),

        supabase
          .from('exports')
          .select('id, created_at, format, status')
          .gte('created_at', startDate.toISOString())
          .order('created_at', { ascending: false }),

        supabase
          .from('files')
          .select('id, created_at, file_type, file_size, status')
          .gte('created_at', startDate.toISOString())
          .order('created_at', { ascending: false })
      ]);

      // Extract data with fallbacks
      summaries = summariesResult.status === 'fulfilled' && summariesResult.value.data
        ? summariesResult.value.data
        : [];

      exports = exportsResult.status === 'fulfilled' && exportsResult.value.data
        ? exportsResult.value.data
        : [];

      files = filesResult.status === 'fulfilled' && filesResult.value.data
        ? filesResult.value.data
        : [];
  devLog.log(`📊 Analytics data: ${summaries.length} summaries, ${exports.length} exports, ${files.length} files`);

    } catch (supabaseError) {
      console.warn('Supabase query failed, using empty data:', supabaseError);
      // Data arrays already initialized as empty
    }

    // Calculate statistics
    const stats = {
      totalSummaries: summaries.length,
      monthlyUploads: files.length,
      totalUsers: 1, // Current user
      avgProcessingTime: summaries.length > 0 
        ? summaries.reduce((acc, s) => acc + (s.processing_time_ms || 0), 0) / summaries.length / 1000
        : 0,
      successRate: summaries.length > 0 
        ? (summaries.filter(s => s.processing_time_ms > 0).length / summaries.length) * 100
        : 100,
      totalExports: exports.length
    };

    // Generate chart data
    const chartData = {
      summaryTrends: generateTrendData(summaries, days),
      languageDistribution: generateLanguageDistribution(summaries),
      contentTypes: generateContentTypes(summaries),
      processingTimes: generateProcessingTimes(summaries)
    };

    return NextResponse.json({
      stats,
      chartData,
      timeRange
    });

  } catch (error) {
    console.error('Dashboard analytics error:', error);
    return NextResponse.json(
      { error: 'Failed to load analytics data' },
      { status: 500 }
    );
  }
}

function generateTrendData(summaries: any[], days: number) {
  const data = [];
  const now = new Date();
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    const dateStr = date.toISOString().split('T')[0];
    
    const daySummaries = summaries.filter(s => 
      s.created_at.startsWith(dateStr)
    );
    
    data.push({
      date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      summaries: daySummaries.length,
      uploads: daySummaries.filter(s => s.source_type === 'file').length
    });
  }
  
  return data;
}

function generateLanguageDistribution(summaries: any[]) {
  const languages: Record<string, number> = {};
  
  summaries.forEach(s => {
    const lang = s.language || 'en';
    languages[lang] = (languages[lang] || 0) + 1;
  });

  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];
  
  return Object.entries(languages).map(([language, count], index) => ({
    language: getLanguageName(language),
    count,
    color: colors[index % colors.length]
  }));
}

function generateContentTypes(summaries: any[]) {
  const types: Record<string, number> = {};
  
  summaries.forEach(s => {
    const type = s.source_type || 'manual';
    types[type] = (types[type] || 0) + 1;
  });

  return Object.entries(types).map(([type, count]) => ({
    type: type.charAt(0).toUpperCase() + type.slice(1),
    count
  }));
}

function generateProcessingTimes(summaries: any[]) {
  const hours = ['00', '04', '08', '12', '16', '20'];
  
  return hours.map(hour => {
    const hourSummaries = summaries.filter(s => {
      const createdHour = new Date(s.created_at).getHours();
      return createdHour >= parseInt(hour) && createdHour < parseInt(hour) + 4;
    });
    
    const avgTime = hourSummaries.length > 0
      ? hourSummaries.reduce((acc, s) => acc + (s.processing_time_ms || 0), 0) / hourSummaries.length / 1000
      : 0;
    
    return {
      hour: `${hour}:00`,
      avgTime: Math.round(avgTime * 100) / 100
    };
  });
}

function getLanguageName(code: string): string {
  const languages: Record<string, string> = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'pt': 'Portuguese',
    'it': 'Italian',
    'ru': 'Russian',
    'zh': 'Chinese',
    'ja': 'Japanese',
    'ko': 'Korean'
  };
  
  return languages[code] || code.toUpperCase();
}
