'use client';

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence, PanInfo } from 'framer-motion';
import { ChevronLeft, ChevronRight, Grip, Smartphone, Tablet, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';

interface GridItem {
  id: string;
  component: React.ReactNode;
  span?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  priority?: number; // Higher priority items appear first on mobile
  lazy?: boolean; // Enable lazy loading for this item
  placeholder?: React.ReactNode; // Placeholder while loading
}

interface MobileOptimizedGridProps {
  items: GridItem[];
  className?: string;
  enableSwipe?: boolean;
  enableReorder?: boolean;
  enableLazyLoading?: boolean;
  onReorder?: (items: GridItem[]) => void;
  onItemVisible?: (itemId: string) => void;
}

export function MobileOptimizedGrid({
  items,
  className = '',
  enableSwipe = true,
  enableReorder = false,
  enableLazyLoading = true,
  onReorder,
  onItemVisible
}: MobileOptimizedGridProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [orderedItems, setOrderedItems] = useState(items);
  const [loadedItems, setLoadedItems] = useState<Set<string>>(new Set());
  const containerRef = useRef<HTMLDivElement>(null);

  // Enhanced responsive breakpoint detection
  useEffect(() => {
    const checkViewport = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    checkViewport();
    window.addEventListener('resize', checkViewport);
    return () => window.removeEventListener('resize', checkViewport);
  }, []);

  // Memoized sorted items for performance
  const sortedItems = useMemo(() => {
    return [...orderedItems].sort((a, b) => {
      if (isMobile) {
        return (b.priority || 0) - (a.priority || 0);
      }
      return 0;
    });
  }, [orderedItems, isMobile]);

  // Lazy loading callback
  const handleItemVisible = useCallback((itemId: string) => {
    setLoadedItems(prev => new Set([...prev, itemId]));
    onItemVisible?.(itemId);
  }, [onItemVisible]);

  // Lazy loaded grid item component
  const LazyGridItem = ({ item, index }: { item: GridItem; index: number }) => {
    const { ref, shouldLoad } = useLazyLoad(enableLazyLoading && item.lazy);

    useEffect(() => {
      if (shouldLoad && !loadedItems.has(item.id)) {
        handleItemVisible(item.id);
      }
    }, [shouldLoad, item.id]);

    const getGridSpan = () => {
      if (isMobile) return item.span?.mobile || 1;
      if (isTablet) return item.span?.tablet || 1;
      return item.span?.desktop || 1;
    };

    return (
      <motion.div
        ref={ref}
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3, delay: index * 0.1 }}
        className={`
          ${isMobile ? 'col-span-full' : `col-span-${getGridSpan()}`}
          ${enableReorder && draggedItem === item.id ? 'z-50' : ''}
        `}
        drag={enableReorder}
        dragConstraints={containerRef}
        onDragStart={() => setDraggedItem(item.id)}
        onDragEnd={() => setDraggedItem(null)}
        whileDrag={{ scale: 1.05, rotate: 2 }}
      >
        {shouldLoad || !item.lazy ? (
          item.component
        ) : (
          item.placeholder || (
            <div className="w-full h-32 bg-gray-100 dark:bg-gray-800 rounded-lg animate-pulse flex items-center justify-center">
              <div className="text-gray-400 text-sm">Loading...</div>
            </div>
          )
        )}
      </motion.div>
    );
  };

  useEffect(() => {
    setOrderedItems(items);
  }, [items]);

  // Enhanced swipe handling with momentum and velocity
  const handleSwipe = useCallback((direction: 'left' | 'right') => {
    if (!isMobile || !enableSwipe) return;

    if (direction === 'left' && currentIndex < sortedItems.length - 1) {
      setCurrentIndex(prev => prev + 1);
    } else if (direction === 'right' && currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
    }
  }, [isMobile, enableSwipe, currentIndex, sortedItems.length]);

  const handlePanEnd = useCallback((event: any, info: PanInfo) => {
    if (!enableSwipe || !isMobile) return;

    const threshold = 50;
    const velocity = Math.abs(info.velocity.x);
    const offset = info.offset.x;

    // Consider both offset and velocity for more responsive swiping
    const shouldSwipe = Math.abs(offset) > threshold || velocity > 500;

    if (shouldSwipe) {
      if (offset > 0 || (velocity > 500 && info.velocity.x > 0)) {
        handleSwipe('right');
      } else if (offset < 0 || (velocity > 500 && info.velocity.x < 0)) {
        handleSwipe('left');
      }
    }
  }, [enableSwipe, isMobile, handleSwipe]);

  const handleReorderDragEnd = (event: any, info: PanInfo, itemId: string) => {
    if (!enableReorder) return;
    
    const draggedIndex = orderedItems.findIndex(item => item.id === itemId);
    const containerRect = containerRef.current?.getBoundingClientRect();
    
    if (!containerRect) return;
    
    // Calculate drop position based on mouse position
    const dropY = event.clientY - containerRect.top;
    const itemHeight = containerRect.height / orderedItems.length;
    const dropIndex = Math.floor(dropY / itemHeight);
    
    if (dropIndex !== draggedIndex && dropIndex >= 0 && dropIndex < orderedItems.length) {
      const newItems = [...orderedItems];
      const [draggedItem] = newItems.splice(draggedIndex, 1);
      newItems.splice(dropIndex, 0, draggedItem);
      
      setOrderedItems(newItems);
      if (onReorder) {
        onReorder(newItems);
      }
    }
    
    setDraggedItem(null);
  };

  const getGridClasses = () => {
    if (isMobile && enableSwipe) {
      return 'flex overflow-hidden';
    }
    return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6';
  };

  const getItemClasses = (item: GridItem) => {
    if (isMobile && enableSwipe) {
      return 'flex-shrink-0 w-full px-2';
    }
    
    const { span = {} } = item;
    const mobileSpan = span.mobile || 1;
    const tabletSpan = span.tablet || 1;
    const desktopSpan = span.desktop || 1;
    
    return `col-span-${mobileSpan} md:col-span-${tabletSpan} lg:col-span-${desktopSpan}`;
  };



  return (
    <div className={`relative ${className}`}>
      {/* Enhanced mobile navigation with device indicators */}
      {isMobile && enableSwipe && sortedItems.length > 1 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div className="flex items-center space-x-2">
            <Smartphone className="h-4 w-4 text-blue-600" />
            <span className="text-xs text-gray-600 dark:text-gray-400">Mobile View</span>
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSwipe('right')}
              disabled={currentIndex === 0}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div className="flex space-x-1">
              {sortedItems.map((_, index) => (
                <motion.button
                  key={index}
                  className={`h-2 w-2 rounded-full transition-all duration-200 ${
                    index === currentIndex
                      ? 'bg-blue-600 scale-125'
                      : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400'
                  }`}
                  onClick={() => setCurrentIndex(index)}
                  whileTap={{ scale: 0.8 }}
                />
              ))}
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSwipe('left')}
              disabled={currentIndex === sortedItems.length - 1}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="text-xs text-gray-500">
            {currentIndex + 1} / {sortedItems.length}
          </div>
        </motion.div>
      )}

      {/* Tablet/Desktop view indicator */}
      {!isMobile && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex items-center space-x-2 mb-4 text-xs text-gray-500"
        >
          {isTablet ? (
            <>
              <Tablet className="h-4 w-4 text-green-600" />
              <span>Tablet View</span>
            </>
          ) : (
            <>
              <Monitor className="h-4 w-4 text-purple-600" />
              <span>Desktop View</span>
            </>
          )}
          <span>•</span>
          <span>{sortedItems.length} items</span>
          {enableLazyLoading && (
            <>
              <span>•</span>
              <span>{loadedItems.size} loaded</span>
            </>
          )}
        </motion.div>
      )}

      {/* Enhanced grid container with touch support */}
      <motion.div
        ref={containerRef}
        className={getGridClasses()}
        style={isMobile && enableSwipe ? {
          transform: `translateX(-${currentIndex * 100}%)`,
          transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        } : {}}
        onPanEnd={isMobile && enableSwipe ? handlePanEnd : undefined}
        drag={isMobile && enableSwipe ? "x" : false}
        dragConstraints={{ left: 0, right: 0 }}
        dragElastic={0.1}
        whileDrag={{ cursor: 'grabbing' }}
      >
        <AnimatePresence mode="wait">
          {sortedItems.map((item, index) => (
            <LazyGridItem
              key={item.id}
              item={item}
              index={index}
            />
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Mobile swipe hint */}
      {isMobile && enableSwipe && sortedItems.length > 1 && currentIndex === 0 && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0 }}
          className="absolute top-1/2 right-4 transform -translate-y-1/2 pointer-events-none"
        >
          <div className="bg-black/70 text-white text-xs px-2 py-1 rounded-lg flex items-center space-x-1">
            <span>Swipe</span>
            <ChevronLeft className="h-3 w-3" />
          </div>
        </motion.div>
      )}
    </div>
  );
}

// Hook for managing grid state
export function useGridState(initialItems: GridItem[]) {
  const [items, setItems] = useState(initialItems);
  const [isReordering, setIsReordering] = useState(false);

  const updateItem = (id: string, updates: Partial<GridItem>) => {
    setItems(prev => prev.map(item => 
      item.id === id ? { ...item, ...updates } : item
    ));
  };

  const addItem = (item: GridItem, position?: number) => {
    setItems(prev => {
      if (position !== undefined) {
        const newItems = [...prev];
        newItems.splice(position, 0, item);
        return newItems;
      }
      return [...prev, item];
    });
  };

  const removeItem = (id: string) => {
    setItems(prev => prev.filter(item => item.id !== id));
  };

  const reorderItems = (newItems: GridItem[]) => {
    setItems(newItems);
  };

  const toggleReordering = () => {
    setIsReordering(!isReordering);
  };

  return {
    items,
    isReordering,
    updateItem,
    addItem,
    removeItem,
    reorderItems,
    toggleReordering
  };
}

// Responsive breakpoint hook
export function useResponsiveBreakpoint() {
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setBreakpoint('mobile');
      } else if (width < 1024) {
        setBreakpoint('tablet');
      } else {
        setBreakpoint('desktop');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
}
