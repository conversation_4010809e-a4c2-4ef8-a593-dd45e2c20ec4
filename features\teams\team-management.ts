/**
 * Team Management Core Functions
 * 
 * Core functionality for managing team members, roles, and organization settings
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';
import { 
  TeamMember, 
  TeamRole, 
  OrganizationSettings, 
  TeamStats,
  PLAN_LIMITS 
} from './types';
import { checkPermission, validateRoleChange } from './rbac';

/**
 * Get all team members for an organization
 */
export async function getTeamMembers(
  organizationId: string,
  requesterId?: string
): Promise<{ members: TeamMember[]; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Check permissions if requester is specified
    if (requesterId) {
      const canView = await checkPermission(requesterId, organizationId, 'settings:view');
      if (!canView) {
        return { members: [], error: 'Insufficient permissions' };
      }
    }

    const { data: members, error } = await supabase
      .from('user_organizations')
      .select(`
        *,
        user:profiles!user_organizations_user_id_fkey (
          id,
          email,
          full_name,
          avatar_url
        )
      `)
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: true });

    if (error) {
      return { members: [], error: error.message };
    }

    const formattedMembers: TeamMember[] = members.map(member => ({
      id: member.id,
      userId: member.user_id,
      organizationId: member.organization_id,
      role: member.role,
      permissions: member.permissions || {},
      invitedBy: member.invited_by,
      invitedAt: member.invited_at,
      acceptedAt: member.accepted_at,
      createdAt: member.created_at,
      updatedAt: member.updated_at,
      user: {
        id: member.user.id,
        email: member.user.email,
        fullName: member.user.full_name,
        avatarUrl: member.user.avatar_url
      }
    }));

    return { members: formattedMembers };

  } catch (error) {
    console.error('Failed to get team members:', error);
    return { members: [], error: 'Failed to get team members' };
  }
}

/**
 * Update team member role
 */
export async function updateMemberRole(
  organizationId: string,
  memberId: string,
  newRole: TeamRole,
  requesterId: string,
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get member details
    const { data: member } = await supabase
      .from('user_organizations')
      .select('user_id, role')
      .eq('id', memberId)
      .eq('organization_id', organizationId)
      .single();

    if (!member) {
      return { success: false, error: 'Member not found' };
    }

    // Validate role change
    const validation = await validateRoleChange(
      requesterId,
      organizationId,
      member.user_id,
      newRole
    );

    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    // Update role
    const { error } = await supabase
      .from('user_organizations')
      .update({
        role: newRole,
        updated_at: new Date().toISOString()
      })
      .eq('id', memberId);

    if (error) {
      return { success: false, error: error.message };
    }

    // Log activity
    await logTeamActivity(
      organizationId,
      requesterId,
      'role_changed',
      'member',
      memberId,
      {
        targetUserId: member.user_id,
        oldRole: member.role,
        newRole,
        reason
      }
    );

    return { success: true };

  } catch (error) {
    console.error('Failed to update member role:', error);
    return { success: false, error: 'Failed to update member role' };
  }
}

/**
 * Remove team member
 */
export async function removeMember(
  organizationId: string,
  memberId: string,
  requesterId: string,
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Check permissions
    const canRemove = await checkPermission(requesterId, organizationId, 'members:remove');
    if (!canRemove) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Get member details
    const { data: member } = await supabase
      .from('user_organizations')
      .select('user_id, role')
      .eq('id', memberId)
      .eq('organization_id', organizationId)
      .single();

    if (!member) {
      return { success: false, error: 'Member not found' };
    }

    // Prevent removing the last owner
    if (member.role === 'owner') {
      const { data: owners } = await supabase
        .from('user_organizations')
        .select('id')
        .eq('organization_id', organizationId)
        .eq('role', 'owner');

      if (owners && owners.length <= 1) {
        return { success: false, error: 'Cannot remove the last owner' };
      }
    }

    // Remove member
    const { error } = await supabase
      .from('user_organizations')
      .delete()
      .eq('id', memberId);

    if (error) {
      return { success: false, error: error.message };
    }

    // Log activity
    await logTeamActivity(
      organizationId,
      requesterId,
      'member_removed',
      'member',
      memberId,
      {
        targetUserId: member.user_id,
        role: member.role,
        reason
      }
    );

    return { success: true };

  } catch (error) {
    console.error('Failed to remove member:', error);
    return { success: false, error: 'Failed to remove member' };
  }
}

/**
 * Get organization settings
 */
export async function getOrganizationSettings(
  organizationId: string,
  requesterId?: string
): Promise<{ settings: OrganizationSettings | null; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Check permissions if requester is specified
    if (requesterId) {
      const canView = await checkPermission(requesterId, organizationId, 'settings:view');
      if (!canView) {
        return { settings: null, error: 'Insufficient permissions' };
      }
    }

    const { data: organization, error } = await supabase
      .from('organizations')
      .select('name, settings, plan, max_members')
      .eq('id', organizationId)
      .single();

    if (error) {
      return { settings: null, error: error.message };
    }

    // Merge with default settings
    const defaultSettings: OrganizationSettings = {
      name: organization.name,
      allowMemberInvites: true,
      requireApprovalForInvites: false,
      defaultMemberRole: 'viewer',
      maxMembers: organization.max_members || PLAN_LIMITS[organization.plan as keyof typeof PLAN_LIMITS]?.maxMembers || 3,
      allowWorkspaceSharing: true,
      defaultWorkspacePermissions: {
        canSummarize: true,
        canExport: false,
        canViewHistory: true,
        canManageSettings: false
      },
      defaultSummaryVisibility: 'team',
      allowExternalSharing: false,
      retentionDays: 365,
      emailNotifications: {
        newMember: true,
        newSummary: false,
        weeklyDigest: true,
        billingUpdates: true
      },
      enabledIntegrations: [],
      slackNotifications: {
        enabled: false,
        events: []
      }
    };

    const settings = {
      ...defaultSettings,
      ...organization.settings
    };

    return { settings };

  } catch (error) {
    console.error('Failed to get organization settings:', error);
    return { settings: null, error: 'Failed to get organization settings' };
  }
}

/**
 * Update organization settings
 */
export async function updateOrganizationSettings(
  organizationId: string,
  settings: Partial<OrganizationSettings>,
  requesterId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Check permissions
    const canEdit = await checkPermission(requesterId, organizationId, 'settings:edit');
    if (!canEdit) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Get current settings
    const { settings: currentSettings } = await getOrganizationSettings(organizationId);
    if (!currentSettings) {
      return { success: false, error: 'Failed to get current settings' };
    }

    // Merge settings
    const updatedSettings = {
      ...currentSettings,
      ...settings
    };

    // Update organization
    const { error } = await supabase
      .from('organizations')
      .update({
        name: updatedSettings.name,
        settings: updatedSettings,
        updated_at: new Date().toISOString()
      })
      .eq('id', organizationId);

    if (error) {
      return { success: false, error: error.message };
    }

    // Log activity
    await logTeamActivity(
      organizationId,
      requesterId,
      'settings_updated',
      'organization',
      organizationId,
      { updatedFields: Object.keys(settings) }
    );

    return { success: true };

  } catch (error) {
    console.error('Failed to update organization settings:', error);
    return { success: false, error: 'Failed to update organization settings' };
  }
}

/**
 * Get team statistics
 */
export async function getTeamStats(
  organizationId: string,
  requesterId?: string
): Promise<{ stats: TeamStats | null; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Check permissions if requester is specified
    if (requesterId) {
      const canView = await checkPermission(requesterId, organizationId, 'analytics:view');
      if (!canView) {
        return { stats: null, error: 'Insufficient permissions' };
      }
    }

    // Get member counts
    const { data: members } = await supabase
      .from('user_organizations')
      .select('role, created_at')
      .eq('organization_id', organizationId);

    // Get workspace count
    const { data: workspaces } = await supabase
      .from('shared_workspaces')
      .select('id')
      .eq('organization_id', organizationId);

    // Get summary counts
    const { data: summaries } = await supabase
      .from('summaries')
      .select('created_at')
      .eq('organization_id', organizationId);

    // Calculate stats
    const totalMembers = members?.length || 0;
    const membersByRole = members?.reduce((acc, member) => {
      acc[member.role as TeamRole] = (acc[member.role as TeamRole] || 0) + 1;
      return acc;
    }, {
      owner: 0,
      admin: 0,
      editor: 0,
      viewer: 0
    } as Record<TeamRole, number>) || {
      owner: 0,
      admin: 0,
      editor: 0,
      viewer: 0
    };

    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const summariesThisMonth = summaries?.filter(
      s => new Date(s.created_at) > thirtyDaysAgo
    ).length || 0;

    const stats: TeamStats = {
      totalMembers,
      membersByRole,
      totalWorkspaces: workspaces?.length || 0,
      totalSummaries: summaries?.length || 0,
      summariesThisMonth,
      activeMembers: totalMembers, // Simplified for now
      storageUsed: 0, // Would need to calculate actual storage
      apiCallsThisMonth: 0 // Would need to track API calls
    };

    return { stats };

  } catch (error) {
    console.error('Failed to get team stats:', error);
    return { stats: null, error: 'Failed to get team stats' };
  }
}

/**
 * Log team activity
 */
async function logTeamActivity(
  organizationId: string,
  userId: string,
  action: string,
  target: string,
  targetId?: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();
    
    await supabase
      .from('team_activity')
      .insert({
        organization_id: organizationId,
        user_id: userId,
        action,
        target,
        target_id: targetId,
        metadata,
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('Failed to log team activity:', error);
  }
}
