'use client';

import React, { useEffect, useState } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';
import { initPostHog, analytics } from '@/lib/posthog.client';

interface PostHogProviderProps {
  children: React.ReactNode;
}

export default function PostHogProvider({ children }: PostHogProviderProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { userId, isSignedIn } = useAuth();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialize PostHog
    const initializeAnalytics = async () => {
      try {
        if (typeof window !== 'undefined') {
          initPostHog();
          setIsInitialized(true);
        }
      } catch (error) {
        console.warn('PostHog initialization failed:', error);
        setIsInitialized(true);
      }
    };

    initializeAnalytics();
  }, []);

  // Identify user when signed in
  useEffect(() => {
    if (isInitialized && isSignedIn && userId) {
      analytics.identify(userId, {
        signed_in: true,
        auth_provider: 'clerk'
      });
    } else if (isInitialized && !isSignedIn) {
      analytics.reset();
    }
  }, [isInitialized, isSignedIn, userId]);

  // Track page views
  useEffect(() => {
    if (isInitialized && pathname) {
      try {
        const url = pathname + (searchParams?.toString() ? `?${searchParams.toString()}` : '');
        analytics.pageview(url);
      } catch (error) {
        console.warn('Page view tracking failed:', error);
      }
    }
  }, [pathname, searchParams, isInitialized]);

  return <>{children}</>;
}
