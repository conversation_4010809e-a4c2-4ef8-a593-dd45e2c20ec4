name: 🐛 Bug Report
description: Report a bug or unexpected behavior
title: "[Bug]: "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug! Please fill out the form below with as much detail as possible.

  - type: checkboxes
    id: checklist
    attributes:
      label: Pre-submission Checklist
      description: Please verify the following before submitting
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have read the [Contributing Guidelines](../CONTRIBUTING.md)
          required: true
        - label: I have tested this on the latest version
          required: true

  - type: dropdown
    id: package
    attributes:
      label: Package/Module
      description: Which package or module is affected?
      options:
        - "apps/slack-summary-scribe (Main App)"
        - "packages/integration-sdk"
        - "packages/admin-ui"
        - "packages/drip-campaign-engine"
        - "packages/ui-kit"
        - "tools/eslint-config"
        - "tools/typescript-config"
        - "Other/Unknown"
    validations:
      required: true

  - type: dropdown
    id: severity
    attributes:
      label: Severity
      description: How severe is this bug?
      options:
        - "Critical (Blocks core functionality)"
        - "High (Significant impact)"
        - "Medium (Moderate impact)"
        - "Low (Minor issue)"
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '...'
        3. Scroll down to '...'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: What you expected to happen
      placeholder: Describe what should happen...
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: What actually happened
      placeholder: Describe what actually happened...
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: Environment
      description: Please provide your environment details
      placeholder: |
        - OS: [e.g. macOS 14.0, Windows 11, Ubuntu 22.04]
        - Node.js version: [e.g. 18.17.0]
        - npm version: [e.g. 9.8.1]
        - Browser: [e.g. Chrome 119, Safari 17]
        - Package version: [e.g. 1.0.0]
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Error Logs
      description: Please include any relevant error logs or console output
      placeholder: Paste error logs here...
      render: shell

  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your problem
      placeholder: Drag and drop screenshots here...

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context about the problem here
      placeholder: Any additional information...

  - type: checkboxes
    id: contribution
    attributes:
      label: Contribution
      description: Would you like to contribute to fixing this bug?
      options:
        - label: I would like to work on fixing this bug
        - label: I can provide additional testing
        - label: I can help with documentation updates
