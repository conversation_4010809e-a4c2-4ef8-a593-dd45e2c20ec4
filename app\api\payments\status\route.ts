/**
 * Payment Gateway Status API Route
 * Check health and status of payment gateways
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { getPaymentGatewayStatus } from '@/lib/dual-payment-gateway';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/payments/status
 * Get payment gateway status and health
 */
export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;
    

    const status = await getPaymentGatewayStatus();

    return NextResponse.json({
      success: true,
      ...status,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Payment status error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to get payment status' },
      { status: 500 }
    );
  }
}
