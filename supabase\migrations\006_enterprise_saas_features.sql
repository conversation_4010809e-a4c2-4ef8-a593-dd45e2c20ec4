-- =====================================================
-- ENTERPRISE SAAS FEATURES - DATABASE SCHEMA
-- Comprehensive schema for advanced SaaS functionality
-- =====================================================

-- 1. Enhanced Subscriptions Table
CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  
  -- Subscription Details
  subscription_tier TEXT NOT NULL CHECK (subscription_tier IN ('FREE', 'PRO', 'ENTERPRISE')) DEFAULT 'FREE',
  status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'past_due', 'incomplete', 'trialing', 'unpaid')) DEFAULT 'active',
  
  -- Stripe Integration
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT,
  stripe_price_id TEXT,
  
  -- Cashfree Integration (Fallback)
  cashfree_customer_id TEXT,
  cashfree_subscription_id TEXT,
  cashfree_order_id TEXT,
  
  -- Billing Periods
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  trial_start TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  
  -- Subscription Management
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  canceled_at TIMESTAMP WITH TIME ZONE,
  
  -- Usage Limits
  monthly_summary_limit INTEGER DEFAULT 10, -- FREE: 10, PRO: 500, ENTERPRISE: unlimited
  monthly_summaries_used INTEGER DEFAULT 0,
  
  -- Metadata
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(user_id), -- One subscription per user
  UNIQUE(stripe_subscription_id),
  UNIQUE(cashfree_subscription_id)
);

-- 2. Team Management (Enterprise Feature)
CREATE TABLE IF NOT EXISTS teams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  
  -- Team Limits (based on subscription)
  max_members INTEGER DEFAULT 5, -- PRO: 5, ENTERPRISE: unlimited
  current_members INTEGER DEFAULT 1,
  
  -- Team Settings
  settings JSONB DEFAULT '{}',
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Team Memberships
CREATE TABLE IF NOT EXISTS team_memberships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Role-based Access Control
  role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member', 'viewer')) DEFAULT 'member',
  
  -- Permissions
  permissions JSONB DEFAULT '{}',
  
  -- Invitation Management
  invited_by UUID REFERENCES auth.users(id),
  invited_at TIMESTAMP WITH TIME ZONE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Status
  status TEXT NOT NULL CHECK (status IN ('active', 'pending', 'suspended')) DEFAULT 'active',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(team_id, user_id)
);

-- 4. Scheduled Posts (Slack Auto-Posting)
CREATE TABLE IF NOT EXISTS scheduled_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  
  -- Scheduling Details
  name TEXT NOT NULL,
  description TEXT,
  schedule_type TEXT NOT NULL CHECK (schedule_type IN ('daily', 'weekly', 'monthly', 'custom')),
  cron_expression TEXT, -- For custom schedules
  timezone TEXT DEFAULT 'UTC',
  
  -- Slack Configuration
  slack_channel_id TEXT NOT NULL,
  slack_channel_name TEXT NOT NULL,
  slack_workspace_id TEXT NOT NULL,
  
  -- Template Configuration
  template_id UUID REFERENCES post_templates(id),
  custom_template TEXT,
  
  -- Filters and Conditions
  summary_filters JSONB DEFAULT '{}', -- Filter which summaries to post
  
  -- Status and Execution
  is_active BOOLEAN DEFAULT TRUE,
  last_executed_at TIMESTAMP WITH TIME ZONE,
  next_execution_at TIMESTAMP WITH TIME ZONE,
  execution_count INTEGER DEFAULT 0,
  
  -- Metadata
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Post Templates
CREATE TABLE IF NOT EXISTS post_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  
  -- Template Details
  name TEXT NOT NULL,
  description TEXT,
  template_content TEXT NOT NULL, -- Handlebars/Mustache template
  
  -- Template Type
  template_type TEXT NOT NULL CHECK (template_type IN ('slack', 'email', 'webhook')) DEFAULT 'slack',
  
  -- Template Variables
  variables JSONB DEFAULT '{}', -- Available template variables
  
  -- Sharing and Visibility
  is_public BOOLEAN DEFAULT FALSE,
  is_system_template BOOLEAN DEFAULT FALSE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. CRM Integrations
CREATE TABLE IF NOT EXISTS crm_integrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  
  -- CRM Provider
  provider TEXT NOT NULL CHECK (provider IN ('hubspot', 'salesforce', 'notion', 'pipedrive', 'airtable')),
  
  -- OAuth Tokens
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  token_expires_at TIMESTAMP WITH TIME ZONE,
  
  -- Provider-specific Configuration
  provider_config JSONB DEFAULT '{}', -- Store provider-specific settings
  
  -- Integration Settings
  auto_sync_enabled BOOLEAN DEFAULT TRUE,
  sync_frequency TEXT DEFAULT 'daily' CHECK (sync_frequency IN ('realtime', 'hourly', 'daily', 'weekly')),
  
  -- Mapping Configuration
  field_mappings JSONB DEFAULT '{}', -- Map summary fields to CRM fields
  
  -- Status
  is_active BOOLEAN DEFAULT TRUE,
  last_sync_at TIMESTAMP WITH TIME ZONE,
  sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'syncing', 'success', 'error')),
  sync_error TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, provider) -- One integration per provider per user
);

-- 7. Analytics and Usage Tracking
CREATE TABLE IF NOT EXISTS usage_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  
  -- Event Details
  event_type TEXT NOT NULL, -- 'summary_created', 'ai_model_used', 'export_generated', etc.
  event_data JSONB DEFAULT '{}',
  
  -- AI Model Usage
  ai_model_used TEXT,
  tokens_consumed INTEGER DEFAULT 0,
  processing_time_ms INTEGER DEFAULT 0,
  
  -- Resource Usage
  api_endpoint TEXT,
  response_status INTEGER,
  
  -- Billing and Costs
  cost_cents INTEGER DEFAULT 0, -- Cost in cents
  
  -- Metadata
  user_agent TEXT,
  ip_address INET,
  session_id TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Audit Logs (Enterprise Security)
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  
  -- Event Details
  event_type TEXT NOT NULL, -- 'user_login', 'data_export', 'settings_changed', etc.
  event_category TEXT NOT NULL CHECK (event_category IN ('auth', 'data', 'admin', 'security', 'billing')),
  
  -- Event Data
  resource_type TEXT, -- 'user', 'summary', 'team', etc.
  resource_id TEXT,
  action TEXT NOT NULL, -- 'create', 'read', 'update', 'delete'
  
  -- Change Tracking
  old_values JSONB,
  new_values JSONB,
  
  -- Context
  ip_address INET,
  user_agent TEXT,
  session_id TEXT,
  
  -- Risk Assessment
  risk_level TEXT DEFAULT 'low' CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Payment Methods (Dual Gateway Support)
CREATE TABLE IF NOT EXISTS payment_methods (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Payment Gateway
  provider TEXT NOT NULL CHECK (provider IN ('stripe', 'cashfree')),
  provider_customer_id TEXT NOT NULL,
  provider_payment_method_id TEXT,
  
  -- Card Details (for display)
  card_brand TEXT,
  card_last_four TEXT,
  card_exp_month INTEGER,
  card_exp_year INTEGER,
  
  -- Billing Address
  billing_address JSONB DEFAULT '{}',
  
  -- Status
  is_default BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Metadata
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. Webhook Events (for payment processing)
CREATE TABLE IF NOT EXISTS webhook_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Webhook Details
  provider TEXT NOT NULL CHECK (provider IN ('stripe', 'cashfree', 'slack', 'hubspot')),
  event_type TEXT NOT NULL,
  event_id TEXT NOT NULL, -- Provider's event ID
  
  -- Processing Status
  status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'processed', 'failed', 'retrying')) DEFAULT 'pending',
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  
  -- Event Data
  event_data JSONB NOT NULL,
  
  -- Processing Results
  processed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(provider, event_id) -- Prevent duplicate processing
);

-- 11. SSO Configuration (Enterprise)
CREATE TABLE IF NOT EXISTS sso_configurations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  enforce_sso BOOLEAN DEFAULT false,
  allow_email_password BOOLEAN DEFAULT true,
  session_timeout INTEGER DEFAULT 480, -- minutes
  require_mfa BOOLEAN DEFAULT false,
  allowed_domains TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(organization_id)
);

-- 12. SSO Providers (Enterprise)
CREATE TABLE IF NOT EXISTS sso_providers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('saml', 'oidc', 'oauth')),
  enabled BOOLEAN DEFAULT true,
  configuration JSONB NOT NULL DEFAULT '{}',
  domains TEXT[] DEFAULT '{}',
  auto_provision BOOLEAN DEFAULT false,
  default_role TEXT DEFAULT 'member' CHECK (default_role IN ('admin', 'member')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduled_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE crm_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE sso_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE sso_providers ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- RLS POLICIES
-- =====================================================

-- Subscriptions Policies
CREATE POLICY "Users can view their own subscription" ON subscriptions
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own subscription" ON subscriptions
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Service role can manage all subscriptions" ON subscriptions
  FOR ALL TO service_role USING (true) WITH CHECK (true);

-- Teams Policies
CREATE POLICY "Users can view teams in their organizations" ON teams
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id FROM user_organizations
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization admins can manage teams" ON teams
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id FROM user_organizations
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

-- Team Memberships Policies
CREATE POLICY "Users can view team memberships for their teams" ON team_memberships
  FOR SELECT USING (
    team_id IN (
      SELECT t.id FROM teams t
      JOIN user_organizations uo ON t.organization_id = uo.organization_id
      WHERE uo.user_id = auth.uid()
    )
  );

CREATE POLICY "Team admins can manage memberships" ON team_memberships
  FOR ALL USING (
    team_id IN (
      SELECT tm.team_id FROM team_memberships tm
      WHERE tm.user_id = auth.uid() AND tm.role IN ('owner', 'admin')
    )
  );

-- Scheduled Posts Policies
CREATE POLICY "Users can manage their own scheduled posts" ON scheduled_posts
  FOR ALL USING (user_id = auth.uid());

-- Post Templates Policies
CREATE POLICY "Users can view public templates and their own templates" ON post_templates
  FOR SELECT USING (
    is_public = true OR
    user_id = auth.uid() OR
    organization_id IN (
      SELECT organization_id FROM user_organizations
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage their own templates" ON post_templates
  FOR ALL USING (user_id = auth.uid());

-- CRM Integrations Policies
CREATE POLICY "Users can manage their own CRM integrations" ON crm_integrations
  FOR ALL USING (user_id = auth.uid());

-- Usage Analytics Policies
CREATE POLICY "Users can view their own analytics" ON usage_analytics
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Organization admins can view team analytics" ON usage_analytics
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id FROM user_organizations
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

CREATE POLICY "Service role can insert analytics" ON usage_analytics
  FOR INSERT TO service_role WITH CHECK (true);

-- Audit Logs Policies (Enterprise only)
CREATE POLICY "Organization owners can view audit logs" ON audit_logs
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id FROM user_organizations
      WHERE user_id = auth.uid() AND role = 'owner'
    )
  );

CREATE POLICY "Service role can insert audit logs" ON audit_logs
  FOR INSERT TO service_role WITH CHECK (true);

-- Payment Methods Policies
CREATE POLICY "Users can manage their own payment methods" ON payment_methods
  FOR ALL USING (user_id = auth.uid());

-- Webhook Events Policies (Service role only)
CREATE POLICY "Service role can manage webhook events" ON webhook_events
  FOR ALL TO service_role USING (true) WITH CHECK (true);

-- SSO Configuration Policies (Enterprise only)
CREATE POLICY "Organization owners can manage SSO config" ON sso_configurations
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id FROM user_organizations
      WHERE user_id = auth.uid() AND role = 'owner'
    )
  );

-- SSO Providers Policies (Enterprise only)
CREATE POLICY "Organization owners can manage SSO providers" ON sso_providers
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id FROM user_organizations
      WHERE user_id = auth.uid() AND role = 'owner'
    )
  );

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Subscriptions indexes
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer ON subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_tier ON subscriptions(subscription_tier);

-- Teams indexes
CREATE INDEX IF NOT EXISTS idx_teams_organization_id ON teams(organization_id);
CREATE INDEX IF NOT EXISTS idx_teams_created_by ON teams(created_by);

-- Team memberships indexes
CREATE INDEX IF NOT EXISTS idx_team_memberships_team_id ON team_memberships(team_id);
CREATE INDEX IF NOT EXISTS idx_team_memberships_user_id ON team_memberships(user_id);
CREATE INDEX IF NOT EXISTS idx_team_memberships_role ON team_memberships(role);

-- Scheduled posts indexes
CREATE INDEX IF NOT EXISTS idx_scheduled_posts_user_id ON scheduled_posts(user_id);
CREATE INDEX IF NOT EXISTS idx_scheduled_posts_next_execution ON scheduled_posts(next_execution_at);
CREATE INDEX IF NOT EXISTS idx_scheduled_posts_active ON scheduled_posts(is_active);

-- CRM integrations indexes
CREATE INDEX IF NOT EXISTS idx_crm_integrations_user_id ON crm_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_crm_integrations_provider ON crm_integrations(provider);
CREATE INDEX IF NOT EXISTS idx_crm_integrations_active ON crm_integrations(is_active);

-- Usage analytics indexes
CREATE INDEX IF NOT EXISTS idx_usage_analytics_user_id ON usage_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_analytics_event_type ON usage_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_usage_analytics_created_at ON usage_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_usage_analytics_organization_id ON usage_analytics(organization_id);

-- Audit logs indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_organization_id ON audit_logs(organization_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_risk_level ON audit_logs(risk_level);

-- Payment methods indexes
CREATE INDEX IF NOT EXISTS idx_payment_methods_user_id ON payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_provider ON payment_methods(provider);
CREATE INDEX IF NOT EXISTS idx_payment_methods_default ON payment_methods(is_default);

-- Webhook events indexes
CREATE INDEX IF NOT EXISTS idx_webhook_events_provider ON webhook_events(provider);
CREATE INDEX IF NOT EXISTS idx_webhook_events_status ON webhook_events(status);
CREATE INDEX IF NOT EXISTS idx_webhook_events_created_at ON webhook_events(created_at);

-- SSO configuration indexes
CREATE INDEX IF NOT EXISTS idx_sso_configurations_organization_id ON sso_configurations(organization_id);

-- SSO providers indexes
CREATE INDEX IF NOT EXISTS idx_sso_providers_organization_id ON sso_providers(organization_id);
CREATE INDEX IF NOT EXISTS idx_sso_providers_type ON sso_providers(type);
CREATE INDEX IF NOT EXISTS idx_sso_providers_enabled ON sso_providers(enabled);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teams_updated_at BEFORE UPDATE ON teams
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_team_memberships_updated_at BEFORE UPDATE ON team_memberships
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scheduled_posts_updated_at BEFORE UPDATE ON scheduled_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_post_templates_updated_at BEFORE UPDATE ON post_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_crm_integrations_updated_at BEFORE UPDATE ON crm_integrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_methods_updated_at BEFORE UPDATE ON payment_methods
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sso_configurations_updated_at BEFORE UPDATE ON sso_configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sso_providers_updated_at BEFORE UPDATE ON sso_providers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
