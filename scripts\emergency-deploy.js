#!/usr/bin/env node

/**
 * Emergency Deployment Script
 * Multiple fallback strategies for Vercel deployment
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function log(message, type = 'info') {
  const prefix = {
    info: 'ℹ️',
    success: '✅',
    error: '❌',
    warning: '⚠️'
  }[type];
  console.log(`${prefix} ${message}`);
}

async function emergencyDeploy() {
  log('🚨 Starting emergency deployment process...', 'warning');

  // Strategy 1: Clean deployment with optimized config
  log('Strategy 1: Clean deployment with optimized config');
  try {
    // Clean everything
    if (fs.existsSync('.next')) execSync('rm -rf .next', { stdio: 'pipe' });
    if (fs.existsSync('.vercel')) execSync('rm -rf .vercel', { stdio: 'pipe' });
    
    // Deploy with main config
    execSync('vercel --prod --yes --force', {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_OPTIONS: '--max_old_space_size=8192',
        NEXT_TELEMETRY_DISABLED: '1',
        SKIP_ENV_VALIDATION: 'true'
      }
    });
    
    log('Strategy 1 successful!', 'success');
    return true;
  } catch (error) {
    log('Strategy 1 failed, trying Strategy 2...', 'warning');
  }

  // Strategy 2: Use fallback config
  log('Strategy 2: Using fallback configuration');
  try {
    execSync('vercel --prod --yes --local-config vercel-fallback.json', {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_OPTIONS: '--max_old_space_size=8192',
        NEXT_TELEMETRY_DISABLED: '1'
      }
    });
    
    log('Strategy 2 successful!', 'success');
    return true;
  } catch (error) {
    log('Strategy 2 failed, trying Strategy 3...', 'warning');
  }

  // Strategy 3: Minimal config
  log('Strategy 3: Using minimal configuration');
  try {
    execSync('vercel --prod --yes --local-config vercel-minimal.json', {
      stdio: 'inherit',
      env: {
        NODE_ENV: 'production',
        NEXT_TELEMETRY_DISABLED: '1',
        SKIP_ENV_VALIDATION: 'true'
      }
    });
    
    log('Strategy 3 successful!', 'success');
    return true;
  } catch (error) {
    log('Strategy 3 failed, trying GitHub integration...', 'warning');
  }

  // Strategy 4: GitHub integration
  log('Strategy 4: GitHub integration deployment');
  try {
    log('Pushing to GitHub for automatic deployment...', 'info');
    execSync('git add .', { stdio: 'pipe' });
    execSync('git commit -m "fix: emergency deployment optimizations"', { stdio: 'pipe' });
    execSync('git push origin main', { stdio: 'inherit' });
    
    log('Code pushed to GitHub. Check Vercel dashboard for automatic deployment.', 'success');
    return true;
  } catch (error) {
    log('GitHub push failed, trying Netlify...', 'warning');
  }

  // Strategy 5: Netlify fallback
  log('Strategy 5: Netlify deployment fallback');
  try {
    // Check if Netlify CLI is available
    try {
      execSync('netlify --version', { stdio: 'pipe' });
    } catch {
      log('Installing Netlify CLI...', 'info');
      execSync('npm install -g netlify-cli', { stdio: 'inherit' });
    }

    // Build for Netlify
    log('Building for Netlify...', 'info');
    execSync('npm run build:next', {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_OPTIONS: '--max_old_space_size=8192',
        NEXT_TELEMETRY_DISABLED: '1'
      }
    });

    // Deploy to Netlify
    execSync('netlify deploy --prod --dir=.next', { stdio: 'inherit' });
    
    log('Netlify deployment successful!', 'success');
    return true;
  } catch (error) {
    log('Netlify deployment failed', 'error');
  }

  log('All deployment strategies failed', 'error');
  return false;
}

// Run emergency deployment
emergencyDeploy().then(success => {
  if (success) {
    log('🎉 Emergency deployment completed!', 'success');
  } else {
    log('💥 All deployment strategies failed', 'error');
    console.log('\n📋 Manual steps to try:');
    console.log('1. Check Vercel dashboard for specific error messages');
    console.log('2. Verify all environment variables are set correctly');
    console.log('3. Try deploying a minimal version first');
    console.log('4. Contact Vercel support if issues persist');
    process.exit(1);
  }
}).catch(error => {
  log(`Deployment script error: ${error.message}`, 'error');
  process.exit(1);
});
