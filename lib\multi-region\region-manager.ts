import { devLog } from '@/lib/console-cleaner';
/**
 * Multi-Region Architecture Manager
 * 
 * Handles data residency, regional routing, and compliance requirements
 * for US + EU data privacy split and global scaling.
 */

export interface RegionConfig {
  code: string;
  name: string;
  dataCenter: string;
  timezone: string;
  currency: string;
  languages: string[];
  gdprCompliant: boolean;
  supabaseUrl: string;
  supabaseKey: string;
  vercelRegion: string;
  cdnEndpoint: string;
  emailProvider: {
    region: string;
    endpoint: string;
  };
  aiProvider: {
    region: string;
    endpoint: string;
  };
}

export interface UserRegionData {
  userId: string;
  primaryRegion: string;
  allowedRegions: string[];
  dataResidencyRequirements: string[];
  gdprSubject: boolean;
  lastRegionUpdate: string;
}

export interface RegionRoutingRule {
  id: string;
  name: string;
  priority: number;
  conditions: {
    ipCountries?: string[];
    userCountries?: string[];
    organizationRegions?: string[];
    gdprRequired?: boolean;
  };
  targetRegion: string;
  fallbackRegion: string;
}

export class RegionManager {
  private regions: Map<string, RegionConfig> = new Map();
  private routingRules: RegionRoutingRule[] = [];
  private userRegionCache: Map<string, UserRegionData> = new Map();

  constructor() {
    this.initializeRegions();
    this.initializeRoutingRules();
  }

  /**
   * Get optimal region for user based on location and requirements
   */
  async getOptimalRegion(request: {
    userId?: string;
    organizationId?: string;
    ipAddress?: string;
    userCountry?: string;
    gdprRequired?: boolean;
  }): Promise<RegionConfig> {
    // Check user's stored region preference first
    if (request.userId) {
      const userRegionData = await this.getUserRegionData(request.userId);
      if (userRegionData && this.regions.has(userRegionData.primaryRegion)) {
        return this.regions.get(userRegionData.primaryRegion)!;
      }
    }

    // Apply routing rules
    for (const rule of this.routingRules.sort((a, b) => a.priority - b.priority)) {
      if (this.matchesRoutingRule(rule, request)) {
        const region = this.regions.get(rule.targetRegion);
        if (region) {
          return region;
        }
        
        // Fallback if target region is unavailable
        const fallbackRegion = this.regions.get(rule.fallbackRegion);
        if (fallbackRegion) {
          return fallbackRegion;
        }
      }
    }

    // Default to US region
    return this.regions.get('us-east-1')!;
  }

  /**
   * Set user's primary region with data residency compliance
   */
  async setUserRegion(
    userId: string,
    regionCode: string,
    requirements: {
      gdprSubject?: boolean;
      dataResidencyCountries?: string[];
      organizationPolicy?: string;
    } = {}
  ): Promise<{ success: boolean; migrationRequired: boolean; error?: string }> {
    const targetRegion = this.regions.get(regionCode);
    if (!targetRegion) {
      return { success: false, migrationRequired: false, error: 'Invalid region code' };
    }

    // Check GDPR compliance
    if (requirements.gdprSubject && !targetRegion.gdprCompliant) {
      return { 
        success: false, 
        migrationRequired: false, 
        error: 'Target region is not GDPR compliant' 
      };
    }

    const currentUserData = await this.getUserRegionData(userId);
    const migrationRequired = !!(currentUserData &&
      currentUserData.primaryRegion !== regionCode);

    const userRegionData: UserRegionData = {
      userId,
      primaryRegion: regionCode,
      allowedRegions: this.getAllowedRegions(requirements),
      dataResidencyRequirements: requirements.dataResidencyCountries || [],
      gdprSubject: requirements.gdprSubject || false,
      lastRegionUpdate: new Date().toISOString()
    };

    // Store user region data
    await this.storeUserRegionData(userRegionData);
    this.userRegionCache.set(userId, userRegionData);

    return { success: true, migrationRequired };
  }

  /**
   * Get region-specific database client
   */
  getRegionalDatabase(regionCode: string) {
    const region = this.regions.get(regionCode);
    if (!region) {
      throw new Error(`Region ${regionCode} not found`);
    }

    // Return region-specific Supabase client
    const { createClient } = require('@supabase/supabase-js');
    return createClient(region.supabaseUrl, region.supabaseKey);
  }

  /**
   * Get region-specific AI service endpoint
   */
  getRegionalAIEndpoint(regionCode: string): string {
    const region = this.regions.get(regionCode);
    if (!region) {
      throw new Error(`Region ${regionCode} not found`);
    }

    return region.aiProvider.endpoint;
  }

  /**
   * Get region-specific CDN endpoint
   */
  getRegionalCDN(regionCode: string): string {
    const region = this.regions.get(regionCode);
    if (!region) {
      throw new Error(`Region ${regionCode} not found`);
    }

    return region.cdnEndpoint;
  }

  /**
   * Migrate user data between regions
   */
  async migrateUserData(
    userId: string,
    fromRegion: string,
    toRegion: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const sourceDb = this.getRegionalDatabase(fromRegion);
      const targetDb = this.getRegionalDatabase(toRegion);

      // Get all user data from source region
      const { data: userData, error: fetchError } = await sourceDb
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (fetchError) {
        return { success: false, error: `Failed to fetch user data: ${fetchError.message}` };
      }

      // Get user's summaries
      const { data: summaries, error: summariesError } = await sourceDb
        .from('summaries')
        .select('*')
        .eq('user_id', userId);

      if (summariesError) {
        return { success: false, error: `Failed to fetch summaries: ${summariesError.message}` };
      }

      // Get user's organizations
      const { data: organizations, error: orgsError } = await sourceDb
        .from('user_organizations')
        .select('*')
        .eq('user_id', userId);

      if (orgsError) {
        return { success: false, error: `Failed to fetch organizations: ${orgsError.message}` };
      }

      // Insert data into target region
      const { error: insertUserError } = await targetDb
        .from('profiles')
        .upsert(userData);

      if (insertUserError) {
        return { success: false, error: `Failed to insert user data: ${insertUserError.message}` };
      }

      if (summaries && summaries.length > 0) {
        const { error: insertSummariesError } = await targetDb
          .from('summaries')
          .upsert(summaries);

        if (insertSummariesError) {
          return { success: false, error: `Failed to insert summaries: ${insertSummariesError.message}` };
        }
      }

      if (organizations && organizations.length > 0) {
        const { error: insertOrgsError } = await targetDb
          .from('user_organizations')
          .upsert(organizations);

        if (insertOrgsError) {
          return { success: false, error: `Failed to insert organizations: ${insertOrgsError.message}` };
        }
      }

      // Update user region
      await this.setUserRegion(userId, toRegion);

      // Schedule cleanup of source region data (after verification period)
      await this.scheduleDataCleanup(userId, fromRegion);

      return { success: true };

    } catch (error) {
      console.error('Data migration error:', error);
      return { 
        success: false, 
        error: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }

  /**
   * Get GDPR compliance status for region
   */
  isGDPRCompliant(regionCode: string): boolean {
    const region = this.regions.get(regionCode);
    return region ? region.gdprCompliant : false;
  }

  /**
   * Get all available regions
   */
  getAllRegions(): RegionConfig[] {
    return Array.from(this.regions.values());
  }

  /**
   * Get region by code
   */
  getRegion(regionCode: string): RegionConfig | undefined {
    return this.regions.get(regionCode);
  }

  /**
   * Health check for all regions
   */
  async healthCheckAllRegions(): Promise<Record<string, {
    healthy: boolean;
    latency: number;
    error?: string;
  }>> {
    const results: Record<string, any> = {};

    const healthChecks = Array.from(this.regions.entries()).map(async ([code, region]) => {
      const start = Date.now();
      
      try {
        const db = this.getRegionalDatabase(code);
        await db.from('profiles').select('id').limit(1);
        
        results[code] = {
          healthy: true,
          latency: Date.now() - start
        };
      } catch (error) {
        results[code] = {
          healthy: false,
          latency: Date.now() - start,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    await Promise.all(healthChecks);
    return results;
  }

  // Private methods

  private initializeRegions(): void {
    // US East Region (Primary)
    this.regions.set('us-east-1', {
      code: 'us-east-1',
      name: 'US East (Virginia)',
      dataCenter: 'us-east-1',
      timezone: 'America/New_York',
      currency: 'USD',
      languages: ['en'],
      gdprCompliant: false,
      supabaseUrl: process.env.SUPABASE_US_URL!,
      supabaseKey: process.env.SUPABASE_US_ANON_KEY!,
      vercelRegion: 'iad1',
      cdnEndpoint: 'https://cdn-us.slacksummaryscribe.com',
      emailProvider: {
        region: 'us-east-1',
        endpoint: 'https://api.resend.com'
      },
      aiProvider: {
        region: 'us-east-1',
        endpoint: 'https://api.openai.com'
      }
    });

    // EU West Region (GDPR Compliant)
    this.regions.set('eu-west-1', {
      code: 'eu-west-1',
      name: 'EU West (Ireland)',
      dataCenter: 'eu-west-1',
      timezone: 'Europe/Dublin',
      currency: 'EUR',
      languages: ['en', 'de', 'fr', 'es', 'it'],
      gdprCompliant: true,
      supabaseUrl: process.env.SUPABASE_EU_URL!,
      supabaseKey: process.env.SUPABASE_EU_ANON_KEY!,
      vercelRegion: 'dub1',
      cdnEndpoint: 'https://cdn-eu.slacksummaryscribe.com',
      emailProvider: {
        region: 'eu-west-1',
        endpoint: 'https://api.eu.resend.com'
      },
      aiProvider: {
        region: 'eu-west-1',
        endpoint: 'https://api.eu.openai.com'
      }
    });

    // Asia Pacific Region
    this.regions.set('ap-southeast-1', {
      code: 'ap-southeast-1',
      name: 'Asia Pacific (Singapore)',
      dataCenter: 'ap-southeast-1',
      timezone: 'Asia/Singapore',
      currency: 'USD',
      languages: ['en', 'zh', 'ja', 'ko'],
      gdprCompliant: false,
      supabaseUrl: process.env.SUPABASE_AP_URL!,
      supabaseKey: process.env.SUPABASE_AP_ANON_KEY!,
      vercelRegion: 'sin1',
      cdnEndpoint: 'https://cdn-ap.slacksummaryscribe.com',
      emailProvider: {
        region: 'ap-southeast-1',
        endpoint: 'https://api.resend.com'
      },
      aiProvider: {
        region: 'ap-southeast-1',
        endpoint: 'https://api.openai.com'
      }
    });
  }

  private initializeRoutingRules(): void {
    this.routingRules = [
      {
        id: 'gdpr-required',
        name: 'GDPR Required Users',
        priority: 1,
        conditions: { gdprRequired: true },
        targetRegion: 'eu-west-1',
        fallbackRegion: 'us-east-1'
      },
      {
        id: 'eu-users',
        name: 'European Users',
        priority: 2,
        conditions: { 
          ipCountries: ['DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'IE', 'PT', 'FI', 'SE', 'DK', 'NO'],
          userCountries: ['DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'IE', 'PT', 'FI', 'SE', 'DK', 'NO']
        },
        targetRegion: 'eu-west-1',
        fallbackRegion: 'us-east-1'
      },
      {
        id: 'asia-users',
        name: 'Asian Users',
        priority: 3,
        conditions: { 
          ipCountries: ['SG', 'JP', 'KR', 'AU', 'NZ', 'HK', 'TW', 'MY', 'TH', 'ID', 'PH', 'VN'],
          userCountries: ['SG', 'JP', 'KR', 'AU', 'NZ', 'HK', 'TW', 'MY', 'TH', 'ID', 'PH', 'VN']
        },
        targetRegion: 'ap-southeast-1',
        fallbackRegion: 'us-east-1'
      }
    ];
  }

  private matchesRoutingRule(rule: RegionRoutingRule, request: any): boolean {
    const { conditions } = rule;

    if (conditions.gdprRequired !== undefined && conditions.gdprRequired !== request.gdprRequired) {
      return false;
    }

    if (conditions.ipCountries && request.ipAddress) {
      // In production, you would use a GeoIP service to get country from IP
      // For now, we'll assume the country is passed in the request
      const ipCountry = this.getCountryFromIP(request.ipAddress);
      if (!conditions.ipCountries.includes(ipCountry)) {
        return false;
      }
    }

    if (conditions.userCountries && request.userCountry) {
      if (!conditions.userCountries.includes(request.userCountry)) {
        return false;
      }
    }

    return true;
  }

  private getCountryFromIP(ipAddress: string): string {
    // Placeholder - in production, use a GeoIP service like MaxMind
    // For development, return a default country
    return 'US';
  }

  private getAllowedRegions(requirements: any): string[] {
    const allowed = ['us-east-1'];
    
    if (requirements.gdprSubject) {
      allowed.push('eu-west-1');
    }
    
    if (requirements.dataResidencyCountries?.some((country: string) => 
      ['SG', 'JP', 'KR', 'AU'].includes(country)
    )) {
      allowed.push('ap-southeast-1');
    }
    
    return allowed;
  }

  private async getUserRegionData(userId: string): Promise<UserRegionData | null> {
    // Check cache first
    if (this.userRegionCache.has(userId)) {
      return this.userRegionCache.get(userId)!;
    }

    // In production, fetch from database
    // For now, return null
    return null;
  }

  private async storeUserRegionData(data: UserRegionData): Promise<void> {
    // In production, store in database
    // For now, just update cache
    this.userRegionCache.set(data.userId, data);
  }

  private async scheduleDataCleanup(userId: string, regionCode: string): Promise<void> {
    // In production, schedule a job to clean up data after verification period
  devLog.log(`Scheduled data cleanup for user ${userId} in region ${regionCode}`);
  }
}

// Global region manager instance
export const regionManager = new RegionManager();
