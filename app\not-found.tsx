'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Home, ArrowLeft, Search, RefreshCw, Bug, MapPin } from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

export default function NotFound() {
  const router = useRouter();

  useEffect(() => {
    // Log 404 error to analytics
    if (typeof window !== 'undefined') {
      // PostHog tracking
      if ((window as any).posthog) {
        try {
          (window as any).posthog.capture('404_page_viewed', {
            url: window.location.href,
            referrer: document.referrer,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.warn('Failed to log 404 to PostHog:', error);
        }
      }

      // Sentry tracking
      if ((window as any).Sentry) {
        try {
          (window as any).Sentry.withScope((scope: any) => {
            scope.setTag('errorType', '404');
            scope.setLevel('warning');
            scope.setContext('pageInfo', {
              url: window.location.href,
              referrer: document.referrer,
              userAgent: navigator.userAgent
            });
            (window as any).Sentry.captureMessage('404 Page Not Found', 'warning');
          });
        } catch (error) {
          console.warn('Failed to log 404 to Sentry:', error);
        }
      }
    }
  }, []);

  const handleGoBack = () => {
    try {
      if (window.history.length > 1) {
        router.back();
      } else {
        router.push('/');
      }
    } catch (error) {
      console.error('Navigation error:', error);
      window.location.href = '/';
    }
  };

  const handleRefresh = () => {
    if (typeof window !== 'undefined') {
      // Log refresh attempt
      if ((window as any).posthog) {
        (window as any).posthog.capture('404_page_refresh', {
          url: window.location.href
        });
      }

      toast.loading('Refreshing page...', { duration: 1000 });
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  };

  const handleReportIssue = () => {
    const issueReport = {
      type: '404 - Page Not Found',
      url: window.location.href,
      referrer: document.referrer,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    };

    const subject = encodeURIComponent('404 Error Report - Page Not Found');
    const body = encodeURIComponent(`Page Not Found Report:\n\n${JSON.stringify(issueReport, null, 2)}`);
    const mailtoUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;

    window.open(mailtoUrl, '_blank');

    // Log error report
    if ((window as any).posthog) {
      (window as any).posthog.capture('404_issue_reported', {
        url: window.location.href
      });
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <div className="text-center max-w-md w-full">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-6xl font-bold text-gray-300 dark:text-gray-600 mb-4">404</div>
          <div className="w-24 h-24 mx-auto mb-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
            <Search className="w-12 h-12 text-blue-600 dark:text-blue-400" />
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Page Not Found</CardTitle>
            <CardDescription>
              Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Button asChild className="w-full">
                <Link href="/">
                  <Home className="w-4 h-4 mr-2" />
                  Go Home
                </Link>
              </Button>

              <div className="grid grid-cols-2 gap-2">
                <Button onClick={handleGoBack} variant="outline">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Go Back
                </Button>
                <Button onClick={handleRefresh} variant="outline">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
              </div>

              <Button asChild variant="ghost" className="w-full">
                <Link href="/dashboard">
                  <Search className="w-4 h-4 mr-2" />
                  Browse Dashboard
                </Link>
              </Button>
            </div>

            <div className="pt-4 border-t">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Still having trouble? We're here to help.
              </p>
              <Button
                onClick={handleReportIssue}
                variant="ghost"
                size="sm"
                className="w-full text-gray-500 hover:text-gray-700"
              >
                <Bug className="h-4 w-4 mr-2" />
                Report This Issue
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && (
              <div className="pt-4 border-t">
                <details className="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-xs">
                  <summary className="cursor-pointer font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100">
                    🔍 Debug Info (Development)
                  </summary>
                  <div className="mt-3 space-y-2">
                    <div>
                      <strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'SSR'}
                    </div>
                    <div>
                      <strong>Referrer:</strong> {typeof window !== 'undefined' ? document.referrer || 'Direct access' : 'SSR'}
                    </div>
                    <div>
                      <strong>Timestamp:</strong> {new Date().toISOString()}
                    </div>
                  </div>
                </details>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="mt-8 text-xs text-gray-500 dark:text-gray-400">
          Error Code: 404 | Page Not Found
        </div>
      </div>
    </div>
  );
}
