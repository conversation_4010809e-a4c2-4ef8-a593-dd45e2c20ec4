# 🚀 Advanced Retention & Growth

## Overview
Sophisticated retention and growth systems with drip campaigns, usage-based upsells, and automated Slack delivery for maximum user engagement and revenue optimization.

## Features
- ✅ Drip email campaigns with behavioral triggers
- ✅ Usage-based upsell notifications and banners
- ✅ Automated Slack summary delivery via Web API
- ✅ Advanced segmentation and targeting
- ✅ A/B testing for campaigns and messaging
- ✅ Cohort analysis and retention tracking
- ✅ Predictive churn modeling
- 🟡 AI-powered content personalization
- 🔴 Advanced lifecycle marketing automation

## Module Structure
```
/features/growth/
├── README.md                   # This file
├── types.ts                   # Growth system types
├── campaigns/
│   ├── drip-campaigns.ts      # Email drip campaign system
│   ├── campaign-builder.ts    # Visual campaign builder
│   ├── triggers.ts            # Behavioral trigger system
│   └── templates.ts           # Email template management
├── upsells/
│   ├── usage-tracking.ts      # Usage-based upsell triggers
│   ├── upsell-banners.ts      # In-app upsell components
│   ├── pricing-experiments.ts # A/B testing for pricing
│   └── conversion-tracking.ts # Upsell conversion analytics
├── slack-delivery/
│   ├── slack-scheduler.ts     # Automated Slack delivery
│   ├── digest-generator.ts    # Weekly/monthly digest creation
│   ├── slack-api.ts           # Slack Web API integration
│   └── delivery-tracking.ts   # Delivery success tracking
├── segmentation/
│   ├── user-segments.ts       # User segmentation engine
│   ├── behavioral-scoring.ts  # User behavior scoring
│   ├── cohort-analysis.ts     # Cohort tracking and analysis
│   └── churn-prediction.ts    # Predictive churn modeling
├── analytics/
│   ├── growth-metrics.ts      # Growth KPI tracking
│   ├── campaign-analytics.ts  # Campaign performance
│   ├── retention-analysis.ts  # Retention curve analysis
│   └── revenue-attribution.ts # Revenue attribution modeling
└── components/
    ├── CampaignBuilder.tsx    # Visual campaign builder
    ├── UpsellBanner.tsx       # Usage-based upsell banners
    ├── GrowthDashboard.tsx    # Growth analytics dashboard
    ├── SegmentManager.tsx     # User segment management
    └── RetentionCharts.tsx    # Retention visualization
```

## Database Schema
```sql
-- Drip campaigns
CREATE TABLE drip_campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  trigger_type TEXT NOT NULL CHECK (trigger_type IN ('signup', 'trial_start', 'trial_end', 'inactive', 'usage_limit', 'custom')),
  trigger_config JSONB NOT NULL,
  steps JSONB NOT NULL, -- Array of campaign steps
  target_segments TEXT[],
  enabled BOOLEAN DEFAULT true,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Campaign enrollments
CREATE TABLE campaign_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id UUID REFERENCES drip_campaigns(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  current_step INTEGER DEFAULT 0,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'unsubscribed')),
  enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}'
);

-- Campaign step executions
CREATE TABLE campaign_executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  enrollment_id UUID REFERENCES campaign_enrollments(id) ON DELETE CASCADE,
  step_number INTEGER NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'opened', 'clicked', 'failed')),
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  executed_at TIMESTAMP WITH TIME ZONE,
  opened_at TIMESTAMP WITH TIME ZONE,
  clicked_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  metadata JSONB DEFAULT '{}'
);

-- User segments
CREATE TABLE user_segments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  conditions JSONB NOT NULL, -- Segment criteria
  user_count INTEGER DEFAULT 0,
  last_calculated_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User segment memberships
CREATE TABLE user_segment_memberships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  segment_id UUID REFERENCES user_segments(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(segment_id, user_id)
);

-- Usage tracking for upsells
CREATE TABLE usage_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  event_data JSONB DEFAULT '{}',
  usage_count INTEGER DEFAULT 1,
  period_start TIMESTAMP WITH TIME ZONE NOT NULL,
  period_end TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Upsell opportunities
CREATE TABLE upsell_opportunities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  opportunity_type TEXT NOT NULL,
  trigger_event TEXT NOT NULL,
  current_plan TEXT NOT NULL,
  suggested_plan TEXT NOT NULL,
  potential_revenue NUMERIC,
  priority_score INTEGER DEFAULT 0,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'presented', 'converted', 'dismissed', 'expired')),
  presented_at TIMESTAMP WITH TIME ZONE,
  converted_at TIMESTAMP WITH TIME ZONE,
  dismissed_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Slack delivery tracking
CREATE TABLE slack_deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  delivery_type TEXT NOT NULL CHECK (delivery_type IN ('daily', 'weekly', 'monthly', 'on_demand')),
  slack_channel_id TEXT NOT NULL,
  slack_message_ts TEXT,
  summary_ids UUID[],
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed')),
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  delivered_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Drip Campaign System

### Campaign Builder
```typescript
// features/growth/campaigns/drip-campaigns.ts
import { createSupabaseServerClient } from '@/lib/supabase-server';

export interface DripCampaign {
  id: string;
  name: string;
  description?: string;
  triggerType: 'signup' | 'trial_start' | 'trial_end' | 'inactive' | 'usage_limit' | 'custom';
  triggerConfig: TriggerConfig;
  steps: CampaignStep[];
  targetSegments: string[];
  enabled: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface TriggerConfig {
  // For signup trigger
  delayHours?: number;
  
  // For trial triggers
  trialDays?: number;
  
  // For inactive trigger
  inactiveDays?: number;
  
  // For usage limit trigger
  usageThreshold?: number;
  usageType?: string;
  
  // For custom trigger
  customConditions?: Record<string, any>;
}

export interface CampaignStep {
  stepNumber: number;
  name: string;
  delayHours: number;
  emailTemplate: string;
  subject: string;
  content: string;
  ctaText?: string;
  ctaUrl?: string;
  conditions?: StepCondition[];
}

export interface StepCondition {
  type: 'plan' | 'usage' | 'behavior' | 'custom';
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
  value: any;
}

export interface CampaignEnrollment {
  id: string;
  campaignId: string;
  userId: string;
  currentStep: number;
  status: 'active' | 'completed' | 'paused' | 'unsubscribed';
  enrolledAt: string;
  completedAt?: string;
  metadata: Record<string, any>;
}

/**
 * Create a new drip campaign
 */
export async function createDripCampaign(
  campaign: Omit<DripCampaign, 'id' | 'createdAt' | 'updatedAt'>
): Promise<{ success: boolean; campaignId?: string; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { data, error } = await supabase
      .from('drip_campaigns')
      .insert({
        name: campaign.name,
        description: campaign.description,
        trigger_type: campaign.triggerType,
        trigger_config: campaign.triggerConfig,
        steps: campaign.steps,
        target_segments: campaign.targetSegments,
        enabled: campaign.enabled,
        created_by: campaign.createdBy,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, campaignId: data.id };

  } catch (error) {
    console.error('Failed to create drip campaign:', error);
    return { success: false, error: 'Failed to create campaign' };
  }
}

/**
 * Enroll user in drip campaign
 */
export async function enrollUserInCampaign(
  userId: string,
  campaignId: string,
  metadata: Record<string, any> = {}
): Promise<{ success: boolean; enrollmentId?: string; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Check if user is already enrolled
    const { data: existingEnrollment } = await supabase
      .from('campaign_enrollments')
      .select('id')
      .eq('campaign_id', campaignId)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (existingEnrollment) {
      return { success: false, error: 'User already enrolled in this campaign' };
    }

    // Create enrollment
    const { data: enrollment, error } = await supabase
      .from('campaign_enrollments')
      .insert({
        campaign_id: campaignId,
        user_id: userId,
        current_step: 0,
        status: 'active',
        enrolled_at: new Date().toISOString(),
        metadata
      })
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    // Schedule first step
    await scheduleNextCampaignStep(enrollment.id);

    return { success: true, enrollmentId: enrollment.id };

  } catch (error) {
    console.error('Failed to enroll user in campaign:', error);
    return { success: false, error: 'Failed to enroll user' };
  }
}

/**
 * Schedule next campaign step
 */
async function scheduleNextCampaignStep(enrollmentId: string): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get enrollment and campaign details
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('campaign_enrollments')
      .select(`
        *,
        drip_campaigns (*)
      `)
      .eq('id', enrollmentId)
      .single();

    if (enrollmentError || !enrollment) {
      throw new Error('Enrollment not found');
    }

    const campaign = enrollment.drip_campaigns;
    const nextStepNumber = enrollment.current_step + 1;
    const nextStep = campaign.steps.find((step: CampaignStep) => step.stepNumber === nextStepNumber);

    if (!nextStep) {
      // Campaign completed
      await supabase
        .from('campaign_enrollments')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', enrollmentId);
      return;
    }

    // Calculate scheduled time
    const scheduledFor = new Date();
    scheduledFor.setHours(scheduledFor.getHours() + nextStep.delayHours);

    // Create execution record
    await supabase
      .from('campaign_executions')
      .insert({
        enrollment_id: enrollmentId,
        step_number: nextStepNumber,
        status: 'pending',
        scheduled_for: scheduledFor.toISOString(),
        metadata: {
          stepName: nextStep.name,
          emailTemplate: nextStep.emailTemplate
        }
      });

  } catch (error) {
    console.error('Failed to schedule campaign step:', error);
  }
}

/**
 * Process pending campaign executions
 */
export async function processPendingCampaignExecutions(): Promise<{
  processed: number;
  successful: number;
  failed: number;
}> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get pending executions
    const { data: executions, error } = await supabase
      .from('campaign_executions')
      .select(`
        *,
        campaign_enrollments (
          user_id,
          metadata,
          drip_campaigns (
            steps
          )
        )
      `)
      .eq('status', 'pending')
      .lte('scheduled_for', new Date().toISOString())
      .limit(50);

    if (error || !executions) {
      return { processed: 0, successful: 0, failed: 0 };
    }

    let successful = 0;
    let failed = 0;

    for (const execution of executions) {
      try {
        await executeCampaignStep(execution);
        successful++;
      } catch (error) {
        console.error(`Failed to execute campaign step ${execution.id}:`, error);
        failed++;
        
        // Update execution with error
        await supabase
          .from('campaign_executions')
          .update({
            status: 'failed',
            executed_at: new Date().toISOString(),
            error_message: error instanceof Error ? error.message : 'Unknown error'
          })
          .eq('id', execution.id);
      }
    }

    return { processed: executions.length, successful, failed };

  } catch (error) {
    console.error('Failed to process campaign executions:', error);
    return { processed: 0, successful: 0, failed: 0 };
  }
}

/**
 * Execute individual campaign step
 */
async function executeCampaignStep(execution: any): Promise<void> {
  const supabase = await createSupabaseServerClient();
  
  try {
    const enrollment = execution.campaign_enrollments;
    const campaign = enrollment.drip_campaigns;
    const step = campaign.steps.find((s: CampaignStep) => s.stepNumber === execution.step_number);
    
    if (!step) {
      throw new Error('Campaign step not found');
    }

    // Check step conditions
    const conditionsMet = await evaluateStepConditions(step.conditions || [], enrollment.user_id);
    if (!conditionsMet) {
      // Skip this step and schedule next
      await supabase
        .from('campaign_executions')
        .update({
          status: 'skipped',
          executed_at: new Date().toISOString()
        })
        .eq('id', execution.id);
      
      await scheduleNextCampaignStep(enrollment.id);
      return;
    }

    // Send email
    await sendCampaignEmail(enrollment.user_id, step, enrollment.metadata);

    // Update execution status
    await supabase
      .from('campaign_executions')
      .update({
        status: 'sent',
        executed_at: new Date().toISOString()
      })
      .eq('id', execution.id);

    // Update enrollment current step
    await supabase
      .from('campaign_enrollments')
      .update({
        current_step: execution.step_number
      })
      .eq('id', enrollment.id);

    // Schedule next step
    await scheduleNextCampaignStep(enrollment.id);

  } catch (error) {
    throw error;
  }
}

/**
 * Evaluate step conditions
 */
async function evaluateStepConditions(
  conditions: StepCondition[],
  userId: string
): Promise<boolean> {
  if (conditions.length === 0) return true;

  // Get user data for condition evaluation
  const supabase = await createSupabaseServerClient();
  
  const { data: user } = await supabase
    .from('profiles')
    .select(`
      *,
      user_organizations (
        organizations (
          plan
        )
      )
    `)
    .eq('id', userId)
    .single();

  if (!user) return false;

  // Evaluate each condition
  for (const condition of conditions) {
    let conditionMet = false;

    switch (condition.type) {
      case 'plan':
        const userPlan = user.user_organizations?.[0]?.organizations?.plan || 'FREE';
        conditionMet = evaluateCondition(userPlan, condition.operator, condition.value);
        break;
        
      case 'usage':
        // Get usage data and evaluate
        const usage = await getUserUsage(userId, condition.value.metric);
        conditionMet = evaluateCondition(usage, condition.operator, condition.value.threshold);
        break;
        
      case 'behavior':
        // Evaluate behavioral conditions
        conditionMet = await evaluateBehaviorCondition(userId, condition);
        break;
        
      default:
        conditionMet = true;
    }

    if (!conditionMet) return false;
  }

  return true;
}

/**
 * Evaluate condition operator
 */
function evaluateCondition(actual: any, operator: string, expected: any): boolean {
  switch (operator) {
    case 'equals':
      return actual === expected;
    case 'not_equals':
      return actual !== expected;
    case 'greater_than':
      return actual > expected;
    case 'less_than':
      return actual < expected;
    case 'contains':
      return String(actual).includes(String(expected));
    default:
      return false;
  }
}

/**
 * Send campaign email
 */
async function sendCampaignEmail(
  userId: string,
  step: CampaignStep,
  metadata: Record<string, any>
): Promise<void> {
  // Get user email
  const supabase = await createSupabaseServerClient();
  const { data: user } = await supabase
    .from('profiles')
    .select('email, full_name')
    .eq('id', userId)
    .single();

  if (!user) {
    throw new Error('User not found');
  }

  // Personalize content
  let personalizedContent = step.content;
  let personalizedSubject = step.subject;

  // Replace placeholders
  const placeholders = {
    firstName: user.full_name?.split(' ')[0] || 'there',
    email: user.email,
    ...metadata
  };

  Object.entries(placeholders).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    personalizedContent = personalizedContent.replace(new RegExp(placeholder, 'g'), String(value));
    personalizedSubject = personalizedSubject.replace(new RegExp(placeholder, 'g'), String(value));
  });

  // Send email using your email service
  // This would integrate with Resend or your email provider
  console.log('Sending campaign email:', {
    to: user.email,
    subject: personalizedSubject,
    content: personalizedContent
  });
}

/**
 * Get user usage for condition evaluation
 */
async function getUserUsage(userId: string, metric: string): Promise<number> {
  const supabase = await createSupabaseServerClient();
  
  const { data: usage } = await supabase
    .from('usage_events')
    .select('usage_count')
    .eq('user_id', userId)
    .eq('event_type', metric)
    .gte('period_start', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

  return usage?.reduce((sum, event) => sum + event.usage_count, 0) || 0;
}

/**
 * Evaluate behavioral condition
 */
async function evaluateBehaviorCondition(userId: string, condition: StepCondition): Promise<boolean> {
  // Implement behavioral condition evaluation
  // This could check things like:
  // - Last login date
  // - Feature usage patterns
  // - Engagement scores
  // - etc.
  return true; // Placeholder
}
