/**
 * Team Management Service
 * Enterprise team invitation, role management, and workspace limits
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';
import { SentryTracker } from '@/lib/sentry.client';
import { sendEmail } from '@/lib/resend';

export interface TeamMember {
  id: string;
  user_id: string;
  organization_id: string;
  email: string;
  role: 'owner' | 'admin' | 'member';
  status: 'active' | 'pending' | 'suspended';
  invited_by: string;
  invited_at: string;
  joined_at?: string;
  last_active_at?: string;
  permissions: {
    can_invite_members: boolean;
    can_manage_settings: boolean;
    can_view_analytics: boolean;
    can_export_data: boolean;
    can_manage_integrations: boolean;
  };
  created_at: string;
  updated_at: string;
}

export interface TeamInvitation {
  id: string;
  organization_id: string;
  email: string;
  role: 'admin' | 'member';
  invited_by: string;
  invitation_token: string;
  expires_at: string;
  status: 'pending' | 'accepted' | 'expired' | 'cancelled';
  created_at: string;
  updated_at: string;
}

export interface WorkspaceLimits {
  organization_id: string;
  subscription_tier: 'FREE' | 'PRO' | 'ENTERPRISE';
  max_members: number;
  current_members: number;
  max_workspaces: number;
  current_workspaces: number;
  max_monthly_summaries: number;
  current_monthly_summaries: number;
  features_enabled: {
    team_analytics: boolean;
    crm_integrations: boolean;
    slack_scheduler: boolean;
    priority_support: boolean;
    sso_integration: boolean;
    audit_logs: boolean;
  };
}

/**
 * Get team members for organization
 */
export async function getTeamMembers(
  organizationId: string,
  requestingUserId: string
): Promise<{ success: boolean; members?: TeamMember[]; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Check if requesting user has permission to view team
    const { data: requestingMember } = await supabase
      .from('team_memberships')
      .select('role')
      .eq('organization_id', organizationId)
      .eq('user_id', requestingUserId)
      .eq('status', 'active')
      .single();

    if (!requestingMember) {
      return { success: false, error: 'Access denied' };
    }

    // Get team members
    const { data: members, error } = await supabase
      .from('team_memberships')
      .select(`
        *,
        user_profiles!inner(email, full_name)
      `)
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: true });

    if (error) {
      throw error;
    }

    const formattedMembers: TeamMember[] = (members || []).map(member => ({
      id: member.id,
      user_id: member.user_id,
      organization_id: member.organization_id,
      email: member.user_profiles.email,
      role: member.role,
      status: member.status,
      invited_by: member.invited_by,
      invited_at: member.invited_at,
      joined_at: member.joined_at,
      last_active_at: member.last_active_at,
      permissions: getPermissionsForRole(member.role),
      created_at: member.created_at,
      updated_at: member.updated_at,
    }));

    return { success: true, members: formattedMembers };

  } catch (error) {
    console.error('Error getting team members:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get team members',
    };
  }
}

/**
 * Invite team member
 */
export async function inviteTeamMember(
  organizationId: string,
  inviterUserId: string,
  email: string,
  role: 'admin' | 'member'
): Promise<{ success: boolean; invitation?: TeamInvitation; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Check if inviter has permission
    const { data: inviter } = await supabase
      .from('team_memberships')
      .select('role')
      .eq('organization_id', organizationId)
      .eq('user_id', inviterUserId)
      .eq('status', 'active')
      .single();

    if (!inviter || !['owner', 'admin'].includes(inviter.role)) {
      return { success: false, error: 'Insufficient permissions to invite members' };
    }

    // Check workspace limits
    const limitsResult = await checkWorkspaceLimits(organizationId);
    if (!limitsResult.success || !limitsResult.limits) {
      return { success: false, error: 'Failed to check workspace limits' };
    }

    if (limitsResult.limits.current_members >= limitsResult.limits.max_members) {
      return { 
        success: false, 
        error: `Member limit reached (${limitsResult.limits.max_members}). Upgrade to add more members.` 
      };
    }

    // Check if user is already a member or has pending invitation
    const { data: existingMember } = await supabase
      .from('team_memberships')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('email', email)
      .single();

    if (existingMember) {
      return { success: false, error: 'User is already a team member' };
    }

    const { data: existingInvitation } = await supabase
      .from('team_invitations')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('email', email)
      .eq('status', 'pending')
      .single();

    if (existingInvitation) {
      return { success: false, error: 'Invitation already sent to this email' };
    }

    // Generate invitation token
    const invitationToken = generateInvitationToken();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

    // Create invitation
    const { data: invitation, error: invitationError } = await supabase
      .from('team_invitations')
      .insert({
        organization_id: organizationId,
        email,
        role,
        invited_by: inviterUserId,
        invitation_token: invitationToken,
        expires_at: expiresAt.toISOString(),
        status: 'pending',
      })
      .select()
      .single();

    if (invitationError) {
      throw invitationError;
    }

    // Send invitation email
    await sendInvitationEmail(email, organizationId, invitationToken, role);

    return { success: true, invitation };

  } catch (error) {
    console.error('Error inviting team member:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to invite team member',
    };
  }
}

/**
 * Accept team invitation
 */
export async function acceptTeamInvitation(
  invitationToken: string,
  userId: string
): Promise<{ success: boolean; membership?: TeamMember; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Get invitation
    const { data: invitation, error: invitationError } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('invitation_token', invitationToken)
      .eq('status', 'pending')
      .single();

    if (invitationError || !invitation) {
      return { success: false, error: 'Invalid or expired invitation' };
    }

    // Check if invitation is expired
    if (new Date(invitation.expires_at) < new Date()) {
      await supabase
        .from('team_invitations')
        .update({ status: 'expired' })
        .eq('id', invitation.id);

      return { success: false, error: 'Invitation has expired' };
    }

    // Create team membership
    const { data: membership, error: membershipError } = await supabase
      .from('team_memberships')
      .insert({
        user_id: userId,
        organization_id: invitation.organization_id,
        email: invitation.email,
        role: invitation.role,
        status: 'active',
        invited_by: invitation.invited_by,
        invited_at: invitation.created_at,
        joined_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (membershipError) {
      throw membershipError;
    }

    // Mark invitation as accepted
    await supabase
      .from('team_invitations')
      .update({ status: 'accepted' })
      .eq('id', invitation.id);

    const formattedMembership: TeamMember = {
      id: membership.id,
      user_id: membership.user_id,
      organization_id: membership.organization_id,
      email: membership.email,
      role: membership.role,
      status: membership.status,
      invited_by: membership.invited_by,
      invited_at: membership.invited_at,
      joined_at: membership.joined_at,
      permissions: getPermissionsForRole(membership.role),
      created_at: membership.created_at,
      updated_at: membership.updated_at,
    };

    return { success: true, membership: formattedMembership };

  } catch (error) {
    console.error('Error accepting invitation:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to accept invitation',
    };
  }
}

/**
 * Update team member role
 */
export async function updateTeamMemberRole(
  organizationId: string,
  targetUserId: string,
  newRole: 'admin' | 'member',
  requestingUserId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Check permissions
    const { data: requestingMember } = await supabase
      .from('team_memberships')
      .select('role')
      .eq('organization_id', organizationId)
      .eq('user_id', requestingUserId)
      .eq('status', 'active')
      .single();

    if (!requestingMember || !['owner', 'admin'].includes(requestingMember.role)) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Cannot change owner role
    const { data: targetMember } = await supabase
      .from('team_memberships')
      .select('role')
      .eq('organization_id', organizationId)
      .eq('user_id', targetUserId)
      .eq('status', 'active')
      .single();

    if (!targetMember) {
      return { success: false, error: 'Team member not found' };
    }

    if (targetMember.role === 'owner') {
      return { success: false, error: 'Cannot change owner role' };
    }

    // Update role
    const { error } = await supabase
      .from('team_memberships')
      .update({ 
        role: newRole,
        updated_at: new Date().toISOString(),
      })
      .eq('organization_id', organizationId)
      .eq('user_id', targetUserId);

    if (error) {
      throw error;
    }

    return { success: true };

  } catch (error) {
    console.error('Error updating member role:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update member role',
    };
  }
}

/**
 * Remove team member
 */
export async function removeTeamMember(
  organizationId: string,
  targetUserId: string,
  requestingUserId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Check permissions
    const { data: requestingMember } = await supabase
      .from('team_memberships')
      .select('role')
      .eq('organization_id', organizationId)
      .eq('user_id', requestingUserId)
      .eq('status', 'active')
      .single();

    if (!requestingMember || !['owner', 'admin'].includes(requestingMember.role)) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Cannot remove owner
    const { data: targetMember } = await supabase
      .from('team_memberships')
      .select('role')
      .eq('organization_id', organizationId)
      .eq('user_id', targetUserId)
      .eq('status', 'active')
      .single();

    if (!targetMember) {
      return { success: false, error: 'Team member not found' };
    }

    if (targetMember.role === 'owner') {
      return { success: false, error: 'Cannot remove owner' };
    }

    // Remove member
    const { error } = await supabase
      .from('team_memberships')
      .delete()
      .eq('organization_id', organizationId)
      .eq('user_id', targetUserId);

    if (error) {
      throw error;
    }

    return { success: true };

  } catch (error) {
    console.error('Error removing team member:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to remove team member',
    };
  }
}

/**
 * Check workspace limits
 */
export async function checkWorkspaceLimits(
  organizationId: string
): Promise<{ success: boolean; limits?: WorkspaceLimits; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Get subscription info
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('plan')
      .eq('organization_id', organizationId)
      .eq('status', 'ACTIVE')
      .single();

    const subscriptionTier = (subscription?.plan || 'FREE') as 'FREE' | 'PRO' | 'ENTERPRISE';

    // Get current usage
    const { data: members } = await supabase
      .from('team_memberships')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('status', 'active');

    const { data: workspaces } = await supabase
      .from('organizations')
      .select('id')
      .eq('id', organizationId);

    // Get monthly summaries count
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const { data: summaries } = await supabase
      .from('summaries')
      .select('id')
      .eq('organization_id', organizationId)
      .gte('created_at', startOfMonth.toISOString());

    const limits = calculateLimitsForTier(subscriptionTier);
    
    const workspaceLimits: WorkspaceLimits = {
      organization_id: organizationId,
      subscription_tier: subscriptionTier,
      max_members: limits.maxMembers,
      current_members: members?.length || 0,
      max_workspaces: limits.maxWorkspaces,
      current_workspaces: workspaces?.length || 0,
      max_monthly_summaries: limits.maxMonthlySummaries,
      current_monthly_summaries: summaries?.length || 0,
      features_enabled: limits.features,
    };

    return { success: true, limits: workspaceLimits };

  } catch (error) {
    console.error('Error checking workspace limits:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to check workspace limits',
    };
  }
}

// Helper functions

function getPermissionsForRole(role: string) {
  switch (role) {
    case 'owner':
      return {
        can_invite_members: true,
        can_manage_settings: true,
        can_view_analytics: true,
        can_export_data: true,
        can_manage_integrations: true,
      };
    case 'admin':
      return {
        can_invite_members: true,
        can_manage_settings: true,
        can_view_analytics: true,
        can_export_data: true,
        can_manage_integrations: true,
      };
    case 'member':
      return {
        can_invite_members: false,
        can_manage_settings: false,
        can_view_analytics: false,
        can_export_data: false,
        can_manage_integrations: false,
      };
    default:
      return {
        can_invite_members: false,
        can_manage_settings: false,
        can_view_analytics: false,
        can_export_data: false,
        can_manage_integrations: false,
      };
  }
}

function generateInvitationToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

function calculateLimitsForTier(tier: 'FREE' | 'PRO' | 'ENTERPRISE') {
  switch (tier) {
    case 'FREE':
      return {
        maxMembers: 1,
        maxWorkspaces: 1,
        maxMonthlySummaries: 10,
        features: {
          team_analytics: false,
          crm_integrations: false,
          slack_scheduler: false,
          priority_support: false,
          sso_integration: false,
          audit_logs: false,
        },
      };
    case 'PRO':
      return {
        maxMembers: 5,
        maxWorkspaces: 3,
        maxMonthlySummaries: 500,
        features: {
          team_analytics: false,
          crm_integrations: true,
          slack_scheduler: true,
          priority_support: true,
          sso_integration: false,
          audit_logs: false,
        },
      };
    case 'ENTERPRISE':
      return {
        maxMembers: -1, // Unlimited
        maxWorkspaces: -1, // Unlimited
        maxMonthlySummaries: -1, // Unlimited
        features: {
          team_analytics: true,
          crm_integrations: true,
          slack_scheduler: true,
          priority_support: true,
          sso_integration: true,
          audit_logs: true,
        },
      };
    default:
      return calculateLimitsForTier('FREE');
  }
}

async function sendInvitationEmail(
  email: string,
  organizationId: string,
  invitationToken: string,
  role: string
): Promise<void> {
  const invitationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invite/${invitationToken}`;
  
  const emailContent = `
    <h2>You've been invited to join a team!</h2>
    <p>You've been invited to join a team as a ${role}.</p>
    <p>Click the link below to accept the invitation:</p>
    <a href="${invitationUrl}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Accept Invitation</a>
    <p>This invitation will expire in 7 days.</p>
    <p>If you didn't expect this invitation, you can safely ignore this email.</p>
  `;

  await sendEmail({
    to: email,
    subject: 'Team Invitation - Slack Summary Scribe',
    html: emailContent,
  });
}
