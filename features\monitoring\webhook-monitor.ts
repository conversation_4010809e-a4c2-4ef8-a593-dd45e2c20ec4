/**
 * Webhook Monitoring & Retry System
 * 
 * Monitors webhook deliveries and implements intelligent retry logic
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';

export interface WebhookDelivery {
  id: string;
  provider: 'stripe' | 'slack' | 'custom';
  event_type: string;
  webhook_id: string;
  status: 'pending' | 'delivered' | 'failed' | 'retrying';
  attempts: number;
  max_attempts: number;
  last_attempt_at?: string;
  next_retry_at?: string;
  response_code?: number;
  response_body?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface RetryConfig {
  maxRetries: number;
  backoffMs: number;
  maxBackoffMs: number;
  backoffMultiplier: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 5,
  backoffMs: 1000,
  maxBackoffMs: 300000, // 5 minutes
  backoffMultiplier: 2
};

/**
 * Monitor webhook delivery and handle retries
 */
export async function monitorWebhookDelivery(
  provider: 'stripe' | 'slack' | 'custom',
  webhookId: string,
  config: Partial<RetryConfig> = {}
): Promise<{ success: boolean; delivery?: WebhookDelivery; error?: string }> {
  const retryConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  
  try {
    const supabase = await createSupabaseServerClient();
    
    // Check if delivery record exists
    let { data: delivery, error } = await supabase
      .from('webhook_deliveries')
      .select('*')
      .eq('webhook_id', webhookId)
      .eq('provider', provider)
      .single();

    if (error && error.code !== 'PGRST116') {
      return { success: false, error: error.message };
    }

    if (!delivery) {
      // Create new delivery record
      const { data: newDelivery, error: createError } = await supabase
        .from('webhook_deliveries')
        .insert({
          provider,
          webhook_id: webhookId,
          event_type: 'unknown',
          status: 'pending',
          attempts: 0,
          max_attempts: retryConfig.maxRetries,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        return { success: false, error: createError.message };
      }

      delivery = newDelivery;
    }

    // Check if we should retry
    if (delivery.status === 'failed' && delivery.attempts < delivery.max_attempts) {
      const shouldRetry = await shouldRetryWebhook(delivery);
      
      if (shouldRetry) {
        await scheduleWebhookRetry(delivery, retryConfig);
        return { success: true, delivery };
      }
    }

    return { success: true, delivery };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Record webhook delivery attempt
 */
export async function recordWebhookAttempt(
  webhookId: string,
  provider: string,
  success: boolean,
  responseCode?: number,
  responseBody?: string,
  errorMessage?: string
): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { data: delivery } = await supabase
      .from('webhook_deliveries')
      .select('*')
      .eq('webhook_id', webhookId)
      .eq('provider', provider)
      .single();

    if (!delivery) return;

    const newAttempts = delivery.attempts + 1;
    const newStatus = success ? 'delivered' : 
                     newAttempts >= delivery.max_attempts ? 'failed' : 'retrying';

    await supabase
      .from('webhook_deliveries')
      .update({
        status: newStatus,
        attempts: newAttempts,
        last_attempt_at: new Date().toISOString(),
        response_code: responseCode,
        response_body: responseBody,
        error_message: errorMessage,
        updated_at: new Date().toISOString()
      })
      .eq('id', delivery.id);

  } catch (error) {
    console.error('Failed to record webhook attempt:', error);
  }
}

/**
 * Check if webhook should be retried
 */
async function shouldRetryWebhook(delivery: WebhookDelivery): Promise<boolean> {
  // Don't retry if max attempts reached
  if (delivery.attempts >= delivery.max_attempts) {
    return false;
  }

  // Don't retry if next retry time hasn't passed
  if (delivery.next_retry_at) {
    const nextRetry = new Date(delivery.next_retry_at);
    if (nextRetry > new Date()) {
      return false;
    }
  }

  // Don't retry certain error codes
  const nonRetryableCodes = [400, 401, 403, 404, 422];
  if (delivery.response_code && nonRetryableCodes.includes(delivery.response_code)) {
    return false;
  }

  return true;
}

/**
 * Schedule webhook retry with exponential backoff
 */
async function scheduleWebhookRetry(
  delivery: WebhookDelivery, 
  config: RetryConfig
): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Calculate next retry time with exponential backoff
    const backoffMs = Math.min(
      config.backoffMs * Math.pow(config.backoffMultiplier, delivery.attempts),
      config.maxBackoffMs
    );
    
    const nextRetryAt = new Date(Date.now() + backoffMs);

    await supabase
      .from('webhook_deliveries')
      .update({
        status: 'retrying',
        next_retry_at: nextRetryAt.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', delivery.id);

  } catch (error) {
    console.error('Failed to schedule webhook retry:', error);
  }
}

/**
 * Get webhook delivery statistics
 */
export async function getWebhookStats(
  provider?: string,
  timeframe: 'hour' | 'day' | 'week' = 'day'
): Promise<{
  total: number;
  delivered: number;
  failed: number;
  retrying: number;
  successRate: number;
  averageAttempts: number;
}> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Calculate time range
    const now = new Date();
    const timeRanges = {
      hour: new Date(now.getTime() - 60 * 60 * 1000),
      day: new Date(now.getTime() - 24 * 60 * 60 * 1000),
      week: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    };
    
    let query = supabase
      .from('webhook_deliveries')
      .select('status, attempts')
      .gte('created_at', timeRanges[timeframe].toISOString());

    if (provider) {
      query = query.eq('provider', provider);
    }

    const { data: deliveries, error } = await query;

    if (error || !deliveries) {
      return {
        total: 0,
        delivered: 0,
        failed: 0,
        retrying: 0,
        successRate: 0,
        averageAttempts: 0
      };
    }

    const stats = deliveries.reduce((acc, delivery) => {
      acc.total++;
      if (delivery.status === 'delivered') acc.delivered++;
      if (delivery.status === 'failed') acc.failed++;
      if (delivery.status === 'retrying') acc.retrying++;
      acc.totalAttempts += delivery.attempts;
      return acc;
    }, {
      total: 0,
      delivered: 0,
      failed: 0,
      retrying: 0,
      totalAttempts: 0
    });

    return {
      ...stats,
      successRate: stats.total > 0 ? (stats.delivered / stats.total) * 100 : 0,
      averageAttempts: stats.total > 0 ? stats.totalAttempts / stats.total : 0
    };

  } catch (error) {
    console.error('Failed to get webhook stats:', error);
    return {
      total: 0,
      delivered: 0,
      failed: 0,
      retrying: 0,
      successRate: 0,
      averageAttempts: 0
    };
  }
}

/**
 * Process pending webhook retries
 */
export async function processPendingRetries(): Promise<{
  processed: number;
  successful: number;
  failed: number;
}> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get webhooks ready for retry
    const { data: pendingRetries, error } = await supabase
      .from('webhook_deliveries')
      .select('*')
      .eq('status', 'retrying')
      .lte('next_retry_at', new Date().toISOString())
      .limit(50); // Process in batches

    if (error || !pendingRetries) {
      return { processed: 0, successful: 0, failed: 0 };
    }

    let successful = 0;
    let failed = 0;

    for (const delivery of pendingRetries) {
      try {
        // Attempt to reprocess the webhook
        const success = await retryWebhookDelivery(delivery);
        if (success) {
          successful++;
        } else {
          failed++;
        }
      } catch (error) {
        console.error(`Failed to retry webhook ${delivery.id}:`, error);
        failed++;
      }
    }

    return {
      processed: pendingRetries.length,
      successful,
      failed
    };

  } catch (error) {
    console.error('Failed to process pending retries:', error);
    return { processed: 0, successful: 0, failed: 0 };
  }
}

/**
 * Retry a specific webhook delivery
 */
async function retryWebhookDelivery(delivery: WebhookDelivery): Promise<boolean> {
  // This would contain the actual retry logic specific to each provider
  // For now, we'll simulate the retry
  
  try {
    // Simulate webhook retry based on provider
    switch (delivery.provider) {
      case 'stripe':
        // Retry Stripe webhook processing
        return await retryStripeWebhook(delivery);
      case 'slack':
        // Retry Slack webhook processing
        return await retrySlackWebhook(delivery);
      default:
        return false;
    }
  } catch (error) {
    await recordWebhookAttempt(
      delivery.webhook_id,
      delivery.provider,
      false,
      undefined,
      undefined,
      error instanceof Error ? error.message : 'Unknown error'
    );
    return false;
  }
}

async function retryStripeWebhook(delivery: WebhookDelivery): Promise<boolean> {
  // Implement Stripe-specific retry logic
  // This would re-process the original Stripe event
  return true; // Placeholder
}

async function retrySlackWebhook(delivery: WebhookDelivery): Promise<boolean> {
  // Implement Slack-specific retry logic
  // This would re-process the original Slack event
  return true; // Placeholder
}
