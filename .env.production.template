# =============================================================================
# SLACK SUMMARY SCRIBE - PRODUCTION ENVIRONMENT TEMPLATE
# =============================================================================
# Copy this file to .env.local and fill in your actual values
# DO NOT commit this file with real secrets to version control

# =============================================================================
# SUPABASE CONFIGURATION (REQUIRED)
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_URL=https://your-project.supabase.co
DATABASE_URL=postgresql://postgres.your-project:[password]@aws-0-region.pooler.supabase.com:6543/postgres

# =============================================================================
# AUTHENTICATION (REQUIRED)
# =============================================================================
NEXTAUTH_SECRET=your-secure-random-secret-key-32-chars-min
NEXTAUTH_URL=https://yourdomain.com

# =============================================================================
# SLACK OAUTH (REQUIRED FOR SLACK INTEGRATION)
# =============================================================================
NEXT_PUBLIC_SLACK_CLIENT_ID=1234567890.1234567890
SLACK_CLIENT_ID=1234567890.1234567890
SLACK_CLIENT_SECRET=your-slack-client-secret
SLACK_SIGNING_SECRET=your-slack-signing-secret
SLACK_WEBHOOK_URL=https://yourdomain.com/api/slack/oauth/callback

# =============================================================================
# AI SERVICES (REQUIRED)
# =============================================================================
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key

# =============================================================================
# EMAIL SERVICE (REQUIRED)
# =============================================================================
RESEND_API_KEY=re_your-resend-api-key
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# =============================================================================
# PAYMENTS (RECOMMENDED FOR SAAS)
# =============================================================================
# Cashfree (Primary)
CASHFREE_APP_ID=your-cashfree-app-id
CASHFREE_SECRET_KEY=your-cashfree-secret-key

# Stripe (Alternative)
STRIPE_SECRET_KEY=sk_live_your-stripe-secret-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-publishable-key

# =============================================================================
# ANALYTICS & MONITORING (RECOMMENDED)
# =============================================================================
# PostHog Analytics
NEXT_PUBLIC_POSTHOG_KEY=phc_your-posthog-project-key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
POSTHOG_KEY=phc_your-posthog-project-key
POSTHOG_HOST=https://app.posthog.com
POSTHOG_API_KEY=your-posthog-api-key

# Sentry Error Tracking
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project

# =============================================================================
# SITE CONFIGURATION (REQUIRED)
# =============================================================================
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXT_PUBLIC_ENVIRONMENT=production

# =============================================================================
# OPTIONAL INTEGRATIONS
# =============================================================================
# Notion API
NOTION_API_TOKEN=secret_your-notion-integration-token
NOTION_DATABASE_ID=your-notion-database-id
NOTION_CLIENT_ID=your-notion-oauth-client-id
NOTION_CLIENT_SECRET=your-notion-oauth-client-secret

# HubSpot CRM
HUBSPOT_CLIENT_ID=your-hubspot-client-id
HUBSPOT_CLIENT_SECRET=your-hubspot-client-secret

# Salesforce CRM
SALESFORCE_CLIENT_ID=your-salesforce-client-id
SALESFORCE_CLIENT_SECRET=your-salesforce-client-secret

# =============================================================================
# FEATURE FLAGS
# =============================================================================
NEXT_PUBLIC_FETCH_TIMEOUT=10000
NEXT_PUBLIC_FEATURE_AI_MODEL_ROUTING=true
NEXT_PUBLIC_FEATURE_PREMIUM_AI=true
NEXT_PUBLIC_FEATURE_AI_ANALYTICS=true
NEXT_PUBLIC_FEATURE_ENTERPRISE_SECURITY=true

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
NODE_ENV=production

# =============================================================================
# INSTRUCTIONS FOR SETUP
# =============================================================================
# 1. Copy this file to .env.local
# 2. Replace all placeholder values with your actual credentials
# 3. Run: npm run validate-env to check configuration
# 4. Never commit .env.local to version control
# 5. For Vercel deployment, add these as environment variables in dashboard
