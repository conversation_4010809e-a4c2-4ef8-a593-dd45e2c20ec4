'use client';

/**
 * AI Model Selector Component
 * Displays available AI models based on subscription tier with upgrade prompts
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Zap, Crown, Star, ArrowUp } from 'lucide-react';
import { getCurrentUserClient } from '@/lib/user-management';

interface AIModel {
  id: string;
  name: string;
  description: string;
  features: string[];
  requiredPlan: string;
  speed: 'fast' | 'medium' | 'slow';
  quality: 'good' | 'excellent' | 'premium';
  costPerToken: number;
}

interface ModelSelectorProps {
  selectedModel?: string;
  onModelSelect: (modelId: string) => void;
  disabled?: boolean;
  showUpgradePrompts?: boolean;
}

export default function ModelSelector({
  selectedModel,
  onModelSelect,
  disabled = false,
  showUpgradePrompts = true,
}: ModelSelectorProps) {
  const [user, setUser] = useState<any>(null);
  const [availableModels, setAvailableModels] = useState<Record<string, AIModel>>({});
  const [subscriptionTier, setSubscriptionTier] = useState<string>('FREE');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { getCurrentUserClient } = await import('@/lib/user-management-client');
        const currentUser = await getCurrentUserClient();
        setUser(currentUser);
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    };

    fetchUser();
  }, []);

  useEffect(() => {
    fetchAvailableModels();
  }, [user]);

  const fetchAvailableModels = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const response = await fetch('/api/ai/models');
      const data = await response.json();

      if (data.success) {
        setAvailableModels(data.models || {});
        setSubscriptionTier(data.subscription_tier || 'FREE');
      } else {
        setError(data.error || 'Failed to fetch available models');
      }
    } catch (err) {
      setError('Failed to load AI models');
      console.error('Error fetching AI models:', err);
    } finally {
      setLoading(false);
    }
  };

  const getModelIcon = (model: AIModel) => {
    switch (model.quality) {
      case 'premium':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'excellent':
        return <Star className="h-4 w-4 text-blue-500" />;
      default:
        return <Zap className="h-4 w-4 text-green-500" />;
    }
  };

  const getSpeedBadge = (speed: string) => {
    const colors = {
      fast: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      slow: 'bg-red-100 text-red-800',
    };
    return (
      <Badge variant="secondary" className={colors[speed as keyof typeof colors]}>
        {speed}
      </Badge>
    );
  };

  const getQualityBadge = (quality: string) => {
    const colors = {
      good: 'bg-blue-100 text-blue-800',
      excellent: 'bg-purple-100 text-purple-800',
      premium: 'bg-yellow-100 text-yellow-800',
    };
    return (
      <Badge variant="secondary" className={colors[quality as keyof typeof colors]}>
        {quality}
      </Badge>
    );
  };

  const canUseModel = (model: AIModel) => {
    const tierHierarchy = { FREE: 0, PRO: 1, ENTERPRISE: 2 };
    const userTierLevel = tierHierarchy[subscriptionTier as keyof typeof tierHierarchy] || 0;
    const requiredTierLevel = tierHierarchy[model.requiredPlan as keyof typeof tierHierarchy] || 0;
    return userTierLevel >= requiredTierLevel;
  };

  const getUpgradeMessage = (model: AIModel) => {
    if (model.requiredPlan === 'PRO') {
      return 'Upgrade to Pro ($29/month) to use this model';
    }
    if (model.requiredPlan === 'ENTERPRISE') {
      return 'Upgrade to Enterprise ($99/month) to use this model';
    }
    return '';
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading AI models...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  const modelEntries = Object.entries(availableModels);
  const availableModelEntries = modelEntries.filter(([_, model]) => canUseModel(model));
  const lockedModelEntries = modelEntries.filter(([_, model]) => !canUseModel(model));

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            AI Model Selection
          </CardTitle>
          <CardDescription>
            Choose the AI model for your summary. Higher tier models provide better quality and features.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Model Selector */}
            <div>
              <label className="text-sm font-medium mb-2 block">
                Available Models (Current Plan: {subscriptionTier})
              </label>
              <Select
                value={selectedModel}
                onValueChange={onModelSelect}
                disabled={disabled || availableModelEntries.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select an AI model" />
                </SelectTrigger>
                <SelectContent>
                  {availableModelEntries.map(([modelId, model]) => (
                    <SelectItem key={modelId} value={modelId}>
                      <div className="flex items-center gap-2">
                        {getModelIcon(model)}
                        <span>{model.name}</span>
                        {getSpeedBadge(model.speed)}
                        {getQualityBadge(model.quality)}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Selected Model Details */}
            {selectedModel && availableModels[selectedModel] && (
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    {getModelIcon(availableModels[selectedModel])}
                    <div className="flex-1">
                      <h4 className="font-medium">{availableModels[selectedModel].name}</h4>
                      <p className="text-sm text-gray-600 mb-2">
                        {availableModels[selectedModel].description}
                      </p>
                      <div className="flex gap-2 mb-2">
                        {getSpeedBadge(availableModels[selectedModel].speed)}
                        {getQualityBadge(availableModels[selectedModel].quality)}
                        <Badge variant="outline">
                          {availableModels[selectedModel].requiredPlan}
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-500">
                        Features: {availableModels[selectedModel].features.join(', ')}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Locked Models (Upgrade Prompts) */}
            {showUpgradePrompts && lockedModelEntries.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2 text-gray-700">
                  Premium Models (Upgrade Required)
                </h4>
                <div className="space-y-2">
                  {lockedModelEntries.map(([modelId, model]) => (
                    <Card key={modelId} className="bg-gray-50 border-gray-200 opacity-75">
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getModelIcon(model)}
                            <span className="font-medium text-gray-700">{model.name}</span>
                            {getSpeedBadge(model.speed)}
                            {getQualityBadge(model.quality)}
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-500">
                              {getUpgradeMessage(model)}
                            </span>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-xs"
                              onClick={() => {
                                // Navigate to pricing page
                                window.open('/pricing', '_blank');
                              }}
                            >
                              <ArrowUp className="h-3 w-3 mr-1" />
                              Upgrade
                            </Button>
                          </div>
                        </div>
                        <p className="text-xs text-gray-600 mt-1">{model.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Subscription Info */}
            <Alert>
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <span>
                    Current Plan: <strong>{subscriptionTier}</strong> • 
                    {availableModelEntries.length} model{availableModelEntries.length !== 1 ? 's' : ''} available
                  </span>
                  {subscriptionTier === 'FREE' && (
                    <Button
                      size="sm"
                      onClick={() => window.open('/pricing', '_blank')}
                    >
                      <ArrowUp className="h-3 w-3 mr-1" />
                      Upgrade Plan
                    </Button>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
