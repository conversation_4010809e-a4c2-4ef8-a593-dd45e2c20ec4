# 🚀 PRODUCTION DEPLOYMENT SCRIPT (PowerShell)
# Slack Summary Scribe - Automated Vercel Deployment

param(
    [switch]$SkipBuild = $false,
    [switch]$SkipTests = $false
)

Write-Host "🚀 Starting Production Deployment for Slack Summary Scribe..." -ForegroundColor Cyan
Write-Host "==================================================" -ForegroundColor Cyan

# Helper functions
function Write-Info {
    param($Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

try {
    # Check prerequisites
    Write-Host ""
    Write-Info "Checking prerequisites..."

    # Check if we're in the right directory
    if (-not (Test-Path "package.json")) {
        Write-Error "package.json not found. Please run this script from the project root."
        exit 1
    }
    Write-Success "Project root directory confirmed"

    # Check if Vercel CLI is installed
    try {
        $vercelVersion = vercel --version 2>$null
        Write-Success "Vercel CLI is available (version: $vercelVersion)"
    }
    catch {
        Write-Warning "Vercel CLI not found. Installing..."
        npm install -g vercel
        Write-Success "Vercel CLI installed"
    }

    # Check if user is logged in to Vercel
    try {
        $vercelUser = vercel whoami 2>$null
        Write-Success "Logged in to Vercel as: $vercelUser"
    }
    catch {
        Write-Error "Not logged in to Vercel. Please run: vercel login"
        exit 1
    }

    # Pre-deployment validation
    Write-Host ""
    Write-Info "Running pre-deployment validation..."

    # Check environment template
    if (Test-Path ".env.production.template") {
        Write-Success "Production environment template found"
    }
    else {
        Write-Warning "No .env.production.template found"
    }

    # Check critical files
    $criticalFiles = @(
        "next.config.mjs",
        "app/layout.tsx", 
        "app/page.tsx",
        "lib/supabase-server.ts",
        "lib/supabase-browser.ts"
    )

    foreach ($file in $criticalFiles) {
        if (Test-Path $file) {
            Write-Success "Critical file exists: $file"
        }
        else {
            Write-Error "Critical file missing: $file"
            exit 1
        }
    }

    # Run build test (unless skipped)
    if (-not $SkipBuild) {
        Write-Host ""
        Write-Info "Testing production build..."
        
        # Clean previous build
        if (Test-Path ".next") {
            Remove-Item -Recurse -Force ".next"
            Write-Success "Cleaned previous build"
        }

        # Run build
        $buildResult = npm run build
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Build completed successfully"
        }
        else {
            Write-Error "Build failed. Please fix build errors before deploying."
            exit 1
        }
    }

    # Run tests (unless skipped)
    if (-not $SkipTests) {
        Write-Host ""
        Write-Info "Running pre-deployment tests..."
        
        # Type check
        try {
            npm run type-check 2>$null
            Write-Success "TypeScript validation passed"
        }
        catch {
            Write-Warning "TypeScript validation failed or not configured"
        }

        # Lint check
        try {
            npm run lint 2>$null
            Write-Success "Linting passed"
        }
        catch {
            Write-Warning "Linting failed or not configured"
        }
    }

    # Deploy to Vercel
    Write-Host ""
    Write-Info "Deploying to Vercel production..."

    $deployOutput = vercel --prod --yes 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Deployment completed successfully!"
        
        # Extract deployment URL
        $deploymentUrl = ($deployOutput | Select-String -Pattern "https://[^\s]+" | Select-Object -First 1).Matches.Value
        
        if ($deploymentUrl) {
            Write-Host ""
            Write-Host "==================================================" -ForegroundColor Green
            Write-Success "🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!"
            Write-Host "==================================================" -ForegroundColor Green

            Write-Host ""
            Write-Info "📍 Production URL: $deploymentUrl"
            Write-Info "🕒 Deployed at: $(Get-Date)"

            Write-Host ""
            Write-Host "📋 NEXT STEPS:" -ForegroundColor Cyan
            Write-Host "1. Configure environment variables in Vercel Dashboard"
            Write-Host "2. Set up custom domain (if applicable)"
            Write-Host "3. Configure Stripe webhooks"
            Write-Host "4. Test all critical user flows"
            Write-Host "5. Monitor error rates and performance"

            Write-Host ""
            Write-Host "🔗 IMPORTANT LINKS:" -ForegroundColor Cyan
            Write-Host "• Production App: $deploymentUrl"
            Write-Host "• Vercel Dashboard: https://vercel.com/dashboard"
            Write-Host "• Environment Variables: https://vercel.com/dashboard → Settings → Environment Variables"

            Write-Host ""
            Write-Host "🧪 TESTING CHECKLIST:" -ForegroundColor Cyan
            $testItems = @(
                "User registration/login",
                "Slack OAuth connection", 
                "File upload (PDF/DOCX)",
                "AI summarization",
                "Export functionality",
                "Billing/subscription",
                "Email notifications",
                "Error tracking",
                "Analytics tracking"
            )

            for ($i = 0; $i -lt $testItems.Length; $i++) {
                Write-Host "   $($i + 1). [ ] $($testItems[$i])"
            }

            Write-Host ""
            Write-Host "🚨 MONITORING DASHBOARDS:" -ForegroundColor Cyan
            Write-Host "• Vercel: https://vercel.com/dashboard"
            Write-Host "• Sentry: https://sentry.io/organizations/your-org/"
            Write-Host "• PostHog: https://app.posthog.com/"
            Write-Host "• Supabase: https://supabase.com/dashboard"
            Write-Host "• Stripe: https://dashboard.stripe.com/"

            Write-Host ""
            Write-Success "🎯 Your Slack Summary Scribe SaaS is now LIVE! 🚀"
            Write-Success "Ready to serve customers worldwide! 🌍"

            Write-Host ""
            Write-Host "📖 For detailed setup instructions, see:" -ForegroundColor Cyan
            Write-Host "   • VERCEL_PRODUCTION_DEPLOYMENT.md"
            Write-Host "   • DEPLOYMENT_CHECKLIST.md"
            Write-Host "   • .env.production.template"

            # Open deployment URL in browser
            Write-Host ""
            Write-Info "Opening deployment in browser..."
            Start-Process $deploymentUrl
        }
        else {
            Write-Warning "Deployment successful but could not extract URL"
        }
    }
    else {
        Write-Error "Deployment failed. Check Vercel logs for details."
        Write-Host $deployOutput
        exit 1
    }
}
catch {
    Write-Host ""
    Write-Host "==================================================" -ForegroundColor Red
    Write-Error "❌ DEPLOYMENT FAILED"
    Write-Host "==================================================" -ForegroundColor Red
    
    Write-Error "Error: $($_.Exception.Message)"
    
    Write-Host ""
    Write-Host "🔧 TROUBLESHOOTING STEPS:" -ForegroundColor Yellow
    Write-Host "1. Check Vercel CLI is installed: vercel --version"
    Write-Host "2. Ensure you are logged in: vercel login"
    Write-Host "3. Verify build passes locally: npm run build"
    Write-Host "4. Check environment variables are configured"
    Write-Host "5. Review Vercel deployment logs"
    
    Write-Host ""
    Write-Host "📞 SUPPORT RESOURCES:" -ForegroundColor Yellow
    Write-Host "• Vercel Support: https://vercel.com/help"
    Write-Host "• Next.js Docs: https://nextjs.org/docs"
    Write-Host "• Deployment Guide: ./VERCEL_PRODUCTION_DEPLOYMENT.md"
    
    exit 1
}

Write-Host ""
Write-Host "🎉 Deployment script completed successfully!" -ForegroundColor Green
