'use client';

import { useState, useEffect, useCallback } from 'react';
import { useUser } from '@clerk/nextjs';

interface DemoUsage {
  summaries: { used: number; limit: number; remaining: number };
  exports: { used: number; limit: number; remaining: number };
  aiRequests: { used: number; limit: number; remaining: number };
  fileUploads: { used: number; limit: number; remaining: number };
  slackConnections: { used: number; limit: number; remaining: number };
}

interface DemoStatus {
  isInDemo: boolean;
  trialDaysRemaining: number;
  trialExpired: boolean;
  usage: DemoUsage;
  features: {
    basic_summaries: boolean;
    file_upload: boolean;
    slack_integration: boolean;
    basic_exports: boolean;
    dashboard: boolean;
    analytics: boolean;
    custom_templates: boolean;
    team_management: boolean;
    advanced_ai: boolean;
  };
  upgradePrompt: {
    should_show: boolean;
    message: string;
    reason: string;
    urgency: 'low' | 'medium' | 'high';
  };
}

interface UseDemoStatusReturn {
  demoStatus: DemoStatus | null;
  loading: boolean;
  error: string | null;
  refreshStatus: () => Promise<void>;
  recordUpgradePrompt: () => Promise<void>;
  initializeDemoMode: () => Promise<void>;
}

const DEFAULT_DEMO_STATUS: DemoStatus = {
  isInDemo: false,
  trialDaysRemaining: 0,
  trialExpired: false,
  usage: {
    summaries: { used: 0, limit: -1, remaining: -1 },
    exports: { used: 0, limit: -1, remaining: -1 },
    aiRequests: { used: 0, limit: -1, remaining: -1 },
    fileUploads: { used: 0, limit: -1, remaining: -1 },
    slackConnections: { used: 0, limit: -1, remaining: -1 }
  },
  features: {
    basic_summaries: true,
    file_upload: true,
    slack_integration: true,
    basic_exports: true,
    dashboard: true,
    analytics: true,
    custom_templates: true,
    team_management: true,
    advanced_ai: true
  },
  upgradePrompt: {
    should_show: false,
    message: '',
    reason: '',
    urgency: 'low'
  }
};

export function useDemoStatus(): UseDemoStatusReturn {
  const { user, isLoaded } = useUser();
  const [demoStatus, setDemoStatus] = useState<DemoStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDemoStatus = useCallback(async () => {
    if (!isLoaded || !user) {
      setDemoStatus(DEFAULT_DEMO_STATUS);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/demo/status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch demo status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && data.demo_status) {
        setDemoStatus({
          isInDemo: data.demo_status.is_in_demo,
          trialDaysRemaining: data.demo_status.trial_days_remaining,
          trialExpired: data.demo_status.trial_expired,
          usage: data.demo_status.usage,
          features: data.demo_status.features,
          upgradePrompt: data.demo_status.upgrade_prompt
        });
      } else {
        setDemoStatus(DEFAULT_DEMO_STATUS);
      }
    } catch (err) {
      console.error('Error fetching demo status:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch demo status');
      setDemoStatus(DEFAULT_DEMO_STATUS);
    } finally {
      setLoading(false);
    }
  }, [user, isLoaded]);

  const refreshStatus = useCallback(async () => {
    await fetchDemoStatus();
  }, [fetchDemoStatus]);

  const recordUpgradePrompt = useCallback(async () => {
    if (!user) return;

    try {
      await fetch('/api/demo/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'record_upgrade_prompt'
        }),
      });
    } catch (err) {
      console.error('Error recording upgrade prompt:', err);
    }
  }, [user]);

  const initializeDemoMode = useCallback(async () => {
    if (!user) return;

    try {
      const response = await fetch('/api/demo/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'initialize'
        }),
      });

      if (response.ok) {
        await refreshStatus();
      }
    } catch (err) {
      console.error('Error initializing demo mode:', err);
    }
  }, [user, refreshStatus]);

  // Fetch demo status on mount and when user changes
  useEffect(() => {
    fetchDemoStatus();
  }, [fetchDemoStatus]);

  // Auto-refresh demo status every 5 minutes
  useEffect(() => {
    if (!demoStatus?.isInDemo) return;

    const interval = setInterval(() => {
      fetchDemoStatus();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [demoStatus?.isInDemo, fetchDemoStatus]);

  return {
    demoStatus,
    loading,
    error,
    refreshStatus,
    recordUpgradePrompt,
    initializeDemoMode
  };
}

// Helper hook for checking specific demo limits
export function useDemoLimitCheck() {
  const { demoStatus } = useDemoStatus();

  const canPerformAction = useCallback((
    actionType: 'summaries' | 'exports' | 'aiRequests' | 'fileUploads' | 'slackConnections'
  ): { allowed: boolean; reason?: string; upgradePrompt?: string } => {
    if (!demoStatus?.isInDemo) {
      return { allowed: true };
    }

    if (demoStatus.trialExpired) {
      return {
        allowed: false,
        reason: 'Trial expired',
        upgradePrompt: 'Your trial has expired. Upgrade to continue using this feature.'
      };
    }

    const usage = demoStatus.usage[actionType];
    if (usage.remaining <= 0) {
      return {
        allowed: false,
        reason: 'Usage limit reached',
        upgradePrompt: `You've reached your ${actionType} limit. Upgrade for unlimited access.`
      };
    }

    return { allowed: true };
  }, [demoStatus]);

  const canUseFeature = useCallback((
    feature: 'analytics' | 'customTemplates' | 'teamManagement' | 'advancedAI'
  ): { allowed: boolean; reason?: string; upgradePrompt?: string } => {
    if (!demoStatus?.isInDemo) {
      return { allowed: true };
    }

    if (!demoStatus.features[feature]) {
      return {
        allowed: false,
        reason: 'Premium feature',
        upgradePrompt: `${feature} is a premium feature. Upgrade to unlock it.`
      };
    }

    return { allowed: true };
  }, [demoStatus]);

  return {
    canPerformAction,
    canUseFeature,
    isInDemo: demoStatus?.isInDemo || false,
    trialExpired: demoStatus?.trialExpired || false
  };
}
