import { NextRequest, NextResponse } from 'next/server';
import { createRBACProtectedRoute, Permission } from '@/lib/auth-protection';
import { createSecureApiRoute } from '@/lib/api-security';
import { createSupabaseServerClient } from '@/lib/supabase-server';

/**
 * Admin Stats API
 * 
 * Provides system statistics for admin dashboard
 */
export const GET = createRBACProtectedRoute(
  async (request: NextRequest, authResult) => {
    try {
      const supabase = await createSupabaseServerClient();
      
      // Get user statistics
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id, created_at, last_sign_in_at')
        .order('created_at', { ascending: false });
      
      if (usersError) {
        console.error('Failed to fetch users:', usersError);
      }
      
      // Get subscription statistics
      const { data: subscriptions, error: subscriptionsError } = await supabase
        .from('subscriptions')
        .select('id, status, plan_id, amount')
        .eq('status', 'active');
      
      if (subscriptionsError) {
        console.error('Failed to fetch subscriptions:', subscriptionsError);
      }
      
      // Calculate statistics
      const totalUsers = users?.length || 0;
      const activeUsers = users?.filter(user => {
        if (!user.last_sign_in_at) return false;
        const lastSignIn = new Date(user.last_sign_in_at);
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        return lastSignIn > thirtyDaysAgo;
      }).length || 0;
      
      const totalSubscriptions = subscriptions?.length || 0;
      const revenue = subscriptions?.reduce((total, sub) => total + (sub.amount || 0), 0) || 0;
      
      // System health check (simplified)
      let systemHealth: 'healthy' | 'warning' | 'critical' = 'healthy';
      
      // Check if we have recent activity
      const recentUsers = users?.filter(user => {
        if (!user.last_sign_in_at) return false;
        const lastSignIn = new Date(user.last_sign_in_at);
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return lastSignIn > oneDayAgo;
      }).length || 0;
      
      if (recentUsers === 0 && totalUsers > 0) {
        systemHealth = 'warning';
      }
      
      const stats = {
        totalUsers,
        activeUsers,
        totalSubscriptions,
        revenue: Math.round(revenue / 100), // Convert cents to dollars
        systemHealth,
        recentActivity: recentUsers
      };
      
      return NextResponse.json(stats);
      
    } catch (error) {
      console.error('Failed to get admin stats:', error);
      return NextResponse.json(
        { error: 'Failed to load statistics' },
        { status: 500 }
      );
    }
  },
  {
    requiredPermission: Permission.ANALYTICS_VIEW,
    requireAuth: true,
    rateLimit: 30, // 30 requests per minute
    auditLog: true,
    allowedMethods: ['GET']
  }
);
