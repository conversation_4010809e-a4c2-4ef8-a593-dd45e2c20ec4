import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, Lock, Eye, Trash2, Download, AlertTriangle } from 'lucide-react';

export default function DataPolicyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
              <Shield className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Data Policy
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            How we collect, use, store, and protect your data at Slack Summary Scribe
          </p>
          <div className="flex justify-center mt-4">
            <Badge variant="outline" className="text-sm">
              Last updated: January 2024
            </Badge>
          </div>
        </div>

        <div className="space-y-6">
          {/* Data Collection */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Eye className="h-5 w-5 text-blue-600" />
                <CardTitle>1. Data We Collect</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <h3>Account Information</h3>
              <ul>
                <li>Email address and name (for authentication)</li>
                <li>Slack workspace information (when connected)</li>
                <li>Subscription and billing details</li>
                <li>Usage preferences and settings</li>
              </ul>

              <h3>Content Data</h3>
              <ul>
                <li>Slack messages and conversations (for summarization)</li>
                <li>Uploaded files and documents</li>
                <li>Generated summaries and AI outputs</li>
                <li>Export history and preferences</li>
              </ul>

              <h3>Technical Data</h3>
              <ul>
                <li>IP address and browser information</li>
                <li>Usage analytics and performance metrics</li>
                <li>Error logs and debugging information</li>
                <li>API usage and rate limiting data</li>
              </ul>
            </CardContent>
          </Card>

          {/* Data Usage */}
          <Card>
            <CardHeader>
              <CardTitle>2. How We Use Your Data</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <h3>Primary Purposes</h3>
              <ul>
                <li><strong>AI Summarization:</strong> Process your Slack conversations to generate intelligent summaries</li>
                <li><strong>Service Delivery:</strong> Provide core functionality and features</li>
                <li><strong>Account Management:</strong> Manage your subscription and preferences</li>
                <li><strong>Support:</strong> Provide customer support and troubleshooting</li>
              </ul>

              <h3>Secondary Purposes</h3>
              <ul>
                <li><strong>Analytics:</strong> Understand usage patterns to improve our service</li>
                <li><strong>Security:</strong> Detect and prevent fraud, abuse, and security threats</li>
                <li><strong>Legal Compliance:</strong> Meet legal and regulatory requirements</li>
              </ul>

              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-blue-900 dark:text-blue-100">AI Processing Notice</p>
                    <p className="text-blue-800 dark:text-blue-200 text-sm mt-1">
                      Your content is processed by AI models (DeepSeek, OpenAI) to generate summaries. 
                      We use enterprise-grade APIs with data protection agreements.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Data Storage */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Lock className="h-5 w-5 text-green-600" />
                <CardTitle>3. Data Storage & Security</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <h3>Storage Infrastructure</h3>
              <ul>
                <li><strong>Database:</strong> Supabase (PostgreSQL) with encryption at rest</li>
                <li><strong>File Storage:</strong> Secure cloud storage with access controls</li>
                <li><strong>Backups:</strong> Regular automated backups with encryption</li>
                <li><strong>Geographic Location:</strong> Data stored in secure data centers</li>
              </ul>

              <h3>Security Measures</h3>
              <ul>
                <li>End-to-end encryption for data in transit</li>
                <li>Row-level security (RLS) policies in database</li>
                <li>Regular security audits and penetration testing</li>
                <li>SOC 2 Type II compliance (in progress)</li>
              </ul>

              <h3>Data Retention</h3>
              <ul>
                <li><strong>Active Accounts:</strong> Data retained while account is active</li>
                <li><strong>Deleted Accounts:</strong> Data deleted within 30 days</li>
                <li><strong>Summaries:</strong> Retained for 2 years or until deletion request</li>
                <li><strong>Logs:</strong> Technical logs retained for 90 days</li>
              </ul>
            </CardContent>
          </Card>

          {/* Data Rights */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Download className="h-5 w-5 text-purple-600" />
                <CardTitle>4. Your Data Rights</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <h3>Access & Portability</h3>
              <ul>
                <li><strong>Data Export:</strong> Download all your data in standard formats</li>
                <li><strong>Account Dashboard:</strong> View and manage your data through settings</li>
                <li><strong>API Access:</strong> Programmatic access to your data via API</li>
              </ul>

              <h3>Control & Deletion</h3>
              <ul>
                <li><strong>Selective Deletion:</strong> Delete specific summaries or files</li>
                <li><strong>Account Deletion:</strong> Complete account and data removal</li>
                <li><strong>Data Correction:</strong> Update or correct your information</li>
                <li><strong>Processing Restriction:</strong> Limit how we process your data</li>
              </ul>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                <p className="font-medium text-green-900 dark:text-green-100">Exercise Your Rights</p>
                <p className="text-green-800 dark:text-green-200 text-sm mt-1">
                  Contact us at <a href="mailto:<EMAIL>" className="underline"><EMAIL></a> to exercise any of these rights.
                  We'll respond within 30 days.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Data Sharing */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Trash2 className="h-5 w-5 text-red-600" />
                <CardTitle>5. Data Sharing & Third Parties</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <h3>We DO NOT sell your data</h3>
              <p>We never sell, rent, or trade your personal data or content to third parties for marketing purposes.</p>

              <h3>Limited Sharing</h3>
              <ul>
                <li><strong>AI Providers:</strong> Content sent to AI APIs for processing (with data protection agreements)</li>
                <li><strong>Service Providers:</strong> Infrastructure and support services (under strict contracts)</li>
                <li><strong>Legal Requirements:</strong> When required by law or to protect rights and safety</li>
              </ul>

              <h3>Third-Party Services</h3>
              <ul>
                <li><strong>Slack API:</strong> To access your workspace data (with your permission)</li>
                <li><strong>Analytics:</strong> PostHog for privacy-friendly usage analytics</li>
                <li><strong>Monitoring:</strong> Sentry for error tracking and performance monitoring</li>
                <li><strong>Payments:</strong> Stripe for secure payment processing</li>
              </ul>
            </CardContent>
          </Card>

          {/* Contact */}
          <Card>
            <CardHeader>
              <CardTitle>6. Contact & Questions</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <p>
                If you have questions about this Data Policy or how we handle your data, please contact us:
              </p>
              <ul>
                <li><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
                <li><strong>Data Protection Officer:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
                <li><strong>General Support:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
              </ul>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-6">
                This Data Policy is part of our Terms of Service and Privacy Policy. 
                We may update this policy from time to time, and we'll notify you of significant changes.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
