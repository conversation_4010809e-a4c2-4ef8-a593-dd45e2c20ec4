#!/usr/bin/env tsx

/**
 * Authentication Protection Test Script
 * 
 * Tests both client-side and server-side route protection
 * to ensure Clerk authentication is working properly.
 */

import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

interface TestResult {
  route: string;
  expected: string;
  actual: string;
  passed: boolean;
  responseTime: number;
}

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

/**
 * Test routes that should be protected
 */
const PROTECTED_ROUTES = [
  '/dashboard',
  '/api/dashboard',
  '/api/summaries',
  '/api/upload',
  '/settings',
  '/analytics'
];

/**
 * Test routes that should be public
 */
const PUBLIC_ROUTES = [
  '/',
  '/sign-in',
  '/sign-up',
  '/api/health',
  '/api/healthcheck'
];

/**
 * Test a single route
 */
async function testRoute(route: string, shouldBeProtected: boolean): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${BASE_URL}${route}`, {
      method: 'GET',
      redirect: 'manual', // Don't follow redirects
      headers: {
        'User-Agent': 'Auth-Test/1.0'
      }
    });

    const responseTime = Date.now() - startTime;
    const status = response.status;

    if (shouldBeProtected) {
      // Protected routes should redirect (307/302) or return 401
      const isProtected = status === 307 || status === 302 || status === 401;
      const expected = 'Redirect to sign-in or 401 Unauthorized';
      const actual = `${status} ${response.statusText}`;
      
      return {
        route,
        expected,
        actual,
        passed: isProtected,
        responseTime
      };
    } else {
      // Public routes should be accessible (200)
      const isAccessible = status === 200;
      const expected = '200 OK';
      const actual = `${status} ${response.statusText}`;
      
      return {
        route,
        expected,
        actual,
        passed: isAccessible,
        responseTime
      };
    }

  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      route,
      expected: shouldBeProtected ? 'Protected' : 'Accessible',
      actual: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      passed: false,
      responseTime
    };
  }
}

/**
 * Run all authentication tests
 */
async function runAuthTests(): Promise<void> {
  console.log('🔐 Testing Clerk Authentication Protection...\n');
  console.log(`Base URL: ${BASE_URL}\n`);

  const results: TestResult[] = [];

  // Test protected routes
  console.log('🔒 Testing Protected Routes:');
  console.log('============================');
  
  for (const route of PROTECTED_ROUTES) {
    const result = await testRoute(route, true);
    results.push(result);
    
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${route.padEnd(20)} | ${result.actual} (${result.responseTime}ms)`);
  }

  console.log('\n🌐 Testing Public Routes:');
  console.log('=========================');
  
  // Test public routes
  for (const route of PUBLIC_ROUTES) {
    const result = await testRoute(route, false);
    results.push(result);
    
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${route.padEnd(20)} | ${result.actual} (${result.responseTime}ms)`);
  }

  // Summary
  const totalTests = results.length;
  const passedTests = results.filter(r => r.passed).length;
  const failedTests = totalTests - passedTests;
  const averageResponseTime = Math.round(
    results.reduce((sum, r) => sum + r.responseTime, 0) / totalTests
  );

  console.log('\n📊 Test Summary:');
  console.log('================');
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ${failedTests > 0 ? '❌' : '✅'}`);
  console.log(`Average Response Time: ${averageResponseTime}ms`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

  if (failedTests > 0) {
    console.log('\n❌ Failed Tests:');
    console.log('================');
    results.filter(r => !r.passed).forEach(result => {
      console.log(`- ${result.route}: Expected ${result.expected}, got ${result.actual}`);
    });
    
    console.log('\n🛠️ Troubleshooting:');
    console.log('===================');
    console.log('1. Ensure your development server is running: npm run dev');
    console.log('2. Check that Clerk keys are properly configured in .env.local');
    console.log('3. Verify middleware.ts is working correctly');
    console.log('4. Test manually by visiting the routes in your browser');
    
    process.exit(1);
  } else {
    console.log('\n🎉 All authentication tests passed!');
    console.log('✅ Protected routes are properly secured');
    console.log('✅ Public routes are accessible');
    console.log('✅ Clerk authentication is working correctly');
    console.log('\n🚀 Your app is ready for production!');
  }
}

/**
 * Test specific authentication scenarios
 */
async function testAuthScenarios(): Promise<void> {
  console.log('\n🧪 Testing Authentication Scenarios:');
  console.log('====================================');

  // Test 1: Dashboard redirect
  try {
    const response = await fetch(`${BASE_URL}/dashboard`, {
      redirect: 'manual'
    });
    
    if (response.status === 307 || response.status === 302) {
      const location = response.headers.get('location');
      if (location && location.includes('/sign-in')) {
        console.log('✅ Dashboard correctly redirects to sign-in');
      } else {
        console.log('❌ Dashboard redirects but not to sign-in');
      }
    } else {
      console.log('❌ Dashboard should redirect when not authenticated');
    }
  } catch (error) {
    console.log('❌ Error testing dashboard redirect:', error);
  }

  // Test 2: API protection
  try {
    const response = await fetch(`${BASE_URL}/api/dashboard`);
    
    if (response.status === 401) {
      console.log('✅ API routes properly return 401 for unauthenticated requests');
    } else {
      console.log('❌ API routes should return 401 for unauthenticated requests');
    }
  } catch (error) {
    console.log('❌ Error testing API protection:', error);
  }

  // Test 3: Public route accessibility
  try {
    const response = await fetch(`${BASE_URL}/sign-in`);
    
    if (response.status === 200) {
      console.log('✅ Sign-in page is accessible');
    } else {
      console.log('❌ Sign-in page should be accessible');
    }
  } catch (error) {
    console.log('❌ Error testing sign-in page:', error);
  }
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  try {
    await runAuthTests();
    await testAuthScenarios();
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Authentication tests failed:', error);
    process.exit(1);
  });
}

export { runAuthTests, testAuthScenarios };
