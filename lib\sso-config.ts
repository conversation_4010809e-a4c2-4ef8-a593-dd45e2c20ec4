/**
 * SSO Configuration Service
 * Enterprise Single Sign-On integration with Clerk
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';
import { SentryTracker } from '@/lib/sentry.client';
import { logAuditEvent } from '@/lib/audit-logging';
import { getCurrentUser } from '@/lib/user-management';

export interface SSOProvider {
  id: string;
  name: string;
  type: 'saml' | 'oidc' | 'oauth';
  enabled: boolean;
  configuration: {
    issuer?: string;
    sso_url?: string;
    certificate?: string;
    client_id?: string;
    client_secret?: string;
    discovery_url?: string;
    scopes?: string[];
    attribute_mapping?: {
      email?: string;
      first_name?: string;
      last_name?: string;
      groups?: string[];
    };
  };
  domains: string[];
  auto_provision: boolean;
  default_role: 'admin' | 'member';
  created_at: string;
  updated_at: string;
}

export interface SSOConfiguration {
  organization_id: string;
  providers: SSOProvider[];
  enforce_sso: boolean;
  allow_email_password: boolean;
  session_timeout: number; // in minutes
  require_mfa: boolean;
  allowed_domains: string[];
  created_at: string;
  updated_at: string;
}

/**
 * Get SSO configuration for organization
 */
export async function getSSOConfiguration(
  organizationId: string
): Promise<{ success: boolean; config?: SSOConfiguration; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    const { data: config, error } = await supabase
      .from('sso_configurations')
      .select(`
        *,
        sso_providers (*)
      `)
      .eq('organization_id', organizationId)
      .single();

    if (error && error.code !== 'PGRST116') { // Not found is OK
      throw error;
    }

    if (!config) {
      // Return default configuration
      return {
        success: true,
        config: {
          organization_id: organizationId,
          providers: [],
          enforce_sso: false,
          allow_email_password: true,
          session_timeout: 480, // 8 hours
          require_mfa: false,
          allowed_domains: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      };
    }

    const ssoConfig: SSOConfiguration = {
      organization_id: config.organization_id,
      providers: config.sso_providers || [],
      enforce_sso: config.enforce_sso,
      allow_email_password: config.allow_email_password,
      session_timeout: config.session_timeout,
      require_mfa: config.require_mfa,
      allowed_domains: config.allowed_domains || [],
      created_at: config.created_at,
      updated_at: config.updated_at,
    };

    return { success: true, config: ssoConfig };

  } catch (error) {
    console.error('Error getting SSO configuration:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get SSO configuration',
    };
  }
}

/**
 * Update SSO configuration
 */
export async function updateSSOConfiguration(
  organizationId: string,
  userId: string,
  userEmail: string,
  config: Partial<SSOConfiguration>
): Promise<{ success: boolean; config?: SSOConfiguration; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Update main configuration
    const { data: updatedConfig, error: configError } = await supabase
      .from('sso_configurations')
      .upsert({
        organization_id: organizationId,
        enforce_sso: config.enforce_sso,
        allow_email_password: config.allow_email_password,
        session_timeout: config.session_timeout,
        require_mfa: config.require_mfa,
        allowed_domains: config.allowed_domains,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (configError) {
      throw configError;
    }

    // Log audit event
    await logAuditEvent(
      organizationId,
      userId,
      userEmail,
      'sso.configuration_updated',
      'sso_configuration',
      { changes: config },
      { riskLevel: 'high' }
    );

    // Get updated configuration with providers
    const result = await getSSOConfiguration(organizationId);
    
    return result;

  } catch (error) {
    console.error('Error updating SSO configuration:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update SSO configuration',
    };
  }
}

/**
 * Add SSO provider
 */
export async function addSSOProvider(
  organizationId: string,
  userId: string,
  userEmail: string,
  provider: Omit<SSOProvider, 'id' | 'created_at' | 'updated_at'>
): Promise<{ success: boolean; provider?: SSOProvider; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    const { data: newProvider, error } = await supabase
      .from('sso_providers')
      .insert({
        organization_id: organizationId,
        name: provider.name,
        type: provider.type,
        enabled: provider.enabled,
        configuration: provider.configuration,
        domains: provider.domains,
        auto_provision: provider.auto_provision,
        default_role: provider.default_role,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Log audit event
    await logAuditEvent(
      organizationId,
      userId,
      userEmail,
      'sso.provider_added',
      'sso_provider',
      { provider_name: provider.name, provider_type: provider.type },
      { riskLevel: 'high' }
    );

    return { success: true, provider: newProvider };

  } catch (error) {
    console.error('Error adding SSO provider:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to add SSO provider',
    };
  }
}

/**
 * Update SSO provider
 */
export async function updateSSOProvider(
  organizationId: string,
  providerId: string,
  userId: string,
  userEmail: string,
  updates: Partial<SSOProvider>
): Promise<{ success: boolean; provider?: SSOProvider; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    const { data: updatedProvider, error } = await supabase
      .from('sso_providers')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', providerId)
      .eq('organization_id', organizationId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Log audit event
    await logAuditEvent(
      organizationId,
      userId,
      userEmail,
      'sso.provider_updated',
      'sso_provider',
      { provider_id: providerId, changes: updates },
      { riskLevel: 'high' }
    );

    return { success: true, provider: updatedProvider };

  } catch (error) {
    console.error('Error updating SSO provider:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update SSO provider',
    };
  }
}

/**
 * Remove SSO provider
 */
export async function removeSSOProvider(
  organizationId: string,
  providerId: string,
  userId: string,
  userEmail: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Get provider details for audit log
    const { data: provider } = await supabase
      .from('sso_providers')
      .select('name, type')
      .eq('id', providerId)
      .eq('organization_id', organizationId)
      .single();

    const { error } = await supabase
      .from('sso_providers')
      .delete()
      .eq('id', providerId)
      .eq('organization_id', organizationId);

    if (error) {
      throw error;
    }

    // Log audit event
    await logAuditEvent(
      organizationId,
      userId,
      userEmail,
      'sso.provider_removed',
      'sso_provider',
      { 
        provider_id: providerId, 
        provider_name: provider?.name,
        provider_type: provider?.type,
      },
      { riskLevel: 'high' }
    );

    return { success: true };

  } catch (error) {
    console.error('Error removing SSO provider:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to remove SSO provider',
    };
  }
}

/**
 * Test SSO provider connection
 */
export async function testSSOProvider(
  organizationId: string,
  providerId: string
): Promise<{ success: boolean; test_result?: any; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    const { data: provider, error } = await supabase
      .from('sso_providers')
      .select('*')
      .eq('id', providerId)
      .eq('organization_id', organizationId)
      .single();

    if (error) {
      throw error;
    }

    // Perform connection test based on provider type
    let testResult;
    
    switch (provider.type) {
      case 'saml':
        testResult = await testSAMLConnection(provider);
        break;
      case 'oidc':
        testResult = await testOIDCConnection(provider);
        break;
      case 'oauth':
        testResult = await testOAuthConnection(provider);
        break;
      default:
        throw new Error(`Unsupported provider type: ${provider.type}`);
    }

    return { success: true, test_result: testResult };

  } catch (error) {
    console.error('Error testing SSO provider:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to test SSO provider',
    };
  }
}

// Helper functions for testing different SSO provider types

async function testSAMLConnection(provider: SSOProvider): Promise<any> {
  // Implement SAML connection test
  return {
    status: 'success',
    message: 'SAML connection test successful',
    metadata_url: provider.configuration.sso_url,
  };
}

async function testOIDCConnection(provider: SSOProvider): Promise<any> {
  // Implement OIDC connection test
  try {
    if (provider.configuration.discovery_url) {
      const response = await fetch(provider.configuration.discovery_url);
      const discovery = await response.json();
      
      return {
        status: 'success',
        message: 'OIDC discovery successful',
        endpoints: discovery,
      };
    }
    
    return {
      status: 'warning',
      message: 'No discovery URL configured',
    };
  } catch (error) {
    return {
      status: 'error',
      message: 'OIDC discovery failed',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function testOAuthConnection(provider: SSOProvider): Promise<any> {
  // Implement OAuth connection test
  return {
    status: 'success',
    message: 'OAuth configuration valid',
    client_id: provider.configuration.client_id,
  };
}
