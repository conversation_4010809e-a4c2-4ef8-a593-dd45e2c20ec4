{"name": "@saas-kit/admin-ui", "version": "1.0.0", "description": "Production-ready admin dashboard components for SaaS applications", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "README.md", "LICENSE"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts --external react,react-dom", "dev": "tsup src/index.ts --format cjs,esm --dts --watch --external react,react-dom", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["admin", "dashboard", "ui", "components", "saas", "react", "typescript", "tailwind"], "author": "SaaS Kit Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/saas-kit/admin-ui.git"}, "bugs": {"url": "https://github.com/saas-kit/admin-ui/issues"}, "homepage": "https://github.com/saas-kit/admin-ui#readme", "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@storybook/addon-essentials": "^7.6.0", "@storybook/addon-interactions": "^7.6.0", "@storybook/addon-links": "^7.6.0", "@storybook/blocks": "^7.6.0", "@storybook/react": "^7.6.0", "@storybook/react-vite": "^7.6.0", "@storybook/testing-library": "^0.2.2", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.54.0", "react": "^18.2.0", "react-dom": "^18.2.0", "storybook": "^7.6.0", "tsup": "^8.0.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "engines": {"node": ">=16.0.0"}, "publishConfig": {"access": "public"}}