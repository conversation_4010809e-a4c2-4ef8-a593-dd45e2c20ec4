# 🔒 HTTPS Development Setup Guide

This guide provides complete instructions for setting up HTTPS in local development to fix SSL protocol errors and OAuth redirect issues.

## 🚀 Quick Start

### Option 1: LocalTunnel (Recommended)

```bash
# Install localtunnel globally
npm install -g localtunnel

# Start your Next.js app
npm run dev

# In a new terminal, create HTTPS tunnel
npx localtunnel --port 3000 --subdomain slack-summary-dev

# Or use the npm script
npm run dev:https
```

Your app will be available at: `https://slack-summary-dev.loca.lt`

### Option 2: ngrok (Alternative)

```bash
# Install ngrok (requires account)
# Download from https://ngrok.com/download

# Start your Next.js app
npm run dev

# In a new terminal, create HTTPS tunnel
ngrok http 3000

# Or use the npm script
npm run dev:ngrok
```

## 🔧 Environment Configuration

### For HTTPS Development

Update your `.env.local` file when using HTTPS:

```bash
# HTTPS Development URLs (uncomment when using localtunnel/ngrok)
NEXT_PUBLIC_SITE_URL=https://slack-summary-dev.loca.lt
NEXTAUTH_URL=https://slack-summary-dev.loca.lt
NEXT_PUBLIC_APP_URL=https://slack-summary-dev.loca.lt

# Comment out HTTP URLs
# NEXT_PUBLIC_SITE_URL=http://localhost:3000
# NEXTAUTH_URL=http://localhost:3000
# NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### For HTTP Development (Default)

```bash
# HTTP Development URLs (default)
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXTAUTH_URL=http://localhost:3000
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 🔗 OAuth Provider Configuration

Update your OAuth providers with the correct redirect URLs:

### Supabase Auth Settings

1. Go to your Supabase project dashboard
2. Navigate to Authentication > Settings
3. Update Site URL and Redirect URLs:

```
Site URL: https://slack-summary-dev.loca.lt
Redirect URLs:
- https://slack-summary-dev.loca.lt/auth/callback
- https://slack-summary-dev.loca.lt/api/auth/callback
- http://localhost:3000/auth/callback (for fallback)
```

### Google OAuth (Google Cloud Console)

1. Go to Google Cloud Console
2. Navigate to APIs & Services > Credentials
3. Edit your OAuth 2.0 Client ID
4. Add to Authorized redirect URIs:

```
https://slack-summary-dev.loca.lt/auth/callback
https://your-project.supabase.co/auth/v1/callback
```

### GitHub OAuth (GitHub Developer Settings)

1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Edit your OAuth App
3. Update Authorization callback URL:

```
https://your-project.supabase.co/auth/v1/callback
```

### Slack OAuth (Slack API)

1. Go to Slack API > Your Apps
2. Navigate to OAuth & Permissions
3. Add to Redirect URLs:

```
https://your-project.supabase.co/auth/v1/callback
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. ERR_SSL_PROTOCOL_ERROR
- **Cause**: Mixed HTTP/HTTPS URLs
- **Solution**: Ensure all environment variables use the same protocol

#### 2. Infinite Redirect Loops
- **Cause**: Missing auth callback route or incorrect redirect URLs
- **Solution**: Verify `/auth/callback` route exists and OAuth providers are configured correctly

#### 3. Session Not Detected
- **Cause**: Cookie domain mismatch
- **Solution**: Our enhanced cookie configuration automatically handles this

#### 4. LocalTunnel Connection Issues
- **Cause**: Subdomain conflicts or network issues
- **Solution**: Try a different subdomain or use ngrok

### Debug Commands

```bash
# Check if auth callback route exists
ls -la app/auth/callback/

# Verify environment variables
npm run check-env

# Test HTTPS tunnel
curl -I https://slack-summary-dev.loca.lt

# Check Supabase connection
npm run validate-env
```

## 📋 Verification Checklist

- [ ] HTTPS tunnel is running and accessible
- [ ] Environment variables updated for HTTPS
- [ ] Supabase redirect URLs configured
- [ ] OAuth providers updated with HTTPS URLs
- [ ] Auth callback route exists (`app/auth/callback/route.ts`)
- [ ] No SSL protocol errors in browser console
- [ ] OAuth login redirects properly
- [ ] Session persists across page refreshes

## 🔄 Switching Between HTTP and HTTPS

### To HTTPS Development:
1. Start HTTPS tunnel: `npm run dev:https`
2. Update `.env.local` with HTTPS URLs
3. Update OAuth provider settings
4. Restart Next.js: `npm run dev`

### Back to HTTP Development:
1. Stop HTTPS tunnel
2. Update `.env.local` with HTTP URLs
3. Restart Next.js: `npm run dev`

## 🚀 Production Deployment

For production on Vercel:

```bash
# Production environment variables
NEXT_PUBLIC_SITE_URL=https://your-domain.vercel.app
NEXTAUTH_URL=https://your-domain.vercel.app
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
```

Update OAuth providers with production URLs:
- `https://your-domain.vercel.app/auth/callback`
- `https://your-project.supabase.co/auth/v1/callback`

## 📞 Support

If you encounter issues:
1. Check the browser console for errors
2. Verify all URLs use the same protocol (HTTP or HTTPS)
3. Ensure OAuth providers are configured correctly
4. Test with a fresh incognito browser session
