import { devLog } from '@/lib/console-cleaner';
/**
 * Production-Grade Dynamic Import Handler
 * Provides robust error handling, retry mechanisms, and fallbacks for dynamic imports
 */

interface DynamicImportOptions {
  maxRetries?: number;
  retryDelay?: number;
  fallback?: () => Promise<any>;
  onError?: (error: Error, attempt: number) => void;
  timeout?: number;
}

interface ImportCache {
  [key: string]: Promise<any>;
}

class DynamicImportHandler {
  private cache: ImportCache = {};
  private retryAttempts: Map<string, number> = new Map();

  /**
   * Safe dynamic import with retry logic and error handling
   */
  async import<T = any>(
    importFn: () => Promise<T>,
    options: DynamicImportOptions = {}
  ): Promise<T> {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      fallback,
      onError,
      timeout = 30000,
    } = options;

    const importKey = importFn.toString();
    const currentAttempt = this.retryAttempts.get(importKey) || 0;

    // Return cached promise if available
    if (this.cache[importKey] && currentAttempt === 0) {
      return this.cache[importKey];
    }

    const importPromise = this.executeImport(importFn, timeout);
    
    // Cache the promise
    if (currentAttempt === 0) {
      this.cache[importKey] = importPromise;
    }

    try {
      const result = await importPromise;
      // Reset retry count on success
      this.retryAttempts.delete(importKey);
      return result;
    } catch (error) {
      // Remove failed promise from cache
      delete this.cache[importKey];
      
      const attempt = currentAttempt + 1;
      this.retryAttempts.set(importKey, attempt);

      if (onError) {
        onError(error as Error, attempt);
      }

      // Try fallback if available and max retries exceeded
      if (attempt >= maxRetries && fallback) {
        console.warn(`Dynamic import failed after ${maxRetries} attempts, using fallback`);
        try {
          return await fallback();
        } catch (fallbackError) {
          console.error('Fallback also failed:', fallbackError);
          throw error;
        }
      }

      // Retry if attempts remaining
      if (attempt < maxRetries) {
        console.warn(`Dynamic import failed, retrying (${attempt}/${maxRetries})...`);
        await this.delay(retryDelay * attempt);
        return this.import(importFn, options);
      }

      // Max retries exceeded
      this.retryAttempts.delete(importKey);
      throw error;
    }
  }

  /**
   * Execute import with timeout
   */
  private async executeImport<T>(
    importFn: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    return Promise.race([
      importFn(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Import timeout')), timeout)
      ),
    ]);
  }

  /**
   * Delay utility for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clear cache for specific import or all imports
   */
  clearCache(importKey?: string): void {
    if (importKey) {
      delete this.cache[importKey];
      this.retryAttempts.delete(importKey);
    } else {
      this.cache = {};
      this.retryAttempts.clear();
    }
  }

  /**
   * Preload imports for better performance
   */
  async preload(importFns: Array<() => Promise<any>>): Promise<void> {
    const preloadPromises = importFns.map(fn => 
      this.import(fn, { maxRetries: 1 }).catch(error => {
        console.warn('Preload failed:', error);
        return null;
      })
    );

    await Promise.allSettled(preloadPromises);
  }
}

// Singleton instance
export const dynamicImportHandler = new DynamicImportHandler();

/**
 * Safe dynamic import wrapper for React components
 */
export function safeDynamicImport<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: DynamicImportOptions = {}
) {
  return () => dynamicImportHandler.import(importFn, {
    maxRetries: 3,
    retryDelay: 1000,
    onError: (error, attempt) => {
      console.warn(`Component import failed (attempt ${attempt}):`, error);
      
      // Track import failures for monitoring
      if (typeof window !== 'undefined' && (window as any).posthog) {
        (window as any).posthog.capture('dynamic_import_failure', {
          error: error.message,
          attempt,
          component: importFn.toString().slice(0, 100),
        });
      }
    },
    ...options,
  });
}

/**
 * Safe dynamic import for utilities and libraries
 */
export function safeLibraryImport<T = any>(
  importFn: () => Promise<T>,
  fallbackValue?: T,
  options: DynamicImportOptions = {}
) {
  return dynamicImportHandler.import(importFn, {
    maxRetries: 2,
    retryDelay: 500,
    fallback: fallbackValue ? () => Promise.resolve(fallbackValue) : undefined,
    onError: (error, attempt) => {
      console.warn(`Library import failed (attempt ${attempt}):`, error);
    },
    ...options,
  });
}

/**
 * Chunk loading error recovery
 */
export function handleChunkLoadingError(error: Error): void {
  console.error('Chunk loading error detected:', error);
  
  // Clear dynamic import cache
  dynamicImportHandler.clearCache();
  
  // Clear browser caches
  if ('caches' in window) {
    caches.keys().then(names => {
      names.forEach(name => caches.delete(name));
    });
  }
  
  // Clear module cache if available
  if (typeof window !== 'undefined' && (window as any).__webpack_require__) {
    const webpackRequire = (window as any).__webpack_require__;
    if (webpackRequire.cache) {
      Object.keys(webpackRequire.cache).forEach(key => {
        delete webpackRequire.cache[key];
      });
    }
  }
  
  // Reload page as last resort
  setTimeout(() => {
    window.location.reload();
  }, 1000);
}

/**
 * Initialize chunk loading error handlers
 */
export function initializeChunkErrorHandlers(): void {
  if (typeof window === 'undefined') return;

  // Global error handler
  window.addEventListener('error', (event) => {
    const error = event.error;
    const message = event.message || '';

    if (
      error?.name === 'ChunkLoadError' ||
      message.includes('Loading chunk') ||
      message.includes('ChunkLoadError') ||
      message.includes('Failed to import') ||
      message.includes('Loading CSS chunk') ||
      message.includes("Cannot read properties of undefined (reading 'call')") ||
      message.includes('runtime.js') ||
      message.includes('main.js') ||
      message.includes('framework.js')
    ) {
      event.preventDefault();
      handleChunkLoadingError(error || new Error(message));
    }
  });

  // Promise rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    const reason = event.reason;

    if (
      reason?.name === 'ChunkLoadError' ||
      reason?.message?.includes('Loading chunk') ||
      reason?.message?.includes('Failed to import') ||
      reason?.message?.includes('Loading CSS chunk')
    ) {
      event.preventDefault();
      handleChunkLoadingError(reason);
    }
  });

  // Enhanced webpack chunk loading error handler
  if (typeof window !== 'undefined') {
    // Override webpack's chunk loading function
    const originalWebpackChunkLoad = (window as any).__webpack_require__?.f?.j;
    if (originalWebpackChunkLoad) {
      (window as any).__webpack_require__.f.j = function(chunkId: string, promises: Promise<any>[]) {
        const originalPromise = originalWebpackChunkLoad.call(this, chunkId, promises);
        return originalPromise.catch((error: any) => {
          console.warn(`Chunk loading failed for ${chunkId}:`, error);
          handleChunkLoadingError(error);
          throw error;
        });
      };
    }

    // Monitor for runtime errors
    const originalOnerror = window.onerror;
    window.onerror = function(message, source, lineno, colno, error) {
      if (typeof message === 'string' && (
        message.includes('ChunkLoadError') ||
        message.includes('Loading chunk') ||
        message.includes('script error')
      )) {
        handleChunkLoadingError(error || new Error(message));
      }

      if (originalOnerror) {
        return originalOnerror.call(this, message, source, lineno, colno, error);
      }
      return false;
    };
  }
  devLog.log('🛡️ Enhanced chunk loading error handlers initialized');
}

// Auto-initialize on import
if (typeof window !== 'undefined') {
  // Initialize after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeChunkErrorHandlers);
  } else {
    initializeChunkErrorHandlers();
  }
}

export default dynamicImportHandler;
