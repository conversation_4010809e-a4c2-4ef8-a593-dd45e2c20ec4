import { devLog } from '@/lib/console-cleaner';
/**
 * Public Mode User Management System
 *
 * Handles user management for public access mode
 * Creates anonymous users for tracking while maintaining public access
 * No authentication required - all users get full access
 */

import { DEFAULT_PUBLIC_USER, type PublicUser } from '@/lib/dev-auth';

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  last_active_at?: string;
  settings?: UserSettings;
  // Dev mode properties
  orgId?: string;
  plan?: string;
}

export interface UserSettings {
  theme: 'light' | 'dark' | 'system';
  notifications: {
    email: boolean;
    slack: boolean;
    push: boolean;
  };
  timezone: string;
  language: string;
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  plan: 'FREE' | 'PRO' | 'ENTERPRISE';
  created_at: string;
  updated_at: string;
  settings: OrganizationSettings;
}

export interface OrganizationSettings {
  features: string[];
  limits: {
    summaries_per_month: number;
    file_size_mb: number;
    exports_per_month: number;
    users: number;
  };
  integrations: {
    slack_enabled: boolean;
    email_enabled: boolean;
  };
}

/**
 * Get or create anonymous user for public access
 */
export async function getCurrentUser(): Promise<User | null> {
  try {
    // For public access, create a session-based anonymous user
    const sessionId = typeof window !== 'undefined'
      ? sessionStorage.getItem('slack-summarizer-session') || generateAnonymousSession()
      : generateAnonymousSession();

    // Create anonymous user object with full access
    const anonymousUser: User = {
      id: sessionId,
      email: `user_${sessionId.slice(-6)}@slacksummarizer.com`,
      name: `User ${sessionId.slice(-6)}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      orgId: `org_${sessionId}`,
      plan: 'PRO' // Give full access in public mode
    };

    return anonymousUser;
  } catch (error) {
    console.error('Error getting current user:', error);
    // Return default user as fallback
    return {
      id: 'fallback-user',
      email: '<EMAIL>',
      name: 'Public User',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      orgId: 'org-fallback',
      plan: 'PRO'
    };
  }
}

/**
 * Get current user from session (client-side)
 */
export async function getCurrentUserClient(): Promise<User | null> {
  return getCurrentUser();
}

/**
 * Generate anonymous session ID
 */
function generateAnonymousSession(): string {
  const sessionId = 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('slack-summarizer-session', sessionId);
  }
  return sessionId;
}

/**
 * Get user's organization - Public Mode
 */
export async function getUserOrganization(userId: string): Promise<Organization | null> {
  // Return public organization with full features
  return {
    id: `org-${userId}`,
    name: 'Public Organization',
    slug: 'public-org',
    plan: 'PRO',
    settings: {
      features: ['ai_summaries', 'slack_integration', 'exports', 'analytics', 'notifications'],
      limits: {
        summaries_per_month: 1000,
        file_size_mb: 20,
        exports_per_month: 100,
        users: 1
      },
      integrations: {
        slack_enabled: true,
        email_enabled: true
      }
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}

/**
 * Create or update user profile - Public Mode
 */
export async function upsertUserProfile(user: Partial<User>): Promise<User | null> {
  // Return the current user (no actual database operation needed)
  return getCurrentUser();
}

/**
 * Update user settings - Public Mode
 */
export async function updateUserSettings(userId: string, settings: Partial<UserSettings>): Promise<boolean> {
  // Always return success (settings stored in localStorage)
  if (typeof window !== 'undefined') {
    localStorage.setItem('user-settings', JSON.stringify(settings));
  }
  return true;
}

/**
 * Get default user settings
 */
export function getDefaultUserSettings(): UserSettings {
  return {
    theme: 'system',
    notifications: {
      email: true,
      slack: false,
      push: false
    },
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    language: 'en'
  };
}

/**
 * Check if user has access to feature - DEV ONLY
 */
export async function hasFeatureAccess(userId: string, feature: string): Promise<boolean> {
  // All features are available in dev mode
  return true;
}

/**
 * Get user's usage limits - DEV ONLY
 */
export async function getUserLimits(userId: string): Promise<OrganizationSettings['limits'] | null> {
  // Return dev limits
  return {
    monthly_summaries: 100,
    team_members: 10,
    storage_gb: 5
  };
}

/**
 * Track user activity - DEV ONLY
 */
export async function trackUserActivity(userId: string): Promise<void> {
  // No-op in dev mode
  devLog.log('Dev mode: User activity tracked for', userId);
}
