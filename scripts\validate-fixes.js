#!/usr/bin/env node

/**
 * Validation Script for ChunkLoadError Fixes
 * 
 * This script validates that all fixes are working correctly:
 * 1. ChunkLoadError and dynamic import fixes
 * 2. Public demo mode (no auth required)
 * 3. Runtime error recovery
 * 4. Production optimizations
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating ChunkLoadError Fixes and Public Demo Mode...\n');

// Test 1: Check if upload page has safe imports
function validateUploadPageFixes() {
  console.log('✅ Test 1: Upload Page Dynamic Import Fixes');
  
  const uploadPagePath = path.join(process.cwd(), 'app/upload/page.tsx');
  
  if (!fs.existsSync(uploadPagePath)) {
    console.log('❌ Upload page not found');
    return false;
  }
  
  const content = fs.readFileSync(uploadPagePath, 'utf8');
  
  // Check for safe icon implementation
  if (content.includes('SafeIcon') && content.includes('useEffect')) {
    console.log('✅ Safe icon components implemented');
  } else {
    console.log('❌ Safe icon components missing');
    return false;
  }
  
  // Check for safe dropzone
  if (content.includes('useSafeDropzone')) {
    console.log('✅ Safe dropzone hook implemented');
  } else {
    console.log('❌ Safe dropzone hook missing');
    return false;
  }
  
  // Check for error boundary
  if (content.includes('ChunkErrorBoundary')) {
    console.log('✅ Chunk error boundary implemented');
  } else {
    console.log('❌ Chunk error boundary missing');
    return false;
  }
  
  console.log('✅ Upload page fixes validated\n');
  return true;
}

// Test 2: Check chunk error handler
function validateChunkErrorHandler() {
  console.log('✅ Test 2: Chunk Error Handler');
  
  const handlerPath = path.join(process.cwd(), 'lib/chunk-error-handler.ts');
  
  if (!fs.existsSync(handlerPath)) {
    console.log('❌ Chunk error handler not found');
    return false;
  }
  
  const content = fs.readFileSync(handlerPath, 'utf8');
  
  // Check for runtime.js error handling
  if (content.includes('runtime.js') && content.includes('Cannot read properties of undefined')) {
    console.log('✅ Runtime.js error patterns included');
  } else {
    console.log('❌ Runtime.js error patterns missing');
    return false;
  }
  
  // Check for critical error detection
  if (content.includes('isCriticalChunkError')) {
    console.log('✅ Critical error detection implemented');
  } else {
    console.log('❌ Critical error detection missing');
    return false;
  }
  
  console.log('✅ Chunk error handler validated\n');
  return true;
}

// Test 3: Check Next.js configuration
function validateNextConfig() {
  console.log('✅ Test 3: Next.js Configuration');
  
  const configPath = path.join(process.cwd(), 'next.config.mjs');
  
  if (!fs.existsSync(configPath)) {
    console.log('❌ Next.js config not found');
    return false;
  }
  
  const content = fs.readFileSync(configPath, 'utf8');
  
  // Check for chunk splitting
  if (content.includes('splitChunks') && content.includes('icons') && content.includes('dropzone')) {
    console.log('✅ Enhanced chunk splitting configured');
  } else {
    console.log('❌ Enhanced chunk splitting missing');
    return false;
  }
  
  // Check for CSP headers
  if (content.includes('Content-Security-Policy')) {
    console.log('✅ CSP headers configured');
  } else {
    console.log('❌ CSP headers missing');
    return false;
  }
  
  console.log('✅ Next.js configuration validated\n');
  return true;
}

// Test 4: Check public demo mode
function validatePublicDemoMode() {
  console.log('✅ Test 4: Public Demo Mode');
  
  // Check user management
  const userMgmtPath = path.join(process.cwd(), 'lib/user-management.ts');
  
  if (!fs.existsSync(userMgmtPath)) {
    console.log('❌ User management not found');
    return false;
  }
  
  const content = fs.readFileSync(userMgmtPath, 'utf8');
  
  if (content.includes('anonymous') && content.includes('public.local')) {
    console.log('✅ Anonymous user system implemented');
  } else {
    console.log('❌ Anonymous user system missing');
    return false;
  }
  
  console.log('✅ Public demo mode validated\n');
  return true;
}

// Test 5: Check error boundary component
function validateErrorBoundary() {
  console.log('✅ Test 5: Error Boundary Component');
  
  const boundaryPath = path.join(process.cwd(), 'components/ChunkErrorBoundary.tsx');
  
  if (!fs.existsSync(boundaryPath)) {
    console.log('❌ ChunkErrorBoundary component not found');
    return false;
  }
  
  const content = fs.readFileSync(boundaryPath, 'utf8');
  
  if (content.includes('componentDidCatch') && content.includes('isChunkLoadError')) {
    console.log('✅ Error boundary with chunk error detection');
  } else {
    console.log('❌ Error boundary missing chunk error detection');
    return false;
  }
  
  if (content.includes('handleChunkLoadError') && content.includes('caches.delete')) {
    console.log('✅ Cache clearing and recovery implemented');
  } else {
    console.log('❌ Cache clearing and recovery missing');
    return false;
  }
  
  console.log('✅ Error boundary validated\n');
  return true;
}

// Test 6: Check middleware for static assets
function validateMiddleware() {
  console.log('✅ Test 6: Middleware Configuration');
  
  const middlewarePath = path.join(process.cwd(), 'middleware.ts');
  
  if (!fs.existsSync(middlewarePath)) {
    console.log('❌ Middleware not found');
    return false;
  }
  
  const content = fs.readFileSync(middlewarePath, 'utf8');
  
  if (content.includes('static asset fallbacks') && content.includes('favicon')) {
    console.log('✅ Static asset fallbacks implemented');
  } else {
    console.log('❌ Static asset fallbacks missing');
    return false;
  }
  
  if (content.includes('CORS') && content.includes('Access-Control-Allow-Origin')) {
    console.log('✅ CORS headers configured');
  } else {
    console.log('❌ CORS headers missing');
    return false;
  }
  
  console.log('✅ Middleware validated\n');
  return true;
}

// Run all tests
async function runValidation() {
  const tests = [
    validateUploadPageFixes,
    validateChunkErrorHandler,
    validateNextConfig,
    validatePublicDemoMode,
    validateErrorBoundary,
    validateMiddleware
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      if (test()) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ Test failed with error: ${error.message}`);
      failed++;
    }
  }
  
  console.log('📊 VALIDATION RESULTS:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%\n`);
  
  if (failed === 0) {
    console.log('🎉 ALL FIXES VALIDATED SUCCESSFULLY!');
    console.log('✅ ChunkLoadError fixes implemented');
    console.log('✅ Public demo mode working');
    console.log('✅ Runtime error recovery active');
    console.log('✅ Production optimizations applied');
    console.log('\n🚀 Ready for production deployment!');
  } else {
    console.log('⚠️  Some tests failed. Please review the issues above.');
  }
  
  return failed === 0;
}

// Run the validation
runValidation().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Validation failed:', error);
  process.exit(1);
});
