import { devLog } from '@/lib/console-cleaner';
/**
 * Analytics API Routes
 * Provides user and team analytics data
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import {
  getUserAnalytics,
  getTeamAnalytics,
  trackUsageEvent,
  exportAnalytics,
  type AnalyticsFilters,
} from '@/lib/analytics-service';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
// Removed: import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/analytics
 * Get user or team analytics
 */
export async function GET(request: NextRequest) {
  try {
  devLog.log('📊 Analytics API called - Public Live Mode');

    // Get user (anonymous mode supported)
    const user = await getCurrentUser();
    const userId = user?.id || 'anonymous-user';

      const { searchParams } = new URL(request.url);
      const type = searchParams.get('type') || 'user'; // 'user' or 'team'
      const period = (searchParams.get('period') || 'weekly') as 'daily' | 'weekly' | 'monthly';
      const startDate = searchParams.get('start_date');
      const endDate = searchParams.get('end_date');
      const organizationId = searchParams.get('organization_id') || userId;

      const filters: AnalyticsFilters = {
        period,
        start_date: startDate || undefined,
        end_date: endDate || undefined,
        organization_id: organizationId,
      };

      // In public mode, return demo analytics data
      const demoAnalytics = {
        overview: {
          total_summaries: 127,
          total_messages: 3456,
          avg_processing_time: 2.3,
          avg_quality_score: 0.87,
          success_rate: 94.5
        },
        daily_data: [
          { date: '2024-01-01', summaries: 12, usage: 45, quality_score: 0.85 },
          { date: '2024-01-02', summaries: 18, usage: 62, quality_score: 0.88 },
          { date: '2024-01-03', summaries: 15, usage: 53, quality_score: 0.86 },
          { date: '2024-01-04', summaries: 22, usage: 78, quality_score: 0.89 },
          { date: '2024-01-05', summaries: 19, usage: 67, quality_score: 0.87 },
          { date: '2024-01-06', summaries: 25, usage: 89, quality_score: 0.91 },
          { date: '2024-01-07', summaries: 16, usage: 58, quality_score: 0.84 }
        ],
        top_sources: [
          { name: 'File Upload', count: 45, percentage: 35.4 },
          { name: 'Slack Threads', count: 38, percentage: 29.9 },
          { name: 'Direct Input', count: 28, percentage: 22.0 },
          { name: 'API Calls', count: 16, percentage: 12.6 }
        ],
        ai_models: [
          { name: 'DeepSeek R1', usage: 85, avg_quality: 0.89, cost: 0.12 },
          { name: 'GPT-4o Mini', usage: 42, avg_quality: 0.85, cost: 0.45 }
        ],
        recent_activity: [
          { id: 1, type: 'summary_created', title: 'Q4 Planning Meeting', timestamp: new Date().toISOString() },
          { id: 2, type: 'file_uploaded', title: 'Project Requirements.pdf', timestamp: new Date().toISOString() },
          { id: 3, type: 'slack_thread', title: 'Engineering Standup', timestamp: new Date().toISOString() }
        ],
        insights: [
          { type: 'trend', message: 'Summary quality has improved 12% this week' },
          { type: 'usage', message: 'Peak usage time is 2-4 PM' },
          { type: 'efficiency', message: 'Average processing time decreased by 0.5s' }
        ]
      };

      return NextResponse.json({
        success: true,
        data: demoAnalytics,
        type: type
      });

    } catch (error) {
      console.error('Analytics API error:', error);

      return NextResponse.json(
        { error: 'Failed to fetch analytics' },
        { status: 500 }
      );
    }
}

/**
 * POST /api/analytics
 * Track usage event
 */
export async function POST(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const body = await req.json();
      const { event_type, ai_model, tokens_used, cost, metadata } = body;

      if (!event_type) {
        return NextResponse.json(
          { error: 'event_type is required' },
          { status: 400 }
        );
      }

      const validEventTypes = ['summary_created', 'ai_request', 'export', 'crm_sync', 'slack_post'];
      if (!validEventTypes.includes(event_type)) {
        return NextResponse.json(
          { error: `Invalid event_type. Must be one of: ${validEventTypes.join(', ')}` },
          { status: 400 }
        );
      }

      const result = await trackUsageEvent(context.userId, user.id, {
        event_type,
        ai_model,
        tokens_used,
        cost,
        metadata,
      });

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Usage event tracked successfully',
      });

    } catch (error) {
      console.error('Track usage event error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));

      return NextResponse.json(
        { error: 'Failed to track usage event' },
        { status: 500 }
      );
    }
  });
}




