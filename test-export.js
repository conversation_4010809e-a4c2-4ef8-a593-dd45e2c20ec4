/**
 * Test Export Functionality
 */

const { default: fetch } = require('node-fetch');

const testExport = async () => {
  try {
    console.log('🧪 Testing Export Functionality...');
    
    // Test data for export
    const testSummary = {
      id: 'demo-summary-123',
      title: 'Test Meeting Summary',
      summary: 'This is a test summary for export functionality. It includes key points, action items, and insights.',
      keyPoints: [
        'Discussed Q4 planning and budget allocation',
        'Reviewed team performance metrics',
        'Identified areas for improvement'
      ],
      actionItems: [
        'Schedule follow-up meeting for next week',
        'Prepare budget proposal by Friday',
        'Update team on progress'
      ],
      redFlags: [
        'Budget constraints may impact timeline',
        'Resource allocation needs review'
      ],
      metadata: {
        model: 'deepseek-r1',
        processingTime: 2.3,
        wordCount: 150,
        createdAt: new Date().toISOString()
      }
    };
    
    // Test PDF Export
    console.log('\n📄 Testing PDF Export...');
    try {
      const pdfResponse = await fetch('http://localhost:3000/api/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          summary: testSummary,
          format: 'pdf',
          fileName: 'test-summary'
        })
      });
      
      if (pdfResponse.ok) {
        const contentType = pdfResponse.headers.get('content-type');
        console.log('✅ PDF Export Test PASSED');
        console.log('📄 Content Type:', contentType);
        console.log('📄 File Size:', pdfResponse.headers.get('content-length'), 'bytes');
      } else {
        const error = await pdfResponse.json();
        console.log('❌ PDF Export Test FAILED');
        console.log('Error:', error);
      }
    } catch (error) {
      console.log('❌ PDF Export Test ERROR:', error.message);
    }
    
    // Test Excel Export
    console.log('\n📊 Testing Excel Export...');
    try {
      const excelResponse = await fetch('http://localhost:3000/api/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          summary: testSummary,
          format: 'excel',
          fileName: 'test-summary'
        })
      });
      
      if (excelResponse.ok) {
        const contentType = excelResponse.headers.get('content-type');
        console.log('✅ Excel Export Test PASSED');
        console.log('📊 Content Type:', contentType);
        console.log('📊 File Size:', excelResponse.headers.get('content-length'), 'bytes');
      } else {
        const error = await excelResponse.json();
        console.log('❌ Excel Export Test FAILED');
        console.log('Error:', error);
      }
    } catch (error) {
      console.log('❌ Excel Export Test ERROR:', error.message);
    }
    
    // Test Notion Export
    console.log('\n📝 Testing Notion Export...');
    try {
      const notionResponse = await fetch('http://localhost:3000/api/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          summary: testSummary,
          format: 'notion',
          fileName: 'test-summary'
        })
      });
      
      if (notionResponse.ok) {
        const contentType = notionResponse.headers.get('content-type');
        console.log('✅ Notion Export Test PASSED');
        console.log('📝 Content Type:', contentType);
        console.log('📝 File Size:', notionResponse.headers.get('content-length'), 'bytes');
      } else {
        const error = await notionResponse.json();
        console.log('❌ Notion Export Test FAILED');
        console.log('Error:', error);
      }
    } catch (error) {
      console.log('❌ Notion Export Test ERROR:', error.message);
    }
    
    // Test specific Excel export endpoint
    console.log('\n📊 Testing Excel Export Endpoint...');
    try {
      const excelEndpointResponse = await fetch('http://localhost:3000/api/export/excel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          summaryId: 'demo-summary-123'
        })
      });
      
      if (excelEndpointResponse.ok) {
        const contentType = excelEndpointResponse.headers.get('content-type');
        console.log('✅ Excel Endpoint Test PASSED');
        console.log('📊 Content Type:', contentType);
      } else {
        const error = await excelEndpointResponse.json();
        console.log('❌ Excel Endpoint Test FAILED');
        console.log('Error:', error);
      }
    } catch (error) {
      console.log('❌ Excel Endpoint Test ERROR:', error.message);
    }
    
    // Test Notion export endpoint
    console.log('\n📝 Testing Notion Export Endpoint...');
    try {
      const notionEndpointResponse = await fetch('http://localhost:3000/api/export/notion', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          summaryId: 'demo-summary-123'
        })
      });
      
      if (notionEndpointResponse.ok) {
        const contentType = notionEndpointResponse.headers.get('content-type');
        console.log('✅ Notion Endpoint Test PASSED');
        console.log('📝 Content Type:', contentType);
      } else {
        const error = await notionEndpointResponse.json();
        console.log('❌ Notion Endpoint Test FAILED');
        console.log('Error:', error);
      }
    } catch (error) {
      console.log('❌ Notion Endpoint Test ERROR:', error.message);
    }
    
  } catch (error) {
    console.log('❌ Export Test ERROR');
    console.error('Error:', error);
  }
};

// Run the test
testExport();
