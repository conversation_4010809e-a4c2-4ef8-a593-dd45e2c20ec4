/**
 * Analytics Types and Interfaces
 * 
 * TypeScript definitions for analytics data structures
 */

export interface AnalyticsEvent {
  id?: string;
  organizationId?: string;
  userId?: string;
  eventType: string;
  eventName: string;
  properties: Record<string, any>;
  sessionId?: string;
  timestamp?: string;
}

export interface UsageMetric {
  id?: string;
  organizationId?: string;
  metricType: 'usage' | 'performance' | 'business';
  metricName: string;
  value: number;
  dimensions: Record<string, any>;
  periodStart: string;
  periodEnd: string;
  createdAt?: string;
}

export interface BusinessMetric {
  id?: string;
  metricName: string;
  value: number;
  metadata: Record<string, any>;
  periodStart: string;
  periodEnd: string;
  createdAt?: string;
}

export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
  metadata?: Record<string, any>;
}

export interface ChartData {
  type: 'line' | 'bar' | 'pie' | 'area' | 'heatmap';
  title: string;
  data: ChartDataPoint[];
  xAxis?: string;
  yAxis?: string;
  colors?: string[];
  metadata?: Record<string, any>;
}

export interface DateRange {
  start: string;
  end: string;
}

export interface AnalyticsFilter {
  organizationId?: string;
  userId?: string;
  dateRange: DateRange;
  eventTypes?: string[];
  metrics?: string[];
  groupBy?: 'hour' | 'day' | 'week' | 'month';
  limit?: number;
}

export interface DashboardMetrics {
  // Usage metrics
  totalSummaries: number;
  summariesToday: number;
  summariesGrowth: number;
  activeUsers: number;
  activeUsersGrowth: number;
  
  // Business metrics
  totalRevenue: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  conversionRate: number;
  churnRate: number;
  
  // Performance metrics
  avgResponseTime: number;
  errorRate: number;
  uptime: number;
  
  // Plan distribution
  planDistribution: {
    free: number;
    pro: number;
    enterprise: number;
  };
  
  // Feature adoption
  featureUsage: {
    [feature: string]: number;
  };
}

export interface UsageAnalytics {
  summaryCreation: {
    total: number;
    byDay: ChartDataPoint[];
    byPlan: ChartDataPoint[];
    byAiModel: ChartDataPoint[];
  };
  
  userActivity: {
    activeUsers: number;
    newUsers: number;
    returningUsers: number;
    sessionDuration: number;
    byDay: ChartDataPoint[];
  };
  
  workspaceActivity: {
    connectedWorkspaces: number;
    messagesProcessed: number;
    channelsActive: number;
    byWorkspace: ChartDataPoint[];
  };
  
  exportActivity: {
    totalExports: number;
    byFormat: ChartDataPoint[];
    byDay: ChartDataPoint[];
    popularContent: ChartDataPoint[];
  };
}

export interface BusinessAnalytics {
  revenue: {
    mrr: number;
    arr: number;
    growth: number;
    byPlan: ChartDataPoint[];
    trend: ChartDataPoint[];
  };
  
  customers: {
    total: number;
    new: number;
    churned: number;
    ltv: number;
    cac: number;
    byPlan: ChartDataPoint[];
  };
  
  conversion: {
    trialToProRate: number;
    freeToProRate: number;
    proToEnterpriseRate: number;
    funnel: ChartDataPoint[];
  };
  
  retention: {
    day1: number;
    day7: number;
    day30: number;
    cohorts: ChartDataPoint[];
  };
}

export interface PerformanceAnalytics {
  apiPerformance: {
    avgResponseTime: number;
    p95ResponseTime: number;
    requestsPerSecond: number;
    errorRate: number;
    byEndpoint: ChartDataPoint[];
    trend: ChartDataPoint[];
  };
  
  aiPerformance: {
    avgProcessingTime: number;
    successRate: number;
    byModel: ChartDataPoint[];
    qualityScore: number;
  };
  
  systemHealth: {
    uptime: number;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    trend: ChartDataPoint[];
  };
  
  userExperience: {
    pageLoadTime: number;
    timeToFirstByte: number;
    coreWebVitals: {
      lcp: number; // Largest Contentful Paint
      fid: number; // First Input Delay
      cls: number; // Cumulative Layout Shift
    };
  };
}

export interface ExportOptions {
  format: 'pdf' | 'csv' | 'xlsx' | 'json';
  dateRange: DateRange;
  metrics: string[];
  includeCharts: boolean;
  organizationId?: string;
  fileName?: string;
}

export interface RealtimeMetrics {
  activeUsers: number;
  requestsPerMinute: number;
  errorsPerMinute: number;
  summariesInProgress: number;
  queueLength: number;
  responseTime: number;
  timestamp: string;
}

export interface AnalyticsConfig {
  enabled: boolean;
  retentionDays: number;
  realTimeEnabled: boolean;
  exportMaxRecords: number;
  cacheTtl: number;
  batchSize: number;
  flushInterval: number;
}

// Event type definitions
export type EventType = 
  | 'page_view'
  | 'feature_usage'
  | 'user_action'
  | 'system_event'
  | 'business_event'
  | 'error_event';

export type FeatureUsageEvent = 
  | 'summary_created'
  | 'summary_exported'
  | 'workspace_connected'
  | 'team_member_invited'
  | 'plan_upgraded'
  | 'integration_enabled';

export type UserActionEvent = 
  | 'login'
  | 'logout'
  | 'signup'
  | 'profile_updated'
  | 'settings_changed'
  | 'feedback_submitted';

export type SystemEvent = 
  | 'api_call'
  | 'webhook_received'
  | 'background_job'
  | 'cache_hit'
  | 'cache_miss'
  | 'rate_limit_hit';

export type BusinessEvent = 
  | 'trial_started'
  | 'subscription_created'
  | 'subscription_cancelled'
  | 'payment_succeeded'
  | 'payment_failed'
  | 'refund_issued';

// Metric aggregation types
export type AggregationType = 'sum' | 'avg' | 'min' | 'max' | 'count' | 'unique';

export interface MetricDefinition {
  name: string;
  type: AggregationType;
  field: string;
  filters?: Record<string, any>;
  groupBy?: string[];
}

// Chart configuration
export interface ChartConfig {
  type: ChartData['type'];
  title: string;
  description?: string;
  metric: string;
  aggregation: AggregationType;
  groupBy?: string;
  filters?: Record<string, any>;
  colors?: string[];
  height?: number;
  showLegend?: boolean;
  showTooltip?: boolean;
  responsive?: boolean;
}

// Dashboard layout
export interface DashboardLayout {
  id: string;
  name: string;
  description?: string;
  widgets: DashboardWidget[];
  layout: {
    columns: number;
    rows: number;
  };
  permissions: {
    view: string[];
    edit: string[];
  };
}

export interface DashboardWidget {
  id: string;
  type: 'chart' | 'metric' | 'table' | 'text';
  title: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  config: ChartConfig | MetricConfig | TableConfig | TextConfig;
}

export interface MetricConfig {
  metric: string;
  aggregation: AggregationType;
  format: 'number' | 'currency' | 'percentage' | 'duration';
  comparison?: {
    period: 'previous' | 'year_ago';
    showChange: boolean;
  };
}

export interface TableConfig {
  columns: {
    field: string;
    title: string;
    format?: string;
    sortable?: boolean;
  }[];
  pagination: boolean;
  pageSize: number;
  filters: Record<string, any>;
}

export interface TextConfig {
  content: string;
  markdown: boolean;
  fontSize: 'small' | 'medium' | 'large';
  alignment: 'left' | 'center' | 'right';
}
