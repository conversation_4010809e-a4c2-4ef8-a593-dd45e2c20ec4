import { devLog } from '@/lib/console-cleaner';
/**
 * Client-side Initialization Script
 * Handles test mode detection, chunk error recovery, and environment setup
 */

import { getTestModeConfig, logTestModeStatus } from './test-mode-detection';
import { chunkErrorHandler } from './chunk-error-handler';

declare global {
  interface Window {
    __webpack_chunk_load_error_handler__?: (error: any, filename: string) => void;
    __test_mode_logged__?: boolean;
    __client_initialized__?: boolean;
  }
}

/**
 * Initialize client-side error handling and environment detection
 */
export function initializeClient(): void {
  if (typeof window === 'undefined') return;
  
  // Prevent double initialization
  if (window.__client_initialized__) return;
  window.__client_initialized__ = true;
  
  try {
    // Initialize test mode detection
    initializeTestMode();
    
    // Initialize chunk error handling
    initializeChunkErrorHandling();
    
    // Initialize global error handlers
    initializeGlobalErrorHandlers();
    
    // Initialize monitoring
    initializeMonitoring();
  devLog.log('🚀 Client initialization complete');
  } catch (error) {
    console.error('❌ Client initialization failed:', error);
  }
}

/**
 * Initialize test mode detection and logging
 */
function initializeTestMode(): void {
  const config = getTestModeConfig();
  
  // Log test mode status
  logTestModeStatus();
  
  // Set global config for other scripts
  (window as any).__app_config__ = config;
  
  // Add development helpers
  if (config.isDevelopment) {
    // Add global debug helpers
    (window as any).__debug__ = {
      config,
      clearCaches: () => {
        if ('caches' in window) {
          caches.keys().then(names => {
            names.forEach(name => caches.delete(name));
  devLog.log('🧹 Browser caches cleared');
          });
        }
      },
      reloadApp: () => {
        window.location.reload();
      },
      testChunkError: () => {
        throw new Error('ChunkLoadError: Test chunk loading error');
      }
    };
  devLog.log('🔧 Development helpers available at window.__debug__');
  }
}

/**
 * Initialize chunk error handling
 */
function initializeChunkErrorHandling(): void {
  // Set up global chunk error handler
  window.__webpack_chunk_load_error_handler__ = (error: any, filename: string) => {
    console.warn('🔄 Chunk loading error detected:', { error, filename });
    chunkErrorHandler.reset();
    
    // Attempt recovery
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };
  
  // Initialize the chunk error handler
  chunkErrorHandler.reset();
  devLog.log('🛡️ Chunk error handling initialized');
}

/**
 * Initialize global error handlers
 */
function initializeGlobalErrorHandlers(): void {
  // Enhanced unhandled error handler
  window.addEventListener('error', (event) => {
    const error = event.error;
    const message = event.message;
    
    // Check for chunk loading errors
    if (
      error?.name === 'ChunkLoadError' ||
      message?.includes('Loading chunk') ||
      message?.includes('Failed to import')
    ) {
      console.warn('🔄 Global chunk error detected, attempting recovery...');
      
      // Clear caches and reload
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name));
          setTimeout(() => window.location.reload(), 500);
        });
      } else {
        setTimeout(() => window.location.reload(), 500);
      }
      
      return;
    }
    
    // Log other errors for debugging
    console.error('🚨 Global error:', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    });
  });
  
  // Enhanced unhandled promise rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    const reason = event.reason;
    
    // Check for chunk loading promise rejections
    if (
      reason?.message?.includes('Loading chunk') ||
      reason?.message?.includes('Failed to import') ||
      reason?.name === 'ChunkLoadError'
    ) {
      console.warn('🔄 Global promise rejection for chunk loading, attempting recovery...');
      
      // Prevent default handling
      event.preventDefault();
      
      // Clear caches and reload
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name));
          setTimeout(() => window.location.reload(), 500);
        });
      } else {
        setTimeout(() => window.location.reload(), 500);
      }
      
      return;
    }
    
    // Log other promise rejections
    console.error('🚨 Unhandled promise rejection:', reason);
  });
  devLog.log('🛡️ Global error handlers initialized');
}

/**
 * Initialize monitoring services
 */
function initializeMonitoring(): void {
  const config = getTestModeConfig();
  
  // Initialize PostHog if available
  if (typeof window !== 'undefined' && (window as any).posthog) {
    (window as any).posthog.capture('client_initialized', {
      environment: config.environment,
      test_mode: config.isTestMode,
      development: config.isDevelopment,
      timestamp: new Date().toISOString()
    });
  devLog.log('📊 PostHog tracking initialized');
  }
  
  // Initialize Sentry context if available
  if (typeof window !== 'undefined' && (window as any).Sentry) {
    (window as any).Sentry.setContext('app_config', {
      environment: config.environment,
      test_mode: config.isTestMode,
      development: config.isDevelopment
    });
  devLog.log('🔍 Sentry context initialized');
  }
}

/**
 * Check if all required services are available
 */
export function validateClientServices(): {
  isValid: boolean;
  missing: string[];
  available: string[];
} {
  const services = {
    'PostHog': typeof window !== 'undefined' && !!(window as any).posthog,
    'Sentry': typeof window !== 'undefined' && !!(window as any).Sentry,
    'Clerk': typeof window !== 'undefined' && !!(window as any).Clerk,
    'Stripe': typeof window !== 'undefined' && !!(window as any).Stripe,
  };
  
  const available = Object.entries(services)
    .filter(([, isAvailable]) => isAvailable)
    .map(([name]) => name);
    
  const missing = Object.entries(services)
    .filter(([, isAvailable]) => !isAvailable)
    .map(([name]) => name);
  
  const config = getTestModeConfig();
  
  // In development, missing services are warnings, not errors
  const isValid = config.isDevelopment || missing.length === 0;
  
  return {
    isValid,
    missing,
    available
  };
}

/**
 * Auto-initialize on script load
 */
if (typeof window !== 'undefined') {
  // Initialize immediately if DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeClient);
  } else {
    // DOM is already ready
    setTimeout(initializeClient, 0);
  }
}

export default {
  initializeClient,
  validateClientServices,
  getTestModeConfig,
};
