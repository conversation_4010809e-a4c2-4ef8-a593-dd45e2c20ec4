'use client';

/**
 * Admin Audit Dashboard
 * 
 * Comprehensive monitoring dashboard showing all cron jobs, queues, 
 * webhooks, and scheduled workers in one unified view.
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  Database,
  Webhook,
  Zap,
  Shield,
  Globe,
  BarChart3
} from 'lucide-react';

interface AuditResult {
  category: string;
  passed: number;
  failed: number;
  warnings: number;
  issues: Array<{
    severity: 'error' | 'warning';
    message: string;
  }>;
}

interface SystemHealth {
  database: {
    status: 'healthy' | 'degraded' | 'down';
    latency: number;
    connections: number;
    queries: number;
  };
  cache: {
    status: 'healthy' | 'degraded' | 'down';
    hitRate: number;
    memoryUsage: number;
    keyCount: number;
  };
  integrations: {
    status: 'healthy' | 'degraded' | 'down';
    activeTokens: number;
    expiredTokens: number;
    failedWebhooks: number;
  };
  workers: {
    status: 'healthy' | 'degraded' | 'down';
    activeJobs: number;
    failedJobs: number;
    queueSize: number;
  };
}

interface CronJob {
  id: string;
  name: string;
  schedule: string;
  lastRun: string;
  nextRun: string;
  status: 'active' | 'paused' | 'failed';
  successRate: number;
  averageDuration: number;
}

interface QueueJob {
  id: string;
  type: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  priority: number;
  attempts: number;
  createdAt: string;
  processedAt?: string;
  error?: string;
}

interface WebhookEndpoint {
  id: string;
  url: string;
  provider: string;
  status: 'active' | 'inactive' | 'failed';
  lastDelivery: string;
  successRate: number;
  averageResponseTime: number;
}

export default function AuditDashboard() {
  const [auditResults, setAuditResults] = useState<Record<string, AuditResult>>({});
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [cronJobs, setCronJobs] = useState<CronJob[]>([]);
  const [queueJobs, setQueueJobs] = useState<QueueJob[]>([]);
  const [webhooks, setWebhooks] = useState<WebhookEndpoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  useEffect(() => {
    fetchAuditData();
    const interval = setInterval(fetchAuditData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchAuditData = async () => {
    try {
      setLoading(true);
      
      const [auditRes, healthRes, cronRes, queueRes, webhookRes] = await Promise.all([
        fetch('/api/admin/audit/results'),
        fetch('/api/admin/audit/health'),
        fetch('/api/admin/audit/cron-jobs'),
        fetch('/api/admin/audit/queue-jobs'),
        fetch('/api/admin/audit/webhooks')
      ]);

      if (auditRes.ok) {
        const auditData = await auditRes.json();
        setAuditResults(auditData.results || {});
      }

      if (healthRes.ok) {
        const healthData = await healthRes.json();
        setSystemHealth(healthData);
      }

      if (cronRes.ok) {
        const cronData = await cronRes.json();
        setCronJobs(cronData.jobs || []);
      }

      if (queueRes.ok) {
        const queueData = await queueRes.json();
        setQueueJobs(queueData.jobs || []);
      }

      if (webhookRes.ok) {
        const webhookData = await webhookRes.json();
        setWebhooks(webhookData.endpoints || []);
      }

      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to fetch audit data:', error);
    } finally {
      setLoading(false);
    }
  };

  const runFullAudit = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/audit/run', { method: 'POST' });
      
      if (response.ok) {
        await fetchAuditData();
      }
    } catch (error) {
      console.error('Failed to run audit:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'active':
      case 'completed':
        return 'bg-green-500';
      case 'degraded':
      case 'paused':
      case 'processing':
        return 'bg-yellow-500';
      case 'down':
      case 'failed':
      case 'inactive':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getOverallScore = () => {
    if (!auditResults || Object.keys(auditResults).length === 0) return 0;
    
    let totalPassed = 0;
    let totalChecks = 0;
    
    Object.values(auditResults).forEach(result => {
      totalPassed += result.passed;
      totalChecks += result.passed + result.failed + result.warnings;
    });
    
    return totalChecks > 0 ? Math.round((totalPassed / totalChecks) * 100) : 0;
  };

  if (loading && !systemHealth) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Audit Dashboard</h1>
          <p className="text-gray-600">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={fetchAuditData} disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={runFullAudit} disabled={loading}>
            <Shield className="w-4 h-4 mr-2" />
            Run Full Audit
          </Button>
        </div>
      </div>

      {/* Overall Health Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Overall System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{getOverallScore()}%</div>
              <div className="text-sm text-gray-600">Audit Score</div>
              <Progress value={getOverallScore()} className="mt-2" />
            </div>
            
            {systemHealth && (
              <>
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(systemHealth.database.status)}`}></div>
                    <span className="font-semibold">Database</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {systemHealth.database.latency}ms latency
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(systemHealth.cache.status)}`}></div>
                    <span className="font-semibold">Cache</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {Math.round(systemHealth.cache.hitRate * 100)}% hit rate
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(systemHealth.integrations.status)}`}></div>
                    <span className="font-semibold">Integrations</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {systemHealth.integrations.activeTokens} active tokens
                  </div>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="cron">Cron Jobs</TabsTrigger>
          <TabsTrigger value="queues">Queues</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          <TabsTrigger value="audit">Audit Results</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* System Health Cards */}
          {systemHealth && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Database</p>
                      <p className="text-2xl font-bold">{systemHealth.database.connections}</p>
                      <p className="text-sm text-gray-500">Active connections</p>
                    </div>
                    <Database className="w-8 h-8 text-blue-600" />
                  </div>
                  <div className={`mt-2 w-2 h-2 rounded-full ${getStatusColor(systemHealth.database.status)}`}></div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Cache</p>
                      <p className="text-2xl font-bold">{systemHealth.cache.keyCount}</p>
                      <p className="text-sm text-gray-500">Cached keys</p>
                    </div>
                    <Zap className="w-8 h-8 text-yellow-600" />
                  </div>
                  <div className={`mt-2 w-2 h-2 rounded-full ${getStatusColor(systemHealth.cache.status)}`}></div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Workers</p>
                      <p className="text-2xl font-bold">{systemHealth.workers.activeJobs}</p>
                      <p className="text-sm text-gray-500">Active jobs</p>
                    </div>
                    <Activity className="w-8 h-8 text-green-600" />
                  </div>
                  <div className={`mt-2 w-2 h-2 rounded-full ${getStatusColor(systemHealth.workers.status)}`}></div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Integrations</p>
                      <p className="text-2xl font-bold">{systemHealth.integrations.activeTokens}</p>
                      <p className="text-sm text-gray-500">OAuth tokens</p>
                    </div>
                    <Globe className="w-8 h-8 text-purple-600" />
                  </div>
                  <div className={`mt-2 w-2 h-2 rounded-full ${getStatusColor(systemHealth.integrations.status)}`}></div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="cron" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Scheduled Jobs
              </CardTitle>
              <CardDescription>
                Cron jobs and scheduled tasks status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {cronJobs.map((job) => (
                  <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(job.status)}`}></div>
                      <div>
                        <h4 className="font-semibold">{job.name}</h4>
                        <p className="text-sm text-gray-600">{job.schedule}</p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-sm font-medium">{Math.round(job.successRate)}% success</div>
                      <div className="text-xs text-gray-500">
                        Next: {new Date(job.nextRun).toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="queues" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5" />
                Queue Jobs
              </CardTitle>
              <CardDescription>
                Background job processing status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {queueJobs.slice(0, 10).map((job) => (
                  <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <Badge variant={job.status === 'completed' ? 'default' : 
                                   job.status === 'failed' ? 'destructive' : 'secondary'}>
                        {job.status}
                      </Badge>
                      <div>
                        <h4 className="font-semibold">{job.type}</h4>
                        <p className="text-sm text-gray-600">
                          Created: {new Date(job.createdAt).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-sm">Priority: {job.priority}</div>
                      <div className="text-xs text-gray-500">
                        Attempts: {job.attempts}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="webhooks" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Webhook className="w-5 h-5" />
                Webhook Endpoints
              </CardTitle>
              <CardDescription>
                External webhook delivery status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {webhooks.map((webhook) => (
                  <div key={webhook.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(webhook.status)}`}></div>
                      <div>
                        <h4 className="font-semibold">{webhook.provider}</h4>
                        <p className="text-sm text-gray-600 truncate max-w-md">{webhook.url}</p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-sm font-medium">{Math.round(webhook.successRate)}% success</div>
                      <div className="text-xs text-gray-500">
                        {webhook.averageResponseTime}ms avg
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-6">
          {Object.entries(auditResults).map(([category, result]) => (
            <Card key={category}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="capitalize">{category}</span>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm">{result.passed}</span>
                    <XCircle className="w-4 h-4 text-red-600" />
                    <span className="text-sm">{result.failed}</span>
                    <AlertTriangle className="w-4 h-4 text-yellow-600" />
                    <span className="text-sm">{result.warnings}</span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {result.issues.length > 0 && (
                  <div className="space-y-2">
                    {result.issues.map((issue, index) => (
                      <div key={index} className="flex items-start gap-2 p-2 rounded bg-gray-50">
                        {issue.severity === 'error' ? (
                          <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                        ) : (
                          <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
                        )}
                        <span className="text-sm">{issue.message}</span>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}
