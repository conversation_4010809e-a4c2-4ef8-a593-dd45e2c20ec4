/**
 * SUPABASE BROWSER CLIENT WITH CLERK INTEGRATION
 *
 * Client-side Supabase client with Clerk authentication integration
 * for live SaaS with proper user-based Row Level Security (RLS)
 */

'use client'

import { createClient } from '@supabase/supabase-js';
import { useAuth } from '@clerk/nextjs';
import { Database } from './database.types';

let supabaseClient: any = null;

/**
 * Create or get existing Supabase browser client with Clerk integration
 */
export function createBrowserSupabaseClient() {
  if (supabaseClient) {
    return supabaseClient;
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.warn('Supabase credentials not configured, using fallback client');
    // Return a mock client that doesn't throw errors
    return {
      from: () => ({
        select: () => ({ data: [], error: null }),
        insert: () => ({ data: null, error: null }),
        update: () => ({ data: null, error: null }),
        delete: () => ({ data: null, error: null }),
      }),
      storage: {
        from: () => ({
          upload: () => ({ data: null, error: null }),
          download: () => ({ data: null, error: null }),
        })
      }
    };
  }

  supabaseClient = createClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: false, // We use Clerk for session management
    },
  });

  return supabaseClient;
}

/**
 * Hook to get authenticated Supabase client
 */
export function useSupabase() {
  const { userId, isSignedIn } = useAuth();

  const supabase = createBrowserSupabaseClient();

  // If user is signed in, set up the auth context for RLS
  if (isSignedIn && userId && supabase.rest) {
    // Set custom headers for RLS
    supabase.rest.headers['x-user-id'] = userId;
  }

  return {
    supabase,
    userId,
    isAuthenticated: isSignedIn,
  };
}

/**
 * Get current user ID for database operations
 */
export function useCurrentUserId(): string | null {
  const { userId } = useAuth();
  return userId;
}

/**
 * Get OAuth redirect URL for Clerk integration
 */
export function getOAuthRedirectUrl() {
  return `${window.location.origin}/dashboard`;
}

/**
 * Validate client session using Clerk
 */
export function validateClientSession() {
  const { isSignedIn, userId } = useAuth();
  return {
    isValid: isSignedIn,
    user: userId ? { id: userId } : null
  };
}

/**
 * Create realtime subscription (optional for public mode)
 */
export function createRealtimeSubscription(table: string, callback: Function) {
  const client = createBrowserSupabaseClient();

  if (!client.channel) {
    console.warn('Realtime not available, using polling fallback');
    return null;
  }

  return client
    .channel(`public:${table}`)
    .on('postgres_changes', { event: '*', schema: 'public', table }, callback)
    .subscribe();
}


