/**
 * Optimistic UI Updates Hook
 * 
 * Provides utilities for implementing optimistic updates that improve
 * perceived performance by updating the UI immediately before API calls complete.
 */

import { useState, useCallback, useRef } from 'react';

interface OptimisticUpdate<T> {
  id: string;
  type: 'create' | 'update' | 'delete';
  data: T;
  timestamp: number;
  status: 'pending' | 'confirmed' | 'failed';
}

interface UseOptimisticUpdatesOptions<T> {
  onSuccess?: (data: T) => void;
  onError?: (error: Error, rollbackData: T) => void;
  timeout?: number; // Auto-rollback timeout in ms
}

/**
 * Hook for managing optimistic updates
 */
export function useOptimisticUpdates<T>(
  initialData: T[],
  options: UseOptimisticUpdatesOptions<T> = {}
) {
  const [data, setData] = useState<T[]>(initialData);
  const [pendingUpdates, setPendingUpdates] = useState<Map<string, OptimisticUpdate<T>>>(new Map());
  const timeoutRefs = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const { onSuccess, onError, timeout = 10000 } = options;

  /**
   * Generate unique ID for updates (SSR-safe)
   */
  const generateId = useCallback(() => {
    // Use crypto.randomUUID if available
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return `opt_${crypto.randomUUID()}`;
    }
    // Fallback with client-side check
    if (typeof window !== 'undefined') {
      return `opt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    // Server-side fallback
    return `opt_ssr_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  /**
   * Add optimistic update
   */
  const addOptimisticUpdate = useCallback((
    type: 'create' | 'update' | 'delete',
    item: T,
    getId: (item: T) => string,
    apiCall: () => Promise<T>
  ) => {
    const updateId = generateId();
    const update: OptimisticUpdate<T> = {
      id: updateId,
      type,
      data: item,
      timestamp: Date.now(),
      status: 'pending'
    };

    // Add to pending updates
    setPendingUpdates(prev => new Map(prev).set(updateId, update));

    // Apply optimistic update to data
    setData(prev => {
      switch (type) {
        case 'create':
          return [...prev, item];
        
        case 'update':
          return prev.map(existing => 
            getId(existing) === getId(item) ? item : existing
          );
        
        case 'delete':
          return prev.filter(existing => getId(existing) !== getId(item));
        
        default:
          return prev;
      }
    });

    // Set timeout for auto-rollback
    const timeoutId = setTimeout(() => {
      rollbackUpdate(updateId, item, getId);
    }, timeout);
    
    timeoutRefs.current.set(updateId, timeoutId);

    // Execute API call
    apiCall()
      .then((result) => {
        confirmUpdate(updateId, result, getId);
        onSuccess?.(result);
      })
      .catch((error) => {
        rollbackUpdate(updateId, item, getId);
        onError?.(error, item);
      });

    return updateId;
  }, [generateId, timeout, onSuccess, onError]);

  /**
   * Confirm optimistic update (API call succeeded)
   */
  const confirmUpdate = useCallback((
    updateId: string,
    confirmedData: T,
    getId: (item: T) => string
  ) => {
    // Clear timeout
    const timeoutId = timeoutRefs.current.get(updateId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutRefs.current.delete(updateId);
    }

    // Update pending status
    setPendingUpdates(prev => {
      const newMap = new Map(prev);
      const update = newMap.get(updateId);
      if (update) {
        newMap.set(updateId, { ...update, status: 'confirmed', data: confirmedData });
      }
      return newMap;
    });

    // Update data with confirmed result
    setData(prev => {
      const update = pendingUpdates.get(updateId);
      if (!update) return prev;

      switch (update.type) {
        case 'create':
        case 'update':
          return prev.map(item => 
            getId(item) === getId(update.data) ? confirmedData : item
          );
        
        default:
          return prev;
      }
    });

    // Clean up after a delay
    setTimeout(() => {
      setPendingUpdates(prev => {
        const newMap = new Map(prev);
        newMap.delete(updateId);
        return newMap;
      });
    }, 1000);
  }, [pendingUpdates]);

  /**
   * Rollback optimistic update (API call failed or timed out)
   */
  const rollbackUpdate = useCallback((
    updateId: string,
    originalData: T,
    getId: (item: T) => string
  ) => {
    // Clear timeout
    const timeoutId = timeoutRefs.current.get(updateId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutRefs.current.delete(updateId);
    }

    // Update pending status
    setPendingUpdates(prev => {
      const newMap = new Map(prev);
      const update = newMap.get(updateId);
      if (update) {
        newMap.set(updateId, { ...update, status: 'failed' });
      }
      return newMap;
    });

    // Rollback data changes
    setData(prev => {
      const update = pendingUpdates.get(updateId);
      if (!update) return prev;

      switch (update.type) {
        case 'create':
          // Remove the optimistically added item
          return prev.filter(item => getId(item) !== getId(update.data));
        
        case 'update':
          // Restore original data (would need to be stored separately in real implementation)
          return prev.map(item => 
            getId(item) === getId(update.data) ? originalData : item
          );
        
        case 'delete':
          // Restore the deleted item
          return [...prev, originalData];
        
        default:
          return prev;
      }
    });

    // Clean up after a delay
    setTimeout(() => {
      setPendingUpdates(prev => {
        const newMap = new Map(prev);
        newMap.delete(updateId);
        return newMap;
      });
    }, 2000);
  }, [pendingUpdates]);

  /**
   * Create item with optimistic update
   */
  const optimisticCreate = useCallback((
    item: T,
    getId: (item: T) => string,
    apiCall: () => Promise<T>
  ) => {
    return addOptimisticUpdate('create', item, getId, apiCall);
  }, [addOptimisticUpdate]);

  /**
   * Update item with optimistic update
   */
  const optimisticUpdate = useCallback((
    item: T,
    getId: (item: T) => string,
    apiCall: () => Promise<T>
  ) => {
    return addOptimisticUpdate('update', item, getId, apiCall);
  }, [addOptimisticUpdate]);

  /**
   * Delete item with optimistic update
   */
  const optimisticDelete = useCallback((
    item: T,
    getId: (item: T) => string,
    apiCall: () => Promise<T>
  ) => {
    return addOptimisticUpdate('delete', item, getId, apiCall);
  }, [addOptimisticUpdate]);

  /**
   * Check if item is pending
   */
  const isPending = useCallback((itemId: string) => {
    return Array.from(pendingUpdates.values()).some(
      update => update.status === 'pending' && update.id === itemId
    );
  }, [pendingUpdates]);

  /**
   * Get pending updates count
   */
  const getPendingCount = useCallback(() => {
    return Array.from(pendingUpdates.values()).filter(
      update => update.status === 'pending'
    ).length;
  }, [pendingUpdates]);

  /**
   * Clear all pending updates (force refresh)
   */
  const clearPendingUpdates = useCallback(() => {
    // Clear all timeouts
    timeoutRefs.current.forEach(timeoutId => clearTimeout(timeoutId));
    timeoutRefs.current.clear();
    
    // Clear pending updates
    setPendingUpdates(new Map());
    
    // Reset data to initial state
    setData(initialData);
  }, [initialData]);

  return {
    data,
    pendingUpdates: Array.from(pendingUpdates.values()),
    optimisticCreate,
    optimisticUpdate,
    optimisticDelete,
    isPending,
    getPendingCount,
    clearPendingUpdates,
    setData // For manual data updates
  };
}

/**
 * Simple optimistic state hook for single values
 */
export function useOptimisticState<T>(
  initialValue: T,
  options: { timeout?: number } = {}
) {
  const [value, setValue] = useState<T>(initialValue);
  const [isPending, setIsPending] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const { timeout = 10000 } = options;

  const optimisticUpdate = useCallback(async (
    newValue: T,
    apiCall: () => Promise<T>
  ) => {
    const originalValue = value;
    
    // Apply optimistic update
    setValue(newValue);
    setIsPending(true);

    // Set rollback timeout
    timeoutRef.current = setTimeout(() => {
      setValue(originalValue);
      setIsPending(false);
    }, timeout);

    try {
      const result = await apiCall();
      
      // Clear timeout and confirm update
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      setValue(result);
      setIsPending(false);
      
      return result;
    } catch (error) {
      // Rollback on error
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      setValue(originalValue);
      setIsPending(false);
      
      throw error;
    }
  }, [value, timeout]);

  return {
    value,
    isPending,
    optimisticUpdate,
    setValue
  };
}
