/**
 * Demo Mode Service
 * Implements gated demo mode with usage limits and upgrade prompts
 */

import { createSupabaseServerClient } from './supabase-server';
import { auth } from '@clerk/nextjs/server';

// Demo mode configuration
export const DEMO_CONFIG = {
  // Trial period duration
  trialDurationDays: 7,
  
  // Demo usage limits
  limits: {
    summaries: 3,           // 3 summaries during trial
    exports: 2,             // 2 exports during trial
    aiRequests: 5,          // 5 AI requests during trial
    fileUploads: 3,         // 3 file uploads during trial
    slackConnections: 1,    // 1 Slack workspace during trial
  },
  
  // Features available in demo mode
  features: {
    basicSummaries: true,
    fileUpload: true,
    slackIntegration: true,
    basicExports: true,
    dashboard: true,
    analytics: false,       // Premium feature
    customTemplates: false, // Premium feature
    teamManagement: false,  // Premium feature
    advancedAI: false,      // Premium feature
  },
  
  // Upgrade prompts configuration
  upgradePrompts: {
    frequency: 'after_limit', // 'after_limit', 'daily', 'weekly'
    showAfterActions: 2,      // Show upgrade prompt after 2 actions
    maxPromptsPerDay: 3,      // Maximum upgrade prompts per day
  }
};

export interface DemoUsage {
  id: string;
  user_id: string;
  summaries_used: number;
  exports_used: number;
  ai_requests_used: number;
  file_uploads_used: number;
  slack_connections_used: number;
  trial_started_at: string;
  trial_expires_at: string;
  upgrade_prompts_shown: number;
  last_prompt_shown_at?: string;
  created_at: string;
  updated_at: string;
}

export interface DemoStatus {
  isInDemo: boolean;
  trialDaysRemaining: number;
  trialExpired: boolean;
  usage: {
    summaries: { used: number; limit: number; remaining: number };
    exports: { used: number; limit: number; remaining: number };
    aiRequests: { used: number; limit: number; remaining: number };
    fileUploads: { used: number; limit: number; remaining: number };
    slackConnections: { used: number; limit: number; remaining: number };
  };
  canUseFeature: (feature: keyof typeof DEMO_CONFIG.features) => boolean;
  shouldShowUpgradePrompt: boolean;
  upgradePromptMessage: string;
}

/**
 * Initialize demo mode for a new user
 */
export async function initializeDemoMode(userId: string): Promise<DemoUsage | null> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Check if user already has demo usage record
    const { data: existing } = await supabase
      .from('demo_usage')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (existing) {
      return existing;
    }
    
    // Create new demo usage record
    const trialStartDate = new Date();
    const trialEndDate = new Date();
    trialEndDate.setDate(trialStartDate.getDate() + DEMO_CONFIG.trialDurationDays);
    
    const { data, error } = await supabase
      .from('demo_usage')
      .insert({
        user_id: userId,
        summaries_used: 0,
        exports_used: 0,
        ai_requests_used: 0,
        file_uploads_used: 0,
        slack_connections_used: 0,
        trial_started_at: trialStartDate.toISOString(),
        trial_expires_at: trialEndDate.toISOString(),
        upgrade_prompts_shown: 0,
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error initializing demo mode:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in initializeDemoMode:', error);
    return null;
  }
}

/**
 * Get demo status for user
 */
export async function getDemoStatus(userId: string): Promise<DemoStatus> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get demo usage record
    let { data: demoUsage } = await supabase
      .from('demo_usage')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    // Initialize demo mode if not exists
    if (!demoUsage) {
      demoUsage = await initializeDemoMode(userId);
    }
    
    if (!demoUsage) {
      // Fallback to non-demo status
      return createNonDemoStatus();
    }
    
    // Calculate trial status
    const now = new Date();
    const trialExpires = new Date(demoUsage.trial_expires_at);
    const trialExpired = now > trialExpires;
    const trialDaysRemaining = Math.max(0, Math.ceil((trialExpires.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
    
    // Calculate usage status
    const usage = {
      summaries: {
        used: demoUsage.summaries_used,
        limit: DEMO_CONFIG.limits.summaries,
        remaining: Math.max(0, DEMO_CONFIG.limits.summaries - demoUsage.summaries_used)
      },
      exports: {
        used: demoUsage.exports_used,
        limit: DEMO_CONFIG.limits.exports,
        remaining: Math.max(0, DEMO_CONFIG.limits.exports - demoUsage.exports_used)
      },
      aiRequests: {
        used: demoUsage.ai_requests_used,
        limit: DEMO_CONFIG.limits.aiRequests,
        remaining: Math.max(0, DEMO_CONFIG.limits.aiRequests - demoUsage.ai_requests_used)
      },
      fileUploads: {
        used: demoUsage.file_uploads_used,
        limit: DEMO_CONFIG.limits.fileUploads,
        remaining: Math.max(0, DEMO_CONFIG.limits.fileUploads - demoUsage.file_uploads_used)
      },
      slackConnections: {
        used: demoUsage.slack_connections_used,
        limit: DEMO_CONFIG.limits.slackConnections,
        remaining: Math.max(0, DEMO_CONFIG.limits.slackConnections - demoUsage.slack_connections_used)
      }
    };
    
    // Check if should show upgrade prompt
    const shouldShowUpgradePromptNow = shouldShowUpgradePrompt(demoUsage, usage);
    
    return {
      isInDemo: true,
      trialDaysRemaining,
      trialExpired,
      usage,
      canUseFeature: (feature: keyof typeof DEMO_CONFIG.features) => {
        return DEMO_CONFIG.features[feature] === true;
      },
      shouldShowUpgradePrompt: shouldShowUpgradePromptNow,
      upgradePromptMessage: generateUpgradePromptMessage(usage, trialDaysRemaining, trialExpired)
    };
    
  } catch (error) {
    console.error('Error getting demo status:', error);
    return createNonDemoStatus();
  }
}

/**
 * Create non-demo status (for paid users)
 */
function createNonDemoStatus(): DemoStatus {
  return {
    isInDemo: false,
    trialDaysRemaining: 0,
    trialExpired: false,
    usage: {
      summaries: { used: 0, limit: -1, remaining: -1 },
      exports: { used: 0, limit: -1, remaining: -1 },
      aiRequests: { used: 0, limit: -1, remaining: -1 },
      fileUploads: { used: 0, limit: -1, remaining: -1 },
      slackConnections: { used: 0, limit: -1, remaining: -1 }
    },
    canUseFeature: () => true,
    shouldShowUpgradePrompt: false,
    upgradePromptMessage: ''
  };
}

/**
 * Check if should show upgrade prompt now
 */
export function shouldShowUpgradePrompt(
  demoUsage: DemoUsage,
  usage: DemoStatus['usage']
): boolean {
  const now = new Date();
  const lastPromptShown = demoUsage.last_prompt_shown_at ? new Date(demoUsage.last_prompt_shown_at) : null;

  // Check if any limit is reached
  const anyLimitReached = Object.values(usage).some(u => u.remaining === 0);

  // Check daily prompt limit
  if (lastPromptShown) {
    const hoursSinceLastPrompt = (now.getTime() - lastPromptShown.getTime()) / (1000 * 60 * 60);
    if (hoursSinceLastPrompt < 24 && demoUsage.upgrade_prompts_shown >= DEMO_CONFIG.upgradePrompts.maxPromptsPerDay) {
      return false;
    }
  }

  // Show prompt if any limit is reached
  if (DEMO_CONFIG.upgradePrompts.frequency === 'after_limit' && anyLimitReached) {
    return true;
  }

  // Show prompt after certain number of actions
  const totalActions = demoUsage.summaries_used + demoUsage.exports_used + demoUsage.file_uploads_used;
  if (totalActions >= DEMO_CONFIG.upgradePrompts.showAfterActions && totalActions % DEMO_CONFIG.upgradePrompts.showAfterActions === 0) {
    return true;
  }

  return false;
}

/**
 * Generate upgrade prompt message
 */
function generateUpgradePromptMessage(
  usage: DemoStatus['usage'],
  trialDaysRemaining: number,
  trialExpired: boolean
): string {
  if (trialExpired) {
    return 'Your trial has expired. Upgrade to continue using all features.';
  }

  const limitReached = Object.entries(usage).find(([_, u]) => u.remaining === 0);
  if (limitReached) {
    const [feature] = limitReached;
    return `You've reached your ${feature} limit. Upgrade to get unlimited access.`;
  }

  if (trialDaysRemaining <= 2) {
    return `Only ${trialDaysRemaining} days left in your trial. Upgrade now to continue.`;
  }

  return `Upgrade to unlock unlimited summaries, exports, and premium features.`;
}

/**
 * Increment usage counter
 */
export async function incrementDemoUsage(
  userId: string,
  usageType: 'summaries' | 'exports' | 'aiRequests' | 'fileUploads' | 'slackConnections'
): Promise<boolean> {
  try {
    const supabase = await createSupabaseServerClient();

    const columnMap = {
      summaries: 'summaries_used',
      exports: 'exports_used',
      aiRequests: 'ai_requests_used',
      fileUploads: 'file_uploads_used',
      slackConnections: 'slack_connections_used'
    };

    const column = columnMap[usageType];

    const { error } = await supabase
      .from('demo_usage')
      .update({
        [column]: supabase.rpc('increment', { x: 1 }),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (error) {
      console.error('Error incrementing demo usage:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in incrementDemoUsage:', error);
    return false;
  }
}

/**
 * Check if user can perform action
 */
export async function canPerformAction(
  userId: string,
  actionType: 'summaries' | 'exports' | 'aiRequests' | 'fileUploads' | 'slackConnections'
): Promise<{ allowed: boolean; reason?: string; upgradePrompt?: string }> {
  try {
    const demoStatus = await getDemoStatus(userId);

    if (!demoStatus.isInDemo) {
      return { allowed: true };
    }

    if (demoStatus.trialExpired) {
      return {
        allowed: false,
        reason: 'Trial expired',
        upgradePrompt: 'Your trial has expired. Upgrade to continue using all features.'
      };
    }

    const usage = demoStatus.usage[actionType];
    if (usage.remaining <= 0) {
      return {
        allowed: false,
        reason: `${actionType} limit reached`,
        upgradePrompt: `You've reached your ${actionType} limit. Upgrade to get unlimited access.`
      };
    }

    return { allowed: true };
  } catch (error) {
    console.error('Error checking action permission:', error);
    return { allowed: true }; // Allow action on error to avoid blocking users
  }
}

/**
 * Record upgrade prompt shown
 */
export async function recordUpgradePromptShown(userId: string): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();

    await supabase
      .from('demo_usage')
      .update({
        upgrade_prompts_shown: supabase.rpc('increment', { x: 1 }),
        last_prompt_shown_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);
  } catch (error) {
    console.error('Error recording upgrade prompt:', error);
  }
}

/**
 * Check if user is in demo mode
 */
export async function isUserInDemoMode(userId: string): Promise<boolean> {
  try {
    const demoStatus = await getDemoStatus(userId);
    return demoStatus.isInDemo;
  } catch (error) {
    console.error('Error checking demo mode:', error);
    return false;
  }
}
